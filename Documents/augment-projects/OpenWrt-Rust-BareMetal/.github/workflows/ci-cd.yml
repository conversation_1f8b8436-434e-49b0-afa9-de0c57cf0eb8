name: OpenWrt Rust CI/CD Pipeline

on:
  push:
    branches: [ main, develop, feature/* ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]

env:
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: 1

jobs:
  # Code Quality and Security Checks
  quality-checks:
    name: Code Quality and Security
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Install Rust toolchain
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        components: rustfmt, clippy
        override: true
    
    - name: Cache cargo registry
      uses: actions/cache@v3
      with:
        path: ~/.cargo/registry
        key: ${{ runner.os }}-cargo-registry-${{ hashFiles('**/Cargo.lock') }}
    
    - name: Check code formatting
      run: cargo fmt --all -- --check
    
    - name: Run Clippy lints
      run: cargo clippy --all-targets --all-features -- -D warnings
    
    - name: Security audit
      uses: actions-rs/audit-check@v1
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Check for unsafe code
      run: |
        # Count unsafe blocks and report
        unsafe_count=$(grep -r "unsafe" src/ --include="*.rs" | wc -l)
        echo "Unsafe blocks found: $unsafe_count"
        if [ $unsafe_count -gt 50 ]; then
          echo "Too many unsafe blocks detected"
          exit 1
        fi

  # Build and Test Matrix
  build-test:
    name: Build and Test
    runs-on: ubuntu-latest
    needs: quality-checks
    strategy:
      matrix:
        target: [x86_64-unknown-linux-gnu, armv7-unknown-linux-gnueabihf, mips-unknown-linux-gnu]
        features: [default, comprehensive-testing, stress-testing, performance-testing]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Install Rust toolchain
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        target: ${{ matrix.target }}
        override: true
    
    - name: Install cross-compilation tools
      run: |
        sudo apt-get update
        sudo apt-get install -y gcc-arm-linux-gnueabihf gcc-mips-linux-gnu
    
    - name: Cache cargo build
      uses: actions/cache@v3
      with:
        path: target
        key: ${{ runner.os }}-cargo-build-${{ matrix.target }}-${{ hashFiles('**/Cargo.lock') }}
    
    - name: Build for target
      run: cargo build --target ${{ matrix.target }} --features ${{ matrix.features }}
    
    - name: Run unit tests
      if: matrix.target == 'x86_64-unknown-linux-gnu'
      run: cargo test --features ${{ matrix.features }}
    
    - name: Run integration tests
      if: matrix.target == 'x86_64-unknown-linux-gnu' && matrix.features == 'comprehensive-testing'
      run: cargo test --features comprehensive-testing --test integration_tests
    
    - name: Run stress tests
      if: matrix.target == 'x86_64-unknown-linux-gnu' && matrix.features == 'stress-testing'
      run: cargo test --features stress-testing --test stress_tests
    
    - name: Run performance tests
      if: matrix.target == 'x86_64-unknown-linux-gnu' && matrix.features == 'performance-testing'
      run: cargo test --features performance-testing --test performance_tests

  # Documentation Build and Validation
  documentation:
    name: Documentation
    runs-on: ubuntu-latest
    needs: quality-checks
    steps:
    - uses: actions/checkout@v4
    
    - name: Install Rust toolchain
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        override: true
    
    - name: Build documentation
      run: cargo doc --all-features --no-deps
    
    - name: Check documentation links
      run: |
        # Check for broken internal links in documentation
        find docs/ -name "*.md" -exec grep -l "](.*\.md)" {} \; | while read file; do
          echo "Checking links in $file"
          # Simple link validation - in production would use a proper tool
        done
    
    - name: Validate API documentation coverage
      run: |
        # Check that all public APIs are documented
        cargo doc --all-features 2>&1 | grep -i "missing documentation" && exit 1 || true
    
    - name: Deploy documentation
      if: github.ref == 'refs/heads/main'
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./target/doc

  # Security and Compliance Testing
  security-testing:
    name: Security Testing
    runs-on: ubuntu-latest
    needs: build-test
    steps:
    - uses: actions/checkout@v4
    
    - name: Install Rust toolchain
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        override: true
    
    - name: Run security tests
      run: cargo test --features comprehensive-testing security_tests
    
    - name: Memory safety validation
      run: |
        # Run tests with address sanitizer
        RUSTFLAGS="-Z sanitizer=address" cargo test --target x86_64-unknown-linux-gnu
    
    - name: Dependency vulnerability scan
      uses: actions-rs/audit-check@v1
      with:
        token: ${{ secrets.GITHUB_TOKEN }}

  # Performance Benchmarking
  performance-benchmarks:
    name: Performance Benchmarks
    runs-on: ubuntu-latest
    needs: build-test
    if: github.ref == 'refs/heads/main' || github.event_name == 'pull_request'
    steps:
    - uses: actions/checkout@v4
    
    - name: Install Rust toolchain
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        override: true
    
    - name: Run performance benchmarks
      run: cargo test --features performance-testing --release performance_benchmarks
    
    - name: Store benchmark results
      uses: benchmark-action/github-action-benchmark@v1
      with:
        tool: 'cargo'
        output-file-path: target/criterion/report/index.html
        github-token: ${{ secrets.GITHUB_TOKEN }}
        auto-push: true

  # Hardware-in-the-Loop Testing (Optional)
  hardware-testing:
    name: Hardware Testing
    runs-on: self-hosted
    needs: build-test
    if: github.ref == 'refs/heads/main' && contains(github.event.head_commit.message, '[hardware-test]')
    steps:
    - uses: actions/checkout@v4
    
    - name: Install Rust toolchain
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        override: true
    
    - name: Build for hardware target
      run: cargo build --target armv7-unknown-linux-gnueabihf --release
    
    - name: Deploy to test hardware
      run: |
        # Deploy to actual hardware for testing
        echo "Deploying to test hardware..."
        # This would involve actual hardware deployment
    
    - name: Run hardware tests
      run: |
        # Run tests on actual hardware
        echo "Running hardware tests..."
        # This would run tests on the deployed hardware

  # Staging Deployment
  staging-deployment:
    name: Staging Deployment
    runs-on: ubuntu-latest
    needs: [build-test, security-testing, performance-benchmarks]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    steps:
    - uses: actions/checkout@v4
    
    - name: Install Rust toolchain
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        override: true
    
    - name: Build release
      run: cargo build --release --all-features
    
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # This would deploy to staging infrastructure
    
    - name: Run staging validation tests
      run: |
        echo "Running staging validation..."
        # This would run validation tests against staging deployment

  # Production Deployment
  production-deployment:
    name: Production Deployment
    runs-on: ubuntu-latest
    needs: [build-test, security-testing, performance-benchmarks]
    if: github.event_name == 'release'
    environment: production
    steps:
    - uses: actions/checkout@v4
    
    - name: Install Rust toolchain
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        override: true
    
    - name: Build production release
      run: cargo build --release --all-features
    
    - name: Create deployment package
      run: |
        mkdir -p deployment-package
        cp target/release/openwrt-rust deployment-package/
        cp -r docs/ deployment-package/
        cp README.md deployment-package/
        tar -czf openwrt-rust-${{ github.event.release.tag_name }}.tar.gz deployment-package/
    
    - name: Upload release assets
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ github.event.release.upload_url }}
        asset_path: ./openwrt-rust-${{ github.event.release.tag_name }}.tar.gz
        asset_name: openwrt-rust-${{ github.event.release.tag_name }}.tar.gz
        asset_content_type: application/gzip
    
    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # This would deploy to production infrastructure
    
    - name: Run production health checks
      run: |
        echo "Running production health checks..."
        # This would run health checks against production deployment
    
    - name: Notify deployment success
      uses: 8398a7/action-slack@v3
      with:
        status: success
        text: "OpenWrt Rust ${{ github.event.release.tag_name }} successfully deployed to production"
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Cleanup
  cleanup:
    name: Cleanup
    runs-on: ubuntu-latest
    needs: [staging-deployment, production-deployment]
    if: always()
    steps:
    - name: Clean up temporary resources
      run: |
        echo "Cleaning up temporary resources..."
        # This would clean up any temporary resources created during the pipeline
