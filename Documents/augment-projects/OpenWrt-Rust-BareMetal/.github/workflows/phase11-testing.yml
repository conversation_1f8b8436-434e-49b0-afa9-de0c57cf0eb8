name: Phase 11.5 - Hybrid Package Management Testing

on:
  push:
    branches: [ main, phase11-* ]
    paths:
      - 'phase11-testing/**'
      - 'src/openwrt/package_manager.rs'
      - 'src/openwrt/hybrid_package_manager.rs'
      - '.github/workflows/phase11-testing.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'phase11-testing/**'
      - 'src/openwrt/package_manager.rs'
      - 'src/openwrt/hybrid_package_manager.rs'
  schedule:
    # Run daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      test_mode:
        description: 'Test execution mode'
        required: true
        default: 'standard'
        type: choice
        options:
          - standard
          - comprehensive
          - quick
      verbose_output:
        description: 'Enable verbose output'
        required: false
        default: false
        type: boolean

env:
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: 1
  HYBRID_TEST_VERBOSE: ${{ github.event.inputs.verbose_output || '0' }}
  HYBRID_TEST_PARALLEL: 2

jobs:
  # Pre-flight checks and environment setup
  preflight:
    name: Pre-flight Checks
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    outputs:
      test-mode: ${{ steps.config.outputs.test-mode }}
      should-run-comprehensive: ${{ steps.config.outputs.should-run-comprehensive }}
      
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Configure test execution
      id: config
      run: |
        TEST_MODE="${{ github.event.inputs.test_mode || 'standard' }}"
        echo "test-mode=${TEST_MODE}" >> $GITHUB_OUTPUT
        
        if [[ "${TEST_MODE}" == "comprehensive" ]] || [[ "${{ github.event_name }}" == "schedule" ]]; then
          echo "should-run-comprehensive=true" >> $GITHUB_OUTPUT
        else
          echo "should-run-comprehensive=false" >> $GITHUB_OUTPUT
        fi
        
        echo "Test mode: ${TEST_MODE}"
        echo "Comprehensive testing: $(if [[ "${TEST_MODE}" == "comprehensive" ]]; then echo "enabled"; else echo "disabled"; fi)"
    
    - name: Validate test framework structure
      run: |
        echo "Validating Phase 11.5 testing framework structure..."
        
        # Check for required test frameworks
        required_files=(
          "phase11-testing/README.md"
          "phase11-testing/run-all-tests.sh"
          "phase11-testing/package-compatibility/format-validation-suite.sh"
          "phase11-testing/integration-testing/integration-test-runner.sh"
          "phase11-testing/performance-validation/performance-test-suite.sh"
          "phase11-testing/security-validation/security-test-framework.sh"
        )
        
        missing_files=()
        for file in "${required_files[@]}"; do
          if [[ ! -f "${file}" ]]; then
            missing_files+=("${file}")
          fi
        done
        
        if [[ ${#missing_files[@]} -gt 0 ]]; then
          echo "❌ Missing required test framework files:"
          printf '%s\n' "${missing_files[@]}"
          exit 1
        fi
        
        echo "✅ Test framework structure validation passed"

  # Package Compatibility Testing
  package-compatibility:
    name: Package Compatibility Tests
    runs-on: ubuntu-latest
    needs: preflight
    timeout-minutes: 30
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y jq bc curl openssl gnupg2 coreutils
        
    - name: Setup Rust toolchain
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        override: true
        components: rustfmt, clippy
        
    - name: Run package compatibility tests
      run: |
        cd phase11-testing
        chmod +x package-compatibility/format-validation-suite.sh
        ./package-compatibility/format-validation-suite.sh run
        
    - name: Upload package compatibility results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: package-compatibility-results
        path: phase11-testing/package-compatibility/results/
        retention-days: 30

  # Integration Testing
  integration-testing:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: preflight
    timeout-minutes: 45
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y jq bc curl openssl gnupg2 coreutils
        
    - name: Setup Rust toolchain
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        override: true
        
    - name: Run integration tests
      run: |
        cd phase11-testing
        chmod +x integration-testing/integration-test-runner.sh
        ./integration-testing/integration-test-runner.sh run
        
    - name: Upload integration test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: integration-test-results
        path: phase11-testing/integration-testing/results/
        retention-days: 30

  # Performance Validation
  performance-validation:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: preflight
    timeout-minutes: 60
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y jq bc curl sysstat iotop nethogs stress-ng
        
    - name: Setup Rust toolchain
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        override: true
        
    - name: Run performance validation tests
      run: |
        cd phase11-testing
        chmod +x performance-validation/performance-test-suite.sh
        ./performance-validation/performance-test-suite.sh run
        
    - name: Upload performance test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: performance-test-results
        path: phase11-testing/performance-validation/results/
        retention-days: 30

  # Security Validation
  security-validation:
    name: Security Tests
    runs-on: ubuntu-latest
    needs: preflight
    timeout-minutes: 45
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y jq bc curl openssl gnupg2 coreutils util-linux
        
    - name: Install security scanning tools
      run: |
        # Install Grype for vulnerability scanning
        curl -sSfL https://raw.githubusercontent.com/anchore/grype/main/install.sh | sh -s -- -b /usr/local/bin
        
    - name: Setup Rust toolchain
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        override: true
        
    - name: Run security validation tests
      run: |
        cd phase11-testing
        chmod +x security-validation/security-test-framework.sh
        ./security-validation/security-test-framework.sh run
        
    - name: Upload security test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: security-test-results
        path: phase11-testing/security-validation/results/
        retention-days: 30

  # Comprehensive Testing (runs on schedule or manual trigger)
  comprehensive-testing:
    name: Comprehensive Test Suite
    runs-on: ubuntu-latest
    needs: [preflight, package-compatibility, integration-testing, performance-validation, security-validation]
    if: needs.preflight.outputs.should-run-comprehensive == 'true'
    timeout-minutes: 120
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Install comprehensive dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          jq bc curl openssl gnupg2 coreutils util-linux \
          sysstat iotop nethogs stress-ng \
          docker.io podman \
          qemu-system-x86 qemu-utils \
          bridge-utils iptables
          
    - name: Setup Rust toolchain
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        override: true
        components: rustfmt, clippy
        
    - name: Run comprehensive test suite
      env:
        HYBRID_TEST_COMPREHENSIVE: 1
        HYBRID_TEST_PARALLEL: 4
      run: |
        cd phase11-testing
        chmod +x run-all-tests.sh
        ./run-all-tests.sh comprehensive
        
    - name: Upload comprehensive test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: comprehensive-test-results
        path: phase11-testing/results/
        retention-days: 30

  # Test Report Generation and Analysis
  test-reporting:
    name: Generate Test Reports
    runs-on: ubuntu-latest
    needs: [package-compatibility, integration-testing, performance-validation, security-validation]
    if: always()
    timeout-minutes: 15
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download all test results
      uses: actions/download-artifact@v4
      with:
        path: test-artifacts/
        
    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y jq bc
        
    - name: Generate consolidated test report
      run: |
        mkdir -p consolidated-results
        
        # Consolidate all test results
        echo "Consolidating test results..."
        
        # Create consolidated JSON report
        cat > consolidated-results/phase11-5-consolidated-report.json << 'EOF'
        {
          "phase11_5_ci_report": {
            "metadata": {
              "phase": "11.5",
              "milestone": "Testing Framework Development",
              "ci_run_id": "${{ github.run_id }}",
              "ci_run_number": "${{ github.run_number }}",
              "commit_sha": "${{ github.sha }}",
              "branch": "${{ github.ref_name }}",
              "trigger": "${{ github.event_name }}",
              "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
            },
            "test_results": {
              "package_compatibility": "${{ needs.package-compatibility.result }}",
              "integration_testing": "${{ needs.integration-testing.result }}",
              "performance_validation": "${{ needs.performance-validation.result }}",
              "security_validation": "${{ needs.security-validation.result }}"
            },
            "overall_status": "$(if [[ "${{ needs.package-compatibility.result }}" == "success" && "${{ needs.integration-testing.result }}" == "success" && "${{ needs.performance-validation.result }}" == "success" && "${{ needs.security-validation.result }}" == "success" ]]; then echo "SUCCESS"; else echo "FAILURE"; fi)"
          }
        }
        EOF
        
        # Generate summary report
        cat > consolidated-results/test-summary.md << 'EOF'
        # Phase 11.5 - Testing Framework Development CI Report
        
        ## Test Execution Summary
        
        | Test Suite | Status | 
        |------------|--------|
        | Package Compatibility | ${{ needs.package-compatibility.result == 'success' && '✅ PASSED' || '❌ FAILED' }} |
        | Integration Testing | ${{ needs.integration-testing.result == 'success' && '✅ PASSED' || '❌ FAILED' }} |
        | Performance Validation | ${{ needs.performance-validation.result == 'success' && '✅ PASSED' || '❌ FAILED' }} |
        | Security Validation | ${{ needs.security-validation.result == 'success' && '✅ PASSED' || '❌ FAILED' }} |
        
        ## Overall Status
        
        **${{ (needs.package-compatibility.result == 'success' && needs.integration-testing.result == 'success' && needs.performance-validation.result == 'success' && needs.security-validation.result == 'success') && '✅ ALL TESTS PASSED' || '❌ SOME TESTS FAILED' }}**
        
        ## CI Information
        
        - **Run ID**: ${{ github.run_id }}
        - **Commit**: ${{ github.sha }}
        - **Branch**: ${{ github.ref_name }}
        - **Trigger**: ${{ github.event_name }}
        - **Timestamp**: $(date -u +%Y-%m-%dT%H:%M:%SZ)
        
        ## Next Steps
        
        ${{ (needs.package-compatibility.result == 'success' && needs.integration-testing.result == 'success' && needs.performance-validation.result == 'success' && needs.security-validation.result == 'success') && '- ✅ Milestone 11.5 testing framework is ready for production use\n- ✅ Proceed to Milestone 11.6 (Documentation and Migration)' || '- ❌ Review failed test cases and address issues\n- ❌ Re-run tests after fixes are implemented' }}
        EOF
        
    - name: Upload consolidated test report
      uses: actions/upload-artifact@v4
      with:
        name: consolidated-test-report
        path: consolidated-results/
        retention-days: 90
        
    - name: Comment PR with test results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const summary = fs.readFileSync('consolidated-results/test-summary.md', 'utf8');
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: summary
          });

  # Milestone Status Update
  milestone-status:
    name: Update Milestone Status
    runs-on: ubuntu-latest
    needs: [test-reporting]
    if: always() && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Update milestone status
      run: |
        echo "Updating Phase 11.5 milestone status..."
        
        # Determine overall status
        OVERALL_STATUS="FAILED"
        if [[ "${{ needs.package-compatibility.result }}" == "success" && \
              "${{ needs.integration-testing.result }}" == "success" && \
              "${{ needs.performance-validation.result }}" == "success" && \
              "${{ needs.security-validation.result }}" == "success" ]]; then
          OVERALL_STATUS="PASSED"
        fi
        
        echo "Phase 11.5 Testing Framework Status: ${OVERALL_STATUS}"
        
        # Update progress tracker (this would integrate with project management)
        if [[ "${OVERALL_STATUS}" == "PASSED" ]]; then
          echo "✅ Milestone 11.5 substantially complete - testing framework operational"
          echo "✅ Ready to proceed to Milestone 11.6 (Documentation and Migration)"
        else
          echo "❌ Milestone 11.5 requires attention - test failures detected"
          echo "❌ Address test failures before proceeding to next milestone"
        fi

# Workflow completion notification
  notify-completion:
    name: Notify Completion
    runs-on: ubuntu-latest
    needs: [milestone-status]
    if: always()
    
    steps:
    - name: Workflow completion summary
      run: |
        echo "Phase 11.5 Testing Framework CI Workflow Completed"
        echo "=============================================="
        echo "Package Compatibility: ${{ needs.package-compatibility.result }}"
        echo "Integration Testing: ${{ needs.integration-testing.result }}"
        echo "Performance Validation: ${{ needs.performance-validation.result }}"
        echo "Security Validation: ${{ needs.security-validation.result }}"
        echo "=============================================="
        
        if [[ "${{ needs.package-compatibility.result }}" == "success" && \
              "${{ needs.integration-testing.result }}" == "success" && \
              "${{ needs.performance-validation.result }}" == "success" && \
              "${{ needs.security-validation.result }}" == "success" ]]; then
          echo "🎉 Phase 11.5 Testing Framework Development: SUCCESS"
          echo "✅ Hybrid package management testing framework is operational"
          echo "✅ Ready for Milestone 11.6 (Documentation and Migration)"
        else
          echo "⚠️  Phase 11.5 Testing Framework Development: ISSUES DETECTED"
          echo "❌ Review test failures and address before proceeding"
        fi
