name: OpenWrt Rust Container Testing

on:
  push:
    branches: [ main, develop, feature/* ]
    paths:
      - 'container-testing/**'
      - 'src/**'
      - 'Cargo.toml'
      - 'Cargo.lock'
      - '.github/workflows/container-testing.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'container-testing/**'
      - 'src/**'
      - 'Cargo.toml'
      - 'Cargo.lock'
  schedule:
    # Run nightly container tests at 3 AM UTC
    - cron: '0 3 * * *'

env:
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: 1
  CONTAINER_TESTING_LOG_LEVEL: info

jobs:
  # Container Infrastructure Validation
  container-infrastructure:
    name: Container Infrastructure
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Install Podman
      run: |
        sudo apt-get update
        sudo apt-get install -y podman
        podman --version
    
    - name: Install dependencies
      run: |
        sudo apt-get install -y jq bc bridge-utils iperf3 netcat-openbsd
    
    - name: Validate container configurations
      run: |
        cd container-testing
        # Validate JSON configurations
        jq . configs/networks/4port-lan.json
        
        # Check script permissions
        find scripts/ -name "*.sh" -exec test -x {} \; || {
          echo "Making scripts executable..."
          chmod +x scripts/*.sh
        }
    
    - name: Setup container networks
      run: |
        cd container-testing
        ./scripts/4port-lan-setup.sh setup
    
    - name: Validate network setup
      run: |
        cd container-testing
        ./scripts/4port-lan-setup.sh validate
        
        # Verify networks exist
        podman network ls | grep openwrt-
    
    - name: Build container images
      run: |
        cd container-testing
        ./scripts/container-manager.sh build
    
    - name: Verify container images
      run: |
        podman images | grep openwrt-rust
        
        # Test image functionality
        podman run --rm openwrt-rust-testing:latest --help || true
    
    - name: Cleanup infrastructure
      if: always()
      run: |
        cd container-testing
        ./scripts/container-manager.sh stop || true
        ./scripts/4port-lan-setup.sh cleanup || true

  # Container Network Testing
  container-network-tests:
    name: Container Network Tests
    runs-on: ubuntu-latest
    needs: container-infrastructure
    timeout-minutes: 45
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Install Podman and dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y podman jq bc bridge-utils iperf3 netcat-openbsd
    
    - name: Setup testing environment
      run: |
        cd container-testing
        chmod +x scripts/*.sh tests/*/*.sh
        ./scripts/4port-lan-setup.sh setup
        ./scripts/container-manager.sh build
        ./scripts/container-manager.sh start
    
    - name: Wait for containers to be ready
      run: |
        sleep 15
        cd container-testing
        ./scripts/container-manager.sh status
    
    - name: Run network functionality tests
      run: |
        cd container-testing
        ./tests/network/4port-lan-tests.sh
    
    - name: Upload network test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: network-test-results
        path: container-testing/logs/*network*
    
    - name: Cleanup testing environment
      if: always()
      run: |
        cd container-testing
        ./scripts/container-manager.sh stop || true
        ./scripts/4port-lan-setup.sh cleanup || true

  # Container Performance Testing
  container-performance-tests:
    name: Container Performance Tests
    runs-on: ubuntu-latest
    needs: container-infrastructure
    timeout-minutes: 60
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Install Podman and dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y podman jq bc bridge-utils iperf3 netcat-openbsd
    
    - name: Setup testing environment
      run: |
        cd container-testing
        chmod +x scripts/*.sh tests/*/*.sh
        ./scripts/4port-lan-setup.sh setup
        ./scripts/container-manager.sh build
        ./scripts/container-manager.sh start
    
    - name: Wait for containers to be ready
      run: |
        sleep 15
        cd container-testing
        ./scripts/container-manager.sh status
    
    - name: Run performance tests
      run: |
        cd container-testing
        ./tests/performance/network-performance-tests.sh
    
    - name: Upload performance test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: performance-test-results
        path: container-testing/logs/*performance*
    
    - name: Cleanup testing environment
      if: always()
      run: |
        cd container-testing
        ./scripts/container-manager.sh stop || true
        ./scripts/4port-lan-setup.sh cleanup || true

  # Container Integration Testing
  container-integration-tests:
    name: Container Integration Tests
    runs-on: ubuntu-latest
    needs: [container-network-tests, container-performance-tests]
    timeout-minutes: 60
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Install Podman and dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y podman jq bc bridge-utils iperf3 netcat-openbsd python3
    
    - name: Setup testing environment
      run: |
        cd container-testing
        chmod +x scripts/*.sh tests/*/*.sh
        ./scripts/4port-lan-setup.sh setup
        ./scripts/container-manager.sh build
        ./scripts/container-manager.sh start
    
    - name: Wait for containers to be ready
      run: |
        sleep 15
        cd container-testing
        ./scripts/container-manager.sh status
    
    - name: Run integration tests
      run: |
        cd container-testing
        ./tests/integration/container-integration-tests.sh
    
    - name: Upload integration test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: integration-test-results
        path: container-testing/logs/*integration*
    
    - name: Cleanup testing environment
      if: always()
      run: |
        cd container-testing
        ./scripts/container-manager.sh stop || true
        ./scripts/4port-lan-setup.sh cleanup || true

  # Comprehensive Test Suite
  comprehensive-testing:
    name: Comprehensive Container Testing
    runs-on: ubuntu-latest
    needs: container-infrastructure
    if: github.event_name == 'schedule' || contains(github.event.head_commit.message, '[comprehensive-test]')
    timeout-minutes: 90
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Install Podman and dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y podman jq bc bridge-utils iperf3 netcat-openbsd python3
    
    - name: Run comprehensive test suite
      run: |
        cd container-testing
        chmod +x scripts/*.sh tests/*/*.sh
        ./scripts/run-all-tests.sh
    
    - name: Upload comprehensive test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: comprehensive-test-results
        path: container-testing/logs/
    
    - name: Generate test summary
      if: always()
      run: |
        cd container-testing/logs
        echo "## Container Testing Summary" >> $GITHUB_STEP_SUMMARY
        echo "### Test Results" >> $GITHUB_STEP_SUMMARY
        
        if [ -f comprehensive-test-report_*.json ]; then
          latest_report=$(ls -t comprehensive-test-report_*.json | head -1)
          total_suites=$(jq -r '.test_run.total_test_suites' "$latest_report")
          passed_suites=$(jq -r '.test_run.passed_test_suites' "$latest_report")
          success_rate=$(jq -r '.test_run.success_rate' "$latest_report")
          
          echo "- Total Test Suites: $total_suites" >> $GITHUB_STEP_SUMMARY
          echo "- Passed Test Suites: $passed_suites" >> $GITHUB_STEP_SUMMARY
          echo "- Success Rate: $success_rate%" >> $GITHUB_STEP_SUMMARY
        fi

  # Performance Comparison with VM Testing
  performance-comparison:
    name: Container vs VM Performance
    runs-on: ubuntu-latest
    needs: container-performance-tests
    if: github.ref == 'refs/heads/main' || github.event_name == 'pull_request'
    timeout-minutes: 45
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y podman jq bc
    
    - name: Run performance comparison
      run: |
        cd container-testing
        chmod +x scripts/*.sh
        ./scripts/performance-comparison.sh
    
    - name: Upload comparison results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: performance-comparison-results
        path: container-testing/logs/*comparison*
    
    - name: Check performance targets
      run: |
        cd container-testing/logs
        if [ -f performance-comparison_*.json ]; then
          latest_comparison=$(ls -t performance-comparison_*.json | head -1)
          speedup=$(jq -r '.comparison.speedup_factor' "$latest_comparison" 2>/dev/null || echo "0")
          
          if [ "$(echo "$speedup >= 2.0" | bc)" -eq 1 ]; then
            echo "✅ Performance target met: ${speedup}x speedup (target: 2x)"
          else
            echo "⚠️ Performance target not met: ${speedup}x speedup (target: 2x)"
            exit 1
          fi
        fi

  # Security and Compliance
  container-security:
    name: Container Security Testing
    runs-on: ubuntu-latest
    needs: container-infrastructure
    timeout-minutes: 30
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Install security scanning tools
      run: |
        sudo apt-get update
        sudo apt-get install -y podman
        
        # Install container security tools
        curl -sSfL https://raw.githubusercontent.com/anchore/grype/main/install.sh | sh -s -- -b /usr/local/bin
    
    - name: Build container images
      run: |
        cd container-testing
        chmod +x scripts/*.sh
        ./scripts/4port-lan-setup.sh setup
        ./scripts/container-manager.sh build
    
    - name: Run security scans
      run: |
        # Scan container images for vulnerabilities
        grype openwrt-rust-base:latest
        grype openwrt-rust-testing:latest
        grype network-tools:latest
    
    - name: Container configuration security check
      run: |
        cd container-testing
        
        # Check for security best practices
        echo "Checking container configurations..."
        
        # Verify non-root user usage
        if grep -r "USER root" configs/images/; then
          echo "⚠️ Found root user usage in container images"
        else
          echo "✅ No root user usage found"
        fi
        
        # Check for privileged containers
        if grep -r "privileged.*true" configs/; then
          echo "⚠️ Found privileged container usage"
        else
          echo "✅ No privileged container usage found"
        fi
    
    - name: Cleanup
      if: always()
      run: |
        cd container-testing
        ./scripts/container-manager.sh stop || true
        ./scripts/4port-lan-setup.sh cleanup || true
