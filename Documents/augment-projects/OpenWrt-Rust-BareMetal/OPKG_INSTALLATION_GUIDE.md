# OpenWrt Rust Firmware - Essential Packages Installation Guide

## Overview

This guide provides the exact `opkg install` commands to install and integrate the 16 essential OpenWrt packages with your Rust firmware test environment.

## Prerequisites

1. **OpenWrt Environment**: Running OpenWrt system with opkg package manager
2. **Network Access**: Internet connection for package downloads
3. **Rust Firmware**: Your OpenWrt Rust bare-metal firmware project

## Package Installation Commands

### Step 1: Update Package Lists

```bash
opkg update
```

### Step 2: Install Essential Packages by Category

#### 🌐 Core Networking & Utilities

```bash
# Advanced IP routing (mandatory for VLANs/VPNs)
opkg install ip-full

# Basic packet filtering (preinstalled but config-critical)
opkg install firewall

# DHCP/DNS server (with extra features like DoT/DoH)
opkg install dnsmasq-full

# Network bandwidth testing tool
opkg install iperf3

# Network packet analysis
opkg install tcpdump
```

#### 🔒 Security & Encryption

```bash
# Secure file transfers
opkg install openssh-sftp-server

# Trusted SSL certificates (for HTTPS connections)
opkg install ca-bundle

# TLS/SSL tunneling
opkg install stunnel
```

#### 📊 Monitoring & Debugging

```bash
# Console-based traffic monitoring
opkg install vnstat

# Process viewer (better than top)
opkg install htop

# View system logs (preinstalled but essential)
opkg install logread

# System performance monitoring tools
opkg install sysstat
```

#### 🛠️ Admin Essentials

```bash
# Terminal multiplexer (for background tasks)
opkg install screen

# User-friendly text editor
opkg install nano

# HTTP/HTTPS testing tool
opkg install curl

# JSON processor (for API interactions)
opkg install jq
```

### Step 3: One-Line Installation Command

For convenience, install all packages at once:

```bash
opkg install ip-full firewall dnsmasq-full iperf3 tcpdump openssh-sftp-server ca-bundle stunnel vnstat htop logread sysstat screen nano curl jq
```

## Integration with Rust Firmware

### Package Integration Configuration

The packages integrate with your Rust firmware through:

1. **Network Stack**: `ip-full`, `firewall`, `dnsmasq-full` provide networking capabilities
2. **Security Layer**: `openssh-sftp-server`, `ca-bundle`, `stunnel` enhance security
3. **Monitoring Framework**: `vnstat`, `htop`, `sysstat` provide system monitoring
4. **Administrative Tools**: `screen`, `nano`, `curl`, `jq` support management tasks

### Testing Package Integration

After installation, test the integration:

```bash
# Test the enhanced firmware with packages
./test-firmware-with-packages.sh

# Validate package functionality
./validate-essential-packages.sh
```

## Package Usage Examples

### Network Testing
```bash
# Test network bandwidth
iperf3 -s                    # Server mode
iperf3 -c <server_ip>        # Client mode

# Monitor network traffic
vnstat -l                    # Live view
vnstat -d                    # Daily statistics

# Analyze network packets
tcpdump -i eth0              # Capture on eth0
```

### System Monitoring
```bash
# Process monitoring
htop                         # Interactive process viewer

# System performance
iostat 1                     # I/O statistics every second
sar 1 10                     # System activity report

# View system logs
logread                      # Current logs
logread -f                   # Follow logs
```

### Administrative Tasks
```bash
# Terminal multiplexing
screen -S session_name       # Create named session
screen -r session_name       # Reattach to session

# JSON processing
echo '{"key":"value"}' | jq .key    # Extract value
cat config.json | jq '.network'    # Parse configuration

# HTTP testing
curl -I http://example.com   # Headers only
curl -X POST -d '{}' http://api.example.com/endpoint
```

### Security Operations
```bash
# SSL/TLS tunneling
stunnel /etc/stunnel/stunnel.conf

# SFTP server (usually auto-started)
systemctl status openssh-sftp-server

# SSL certificate verification
curl --cacert /etc/ssl/certs/ca-certificates.crt https://example.com
```

## Verification Commands

### Check Installation Status
```bash
# List all installed packages
opkg list-installed | grep -E "(ip-full|firewall|dnsmasq-full|iperf3|tcpdump|openssh-sftp-server|ca-bundle|stunnel|vnstat|htop|logread|sysstat|screen|nano|curl|jq)"

# Check specific package
opkg list-installed | grep package_name

# Get package information
opkg info package_name
```

### Test Package Functionality
```bash
# Test each package
ip --version
fw3 --help
dnsmasq --version
iperf3 --version
tcpdump --version
sftp-server --help
ls /etc/ssl/certs/
stunnel -version
vnstat --version
htop --version
logread -h
iostat -V
screen -version
nano --version
curl --version
jq --version
```

## Troubleshooting

### Common Issues

1. **Package Not Found**
   ```bash
   opkg update
   opkg list | grep package_name
   ```

2. **Dependency Issues**
   ```bash
   opkg install --force-depends package_name
   ```

3. **Storage Space**
   ```bash
   df -h
   opkg clean
   ```

4. **Repository Issues**
   ```bash
   cat /etc/opkg.conf
   ping downloads.openwrt.org
   ```

### Package Alternatives

If a package is not available, consider alternatives:

- `ip-full` → `ip` (basic version)
- `dnsmasq-full` → `dnsmasq` (basic version)
- `nano` → `vi` (minimal editor)
- `htop` → `top` (basic process viewer)

## Integration Results

After successful installation, your OpenWrt Rust firmware will have:

✅ **Complete Networking Stack**: Advanced routing, DNS/DHCP, firewall
✅ **Security Infrastructure**: SSH, SSL/TLS, certificate management
✅ **Monitoring Capabilities**: Traffic, process, and system monitoring
✅ **Administrative Tools**: Terminal management, text editing, HTTP testing

## Next Steps

1. **Configure Services**: Set up dnsmasq, firewall rules, monitoring
2. **Test Integration**: Run comprehensive functionality tests
3. **Performance Tuning**: Optimize package configurations for your use case
4. **Documentation**: Document your specific configuration and usage patterns

## Support

For package-specific issues:
- Check OpenWrt documentation: https://openwrt.org/docs/
- Package-specific man pages: `man package_name`
- OpenWrt forums: https://forum.openwrt.org/

For Rust firmware integration:
- Review `package-integration.json` for configuration details
- Use the provided test scripts for validation
- Check firmware logs for integration issues
