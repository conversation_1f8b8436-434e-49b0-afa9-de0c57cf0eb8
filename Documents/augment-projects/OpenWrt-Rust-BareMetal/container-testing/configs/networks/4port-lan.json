{"name": "openwrt-4port-lan", "version": "1.0.0", "description": "4 Port LAN configuration for OpenWrt Rust container testing", "networks": {"wan": {"name": "openwrt-wan", "driver": "bridge", "subnet": "*************/24", "gateway": "*************", "options": {"com.docker.network.bridge.name": "openwrt-wan0", "com.docker.network.bridge.enable_icc": "true", "com.docker.network.bridge.enable_ip_masquerade": "true"}, "labels": {"openwrt.interface": "wan", "openwrt.port": "1"}}, "lan1": {"name": "openwrt-lan1", "driver": "bridge", "subnet": "***********/24", "gateway": "***********", "options": {"com.docker.network.bridge.name": "openwrt-lan1", "com.docker.network.bridge.enable_icc": "true", "com.docker.network.bridge.enable_ip_masquerade": "false"}, "labels": {"openwrt.interface": "lan", "openwrt.port": "2", "openwrt.vlan": "10"}}, "lan2": {"name": "openwrt-lan2", "driver": "bridge", "subnet": "***********/24", "gateway": "***********", "options": {"com.docker.network.bridge.name": "openwrt-lan2", "com.docker.network.bridge.enable_icc": "true", "com.docker.network.bridge.enable_ip_masquerade": "false"}, "labels": {"openwrt.interface": "lan", "openwrt.port": "3", "openwrt.vlan": "20"}}, "lan3": {"name": "openwrt-lan3", "driver": "bridge", "subnet": "***********/24", "gateway": "***********", "options": {"com.docker.network.bridge.name": "openwrt-lan3", "com.docker.network.bridge.enable_icc": "true", "com.docker.network.bridge.enable_ip_masquerade": "false"}, "labels": {"openwrt.interface": "lan", "openwrt.port": "4", "openwrt.vlan": "30"}}, "management": {"name": "openwrt-mgmt", "driver": "bridge", "subnet": "**********/24", "gateway": "**********", "options": {"com.docker.network.bridge.name": "openwrt-mgmt", "com.docker.network.bridge.enable_icc": "true", "com.docker.network.bridge.enable_ip_masquerade": "false"}, "labels": {"openwrt.interface": "management", "openwrt.purpose": "testing"}}}, "vlans": {"vlan10": {"id": 10, "name": "LAN1-VLAN", "description": "VLAN for LAN port 2", "subnet": "***********/24", "ports": ["lan1"]}, "vlan20": {"id": 20, "name": "LAN2-VLAN", "description": "VLAN for LAN port 3", "subnet": "***********/24", "ports": ["lan2"]}, "vlan30": {"id": 30, "name": "LAN3-VLAN", "description": "VLAN for LAN port 4", "subnet": "***********/24", "ports": ["lan3"]}}, "port_mapping": {"port1": {"interface": "wan", "network": "wan", "type": "external", "description": "WAN port for external connectivity"}, "port2": {"interface": "lan1", "network": "lan1", "type": "internal", "vlan": 10, "description": "LAN port 2 with VLAN 10"}, "port3": {"interface": "lan2", "network": "lan2", "type": "internal", "vlan": 20, "description": "LAN port 3 with VLAN 20"}, "port4": {"interface": "lan3", "network": "lan3", "type": "internal", "vlan": 30, "description": "LAN port 4 with VLAN 30"}}, "firewall_rules": {"wan_to_lan": {"action": "DROP", "description": "Block WAN to LAN traffic by default"}, "lan_to_wan": {"action": "ACCEPT", "description": "Allow LAN to WAN traffic with NAT"}, "inter_vlan": {"action": "DROP", "description": "Block inter-VLAN communication by default"}, "management": {"action": "ACCEPT", "description": "Allow management network access"}}, "testing_scenarios": {"basic_connectivity": {"description": "Test basic connectivity between ports", "networks": ["wan", "lan1", "lan2", "lan3"]}, "vlan_isolation": {"description": "Test VLAN isolation between LAN ports", "networks": ["lan1", "lan2", "lan3"]}, "nat_functionality": {"description": "Test NAT functionality from LAN to WAN", "networks": ["wan", "lan1", "lan2", "lan3"]}, "firewall_rules": {"description": "Test firewall rule enforcement", "networks": ["wan", "lan1", "lan2", "lan3", "management"]}}}