# Use official OpenWrt rootfs as base for native container approach
FROM openwrt/rootfs:x86-64

# Create necessary directories and fix permissions
RUN mkdir -p /var/lock /var/run /tmp && \
    chmod 755 /var/lock /var/run /tmp

# Copy pre-built OpenWrt Rust userspace binary
COPY target/x86_64-unknown-linux-gnu/release/openwrt-userspace /usr/sbin/openwrt-rust

# Make it executable
RUN chmod +x /usr/sbin/openwrt-rust

# Set up OpenWrt Rust as init process (PID 1)
# This allows the Rust implementation to act as the main system process
COPY container-testing/scripts/openwrt-rust-init.sh /sbin/init
RUN chmod +x /sbin/init

# Expose standard OpenWrt ports
EXPOSE 22 80 443

# Use exec format to ensure proper PID 1 handling
CMD ["/sbin/init"]
