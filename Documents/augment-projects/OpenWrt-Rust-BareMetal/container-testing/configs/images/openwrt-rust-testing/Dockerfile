# OpenWrt Rust Testing Container Image
# Testing-specific image with OpenWrt Rust implementation

FROM openwrt-rust-base:latest

# Metadata
LABEL maintainer="OpenWrt Rust Project"
LABEL description="Testing container for OpenWrt Rust implementation"
LABEL version="1.0.0"
LABEL openwrt.component="testing"

# Switch to root for installation
USER root

# Install additional testing dependencies
RUN apt-get update && apt-get install -y \
    # Network testing tools
    nmap \
    netstat-nat \
    ss \
    lsof \
    wireshark-common \
    tshark \
    # Performance testing
    stress-ng \
    sysbench \
    # Monitoring tools
    sysstat \
    iotop \
    nethogs \
    # Debugging tools
    ltrace \
    strace \
    gdb \
    # Container tools
    runc \
    # Additional utilities
    vim \
    nano \
    tmux \
    screen \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install network testing tools from source
RUN cd /tmp \
    && wget https://github.com/HewlettPackard/netperf/archive/netperf-2.7.0.tar.gz \
    && tar -xzf netperf-2.7.0.tar.gz \
    && cd netperf-netperf-2.7.0 \
    && ./configure --enable-demo \
    && make && make install \
    && cd / && rm -rf /tmp/netperf*

# Copy OpenWrt Rust implementation
COPY src/ ${OPENWRT_RUST_HOME}/src/
COPY Cargo.toml Cargo.lock ${OPENWRT_RUST_HOME}/
COPY build.rs ${OPENWRT_RUST_HOME}/

# Copy OpenWrt headers and dependencies
COPY openwrt-headers/ ${OPENWRT_RUST_HOME}/openwrt-headers/

# Set ownership
RUN chown -R openwrt:openwrt ${OPENWRT_RUST_HOME}

# Switch to openwrt user for building
USER openwrt

# Build the OpenWrt Rust implementation
WORKDIR ${OPENWRT_RUST_HOME}
RUN cargo build --release --target x86_64-unknown-linux-gnu

# Create symlinks for easy access
RUN ln -sf ${OPENWRT_RUST_HOME}/target/x86_64-unknown-linux-gnu/release/openwrt-rust-baremetal \
    ${OPENWRT_RUST_HOME}/bin/openwrt-rust

# Switch back to root for final setup
USER root

# Copy container-specific scripts
COPY container-testing/scripts/container-entrypoint.sh /usr/local/bin/
COPY container-testing/scripts/network-setup.sh /usr/local/bin/
COPY container-testing/scripts/test-runner.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/container-entrypoint.sh \
    /usr/local/bin/network-setup.sh \
    /usr/local/bin/test-runner.sh

# Copy test suites
COPY container-testing/tests/ ${OPENWRT_RUST_HOME}/tests/
RUN chown -R openwrt:openwrt ${OPENWRT_RUST_HOME}/tests/

# Copy configuration templates
COPY container-testing/configs/templates/ /etc/openwrt-rust/templates/

# Create runtime directories
RUN mkdir -p \
    /var/run/openwrt-rust \
    /var/lib/openwrt-rust/uci \
    /var/lib/openwrt-rust/network \
    /var/log/openwrt-rust/network \
    /var/log/openwrt-rust/system

# Set up proper permissions
RUN chown -R openwrt:openwrt \
    /var/run/openwrt-rust \
    /var/lib/openwrt-rust \
    /var/log/openwrt-rust

# Configure network interfaces
RUN echo 'auto lo' > /etc/network/interfaces.d/openwrt-rust \
    && echo 'iface lo inet loopback' >> /etc/network/interfaces.d/openwrt-rust

# Set up sysctl for networking
RUN echo 'net.ipv4.ip_forward=1' >> /etc/sysctl.d/99-openwrt-rust.conf \
    && echo 'net.ipv6.conf.all.forwarding=1' >> /etc/sysctl.d/99-openwrt-rust.conf \
    && echo 'net.bridge.bridge-nf-call-iptables=0' >> /etc/sysctl.d/99-openwrt-rust.conf \
    && echo 'net.bridge.bridge-nf-call-ip6tables=0' >> /etc/sysctl.d/99-openwrt-rust.conf

# Create service files for systemd-like behavior
RUN mkdir -p /etc/services.d/openwrt-rust
COPY container-testing/configs/services/openwrt-rust.conf /etc/services.d/openwrt-rust/

# Set up logging configuration
RUN mkdir -p /etc/rsyslog.d
COPY container-testing/configs/logging/openwrt-rust.conf /etc/rsyslog.d/

# Create startup script
RUN cat > /usr/local/bin/startup.sh << 'EOF'
#!/bin/bash
set -e

# Apply sysctl settings
sysctl -p /etc/sysctl.d/99-openwrt-rust.conf

# Start rsyslog for logging
service rsyslog start

# Set up network interfaces
/usr/local/bin/network-setup.sh

# Start OpenWrt Rust implementation
exec /usr/local/bin/container-entrypoint.sh "$@"
EOF

RUN chmod +x /usr/local/bin/startup.sh

# Enhanced health check for testing
COPY container-testing/scripts/testing-health-check.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/testing-health-check.sh

HEALTHCHECK --interval=15s --timeout=5s --start-period=10s --retries=5 \
    CMD /usr/local/bin/testing-health-check.sh

# Expose additional testing ports
EXPOSE 5001 5201 12345 54321

# Default environment for testing
ENV OPENWRT_RUST_MODE=testing
ENV OPENWRT_RUST_LOG_LEVEL=debug
ENV OPENWRT_RUST_NETWORK_MODE=4port-lan

# Default user for testing
USER openwrt

# Set working directory
WORKDIR ${OPENWRT_RUST_HOME}

# Default entrypoint
ENTRYPOINT ["/usr/local/bin/startup.sh"]
CMD ["router"]
