# Network Tools Container Image
# Specialized container for network testing and diagnostics

FROM ubuntu:22.04

# Metadata
LABEL maintainer="OpenWrt Rust Project"
LABEL description="Network tools container for testing and diagnostics"
LABEL version="1.0.0"
LABEL openwrt.component="network-tools"

# Environment variables
ENV DEBIAN_FRONTEND=noninteractive

# Install comprehensive network tools
RUN apt-get update && apt-get install -y \
    # Basic network tools
    iproute2 \
    iputils-ping \
    iputils-tracepath \
    iputils-arping \
    netcat-openbsd \
    telnet \
    wget \
    curl \
    # Advanced network tools
    nmap \
    masscan \
    zmap \
    # Network monitoring
    tcpdump \
    wireshark-common \
    tshark \
    nethogs \
    iftop \
    nload \
    bmon \
    # Network testing
    iperf3 \
    netperf \
    hping3 \
    mtr-tiny \
    # DNS tools
    dnsutils \
    bind9-dnsutils \
    # DHCP tools
    dhcping \
    # Bridge and VLAN tools
    bridge-utils \
    vlan \
    # Routing tools
    quagga \
    bird2 \
    # Load testing
    siege \
    apache2-utils \
    # SSL/TLS tools
    openssl \
    gnutls-bin \
    # Network analysis
    ethtool \
    ss \
    lsof \
    netstat \
    # Packet crafting
    scapy \
    # Network simulation
    mininet \
    # Container networking
    containernetworking-plugins \
    # System tools
    procps \
    htop \
    strace \
    # Text processing
    jq \
    xmlstarlet \
    # Scripting
    python3 \
    python3-pip \
    bash \
    # Cleanup
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Python network tools
RUN pip3 install \
    scapy \
    netaddr \
    ipaddress \
    requests \
    paramiko \
    fabric \
    psutil \
    speedtest-cli

# Install additional network testing tools from source
RUN cd /tmp \
    # Install iperf2 for compatibility
    && wget https://downloads.es.net/pub/iperf/iperf-2.1.9.tar.gz \
    && tar -xzf iperf-2.1.9.tar.gz \
    && cd iperf-2.1.9 \
    && ./configure && make && make install \
    && cd /tmp \
    # Install sockperf
    && wget https://github.com/Mellanox/sockperf/archive/sockperf-3.10.tar.gz \
    && tar -xzf sockperf-3.10.tar.gz \
    && cd sockperf-sockperf-3.10 \
    && ./autogen.sh && ./configure && make && make install \
    && cd / && rm -rf /tmp/*

# Create network tools user
RUN groupadd -r nettools && useradd -r -g nettools -d /home/<USER>/bin/bash nettools

# Create directories for tools and scripts
RUN mkdir -p \
    /opt/network-tools \
    /opt/network-tools/scripts \
    /opt/network-tools/configs \
    /opt/network-tools/results \
    /var/log/network-tools

# Copy network testing scripts
COPY container-testing/scripts/network-tests/ /opt/network-tools/scripts/
RUN chmod +x /opt/network-tools/scripts/*.sh

# Copy network configuration templates
COPY container-testing/configs/network-tools/ /opt/network-tools/configs/

# Create utility scripts
RUN cat > /opt/network-tools/scripts/network-info.sh << 'EOF'
#!/bin/bash
# Network information gathering script

echo "=== Network Interface Information ==="
ip addr show

echo -e "\n=== Routing Table ==="
ip route show

echo -e "\n=== Network Statistics ==="
ss -tuln

echo -e "\n=== ARP Table ==="
ip neigh show

echo -e "\n=== Bridge Information ==="
if command -v brctl &> /dev/null; then
    brctl show
fi

echo -e "\n=== VLAN Information ==="
if [ -f /proc/net/vlan/config ]; then
    cat /proc/net/vlan/config
fi

echo -e "\n=== Network Namespaces ==="
ip netns list
EOF

RUN cat > /opt/network-tools/scripts/connectivity-test.sh << 'EOF'
#!/bin/bash
# Basic connectivity testing script

TARGET=${1:-*******}
COUNT=${2:-5}

echo "=== Connectivity Test to $TARGET ==="

echo "Ping test:"
ping -c $COUNT $TARGET

echo -e "\nTraceroute test:"
traceroute $TARGET

echo -e "\nTCP connectivity test (port 80):"
nc -zv $TARGET 80

echo -e "\nDNS resolution test:"
nslookup google.com
EOF

RUN cat > /opt/network-tools/scripts/performance-test.sh << 'EOF'
#!/bin/bash
# Network performance testing script

SERVER=${1:-iperf.he.net}
PORT=${2:-5201}
DURATION=${3:-10}

echo "=== Network Performance Test ==="

echo "iperf3 TCP test:"
iperf3 -c $SERVER -p $PORT -t $DURATION

echo -e "\niperf3 UDP test:"
iperf3 -c $SERVER -p $PORT -u -t $DURATION

echo -e "\nBandwidth test with speedtest-cli:"
speedtest-cli --simple
EOF

# Make scripts executable
RUN chmod +x /opt/network-tools/scripts/*.sh

# Set ownership
RUN chown -R nettools:nettools /opt/network-tools /var/log/network-tools

# Create health check script
RUN cat > /usr/local/bin/health-check.sh << 'EOF'
#!/bin/bash
# Health check for network tools container

# Check if basic network tools are available
command -v ping >/dev/null 2>&1 || exit 1
command -v ip >/dev/null 2>&1 || exit 1
command -v iperf3 >/dev/null 2>&1 || exit 1

# Check if we can resolve DNS
nslookup google.com >/dev/null 2>&1 || exit 1

exit 0
EOF

RUN chmod +x /usr/local/bin/health-check.sh

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD /usr/local/bin/health-check.sh

# Set PATH to include our tools
ENV PATH="/opt/network-tools/scripts:${PATH}"

# Default working directory
WORKDIR /opt/network-tools

# Default user
USER nettools

# Default command
CMD ["/bin/bash"]
