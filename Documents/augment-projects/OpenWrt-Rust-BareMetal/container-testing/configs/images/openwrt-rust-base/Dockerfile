# OpenWrt Rust Base Container Image
# Base image for OpenWrt Rust implementation testing with opkg support

FROM openwrt/rootfs:x86-64-23.05.2

# Metadata
LABEL maintainer="OpenWrt Rust Project"
LABEL description="OpenWrt container with Rust implementation and opkg support"
LABEL version="1.0.0"
LABEL openwrt.component="base"
LABEL openwrt.version="23.05.2"

# Environment variables
ENV OPENWRT_RUST_HOME=/opt/openwrt-rust
ENV PATH="${OPENWRT_RUST_HOME}/bin:${PATH}"

# Create necessary directories for opkg
RUN mkdir -p /var/lock /var/lib/opkg /tmp/opkg-lists

# Update opkg and install essential packages (handle conflicts)
RUN opkg update

# Install packages that don't conflict first
RUN opkg install \
    # Core networking tools
    ip-full \
    iperf3 \
    tcpdump \
    # Security packages
    openssh-sftp-server \
    ca-bundle \
    stunnel \
    # Monitoring tools
    vnstat \
    htop \
    sysstat \
    # Admin essentials
    screen \
    nano \
    curl \
    jq \
    # Development tools
    git \
    wget \
    # Additional utilities
    procps-ng \
    coreutils-base64 \
    tar \
    gzip

# Handle firewall (use firewall4 which is already installed)
RUN echo "Using existing firewall4 instead of firewall package"

# Handle dnsmasq (use existing dnsmasq instead of dnsmasq-full)
RUN echo "Using existing dnsmasq instead of dnsmasq-full package"

# Install logread if not already present (it's usually built-in)
RUN opkg install logread || echo "logread already available"

# Install Rust toolchain (optional for testing)
# Note: Rust installation in OpenWrt container is optional for package testing
# RUN curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y --default-toolchain 1.75.0
# ENV PATH="/root/.cargo/bin:${PATH}"

# Create application directory
RUN mkdir -p ${OPENWRT_RUST_HOME}
WORKDIR ${OPENWRT_RUST_HOME}

# Create directories for OpenWrt Rust components
RUN mkdir -p \
    ${OPENWRT_RUST_HOME}/src \
    ${OPENWRT_RUST_HOME}/config \
    ${OPENWRT_RUST_HOME}/logs \
    ${OPENWRT_RUST_HOME}/data \
    ${OPENWRT_RUST_HOME}/scripts \
    ${OPENWRT_RUST_HOME}/tests

# Create network configuration directories (OpenWrt style)
RUN mkdir -p \
    /etc/config \
    /var/lib/openwrt \
    /var/log/openwrt \
    /tmp/run

# Set up networking capabilities
RUN echo 'net.ipv4.ip_forward=1' >> /etc/sysctl.conf
RUN echo 'net.ipv6.conf.all.forwarding=1' >> /etc/sysctl.conf

# Create OpenWrt-style init system
RUN mkdir -p /etc/init.d /etc/rc.d

# Create essential OpenWrt configuration files
RUN echo 'OpenWrt 23.05.2 with Rust Implementation' > /etc/openwrt_release
RUN echo 'DISTRIB_ID="OpenWrt"' > /etc/os-release
RUN echo 'DISTRIB_RELEASE="23.05.2"' >> /etc/os-release
RUN echo 'DISTRIB_REVISION="Rust Implementation"' >> /etc/os-release
RUN echo 'DISTRIB_DESCRIPTION="OpenWrt 23.05.2 with Rust"' >> /etc/os-release

# Expose common OpenWrt ports
EXPOSE 22 80 443 53/udp 67/udp 68/udp

# Default volumes for OpenWrt
VOLUME ["/etc/config", "/var/lib/openwrt", "/var/log/openwrt"]

# Default command
CMD ["/bin/ash"]
