# Podman storage.conf for OpenWrt Rust Container Testing
# Configuration for container storage backend

[storage]
# Storage driver to use
driver = "overlay"

# Root directory for storage
runroot = "/run/containers/storage"
graphroot = "/var/lib/containers/storage"

# Storage options for the overlay driver
[storage.options]
# Enable metacopy for overlay driver (improves performance)
overlay.metacopy = "on"

# Mount program for overlay
overlay.mount_program = "/usr/bin/fuse-overlayfs"

# Skip mount home directory
overlay.skip_mount_home = "false"

# Size limit for overlay (in bytes, 0 = unlimited)
overlay.size = "0"

# Use composefs for overlay (if available)
overlay.use_composefs = "false"

# Force mask for overlay
overlay.force_mask = "0000"

# Ignore chown errors
overlay.ignore_chown_errors = "false"

# Mount options for overlay
overlay.mountopt = "nodev,metacopy=on"

# Additional mount options
additionalimagestores = []

# Pull options
pull_options = {
    all_tags = false,
    policy = "missing"
}

# Storage options for different drivers
[storage.options.overlay]
# Overlay specific options
mountopt = "nodev,metacopy=on"
size = "0"
skip_mount_home = "false"
use_composefs = "false"
force_mask = "0000"
ignore_chown_errors = "false"

[storage.options.vfs]
# VFS driver options (fallback)
ignore_chown_errors = "false"

[storage.options.aufs]
# AUFS driver options (legacy)
mountopt = ""

[storage.options.devicemapper]
# Device mapper options
dm.thinpooldev = ""
dm.fs = "ext4"
dm.mkfsarg = ""
dm.mountopt = ""
dm.size = "10G"
dm.basesize = "10G"
dm.loopdatasize = "100G"
dm.loopmetadatasize = "2G"
dm.datadev = ""
dm.metadatadev = ""
dm.blocksize = "64k"
dm.blkdiscard = "false"
dm.override_udev_sync_check = "true"
dm.use_deferred_removal = "true"
dm.use_deferred_deletion = "true"

# Quota configuration
[storage.options.quota]
# Enable quota support
enable = false

# Quota size limit (in bytes)
size = "0"

# Quota inode limit
inodes = "0"

# Temporary directory configuration
[storage.options.tmp]
# Temporary directory for container operations
dir = "/tmp"

# Size limit for temporary directory
size = "0"

# Cleanup configuration
[storage.options.cleanup]
# Enable automatic cleanup
enable = true

# Cleanup interval (in seconds)
interval = 3600

# Maximum age for unused images (in seconds)
max_age = 86400

# Keep minimum number of images
keep_images = 5

# Performance tuning
[storage.options.performance]
# Enable parallel operations
parallel = true

# Number of parallel operations
parallel_count = 4

# Buffer size for operations
buffer_size = "64k"

# Compression configuration
[storage.options.compression]
# Enable compression for layers
enable = false

# Compression algorithm (gzip, zstd, lz4)
algorithm = "gzip"

# Compression level (1-9 for gzip, 1-22 for zstd)
level = 6

# Security configuration
[storage.options.security]
# Enable security labels
enable = true

# Default security context
default_context = "system_u:object_r:container_file_t:s0"

# Enable user namespace support
user_ns = true

# Logging configuration
[storage.options.logging]
# Log level (debug, info, warn, error)
level = "info"

# Log format (text, json)
format = "text"

# Log file path
file = "/var/log/containers/storage.log"

# Maximum log file size
max_size = "10MB"

# Maximum number of log files to keep
max_files = 5

# Network storage configuration (for distributed setups)
[storage.options.network]
# Enable network storage
enable = false

# Network storage backend
backend = "nfs"

# Network storage path
path = ""

# Mount options for network storage
mount_options = "rw,hard,intr"
