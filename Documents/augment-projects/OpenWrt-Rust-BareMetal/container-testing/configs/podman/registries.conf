# Podman registries.conf for OpenWrt Rust Container Testing
# Configuration for container image registries

# Unqualified image search registries
# These registries will be searched when pulling images without a registry prefix
unqualified-search-registries = [
    "docker.io",
    "quay.io",
    "registry.fedoraproject.org",
    "registry.access.redhat.com"
]

# Short name aliases for common images
[aliases]
# Base images
"alpine" = "docker.io/library/alpine"
"ubuntu" = "docker.io/library/ubuntu"
"debian" = "docker.io/library/debian"
"fedora" = "registry.fedoraproject.org/fedora"

# Network tools
"busybox" = "docker.io/library/busybox"
"networktools" = "docker.io/nicolaka/netshoot"

# OpenWrt related
"openwrt" = "docker.io/openwrt/rootfs"

# Registry configuration for docker.io
[[registry]]
prefix = "docker.io"
location = "docker.io"

# Use multiple mirrors for better availability
[[registry.mirror]]
location = "mirror.gcr.io"
insecure = false

[[registry.mirror]]
location = "registry-1.docker.io"
insecure = false

# Registry configuration for quay.io
[[registry]]
prefix = "quay.io"
location = "quay.io"
insecure = false

# Registry configuration for Red Hat
[[registry]]
prefix = "registry.access.redhat.com"
location = "registry.access.redhat.com"
insecure = false

# Registry configuration for Fedora
[[registry]]
prefix = "registry.fedoraproject.org"
location = "registry.fedoraproject.org"
insecure = false

# Local registry configuration (if needed for development)
[[registry]]
prefix = "localhost"
location = "localhost"
insecure = true

# Block certain registries for security
[[registry]]
prefix = "docker.io/malicious"
blocked = true

# Configuration for private registries (template)
# Uncomment and modify as needed
# [[registry]]
# prefix = "private-registry.example.com"
# location = "private-registry.example.com:5000"
# insecure = false
# 
# # Authentication for private registry
# [[registry.mirror]]
# location = "private-registry.example.com:5000"
# insecure = false
# 
# # Credentials can be configured via podman login command
# # or stored in auth.json file

# Registry search configuration
[search]
# Enable search across all configured registries
enable = true

# Maximum number of search results
limit = 25

# Timeout for search operations (in seconds)
timeout = 30

# Filter configuration
[filter]
# Skip TLS verification for insecure registries
skip-tls-verify = false

# Allow HTTP registries (not recommended for production)
allow-http = false
