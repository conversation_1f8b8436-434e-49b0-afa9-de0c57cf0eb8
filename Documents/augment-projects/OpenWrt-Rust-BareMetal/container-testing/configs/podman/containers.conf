# Podman containers.conf for OpenWrt Rust Container Testing
# Configuration for container runtime behavior

[containers]
# Default user namespace mode
userns = "host"

# Default network mode for containers
netns = "bridge"

# Default logging driver
log_driver = "journald"

# Default log size max
log_size_max = "10MB"

# Default log tag
log_tag = "openwrt-rust"

# Enable systemd integration
systemd = true

# Default security options
seccomp_profile = "/usr/share/containers/seccomp.json"
apparmor_profile = "containers-default-0.44.0"

# Default capabilities to add
default_capabilities = [
    "CHOWN",
    "DAC_OVERRIDE", 
    "FOWNER",
    "FSETID",
    "KILL",
    "NET_BIND_SERVICE",
    "NET_RAW",
    "SETFCAP",
    "SETGID",
    "SETPCAP",
    "SETUID",
    "SYS_CHROOT"
]

# Additional capabilities for network testing
add_capabilities = [
    "NET_ADMIN",
    "NET_RAW",
    "SYS_ADMIN"
]

# Default ulimits
default_ulimits = [
    "nofile=65536:65536",
    "nproc=4096:4096"
]

# Default environment variables
env = [
    "PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin",
    "TERM=xterm",
    "OPENWRT_RUST_ENV=container"
]

# Container init
init = true
init_path = "/usr/libexec/podman/catatonit"

# Default volumes
volumes = [
    "/tmp",
    "/var/tmp"
]

# Default devices
devices = []

# Default annotations
annotations = [
    "openwrt.testing=true",
    "openwrt.version=rust-implementation"
]

[engine]
# Container engine configuration

# Runtime to use
runtime = "crun"

# Number of locks
num_locks = 2048

# Event logger
events_logger = "journald"

# Image default transport
image_default_transport = "docker://"

# Image parallel copies
image_parallel_copies = 0

# Infra image
infra_image = "k8s.gcr.io/pause:3.5"

# Network configuration directory
network_config_dir = "/etc/cni/net.d/"

# CNI plugin directories
cni_plugin_dirs = [
    "/usr/libexec/cni",
    "/usr/lib/cni",
    "/usr/local/lib/cni",
    "/opt/cni/bin"
]

# Service timeout
service_timeout = 5

# Stop timeout
stop_timeout = 10

# Remote socket
remote = false

[network]
# Network configuration

# Default network
default_network = "podman"

# Default subnet
default_subnet = "10.88.0.0/16"

# Default subnet pools
default_subnet_pools = [
    {"base": "10.89.0.0/16", "size": 24},
    {"base": "10.90.0.0/16", "size": 24}
]

# Network backend
network_backend = "cni"

# DNS servers
dns_servers = [
    "8.8.8.8",
    "8.8.4.4"
]

# DNS search domains
dns_searches = [
    "openwrt.local"
]

# DNS options
dns_options = [
    "ndots:2",
    "edns0"
]

[secrets]
# Secrets configuration
driver = "file"

[machine]
# Machine configuration for podman machine (if used)
cpus = 2
memory = 2048
disk_size = 20

# Image for podman machine
image = "testing"

# User for podman machine
user = "core"
