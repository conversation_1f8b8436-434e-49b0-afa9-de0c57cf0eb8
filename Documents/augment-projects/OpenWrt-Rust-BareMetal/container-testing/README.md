# OpenWrt Rust Container Testing with 4 Port LAN

This directory contains the Podman-based container testing infrastructure for the OpenWrt Rust implementation, specifically designed for 4 port LAN simulation and testing.

## Overview

The container testing environment provides:
- **4 Port LAN Simulation**: Realistic network topology with WAN + 3 LAN ports
- **VLAN Support**: 802.1Q VLAN tagging and isolation
- **Container Orchestration**: Multi-container testing scenarios
- **Performance Testing**: Network performance benchmarking
- **CI/CD Integration**: Automated testing workflows

## Quick Start

### Prerequisites

- **Podman** 4.0+ with CNI networking support
- **Linux** (primary) or **macOS** (secondary support)
- **jq** for JSON processing
- **bc** for calculations
- **iperf3** for performance testing

### Installation

1. **Set up networks**:
   ```bash
   ./scripts/4port-lan-setup.sh setup
   ```

2. **Build container images**:
   ```bash
   ./scripts/container-manager.sh build
   ```

3. **Start containers**:
   ```bash
   ./scripts/container-manager.sh start
   ```

4. **Run tests**:
   ```bash
   ./tests/network/4port-lan-tests.sh
   ```

## Architecture

### Network Topology

```
                    ┌─────────────────┐
                    │   Host System   │
                    └─────────────────┘
                             │
                    ┌─────────────────┐
                    │  Podman Bridge  │
                    └─────────────────┘
                             │
        ┌────────────────────┼────────────────────┐
        │                    │                    │
   ┌─────────┐         ┌─────────┐         ┌─────────┐
   │ WAN Net │         │ LAN1 Net│         │ LAN2 Net│
   │192.168. │         │192.168. │         │192.168. │
   │100.0/24 │         │  1.0/24 │         │  2.0/24 │
   └─────────┘         └─────────┘         └─────────┘
        │                    │                    │
   ┌─────────┐         ┌─────────┐         ┌─────────┐
   │ Router  │         │Client1  │         │Client2  │
   │Container│         │Container│         │Container│
   └─────────┘         └─────────┘         └─────────┘
```

### Container Architecture

- **Router Container**: OpenWrt Rust implementation with 4 network interfaces
- **Client Containers**: Test clients on different LAN segments
- **Network Isolation**: VLAN-based port isolation
- **Management Network**: Dedicated network for test orchestration

## Directory Structure

```
container-testing/
├── configs/
│   ├── networks/
│   │   └── 4port-lan.json          # Network configuration
│   ├── podman/                     # Podman configurations
│   └── images/                     # Container image definitions
├── scripts/
│   ├── 4port-lan-setup.sh         # Network setup script
│   ├── container-manager.sh       # Container lifecycle management
│   └── container-entrypoint.sh    # Container startup script
├── tests/
│   ├── network/
│   │   └── 4port-lan-tests.sh     # Network functionality tests
│   ├── performance/               # Performance test suites
│   └── integration/               # Integration test scenarios
├── logs/                          # Test logs and results
├── monitoring/                    # Monitoring configurations
└── docs/                          # Additional documentation
```

## Usage

### Network Management

**Set up 4 port LAN networks**:
```bash
./scripts/4port-lan-setup.sh setup
```

**Clean up networks**:
```bash
./scripts/4port-lan-setup.sh cleanup
```

**Validate network configuration**:
```bash
./scripts/4port-lan-setup.sh validate
```

### Container Management

**Build container images**:
```bash
./scripts/container-manager.sh build
```

**Start all containers**:
```bash
./scripts/container-manager.sh start
```

**Check container status**:
```bash
./scripts/container-manager.sh status
```

**Execute commands in containers**:
```bash
./scripts/container-manager.sh exec openwrt-rust-router "ip addr show"
./scripts/container-manager.sh exec openwrt-rust-client1 "ping ***********"
```

**View container logs**:
```bash
./scripts/container-manager.sh logs openwrt-rust-router
```

**Stop all containers**:
```bash
./scripts/container-manager.sh stop
```

### Testing

**Run complete test suite**:
```bash
./tests/network/4port-lan-tests.sh
```

**Individual test categories**:
- Basic connectivity testing
- VLAN isolation validation
- NAT functionality testing
- Port forwarding verification
- Network performance benchmarking
- Firewall rule validation

## Network Configuration

### Port Mapping

| Port | Interface | Network Segment | VLAN ID | Purpose |
|------|-----------|----------------|---------|---------|
| 1    | WAN       | *************/24 | -     | External connectivity |
| 2    | LAN1      | ***********/24   | 10    | Internal network 1 |
| 3    | LAN2      | ***********/24   | 20    | Internal network 2 |
| 4    | LAN3      | ***********/24   | 30    | Internal network 3 |

### VLAN Configuration

- **VLAN 10**: LAN port 2 (***********/24)
- **VLAN 20**: LAN port 3 (***********/24)
- **VLAN 30**: LAN port 4 (***********/24)
- **Inter-VLAN**: Blocked by default (isolation)

### Firewall Rules

- **WAN → LAN**: Blocked (security)
- **LAN → WAN**: Allowed with NAT
- **Inter-VLAN**: Blocked (isolation)
- **Management**: Full access for testing

## Testing Scenarios

### Basic Connectivity
- Container startup and network assignment
- Ping tests between containers
- IP address validation

### VLAN Isolation
- Inter-VLAN communication blocking
- VLAN tag validation
- Port isolation verification

### NAT Functionality
- LAN to WAN translation
- External connectivity (when available)
- Port forwarding rules

### Performance Testing
- Network throughput measurement
- Latency testing
- Concurrent connection handling

### Security Validation
- Firewall rule enforcement
- Access control validation
- Network isolation verification

## Integration with Existing Testing

This container testing environment complements the existing VM testing infrastructure:

- **Speed**: Faster container startup vs. VM boot time
- **Resources**: Lower resource usage for development testing
- **Isolation**: Network-level isolation vs. full system isolation
- **Compatibility**: Same test scenarios, different execution environment

## Troubleshooting

### Common Issues

**Network creation fails**:
```bash
# Check Podman installation
podman --version

# Verify CNI plugins
podman network ls

# Check system permissions
sudo podman network create test-network
```

**Container startup fails**:
```bash
# Check container logs
podman logs openwrt-rust-router

# Verify image exists
podman images | grep openwrt-rust

# Check network connectivity
podman network inspect openwrt-wan
```

**Test failures**:
```bash
# Check container status
./scripts/container-manager.sh status

# Verify network configuration
./scripts/4port-lan-setup.sh validate

# Review test logs
tail -f logs/4port-lan-tests_*.log
```

### Performance Optimization

**Container resource limits**:
```bash
# Set memory limits
podman run --memory=1g --cpus=2 ...

# Monitor resource usage
podman stats
```

**Network performance tuning**:
```bash
# Check network driver settings
podman network inspect openwrt-lan1

# Optimize bridge configuration
sudo sysctl net.bridge.bridge-nf-call-iptables=0
```

## Development Workflow

1. **Setup**: Run network and container setup scripts
2. **Development**: Make changes to OpenWrt Rust implementation
3. **Build**: Rebuild container images with changes
4. **Test**: Execute test suites to validate functionality
5. **Debug**: Use container exec and logs for troubleshooting
6. **Iterate**: Repeat cycle for continuous development

## CI/CD Integration

The container testing environment integrates with existing CI/CD pipelines:

- **GitHub Actions**: Automated testing on code changes
- **Test Reports**: JSON and HTML result generation
- **Performance Tracking**: Historical performance comparison
- **Regression Detection**: Automated failure detection

## Best Practices

### Container Management
- Use consistent naming conventions
- Implement proper cleanup procedures
- Monitor resource usage
- Maintain container image versions

### Network Configuration
- Document network topology changes
- Validate configuration before deployment
- Use consistent IP addressing schemes
- Implement proper security policies

### Testing
- Run tests in isolated environments
- Maintain test data consistency
- Document test scenarios thoroughly
- Implement automated regression testing

## Contributing

When contributing to the container testing infrastructure:

1. Follow existing naming conventions
2. Update documentation for new features
3. Add appropriate test coverage
4. Validate cross-platform compatibility
5. Maintain backward compatibility

## Support

For issues and questions:
- Check troubleshooting section
- Review container and network logs
- Validate prerequisites and setup
- Consult existing VM testing documentation for comparison

---

**Last Updated**: 2025-01-27  
**Version**: 1.0  
**Compatibility**: Podman 4.0+, Linux/macOS
