#!/bin/bash

# Network Performance Tests for OpenWrt Rust Container Testing
# Comprehensive performance benchmarking for container networking

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEST_DIR="${SCRIPT_DIR}/.."
CONFIG_DIR="${TEST_DIR}/../configs"
LOG_DIR="${TEST_DIR}/../logs"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
TEST_LOG="${LOG_DIR}/performance-tests_${TIMESTAMP}.log"
RESULTS_FILE="${LOG_DIR}/performance-results_${TIMESTAMP}.json"

# Test configuration
CONTAINER_PREFIX="openwrt-rust"
ROUTER_CONTAINER="${CONTAINER_PREFIX}-router"
CLIENT_CONTAINERS=("${CONTAINER_PREFIX}-client1" "${CONTAINER_PREFIX}-client2" "${CONTAINER_PREFIX}-client3")

# Performance test parameters
IPERF_DURATION=30
IPERF_PARALLEL=4
PING_COUNT=100
LATENCY_SAMPLES=1000
THROUGHPUT_TESTS=3

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "${TEST_LOG}"
}

info() { log "INFO" "$@"; }
warn() { log "WARN" "$@"; }
error() { log "ERROR" "$@"; }
success() { log "SUCCESS" "$@"; }

# Performance results storage
declare -A performance_results

# Record performance result
record_performance() {
    local test_name=$1
    local metric=$2
    local value=$3
    local unit=$4
    
    performance_results["${test_name}_${metric}"]="${value} ${unit}"
    info "PERFORMANCE: $test_name - $metric: $value $unit"
}

# Container utility functions
container_running() {
    local container_name=$1
    [ "$(podman container inspect "$container_name" --format '{{.State.Running}}' 2>/dev/null)" = "true" ]
}

get_container_ip() {
    local container_name=$1
    local network_name=$2
    podman container inspect "$container_name" \
        --format "{{.NetworkSettings.Networks.${network_name}.IPAddress}}" 2>/dev/null || echo ""
}

exec_in_container() {
    local container_name=$1
    shift
    podman exec "$container_name" "$@"
}

# Test: Network Latency
test_network_latency() {
    info "Testing network latency..."
    
    local client="${CLIENT_CONTAINERS[0]}"
    
    if ! container_running "$client"; then
        error "Client container not running: $client"
        return 1
    fi
    
    local client_ip=$(get_container_ip "$client" "openwrt-lan1")
    
    if [ -z "$client_ip" ]; then
        error "Could not get client IP address"
        return 1
    fi
    
    info "Testing latency from router to client ($client_ip)..."
    
    # Ping test for latency measurement
    local ping_output=$(exec_in_container "$ROUTER_CONTAINER" ping -c "$PING_COUNT" -i 0.1 "$client_ip" 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        local avg_latency=$(echo "$ping_output" | grep 'rtt min/avg/max/mdev' | awk -F'/' '{print $5}')
        local min_latency=$(echo "$ping_output" | grep 'rtt min/avg/max/mdev' | awk -F'/' '{print $4}')
        local max_latency=$(echo "$ping_output" | grep 'rtt min/avg/max/mdev' | awk -F'/' '{print $6}')
        local packet_loss=$(echo "$ping_output" | grep 'packet loss' | awk '{print $6}' | sed 's/%//')
        
        record_performance "latency" "average" "$avg_latency" "ms"
        record_performance "latency" "minimum" "$min_latency" "ms"
        record_performance "latency" "maximum" "$max_latency" "ms"
        record_performance "latency" "packet_loss" "$packet_loss" "%"
        
        success "Latency test completed"
    else
        error "Latency test failed"
        return 1
    fi
}

# Test: TCP Throughput
test_tcp_throughput() {
    info "Testing TCP throughput..."
    
    local client="${CLIENT_CONTAINERS[0]}"
    
    if ! container_running "$client"; then
        error "Client container not running: $client"
        return 1
    fi
    
    local client_ip=$(get_container_ip "$client" "openwrt-lan1")
    
    if [ -z "$client_ip" ]; then
        error "Could not get client IP address"
        return 1
    fi
    
    # Start iperf3 server in client
    info "Starting iperf3 server in client..."
    exec_in_container "$client" iperf3 -s -D >/dev/null 2>&1
    
    sleep 2
    
    # Run multiple throughput tests
    local total_throughput=0
    local successful_tests=0
    
    for ((i=1; i<=THROUGHPUT_TESTS; i++)); do
        info "Running TCP throughput test $i/$THROUGHPUT_TESTS..."
        
        local result=$(exec_in_container "$ROUTER_CONTAINER" iperf3 -c "$client_ip" -t "$IPERF_DURATION" -f M 2>/dev/null | grep 'receiver' | awk '{print $7}' || echo "0")
        
        if [ -n "$result" ] && [ "$(echo "$result > 0" | bc 2>/dev/null || echo 0)" -eq 1 ]; then
            total_throughput=$(echo "$total_throughput + $result" | bc)
            successful_tests=$((successful_tests + 1))
            info "Test $i result: $result Mbits/sec"
        else
            warn "Test $i failed"
        fi
    done
    
    # Stop iperf3 server
    exec_in_container "$client" pkill iperf3 2>/dev/null || true
    
    if [ $successful_tests -gt 0 ]; then
        local avg_throughput=$(echo "scale=2; $total_throughput / $successful_tests" | bc)
        record_performance "tcp_throughput" "average" "$avg_throughput" "Mbits/sec"
        record_performance "tcp_throughput" "tests_completed" "$successful_tests" "count"
        success "TCP throughput test completed"
    else
        error "All TCP throughput tests failed"
        return 1
    fi
}

# Test: UDP Throughput
test_udp_throughput() {
    info "Testing UDP throughput..."
    
    local client="${CLIENT_CONTAINERS[0]}"
    
    if ! container_running "$client"; then
        error "Client container not running: $client"
        return 1
    fi
    
    local client_ip=$(get_container_ip "$client" "openwrt-lan1")
    
    if [ -z "$client_ip" ]; then
        error "Could not get client IP address"
        return 1
    fi
    
    # Start iperf3 server in client
    info "Starting iperf3 server in client..."
    exec_in_container "$client" iperf3 -s -D >/dev/null 2>&1
    
    sleep 2
    
    # Test different UDP bandwidths
    local bandwidths=("10M" "50M" "100M" "500M")
    
    for bandwidth in "${bandwidths[@]}"; do
        info "Testing UDP throughput at $bandwidth..."
        
        local result=$(exec_in_container "$ROUTER_CONTAINER" iperf3 -c "$client_ip" -u -b "$bandwidth" -t 10 -f M 2>/dev/null | grep 'receiver' | awk '{print $7}' || echo "0")
        local loss=$(exec_in_container "$ROUTER_CONTAINER" iperf3 -c "$client_ip" -u -b "$bandwidth" -t 10 2>/dev/null | grep 'receiver' | awk '{print $12}' | sed 's/(//' || echo "0")
        
        if [ -n "$result" ] && [ "$(echo "$result > 0" | bc 2>/dev/null || echo 0)" -eq 1 ]; then
            record_performance "udp_throughput_${bandwidth}" "throughput" "$result" "Mbits/sec"
            record_performance "udp_throughput_${bandwidth}" "packet_loss" "$loss" "%"
        else
            warn "UDP test at $bandwidth failed"
        fi
    done
    
    # Stop iperf3 server
    exec_in_container "$client" pkill iperf3 2>/dev/null || true
    
    success "UDP throughput test completed"
}

# Test: Parallel Connections
test_parallel_connections() {
    info "Testing parallel connections performance..."
    
    local client="${CLIENT_CONTAINERS[0]}"
    
    if ! container_running "$client"; then
        error "Client container not running: $client"
        return 1
    fi
    
    local client_ip=$(get_container_ip "$client" "openwrt-lan1")
    
    if [ -z "$client_ip" ]; then
        error "Could not get client IP address"
        return 1
    fi
    
    # Start iperf3 server in client
    info "Starting iperf3 server in client..."
    exec_in_container "$client" iperf3 -s -D >/dev/null 2>&1
    
    sleep 2
    
    # Test with parallel connections
    info "Testing with $IPERF_PARALLEL parallel connections..."
    
    local result=$(exec_in_container "$ROUTER_CONTAINER" iperf3 -c "$client_ip" -P "$IPERF_PARALLEL" -t "$IPERF_DURATION" -f M 2>/dev/null | grep 'SUM.*receiver' | awk '{print $6}' || echo "0")
    
    if [ -n "$result" ] && [ "$(echo "$result > 0" | bc 2>/dev/null || echo 0)" -eq 1 ]; then
        record_performance "parallel_connections" "total_throughput" "$result" "Mbits/sec"
        
        local per_connection=$(echo "scale=2; $result / $IPERF_PARALLEL" | bc)
        record_performance "parallel_connections" "per_connection" "$per_connection" "Mbits/sec"
        record_performance "parallel_connections" "connection_count" "$IPERF_PARALLEL" "count"
        
        success "Parallel connections test completed"
    else
        error "Parallel connections test failed"
        return 1
    fi
    
    # Stop iperf3 server
    exec_in_container "$client" pkill iperf3 2>/dev/null || true
}

# Test: Connection Establishment Rate
test_connection_rate() {
    info "Testing connection establishment rate..."
    
    local client="${CLIENT_CONTAINERS[0]}"
    
    if ! container_running "$client"; then
        error "Client container not running: $client"
        return 1
    fi
    
    local client_ip=$(get_container_ip "$client" "openwrt-lan1")
    
    if [ -z "$client_ip" ]; then
        error "Could not get client IP address"
        return 1
    fi
    
    # Start a simple TCP server
    exec_in_container "$client" nc -l -p 9999 >/dev/null 2>&1 &
    local server_pid=$!
    
    sleep 1
    
    # Test connection establishment rate
    local start_time=$(date +%s.%N)
    local connections=100
    local successful_connections=0
    
    for ((i=1; i<=connections; i++)); do
        if exec_in_container "$ROUTER_CONTAINER" timeout 1 nc -z "$client_ip" 9999 >/dev/null 2>&1; then
            successful_connections=$((successful_connections + 1))
        fi
    done
    
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc)
    local rate=$(echo "scale=2; $successful_connections / $duration" | bc)
    
    record_performance "connection_rate" "connections_per_second" "$rate" "conn/sec"
    record_performance "connection_rate" "successful_connections" "$successful_connections" "count"
    record_performance "connection_rate" "total_attempts" "$connections" "count"
    
    # Cleanup
    kill $server_pid 2>/dev/null || true
    
    success "Connection rate test completed"
}

# Test: Memory Usage During Load
test_memory_usage() {
    info "Testing memory usage during network load..."
    
    # Get initial memory usage
    local initial_memory=$(exec_in_container "$ROUTER_CONTAINER" free -m | awk 'NR==2{print $3}')
    
    # Start network load test
    local client="${CLIENT_CONTAINERS[0]}"
    local client_ip=$(get_container_ip "$client" "openwrt-lan1")
    
    if [ -n "$client_ip" ]; then
        # Start iperf3 server
        exec_in_container "$client" iperf3 -s -D >/dev/null 2>&1
        
        # Start load test in background
        exec_in_container "$ROUTER_CONTAINER" iperf3 -c "$client_ip" -t 60 -P 8 >/dev/null 2>&1 &
        local load_pid=$!
        
        # Monitor memory usage
        local max_memory=$initial_memory
        local samples=0
        local total_memory=0
        
        for ((i=1; i<=20; i++)); do
            sleep 3
            local current_memory=$(exec_in_container "$ROUTER_CONTAINER" free -m | awk 'NR==2{print $3}')
            
            if [ "$current_memory" -gt "$max_memory" ]; then
                max_memory=$current_memory
            fi
            
            total_memory=$((total_memory + current_memory))
            samples=$((samples + 1))
        done
        
        # Stop load test
        kill $load_pid 2>/dev/null || true
        exec_in_container "$client" pkill iperf3 2>/dev/null || true
        
        local avg_memory=$((total_memory / samples))
        local memory_increase=$((max_memory - initial_memory))
        
        record_performance "memory_usage" "initial" "$initial_memory" "MB"
        record_performance "memory_usage" "maximum" "$max_memory" "MB"
        record_performance "memory_usage" "average" "$avg_memory" "MB"
        record_performance "memory_usage" "increase" "$memory_increase" "MB"
        
        success "Memory usage test completed"
    else
        error "Could not get client IP for memory test"
        return 1
    fi
}

# Generate performance report
generate_performance_report() {
    info "Generating performance report..."
    
    # Create JSON report
    cat > "$RESULTS_FILE" << EOF
{
    "performance_test_run": {
        "timestamp": "$TIMESTAMP",
        "duration_seconds": $(($(date +%s) - $(date -d "$TIMESTAMP" +%s 2>/dev/null || echo $(date +%s)))),
        "test_configuration": {
            "iperf_duration": $IPERF_DURATION,
            "iperf_parallel": $IPERF_PARALLEL,
            "ping_count": $PING_COUNT,
            "throughput_tests": $THROUGHPUT_TESTS
        }
    },
    "performance_results": {
EOF

    local first=true
    for metric in "${!performance_results[@]}"; do
        if [ "$first" = true ]; then
            first=false
        else
            echo "," >> "$RESULTS_FILE"
        fi
        echo "        \"$metric\": \"${performance_results[$metric]}\"" >> "$RESULTS_FILE"
    done

    cat >> "$RESULTS_FILE" << EOF
    }
}
EOF

    # Print summary
    echo ""
    echo "=========================================="
    echo "Network Performance Test Results"
    echo "=========================================="
    echo "Test completed: $(date)"
    echo "Results file: $RESULTS_FILE"
    echo "Log file: $TEST_LOG"
    echo ""
    echo "Key Performance Metrics:"
    
    for metric in "${!performance_results[@]}"; do
        echo "  $metric: ${performance_results[$metric]}"
    done
    
    echo "=========================================="
    
    success "Performance testing completed successfully"
}

# Main execution
main() {
    info "Starting network performance tests..."
    
    # Create log directory
    mkdir -p "$LOG_DIR"
    
    # Run performance tests
    test_network_latency
    test_tcp_throughput
    test_udp_throughput
    test_parallel_connections
    test_connection_rate
    test_memory_usage
    
    # Generate report
    generate_performance_report
}

# Execute main function
main "$@"
