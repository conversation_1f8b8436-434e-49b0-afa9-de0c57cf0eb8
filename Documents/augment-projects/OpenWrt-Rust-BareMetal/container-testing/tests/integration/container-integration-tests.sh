#!/bin/bash

# Container Integration Tests for OpenWrt Rust Testing
# End-to-end integration testing for container environment

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEST_DIR="${SCRIPT_DIR}/.."
CONFIG_DIR="${TEST_DIR}/../configs"
SCRIPTS_DIR="${TEST_DIR}/../scripts"
LOG_DIR="${TEST_DIR}/../logs"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
TEST_LOG="${LOG_DIR}/integration-tests_${TIMESTAMP}.log"
RESULTS_FILE="${LOG_DIR}/integration-results_${TIMESTAMP}.json"

# Test configuration
CONTAINER_PREFIX="openwrt-rust"
ROUTER_CONTAINER="${CONTAINER_PREFIX}-router"
CLIENT_CONTAINERS=("${CONTAINER_PREFIX}-client1" "${CONTAINER_PREFIX}-client2" "${CONTAINER_PREFIX}-client3")
NETWORK_CONFIG="${CONFIG_DIR}/networks/4port-lan.json"

# Test timeouts
SETUP_TIMEOUT=120
TEST_TIMEOUT=60
CLEANUP_TIMEOUT=30

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "${TEST_LOG}"
}

info() { log "INFO" "$@"; }
warn() { log "WARN" "$@"; }
error() { log "ERROR" "$@"; }
success() { log "SUCCESS" "$@"; }

# Test result tracking
declare -A test_results
test_count=0
passed_count=0
failed_count=0

# Record test result
record_test() {
    local test_name=$1
    local result=$2
    local details=${3:-""}
    
    test_count=$((test_count + 1))
    test_results["$test_name"]="$result"
    
    if [ "$result" = "PASS" ]; then
        passed_count=$((passed_count + 1))
        success "TEST PASS: $test_name"
    else
        failed_count=$((failed_count + 1))
        error "TEST FAIL: $test_name - $details"
    fi
}

# Test: Complete Environment Setup
test_environment_setup() {
    info "Testing complete environment setup..."
    
    # Test network setup
    if timeout "$SETUP_TIMEOUT" "${SCRIPTS_DIR}/4port-lan-setup.sh" setup >/dev/null 2>&1; then
        record_test "network_setup" "PASS"
    else
        record_test "network_setup" "FAIL" "Network setup failed"
        return 1
    fi
    
    # Test container image build
    if timeout "$SETUP_TIMEOUT" "${SCRIPTS_DIR}/container-manager.sh" build >/dev/null 2>&1; then
        record_test "container_build" "PASS"
    else
        record_test "container_build" "FAIL" "Container build failed"
        return 1
    fi
    
    # Test container startup
    if timeout "$SETUP_TIMEOUT" "${SCRIPTS_DIR}/container-manager.sh" start >/dev/null 2>&1; then
        record_test "container_startup" "PASS"
    else
        record_test "container_startup" "FAIL" "Container startup failed"
        return 1
    fi
    
    # Wait for containers to be ready
    sleep 10
    
    # Verify all containers are running
    local running_containers=0
    
    if podman container inspect "$ROUTER_CONTAINER" --format '{{.State.Running}}' 2>/dev/null | grep -q "true"; then
        running_containers=$((running_containers + 1))
    fi
    
    for client in "${CLIENT_CONTAINERS[@]}"; do
        if podman container inspect "$client" --format '{{.State.Running}}' 2>/dev/null | grep -q "true"; then
            running_containers=$((running_containers + 1))
        fi
    done
    
    if [ $running_containers -ge 2 ]; then
        record_test "containers_running" "PASS"
    else
        record_test "containers_running" "FAIL" "Only $running_containers containers running"
    fi
}

# Test: End-to-End Network Flow
test_e2e_network_flow() {
    info "Testing end-to-end network flow..."
    
    # Test complete network path: Client -> Router -> External
    local client="${CLIENT_CONTAINERS[0]}"
    
    if ! podman container inspect "$client" --format '{{.State.Running}}' 2>/dev/null | grep -q "true"; then
        record_test "e2e_client_available" "FAIL" "Client container not running"
        return 1
    fi
    
    # Test internal connectivity (client to router)
    local router_ip=$(podman container inspect "$ROUTER_CONTAINER" --format "{{.NetworkSettings.Networks.openwrt-wan.IPAddress}}" 2>/dev/null)
    
    if [ -n "$router_ip" ]; then
        if podman exec "$client" ping -c 3 -W 5 "$router_ip" >/dev/null 2>&1; then
            record_test "e2e_internal_connectivity" "PASS"
        else
            record_test "e2e_internal_connectivity" "FAIL" "Cannot reach router from client"
        fi
    else
        record_test "e2e_router_ip" "FAIL" "Router IP not available"
    fi
    
    # Test DNS resolution through router
    if podman exec "$client" nslookup google.com >/dev/null 2>&1; then
        record_test "e2e_dns_resolution" "PASS"
    else
        record_test "e2e_dns_resolution" "FAIL" "DNS resolution failed"
    fi
    
    # Test HTTP connectivity through router
    if podman exec "$client" curl -s --connect-timeout 10 http://httpbin.org/ip >/dev/null 2>&1; then
        record_test "e2e_http_connectivity" "PASS"
    else
        record_test "e2e_http_connectivity" "FAIL" "HTTP connectivity failed"
    fi
}

# Test: Service Discovery and Communication
test_service_discovery() {
    info "Testing service discovery and communication..."
    
    # Start a test service in one client
    local server_client="${CLIENT_CONTAINERS[0]}"
    local client_client="${CLIENT_CONTAINERS[1]}"
    
    if ! podman container inspect "$server_client" --format '{{.State.Running}}' 2>/dev/null | grep -q "true"; then
        record_test "service_discovery_server_available" "FAIL" "Server client not running"
        return 1
    fi
    
    if ! podman container inspect "$client_client" --format '{{.State.Running}}' 2>/dev/null | grep -q "true"; then
        record_test "service_discovery_client_available" "FAIL" "Client client not running"
        return 1
    fi
    
    # Start HTTP server in server client
    podman exec -d "$server_client" python3 -m http.server 8080 >/dev/null 2>&1
    
    sleep 3
    
    # Get server IP
    local server_ip=$(podman container inspect "$server_client" --format "{{.NetworkSettings.Networks.openwrt-lan1.IPAddress}}" 2>/dev/null)
    
    if [ -n "$server_ip" ]; then
        # Test service discovery via router
        if podman exec "$client_client" curl -s --connect-timeout 5 "http://$server_ip:8080" >/dev/null 2>&1; then
            record_test "service_discovery_communication" "PASS"
        else
            record_test "service_discovery_communication" "FAIL" "Cannot communicate with service"
        fi
    else
        record_test "service_discovery_ip" "FAIL" "Server IP not available"
    fi
    
    # Cleanup
    podman exec "$server_client" pkill -f "python3 -m http.server" 2>/dev/null || true
}

# Test: Container Lifecycle Management
test_container_lifecycle() {
    info "Testing container lifecycle management..."
    
    # Test container stop and restart
    local test_client="${CLIENT_CONTAINERS[2]}"
    
    # Stop container
    if podman stop "$test_client" >/dev/null 2>&1; then
        record_test "container_stop" "PASS"
    else
        record_test "container_stop" "FAIL" "Failed to stop container"
        return 1
    fi
    
    # Verify container is stopped
    if ! podman container inspect "$test_client" --format '{{.State.Running}}' 2>/dev/null | grep -q "true"; then
        record_test "container_stopped_state" "PASS"
    else
        record_test "container_stopped_state" "FAIL" "Container still running after stop"
    fi
    
    # Restart container
    if podman start "$test_client" >/dev/null 2>&1; then
        record_test "container_restart" "PASS"
    else
        record_test "container_restart" "FAIL" "Failed to restart container"
        return 1
    fi
    
    # Wait for container to be ready
    sleep 5
    
    # Verify container is running
    if podman container inspect "$test_client" --format '{{.State.Running}}' 2>/dev/null | grep -q "true"; then
        record_test "container_restarted_state" "PASS"
    else
        record_test "container_restarted_state" "FAIL" "Container not running after restart"
    fi
    
    # Test network connectivity after restart
    local router_ip=$(podman container inspect "$ROUTER_CONTAINER" --format "{{.NetworkSettings.Networks.openwrt-wan.IPAddress}}" 2>/dev/null)
    
    if [ -n "$router_ip" ]; then
        if podman exec "$test_client" ping -c 2 -W 5 "$router_ip" >/dev/null 2>&1; then
            record_test "container_network_after_restart" "PASS"
        else
            record_test "container_network_after_restart" "FAIL" "Network not working after restart"
        fi
    fi
}

# Test: Resource Limits and Monitoring
test_resource_limits() {
    info "Testing resource limits and monitoring..."
    
    # Test memory limits
    local test_client="${CLIENT_CONTAINERS[0]}"
    
    # Get current memory usage
    local memory_usage=$(podman stats --no-stream --format "{{.MemUsage}}" "$test_client" 2>/dev/null | awk '{print $1}' | sed 's/MiB//')
    
    if [ -n "$memory_usage" ] && [ "$(echo "$memory_usage > 0" | bc 2>/dev/null || echo 0)" -eq 1 ]; then
        record_test "memory_monitoring" "PASS"
    else
        record_test "memory_monitoring" "FAIL" "Cannot monitor memory usage"
    fi
    
    # Test CPU monitoring
    local cpu_usage=$(podman stats --no-stream --format "{{.CPU}}" "$test_client" 2>/dev/null | sed 's/%//')
    
    if [ -n "$cpu_usage" ]; then
        record_test "cpu_monitoring" "PASS"
    else
        record_test "cpu_monitoring" "FAIL" "Cannot monitor CPU usage"
    fi
    
    # Test network I/O monitoring
    local network_io=$(podman stats --no-stream --format "{{.NetIO}}" "$test_client" 2>/dev/null)
    
    if [ -n "$network_io" ]; then
        record_test "network_io_monitoring" "PASS"
    else
        record_test "network_io_monitoring" "FAIL" "Cannot monitor network I/O"
    fi
}

# Test: Log Collection and Analysis
test_log_collection() {
    info "Testing log collection and analysis..."
    
    # Test container logs
    local router_logs=$(podman logs "$ROUTER_CONTAINER" 2>/dev/null | wc -l)
    
    if [ "$router_logs" -gt 0 ]; then
        record_test "router_log_collection" "PASS"
    else
        record_test "router_log_collection" "FAIL" "No router logs available"
    fi
    
    # Test application logs
    if [ -d "/var/log/openwrt-rust" ]; then
        local app_logs=$(find /var/log/openwrt-rust -name "*.log" -type f 2>/dev/null | wc -l)
        
        if [ "$app_logs" -gt 0 ]; then
            record_test "application_log_collection" "PASS"
        else
            record_test "application_log_collection" "FAIL" "No application logs found"
        fi
    else
        record_test "application_log_directory" "FAIL" "Log directory not found"
    fi
    
    # Test log rotation
    local test_log="/var/log/openwrt-rust/test.log"
    
    if echo "Test log entry" > "$test_log" 2>/dev/null; then
        record_test "log_writing" "PASS"
        rm -f "$test_log" 2>/dev/null
    else
        record_test "log_writing" "FAIL" "Cannot write to log directory"
    fi
}

# Test: Cleanup and Resource Management
test_cleanup() {
    info "Testing cleanup and resource management..."
    
    # Test container cleanup
    if timeout "$CLEANUP_TIMEOUT" "${SCRIPTS_DIR}/container-manager.sh" stop >/dev/null 2>&1; then
        record_test "container_cleanup" "PASS"
    else
        record_test "container_cleanup" "FAIL" "Container cleanup failed"
    fi
    
    # Test network cleanup
    if timeout "$CLEANUP_TIMEOUT" "${SCRIPTS_DIR}/4port-lan-setup.sh" cleanup >/dev/null 2>&1; then
        record_test "network_cleanup" "PASS"
    else
        record_test "network_cleanup" "FAIL" "Network cleanup failed"
    fi
    
    # Verify containers are stopped
    local running_containers=0
    
    for container in "$ROUTER_CONTAINER" "${CLIENT_CONTAINERS[@]}"; do
        if podman container inspect "$container" --format '{{.State.Running}}' 2>/dev/null | grep -q "true"; then
            running_containers=$((running_containers + 1))
        fi
    done
    
    if [ $running_containers -eq 0 ]; then
        record_test "containers_stopped" "PASS"
    else
        record_test "containers_stopped" "FAIL" "$running_containers containers still running"
    fi
}

# Generate integration test report
generate_integration_report() {
    info "Generating integration test report..."
    
    # Create JSON report
    cat > "$RESULTS_FILE" << EOF
{
    "integration_test_run": {
        "timestamp": "$TIMESTAMP",
        "total_tests": $test_count,
        "passed": $passed_count,
        "failed": $failed_count,
        "success_rate": $(echo "scale=2; $passed_count * 100 / $test_count" | bc 2>/dev/null || echo "0")
    },
    "test_results": {
EOF

    local first=true
    for test_name in "${!test_results[@]}"; do
        if [ "$first" = true ]; then
            first=false
        else
            echo "," >> "$RESULTS_FILE"
        fi
        echo "        \"$test_name\": \"${test_results[$test_name]}\"" >> "$RESULTS_FILE"
    done

    cat >> "$RESULTS_FILE" << EOF
    }
}
EOF

    # Print summary
    echo ""
    echo "=========================================="
    echo "Container Integration Test Results"
    echo "=========================================="
    echo "Total Tests: $test_count"
    echo "Passed: $passed_count"
    echo "Failed: $failed_count"
    echo "Success Rate: $(echo "scale=1; $passed_count * 100 / $test_count" | bc 2>/dev/null || echo "0")%"
    echo ""
    echo "Detailed results: $RESULTS_FILE"
    echo "Test log: $TEST_LOG"
    echo "=========================================="
    
    if [ $failed_count -eq 0 ]; then
        success "All integration tests passed!"
        return 0
    else
        error "$failed_count integration tests failed"
        return 1
    fi
}

# Main execution
main() {
    info "Starting container integration tests..."
    
    # Create log directory
    mkdir -p "$LOG_DIR"
    
    # Run integration test suites
    test_environment_setup
    test_e2e_network_flow
    test_service_discovery
    test_container_lifecycle
    test_resource_limits
    test_log_collection
    test_cleanup
    
    # Generate report
    generate_integration_report
}

# Execute main function
main "$@"
