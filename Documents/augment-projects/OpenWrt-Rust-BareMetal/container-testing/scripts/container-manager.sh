#!/bin/bash

# Container Manager for OpenWrt Rust 4 Port LAN Testing
# Manages container lifecycle, orchestration, and testing scenarios

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_DIR="${SCRIPT_DIR}/../configs"
NETWORK_CONFIG="${CONFIG_DIR}/networks/4port-lan.json"
LOG_DIR="${SCRIPT_DIR}/../logs"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="${LOG_DIR}/container-manager_${TIMESTAMP}.log"

# Container configuration
CONTAINER_PREFIX="openwrt-rust"
BASE_IMAGE="openwrt-rust-testing:latest"
ROUTER_CONTAINER="${CONTAINER_PREFIX}-router"
CLIENT_CONTAINERS=("${CONTAINER_PREFIX}-client1" "${CONTAINER_PREFIX}-client2" "${CONTAINER_PREFIX}-client3")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "${LOG_FILE}"
}

info() { log "INFO" "$@"; }
warn() { log "WARN" "${YELLOW}$*${NC}"; }
error() { log "ERROR" "${RED}$*${NC}"; }
success() { log "SUCCESS" "${GREEN}$*${NC}"; }

# Check prerequisites
check_prerequisites() {
    info "Checking prerequisites..."
    
    # Check if Podman is installed
    if ! command -v podman &> /dev/null; then
        error "Podman is not installed. Please install Podman first."
        exit 1
    fi
    
    # Check if jq is installed
    if ! command -v jq &> /dev/null; then
        error "jq is not installed. Please install jq for JSON processing."
        exit 1
    fi
    
    # Create log directory
    mkdir -p "${LOG_DIR}"
    
    # Check if networks exist
    local networks=$(jq -r '.networks | keys[]' "${NETWORK_CONFIG}")
    for network in $networks; do
        local network_name=$(jq -r ".networks.${network}.name" "${NETWORK_CONFIG}")
        if ! podman network exists "${network_name}"; then
            error "Network ${network_name} does not exist. Run 4port-lan-setup.sh first."
            exit 1
        fi
    done
    
    success "Prerequisites check completed"
}

# Build container images
build_images() {
    info "Building container images..."
    
    # Create Dockerfile for OpenWrt Rust testing
    local dockerfile="${CONFIG_DIR}/images/Dockerfile.openwrt-rust"
    mkdir -p "$(dirname "${dockerfile}")"
    
    cat > "${dockerfile}" << 'EOF'
FROM fedora:38

# Install dependencies
RUN dnf update -y && \
    dnf install -y \
        rust cargo \
        gcc gcc-c++ \
        iproute2 iptables \
        bridge-utils \
        net-tools \
        tcpdump \
        iperf3 \
        netcat \
        curl \
        jq \
        vim \
    && dnf clean all

# Create working directory
WORKDIR /opt/openwrt-rust

# Copy OpenWrt Rust implementation
COPY src/ ./src/
COPY Cargo.toml ./
COPY Cargo.lock ./

# Build the project
RUN cargo build --release

# Create network configuration directory
RUN mkdir -p /etc/openwrt-rust

# Set up entrypoint
COPY container-testing/scripts/container-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/container-entrypoint.sh

ENTRYPOINT ["/usr/local/bin/container-entrypoint.sh"]
CMD ["router"]
EOF
    
    # Build the image
    info "Building OpenWrt Rust testing image..."
    if podman build -t "${BASE_IMAGE}" -f "${dockerfile}" .; then
        success "Built image: ${BASE_IMAGE}"
    else
        error "Failed to build image: ${BASE_IMAGE}"
        exit 1
    fi
}

# Create container entrypoint script
create_entrypoint() {
    local entrypoint="${SCRIPT_DIR}/container-entrypoint.sh"
    
    cat > "${entrypoint}" << 'EOF'
#!/bin/bash

set -euo pipefail

ROLE="${1:-router}"
CONTAINER_NAME="${HOSTNAME}"

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [$ROLE] $*"
}

configure_network() {
    log "Configuring network for role: $ROLE"
    
    case "$ROLE" in
        router)
            # Enable IP forwarding
            echo 1 > /proc/sys/net/ipv4/ip_forward
            
            # Configure iptables for NAT
            iptables -t nat -A POSTROUTING -o eth0 -j MASQUERADE
            iptables -A FORWARD -i eth0 -o eth1 -m state --state RELATED,ESTABLISHED -j ACCEPT
            iptables -A FORWARD -i eth1 -o eth0 -j ACCEPT
            
            log "Router network configuration completed"
            ;;
        client*)
            # Client configuration
            log "Client network configuration completed"
            ;;
        *)
            log "Unknown role: $ROLE"
            ;;
    esac
}

start_services() {
    log "Starting services for role: $ROLE"
    
    case "$ROLE" in
        router)
            # Start OpenWrt Rust implementation
            log "Starting OpenWrt Rust router..."
            cd /opt/openwrt-rust
            exec ./target/release/openwrt-rust-baremetal
            ;;
        client*)
            # Start client services
            log "Starting client services..."
            # Keep container running for testing
            exec tail -f /dev/null
            ;;
        *)
            log "Unknown role: $ROLE"
            exit 1
            ;;
    esac
}

main() {
    log "Container starting with role: $ROLE"
    
    configure_network
    start_services
}

main "$@"
EOF
    
    chmod +x "${entrypoint}"
    success "Created container entrypoint script"
}

# Start router container
start_router() {
    info "Starting router container..."
    
    # Get network names from configuration
    local wan_network=$(jq -r '.networks.wan.name' "${NETWORK_CONFIG}")
    local lan1_network=$(jq -r '.networks.lan1.name' "${NETWORK_CONFIG}")
    local lan2_network=$(jq -r '.networks.lan2.name' "${NETWORK_CONFIG}")
    local lan3_network=$(jq -r '.networks.lan3.name' "${NETWORK_CONFIG}")
    local mgmt_network=$(jq -r '.networks.management.name' "${NETWORK_CONFIG}")
    
    # Stop existing router container if running
    if podman container exists "${ROUTER_CONTAINER}"; then
        warn "Stopping existing router container..."
        podman stop "${ROUTER_CONTAINER}" || true
        podman rm "${ROUTER_CONTAINER}" || true
    fi
    
    # Start router container with multiple networks
    if podman run -d \
        --name "${ROUTER_CONTAINER}" \
        --hostname "openwrt-router" \
        --privileged \
        --cap-add=NET_ADMIN \
        --cap-add=SYS_ADMIN \
        --network "${wan_network}" \
        --network "${lan1_network}" \
        --network "${lan2_network}" \
        --network "${lan3_network}" \
        --network "${mgmt_network}" \
        -v "${SCRIPT_DIR}:/scripts:ro" \
        "${BASE_IMAGE}" router; then
        success "Started router container: ${ROUTER_CONTAINER}"
    else
        error "Failed to start router container"
        exit 1
    fi
}

# Start client containers
start_clients() {
    info "Starting client containers..."
    
    local networks=("lan1" "lan2" "lan3")
    
    for i in "${!CLIENT_CONTAINERS[@]}"; do
        local container_name="${CLIENT_CONTAINERS[$i]}"
        local network_key="${networks[$i]}"
        local network_name=$(jq -r ".networks.${network_key}.name" "${NETWORK_CONFIG}")
        local mgmt_network=$(jq -r '.networks.management.name' "${NETWORK_CONFIG}")
        
        # Stop existing client container if running
        if podman container exists "${container_name}"; then
            warn "Stopping existing client container: ${container_name}"
            podman stop "${container_name}" || true
            podman rm "${container_name}" || true
        fi
        
        # Start client container
        if podman run -d \
            --name "${container_name}" \
            --hostname "client$((i+1))" \
            --network "${network_name}" \
            --network "${mgmt_network}" \
            -v "${SCRIPT_DIR}:/scripts:ro" \
            "${BASE_IMAGE}" "client$((i+1))"; then
            success "Started client container: ${container_name}"
        else
            error "Failed to start client container: ${container_name}"
            exit 1
        fi
    done
}

# Stop all containers
stop_containers() {
    info "Stopping all containers..."
    
    # Stop router container
    if podman container exists "${ROUTER_CONTAINER}"; then
        info "Stopping router container..."
        podman stop "${ROUTER_CONTAINER}" || true
        podman rm "${ROUTER_CONTAINER}" || true
        success "Stopped router container"
    fi
    
    # Stop client containers
    for container_name in "${CLIENT_CONTAINERS[@]}"; do
        if podman container exists "${container_name}"; then
            info "Stopping client container: ${container_name}"
            podman stop "${container_name}" || true
            podman rm "${container_name}" || true
            success "Stopped client container: ${container_name}"
        fi
    done
}

# Show container status
show_status() {
    info "Container status:"
    
    echo -e "\n${BLUE}Router Container:${NC}"
    if podman container exists "${ROUTER_CONTAINER}"; then
        podman ps -a --filter "name=${ROUTER_CONTAINER}" --format "table {{.Names}} {{.Status}} {{.Ports}}"
    else
        echo "Router container not found"
    fi
    
    echo -e "\n${BLUE}Client Containers:${NC}"
    for container_name in "${CLIENT_CONTAINERS[@]}"; do
        if podman container exists "${container_name}"; then
            podman ps -a --filter "name=${container_name}" --format "table {{.Names}} {{.Status}} {{.Ports}}"
        else
            echo "${container_name}: not found"
        fi
    done
    
    echo -e "\n${BLUE}Network Information:${NC}"
    podman network ls --format "table {{.Name}} {{.Driver}} {{.Subnet}}"
}

# Execute command in container
exec_container() {
    local container_name="$1"
    shift
    local command="$*"
    
    if ! podman container exists "${container_name}"; then
        error "Container ${container_name} does not exist"
        exit 1
    fi
    
    info "Executing in ${container_name}: ${command}"
    podman exec -it "${container_name}" bash -c "${command}"
}

# Display help
show_help() {
    cat << EOF
Container Manager for OpenWrt Rust 4 Port LAN Testing

Usage: $0 [COMMAND] [OPTIONS]

Commands:
    build       Build container images
    start       Start all containers (router + clients)
    stop        Stop all containers
    restart     Restart all containers
    status      Show container status
    exec        Execute command in container
    logs        Show container logs
    help        Show this help message

Options:
    --verbose   Enable verbose output

Examples:
    $0 build                           # Build container images
    $0 start                           # Start all containers
    $0 exec openwrt-rust-router "ip a" # Execute command in router
    $0 logs openwrt-rust-client1       # Show client1 logs
    $0 stop                            # Stop all containers

EOF
}

# Main function
main() {
    local command="${1:-help}"
    local verbose_mode=false
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            build|start|stop|restart|status|exec|logs|help)
                command="$1"
                shift
                ;;
            --verbose)
                verbose_mode=true
                set -x
                shift
                ;;
            *)
                if [[ "${command}" == "exec" ]] && [[ $# -ge 2 ]]; then
                    exec_container "$@"
                    exit 0
                elif [[ "${command}" == "logs" ]] && [[ $# -ge 1 ]]; then
                    podman logs "$1"
                    exit 0
                else
                    error "Unknown option: $1"
                    show_help
                    exit 1
                fi
                ;;
        esac
    done
    
    # Execute command
    case "${command}" in
        build)
            info "Building container images..."
            check_prerequisites
            create_entrypoint
            build_images
            success "Container images built successfully!"
            ;;
        start)
            info "Starting containers..."
            check_prerequisites
            start_router
            start_clients
            show_status
            success "All containers started successfully!"
            ;;
        stop)
            info "Stopping containers..."
            stop_containers
            success "All containers stopped!"
            ;;
        restart)
            info "Restarting containers..."
            stop_containers
            sleep 2
            start_router
            start_clients
            show_status
            success "All containers restarted successfully!"
            ;;
        status)
            show_status
            ;;
        help)
            show_help
            ;;
        *)
            error "Unknown command: ${command}"
            show_help
            exit 1
            ;;
    esac
}

# Execute main function with all arguments
main "$@"
