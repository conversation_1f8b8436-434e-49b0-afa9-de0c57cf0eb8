#!/bin/bash

# Comprehensive Test Runner for OpenWrt Rust Container Testing
# Orchestrates complete testing workflow including setup, execution, and cleanup

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_DIR="${SCRIPT_DIR}/../logs"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="${LOG_DIR}/run-all-tests_${TIMESTAMP}.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "${LOG_FILE}"
}

info() { log "INFO" "$@"; }
warn() { log "WARN" "${YELLOW}$*${NC}"; }
error() { log "ERROR" "${RED}$*${NC}"; }
success() { log "SUCCESS" "${GREEN}$*${NC}"; }

# Test execution tracking
TOTAL_TEST_SUITES=0
PASSED_TEST_SUITES=0
FAILED_TEST_SUITES=0

# Execute test suite
run_test_suite() {
    local test_name="$1"
    local test_script="$2"
    
    info "Running test suite: ${test_name}"
    TOTAL_TEST_SUITES=$((TOTAL_TEST_SUITES + 1))
    
    if [[ ! -f "${test_script}" ]]; then
        error "Test script not found: ${test_script}"
        FAILED_TEST_SUITES=$((FAILED_TEST_SUITES + 1))
        return 1
    fi
    
    if [[ ! -x "${test_script}" ]]; then
        warn "Making test script executable: ${test_script}"
        chmod +x "${test_script}"
    fi
    
    if "${test_script}"; then
        success "Test suite PASSED: ${test_name}"
        PASSED_TEST_SUITES=$((PASSED_TEST_SUITES + 1))
        return 0
    else
        error "Test suite FAILED: ${test_name}"
        FAILED_TEST_SUITES=$((FAILED_TEST_SUITES + 1))
        return 1
    fi
}

# Setup testing environment
setup_environment() {
    info "Setting up testing environment..."
    
    # Create log directory
    mkdir -p "${LOG_DIR}"
    
    # Setup networks
    info "Setting up 4 port LAN networks..."
    if ! "${SCRIPT_DIR}/4port-lan-setup.sh" setup; then
        error "Failed to setup networks"
        return 1
    fi
    
    # Build container images
    info "Building container images..."
    if ! "${SCRIPT_DIR}/container-manager.sh" build; then
        error "Failed to build container images"
        return 1
    fi
    
    # Start containers
    info "Starting containers..."
    if ! "${SCRIPT_DIR}/container-manager.sh" start; then
        error "Failed to start containers"
        return 1
    fi
    
    # Wait for containers to be ready
    info "Waiting for containers to be ready..."
    sleep 10
    
    success "Testing environment setup completed"
    return 0
}

# Cleanup testing environment
cleanup_environment() {
    info "Cleaning up testing environment..."
    
    # Stop containers
    info "Stopping containers..."
    "${SCRIPT_DIR}/container-manager.sh" stop || warn "Failed to stop some containers"
    
    # Optional: Clean up networks (commented out to preserve for debugging)
    # info "Cleaning up networks..."
    # "${SCRIPT_DIR}/4port-lan-setup.sh" cleanup || warn "Failed to cleanup some networks"
    
    success "Testing environment cleanup completed"
}

# Generate comprehensive test report
generate_report() {
    info "Generating comprehensive test report..."
    
    local report_file="${LOG_DIR}/comprehensive-test-report_${TIMESTAMP}.json"
    local html_report="${LOG_DIR}/test-report_${TIMESTAMP}.html"
    
    # Create JSON report
    cat > "${report_file}" << EOF
{
  "test_run": {
    "timestamp": "${TIMESTAMP}",
    "total_test_suites": ${TOTAL_TEST_SUITES},
    "passed_test_suites": ${PASSED_TEST_SUITES},
    "failed_test_suites": ${FAILED_TEST_SUITES},
    "success_rate": $(echo "scale=2; ${PASSED_TEST_SUITES} * 100 / ${TOTAL_TEST_SUITES}" | bc -l)
  },
  "environment": {
    "container_testing": true,
    "podman_version": "$(podman --version 2>/dev/null || echo 'unknown')",
    "system": "$(uname -s)",
    "architecture": "$(uname -m)"
  },
  "test_suites": {
    "network_tests": "$([ -f "${LOG_DIR}/test-results_${TIMESTAMP}.json" ] && echo "completed" || echo "not_run")"
  }
}
EOF
    
    # Create HTML report
    cat > "${html_report}" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>OpenWrt Rust Container Testing Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .success { color: green; }
        .failure { color: red; }
        .warning { color: orange; }
        .summary { background-color: #e8f4f8; padding: 15px; border-radius: 5px; margin: 20px 0; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>OpenWrt Rust Container Testing Report</h1>
        <p><strong>Generated:</strong> $(date)</p>
        <p><strong>Test Run ID:</strong> ${TIMESTAMP}</p>
    </div>
    
    <div class="summary">
        <h2>Test Summary</h2>
        <table>
            <tr><th>Metric</th><th>Value</th></tr>
            <tr><td>Total Test Suites</td><td>${TOTAL_TEST_SUITES}</td></tr>
            <tr><td>Passed</td><td class="success">${PASSED_TEST_SUITES}</td></tr>
            <tr><td>Failed</td><td class="failure">${FAILED_TEST_SUITES}</td></tr>
            <tr><td>Success Rate</td><td>$(echo "scale=1; ${PASSED_TEST_SUITES} * 100 / ${TOTAL_TEST_SUITES}" | bc -l)%</td></tr>
        </table>
    </div>
    
    <div>
        <h2>Environment Information</h2>
        <ul>
            <li><strong>System:</strong> $(uname -s) $(uname -m)</li>
            <li><strong>Podman Version:</strong> $(podman --version 2>/dev/null || echo 'Not available')</li>
            <li><strong>Test Type:</strong> Container Testing with 4 Port LAN</li>
        </ul>
    </div>
    
    <div>
        <h2>Log Files</h2>
        <ul>
            <li>Main Log: ${LOG_FILE}</li>
            <li>JSON Report: ${report_file}</li>
        </ul>
    </div>
</body>
</html>
EOF
    
    success "Test reports generated:"
    info "  JSON Report: ${report_file}"
    info "  HTML Report: ${html_report}"
    info "  Main Log: ${LOG_FILE}"
}

# Display help
show_help() {
    cat << EOF
Comprehensive Test Runner for OpenWrt Rust Container Testing

Usage: $0 [OPTIONS]

Options:
    --no-setup      Skip environment setup
    --no-cleanup    Skip environment cleanup
    --verbose       Enable verbose output
    --help          Show this help message

Examples:
    $0                    # Run complete test suite
    $0 --no-cleanup       # Run tests but keep environment for debugging
    $0 --verbose          # Run with verbose output

EOF
}

# Main function
main() {
    local skip_setup=false
    local skip_cleanup=false
    local verbose_mode=false
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --no-setup)
                skip_setup=true
                shift
                ;;
            --no-cleanup)
                skip_cleanup=true
                shift
                ;;
            --verbose)
                verbose_mode=true
                set -x
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    info "Starting comprehensive container testing..."
    
    # Setup environment
    if [[ "${skip_setup}" == "false" ]]; then
        if ! setup_environment; then
            error "Environment setup failed"
            exit 1
        fi
    else
        info "Skipping environment setup"
    fi
    
    # Run test suites
    info "Executing test suites..."
    
    # Network functionality tests
    run_test_suite "Network Functionality" "${SCRIPT_DIR}/../tests/network/4port-lan-tests.sh"
    
    # Add more test suites here as they are developed
    # run_test_suite "Performance Tests" "${SCRIPT_DIR}/../tests/performance/performance-tests.sh"
    # run_test_suite "Integration Tests" "${SCRIPT_DIR}/../tests/integration/integration-tests.sh"
    
    # Generate comprehensive report
    generate_report
    
    # Cleanup environment
    if [[ "${skip_cleanup}" == "false" ]]; then
        cleanup_environment
    else
        info "Skipping environment cleanup"
    fi
    
    # Final summary
    echo -e "\n${BLUE}=== FINAL TEST SUMMARY ===${NC}"
    echo -e "Total Test Suites: ${TOTAL_TEST_SUITES}"
    echo -e "Passed: ${GREEN}${PASSED_TEST_SUITES}${NC}"
    echo -e "Failed: ${RED}${FAILED_TEST_SUITES}${NC}"
    
    if [[ ${FAILED_TEST_SUITES} -eq 0 ]]; then
        success "All test suites passed!"
        exit 0
    else
        error "Some test suites failed!"
        exit 1
    fi
}

# Execute main function with all arguments
main "$@"
