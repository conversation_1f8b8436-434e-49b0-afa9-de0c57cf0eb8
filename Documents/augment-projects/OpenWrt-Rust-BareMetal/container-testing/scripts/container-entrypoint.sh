#!/bin/bash

set -euo pipefail

ROLE="${1:-router}"
CONTAINER_NAME="${HOSTNAME}"

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [$ROLE] $*"
}

configure_network() {
    log "Configuring network for role: $ROLE"
    
    case "$ROLE" in
        router)
            # Enable IP forwarding
            echo 1 > /proc/sys/net/ipv4/ip_forward
            
            # Configure iptables for NAT
            iptables -t nat -A POSTROUTING -o eth0 -j MASQUERADE
            iptables -A FORWARD -i eth0 -o eth1 -m state --state RELATED,ESTABLISHED -j ACCEPT
            iptables -A FORWARD -i eth1 -o eth0 -j ACCEPT
            
            log "Router network configuration completed"
            ;;
        client*)
            # Client configuration
            log "Client network configuration completed"
            ;;
        *)
            log "Unknown role: $ROLE"
            ;;
    esac
}

start_services() {
    log "Starting services for role: $ROLE"
    
    case "$ROLE" in
        router)
            # Start OpenWrt Rust implementation (FFI userspace version)
            log "Starting OpenWrt Rust router in FFI mode..."
            cd /opt/openwrt-rust

            # Check if FFI binary exists
            if [ ! -f "./target/release/openwrt-rust-baremetal" ]; then
                log "ERROR: Binary not found. Building with FFI feature..."
                cargo build --release --features ffi
            fi

            log "Starting OpenWrt Rust implementation in container mode..."
            exec ./target/release/openwrt-rust-baremetal
            ;;
        client*)
            # Start client services
            log "Starting client services..."
            # Keep container running for testing
            exec tail -f /dev/null
            ;;
        *)
            log "Unknown role: $ROLE"
            exit 1
            ;;
    esac
}

main() {
    log "Container starting with role: $ROLE"
    
    configure_network
    start_services
}

main "$@"
