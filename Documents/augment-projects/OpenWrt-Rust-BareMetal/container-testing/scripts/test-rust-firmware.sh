#!/bin/bash

set -euo pipefail

# Test script for OpenWrt Rust firmware in container environment
# This script properly tests the userspace FFI version of the implementation

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
CONTAINER_NAME="openwrt-rust-test"
IMAGE_NAME="openwrt-rust:latest"

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $*"
}

cleanup() {
    log "Cleaning up test environment..."
    podman stop "$CONTAINER_NAME" 2>/dev/null || true
    podman rm "$CONTAINER_NAME" 2>/dev/null || true
}

build_image() {
    log "Building OpenWrt Rust container image with FFI support..."
    cd "$PROJECT_ROOT"
    
    # Build the container image with FFI feature enabled
    podman build \
        -f container-testing/configs/images/Dockerfile.openwrt-rust \
        -t "$IMAGE_NAME" \
        .
    
    log "Container image built successfully"
}

test_firmware_boot() {
    log "Testing OpenWrt Rust firmware boot in container..."
    
    # Start container in detached mode
    podman run -d \
        --name "$CONTAINER_NAME" \
        --privileged \
        --cap-add=NET_ADMIN \
        --cap-add=SYS_ADMIN \
        "$IMAGE_NAME" \
        router
    
    # Wait for container to start
    sleep 5
    
    # Check if container is running
    if podman ps --filter "name=$CONTAINER_NAME" --format "{{.Status}}" | grep -q "Up"; then
        log "✅ Container started successfully"
        
        # Check logs for successful initialization
        log "Checking container logs..."
        podman logs "$CONTAINER_NAME" | tail -20
        
        # Test if the Rust implementation is responding
        log "Testing Rust implementation functionality..."
        podman exec "$CONTAINER_NAME" ps aux | grep -E "(openwrt|rust)" || true
        
        return 0
    else
        log "❌ Container failed to start"
        log "Container logs:"
        podman logs "$CONTAINER_NAME" || true
        return 1
    fi
}

test_network_functionality() {
    log "Testing network functionality..."
    
    # Test basic network commands
    podman exec "$CONTAINER_NAME" ip addr show || true
    podman exec "$CONTAINER_NAME" ip route show || true
    
    # Test if IP forwarding is enabled (router functionality)
    if podman exec "$CONTAINER_NAME" cat /proc/sys/net/ipv4/ip_forward | grep -q "1"; then
        log "✅ IP forwarding enabled"
    else
        log "⚠️  IP forwarding not enabled"
    fi
}

run_comprehensive_test() {
    log "Starting comprehensive OpenWrt Rust firmware test..."
    
    # Trap cleanup on exit
    trap cleanup EXIT
    
    # Clean up any existing containers
    cleanup
    
    # Build the image
    build_image
    
    # Test firmware boot
    if test_firmware_boot; then
        log "✅ Firmware boot test passed"
        
        # Test network functionality
        test_network_functionality
        
        log "✅ All tests completed successfully"
        log "Your OpenWrt Rust implementation is working in container mode!"
    else
        log "❌ Firmware boot test failed"
        log "Check the logs above for error details"
        return 1
    fi
}

show_usage() {
    cat << EOF
Usage: $0 [COMMAND]

Commands:
    test        Run comprehensive firmware test (default)
    build       Build container image only
    boot        Test firmware boot only
    network     Test network functionality only
    logs        Show container logs
    shell       Open shell in running container
    cleanup     Clean up test environment

Examples:
    $0                  # Run full test
    $0 test            # Run full test
    $0 build           # Build image only
    $0 shell           # Open shell in container
EOF
}

main() {
    local command="${1:-test}"
    
    case "$command" in
        test)
            run_comprehensive_test
            ;;
        build)
            build_image
            ;;
        boot)
            cleanup
            build_image
            test_firmware_boot
            ;;
        network)
            test_network_functionality
            ;;
        logs)
            podman logs "$CONTAINER_NAME" || log "Container not running"
            ;;
        shell)
            podman exec -it "$CONTAINER_NAME" /bin/bash || log "Container not running"
            ;;
        cleanup)
            cleanup
            ;;
        help|--help|-h)
            show_usage
            ;;
        *)
            log "Unknown command: $command"
            show_usage
            exit 1
            ;;
    esac
}

main "$@"
