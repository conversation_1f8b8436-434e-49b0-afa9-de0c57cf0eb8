#!/bin/sh

# OpenWrt Rust Init Script
# This script acts as PID 1 in the container and starts the Rust OpenWrt implementation

set -e

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INIT] $*"
}

# Signal handler for graceful shutdown
cleanup() {
    log "Received shutdown signal, stopping OpenWrt Rust..."
    if [ -n "$OPENWRT_PID" ]; then
        kill -TERM "$OPENWRT_PID" 2>/dev/null || true
        wait "$OPENWRT_PID" 2>/dev/null || true
    fi
    log "OpenWrt Rust stopped"
    exit 0
}

# Set up signal handlers
trap cleanup TERM INT

log "OpenWrt Rust Container Init Starting..."

# Initialize OpenWrt environment
log "Setting up OpenWrt environment..."

# Create necessary directories
mkdir -p /var/run /var/lock /var/log /var/lib /tmp
chmod 755 /var/run /var/lock /var/log /var/lib /tmp

# Set up basic networking (container will handle this)
log "Container networking will be handled by container runtime"

# Set hostname
if [ -n "${HOSTNAME:-}" ]; then
    echo "$HOSTNAME" > /proc/sys/kernel/hostname
    log "Hostname set to: $HOSTNAME"
fi

# Start OpenWrt Rust implementation
log "Starting OpenWrt Rust implementation..."

# Determine mode from environment or command line
MODE="${1:-${OPENWRT_MODE:-router}}"
log "OpenWrt mode: $MODE"

# Start the Rust implementation in background
/usr/sbin/openwrt-rust "$MODE" &
OPENWRT_PID=$!

log "OpenWrt Rust started with PID: $OPENWRT_PID"

# Wait for the process and handle signals
wait "$OPENWRT_PID"
EXIT_CODE=$?

log "OpenWrt Rust exited with code: $EXIT_CODE"
exit $EXIT_CODE
