#!/bin/bash

# Production Validation Script for OpenWrt Rust Container Testing
# Comprehensive validation for production deployment readiness

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="${SCRIPT_DIR}/../.."
CONTAINER_DIR="${SCRIPT_DIR}/.."
LOG_DIR="${CONTAINER_DIR}/logs"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
VALIDATION_LOG="${LOG_DIR}/production-validation_${TIMESTAMP}.log"
RESULTS_FILE="${LOG_DIR}/production-validation-results_${TIMESTAMP}.json"

# Validation configuration
PERFORMANCE_THRESHOLD_SPEEDUP=2.0
SECURITY_SCAN_TIMEOUT=300
RELIABILITY_TEST_DURATION=600
STRESS_TEST_DURATION=300

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "${VALIDATION_LOG}"
}

info() { log "INFO" "$@"; }
warn() { log "WARN" "$@"; }
error() { log "ERROR" "$@"; }
success() { log "SUCCESS" "$@"; }

# Validation results storage
declare -A validation_results
validation_count=0
passed_count=0
failed_count=0

# Record validation result
record_validation() {
    local test_name=$1
    local result=$2
    local details=${3:-""}
    
    validation_count=$((validation_count + 1))
    validation_results["$test_name"]="$result"
    
    if [ "$result" = "PASS" ]; then
        passed_count=$((passed_count + 1))
        success "VALIDATION PASS: $test_name"
    else
        failed_count=$((failed_count + 1))
        error "VALIDATION FAIL: $test_name - $details"
    fi
}

# Check production prerequisites
check_production_prerequisites() {
    info "Checking production deployment prerequisites..."
    
    # Check system requirements
    local memory_gb=$(free -g | awk 'NR==2{print $2}')
    if [ "$memory_gb" -ge 8 ]; then
        record_validation "system_memory" "PASS"
    else
        record_validation "system_memory" "FAIL" "Insufficient memory: ${memory_gb}GB (minimum: 8GB)"
    fi
    
    # Check disk space
    local disk_free_gb=$(df / | awk 'NR==2{print int($4/1024/1024)}')
    if [ "$disk_free_gb" -ge 20 ]; then
        record_validation "disk_space" "PASS"
    else
        record_validation "disk_space" "FAIL" "Insufficient disk space: ${disk_free_gb}GB (minimum: 20GB)"
    fi
    
    # Check Podman version
    local podman_version=$(podman --version | awk '{print $3}' | cut -d. -f1,2)
    if [ "$(echo "$podman_version >= 4.0" | bc)" -eq 1 ]; then
        record_validation "podman_version" "PASS"
    else
        record_validation "podman_version" "FAIL" "Podman version too old: $podman_version (minimum: 4.0)"
    fi
    
    # Check required tools
    local required_tools=("jq" "bc" "iperf3" "netcat" "curl")
    local missing_tools=()
    
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#missing_tools[@]} -eq 0 ]; then
        record_validation "required_tools" "PASS"
    else
        record_validation "required_tools" "FAIL" "Missing tools: ${missing_tools[*]}"
    fi
}

# Validate container security
validate_container_security() {
    info "Validating container security configuration..."
    
    # Check for non-root user usage
    local root_usage=$(grep -r "USER root" "${CONTAINER_DIR}/configs/images/" || echo "")
    if [ -z "$root_usage" ]; then
        record_validation "non_root_user" "PASS"
    else
        record_validation "non_root_user" "FAIL" "Root user found in container images"
    fi
    
    # Check for privileged containers
    local privileged_usage=$(grep -r "privileged.*true" "${CONTAINER_DIR}/configs/" || echo "")
    if [ -z "$privileged_usage" ]; then
        record_validation "no_privileged_containers" "PASS"
    else
        record_validation "no_privileged_containers" "FAIL" "Privileged containers found"
    fi
    
    # Check security contexts
    local security_contexts=$(grep -r "security-opt" "${CONTAINER_DIR}/scripts/" || echo "")
    if [ -n "$security_contexts" ]; then
        record_validation "security_contexts" "PASS"
    else
        record_validation "security_contexts" "WARN" "No explicit security contexts found"
    fi
    
    # Validate network security
    local network_isolation=$(jq -r '.vlans | length' "${CONTAINER_DIR}/configs/networks/4port-lan.json")
    if [ "$network_isolation" -ge 3 ]; then
        record_validation "network_isolation" "PASS"
    else
        record_validation "network_isolation" "FAIL" "Insufficient network isolation"
    fi
}

# Validate performance requirements
validate_performance_requirements() {
    info "Validating performance requirements..."
    
    # Run performance comparison
    if "${SCRIPT_DIR}/performance-comparison.sh" >/dev/null 2>&1; then
        local latest_comparison=$(ls -t "${LOG_DIR}"/performance-comparison_*.json | head -1)
        
        if [ -f "$latest_comparison" ]; then
            local speedup=$(jq -r '.comparison.speedup_factor' "$latest_comparison" 2>/dev/null || echo "0")
            local target_met=$(jq -r '.comparison.target_met' "$latest_comparison" 2>/dev/null || echo "false")
            
            if [ "$target_met" = "true" ]; then
                record_validation "performance_target" "PASS" "Speedup: ${speedup}x"
            else
                record_validation "performance_target" "FAIL" "Performance target not met: ${speedup}x (required: ${PERFORMANCE_THRESHOLD_SPEEDUP}x)"
            fi
        else
            record_validation "performance_comparison" "FAIL" "Performance comparison data not available"
        fi
    else
        record_validation "performance_comparison" "FAIL" "Performance comparison failed"
    fi
    
    # Test resource efficiency
    local container_memory_usage=$(podman stats --no-stream --format "{{.MemUsage}}" openwrt-rust-router 2>/dev/null | awk '{print $1}' | sed 's/MiB//' || echo "0")
    
    if [ "$(echo "$container_memory_usage < 2048" | bc)" -eq 1 ]; then
        record_validation "memory_efficiency" "PASS" "Memory usage: ${container_memory_usage}MB"
    else
        record_validation "memory_efficiency" "FAIL" "High memory usage: ${container_memory_usage}MB"
    fi
}

# Validate reliability and stability
validate_reliability() {
    info "Validating reliability and stability..."
    
    # Setup test environment
    "${SCRIPT_DIR}/4port-lan-setup.sh" setup >/dev/null 2>&1
    "${SCRIPT_DIR}/container-manager.sh" build >/dev/null 2>&1
    "${SCRIPT_DIR}/container-manager.sh" start >/dev/null 2>&1
    
    sleep 10
    
    # Long-running stability test
    local start_time=$(date +%s)
    local end_time=$((start_time + RELIABILITY_TEST_DURATION))
    local test_failures=0
    local test_iterations=0
    
    info "Running reliability test for ${RELIABILITY_TEST_DURATION} seconds..."
    
    while [ $(date +%s) -lt $end_time ]; do
        test_iterations=$((test_iterations + 1))
        
        # Test basic connectivity
        if ! "${CONTAINER_DIR}/tests/network/4port-lan-tests.sh" >/dev/null 2>&1; then
            test_failures=$((test_failures + 1))
        fi
        
        sleep 30
    done
    
    local failure_rate=$(echo "scale=2; $test_failures * 100 / $test_iterations" | bc)
    
    if [ "$(echo "$failure_rate <= 1.0" | bc)" -eq 1 ]; then
        record_validation "reliability_test" "PASS" "Failure rate: ${failure_rate}% (${test_failures}/${test_iterations})"
    else
        record_validation "reliability_test" "FAIL" "High failure rate: ${failure_rate}% (${test_failures}/${test_iterations})"
    fi
    
    # Container restart resilience
    info "Testing container restart resilience..."
    
    "${SCRIPT_DIR}/container-manager.sh" restart >/dev/null 2>&1
    sleep 15
    
    if "${CONTAINER_DIR}/tests/network/4port-lan-tests.sh" >/dev/null 2>&1; then
        record_validation "restart_resilience" "PASS"
    else
        record_validation "restart_resilience" "FAIL" "Tests fail after container restart"
    fi
    
    # Cleanup
    "${SCRIPT_DIR}/container-manager.sh" stop >/dev/null 2>&1
    "${SCRIPT_DIR}/4port-lan-setup.sh" cleanup >/dev/null 2>&1
}

# Validate scalability
validate_scalability() {
    info "Validating scalability characteristics..."
    
    # Test multiple container instances
    local max_containers=5
    local successful_containers=0
    
    "${SCRIPT_DIR}/4port-lan-setup.sh" setup >/dev/null 2>&1
    
    for ((i=1; i<=max_containers; i++)); do
        if "${SCRIPT_DIR}/container-manager.sh" start --scale "$i" >/dev/null 2>&1; then
            successful_containers=$i
            sleep 5
        else
            break
        fi
    done
    
    if [ $successful_containers -ge 3 ]; then
        record_validation "container_scalability" "PASS" "Successfully scaled to $successful_containers containers"
    else
        record_validation "container_scalability" "FAIL" "Limited scalability: $successful_containers containers"
    fi
    
    # Test concurrent connections
    local concurrent_connections=100
    local successful_connections=0
    
    # Start test server
    "${SCRIPT_DIR}/container-manager.sh" start >/dev/null 2>&1
    sleep 10
    
    for ((i=1; i<=concurrent_connections; i++)); do
        if timeout 5 "${SCRIPT_DIR}/container-utils.sh" test-connectivity >/dev/null 2>&1; then
            successful_connections=$((successful_connections + 1))
        fi
    done
    
    local connection_success_rate=$(echo "scale=1; $successful_connections * 100 / $concurrent_connections" | bc)
    
    if [ "$(echo "$connection_success_rate >= 95.0" | bc)" -eq 1 ]; then
        record_validation "concurrent_connections" "PASS" "Success rate: ${connection_success_rate}%"
    else
        record_validation "concurrent_connections" "FAIL" "Low success rate: ${connection_success_rate}%"
    fi
    
    # Cleanup
    "${SCRIPT_DIR}/container-manager.sh" stop >/dev/null 2>&1
    "${SCRIPT_DIR}/4port-lan-setup.sh" cleanup >/dev/null 2>&1
}

# Validate monitoring and observability
validate_monitoring() {
    info "Validating monitoring and observability..."
    
    # Check log generation
    "${SCRIPT_DIR}/4port-lan-setup.sh" setup >/dev/null 2>&1
    "${SCRIPT_DIR}/container-manager.sh" start >/dev/null 2>&1
    
    sleep 10
    
    # Test log collection
    local log_files=$(find "${LOG_DIR}" -name "*.log" -type f | wc -l)
    
    if [ $log_files -gt 0 ]; then
        record_validation "log_generation" "PASS" "Found $log_files log files"
    else
        record_validation "log_generation" "FAIL" "No log files generated"
    fi
    
    # Test metrics collection
    if "${SCRIPT_DIR}/container-utils.sh" health-report >/dev/null 2>&1; then
        record_validation "health_monitoring" "PASS"
    else
        record_validation "health_monitoring" "FAIL" "Health monitoring not working"
    fi
    
    # Test performance metrics
    if "${CONTAINER_DIR}/tests/performance/network-performance-tests.sh" >/dev/null 2>&1; then
        record_validation "performance_monitoring" "PASS"
    else
        record_validation "performance_monitoring" "FAIL" "Performance monitoring not working"
    fi
    
    # Cleanup
    "${SCRIPT_DIR}/container-manager.sh" stop >/dev/null 2>&1
    "${SCRIPT_DIR}/4port-lan-setup.sh" cleanup >/dev/null 2>&1
}

# Validate documentation completeness
validate_documentation() {
    info "Validating documentation completeness..."
    
    local required_docs=(
        "README.md"
        "docs/SETUP.md"
        "docs/USAGE.md"
        "docs/TROUBLESHOOTING.md"
    )
    
    local missing_docs=()
    
    for doc in "${required_docs[@]}"; do
        if [ ! -f "${CONTAINER_DIR}/$doc" ]; then
            missing_docs+=("$doc")
        fi
    done
    
    if [ ${#missing_docs[@]} -eq 0 ]; then
        record_validation "documentation_completeness" "PASS"
    else
        record_validation "documentation_completeness" "FAIL" "Missing documentation: ${missing_docs[*]}"
    fi
    
    # Check documentation quality
    local total_doc_lines=0
    for doc in "${required_docs[@]}"; do
        if [ -f "${CONTAINER_DIR}/$doc" ]; then
            local lines=$(wc -l < "${CONTAINER_DIR}/$doc")
            total_doc_lines=$((total_doc_lines + lines))
        fi
    done
    
    if [ $total_doc_lines -gt 1000 ]; then
        record_validation "documentation_quality" "PASS" "Total documentation: $total_doc_lines lines"
    else
        record_validation "documentation_quality" "FAIL" "Insufficient documentation: $total_doc_lines lines"
    fi
}

# Generate production validation report
generate_validation_report() {
    info "Generating production validation report..."
    
    # Create JSON report
    cat > "$RESULTS_FILE" << EOF
{
    "production_validation": {
        "timestamp": "$TIMESTAMP",
        "total_validations": $validation_count,
        "passed": $passed_count,
        "failed": $failed_count,
        "success_rate": $(echo "scale=2; $passed_count * 100 / $validation_count" | bc),
        "production_ready": $([ $failed_count -eq 0 ] && echo "true" || echo "false")
    },
    "validation_results": {
EOF

    local first=true
    for validation_name in "${!validation_results[@]}"; do
        if [ "$first" = true ]; then
            first=false
        else
            echo "," >> "$RESULTS_FILE"
        fi
        echo "        \"$validation_name\": \"${validation_results[$validation_name]}\"" >> "$RESULTS_FILE"
    done

    cat >> "$RESULTS_FILE" << EOF
    },
    "recommendations": {
        "deployment_readiness": "$([ $failed_count -eq 0 ] && echo "READY" || echo "NOT READY")",
        "critical_issues": $failed_count,
        "next_steps": "$([ $failed_count -eq 0 ] && echo "Proceed with production deployment" || echo "Address failed validations before deployment")"
    }
}
EOF

    # Print summary
    echo ""
    echo "=========================================="
    echo "Production Validation Results"
    echo "=========================================="
    echo "Total Validations: $validation_count"
    echo "Passed: $passed_count"
    echo "Failed: $failed_count"
    echo "Success Rate: $(echo "scale=1; $passed_count * 100 / $validation_count" | bc)%"
    echo ""
    
    if [ $failed_count -eq 0 ]; then
        echo "🟢 PRODUCTION READY"
        echo "All validation checks passed. Container testing environment is ready for production deployment."
    else
        echo "🔴 NOT PRODUCTION READY"
        echo "Failed validations must be addressed before production deployment."
        echo ""
        echo "Failed Validations:"
        for validation_name in "${!validation_results[@]}"; do
            if [ "${validation_results[$validation_name]}" = "FAIL" ]; then
                echo "  - $validation_name"
            fi
        done
    fi
    
    echo ""
    echo "Detailed results: $RESULTS_FILE"
    echo "Validation log: $VALIDATION_LOG"
    echo "=========================================="
    
    if [ $failed_count -eq 0 ]; then
        success "Production validation completed successfully!"
        return 0
    else
        error "$failed_count validation(s) failed"
        return 1
    fi
}

# Main execution
main() {
    info "Starting production deployment validation..."
    
    # Create log directory
    mkdir -p "$LOG_DIR"
    
    # Run validation suites
    check_production_prerequisites
    validate_container_security
    validate_performance_requirements
    validate_reliability
    validate_scalability
    validate_monitoring
    validate_documentation
    
    # Generate report
    generate_validation_report
}

# Execute main function
main "$@"
