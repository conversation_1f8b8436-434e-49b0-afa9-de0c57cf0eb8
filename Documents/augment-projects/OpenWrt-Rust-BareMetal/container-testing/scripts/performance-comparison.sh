#!/bin/bash

# Performance Comparison: Container vs VM Testing
# Validates that container testing meets the <50% execution time target

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="${SCRIPT_DIR}/../.."
CONTAINER_DIR="${SCRIPT_DIR}/.."
VM_DIR="${PROJECT_ROOT}/vm-testing"
LOG_DIR="${CONTAINER_DIR}/logs"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
COMPARISON_LOG="${LOG_DIR}/performance-comparison_${TIMESTAMP}.log"
RESULTS_FILE="${LOG_DIR}/performance-comparison_${TIMESTAMP}.json"

# Test configuration
PERFORMANCE_ITERATIONS=3
WARMUP_ITERATIONS=1
TIMEOUT_MINUTES=30

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "${COMPARISON_LOG}"
}

info() { log "INFO" "$@"; }
warn() { log "WARN" "$@"; }
error() { log "ERROR" "$@"; }
success() { log "SUCCESS" "$@"; }

# Performance metrics storage
declare -A container_metrics
declare -A vm_metrics
declare -A comparison_results

# Utility functions
get_timestamp_ms() {
    date +%s%3N
}

calculate_duration() {
    local start_time=$1
    local end_time=$2
    echo "scale=3; ($end_time - $start_time) / 1000" | bc
}

# Check prerequisites
check_prerequisites() {
    info "Checking performance comparison prerequisites..."
    
    # Check if required tools are available
    local required_tools=("podman" "jq" "bc" "timeout")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            error "Required tool not found: $tool"
            exit 1
        fi
    done
    
    # Check if VM testing is available
    if [ ! -d "$VM_DIR" ]; then
        warn "VM testing directory not found: $VM_DIR"
        warn "VM comparison will be skipped"
        VM_AVAILABLE=false
    else
        VM_AVAILABLE=true
    fi
    
    # Create log directory
    mkdir -p "$LOG_DIR"
    
    success "Prerequisites check completed"
}

# Container testing performance measurement
measure_container_performance() {
    info "Measuring container testing performance..."
    
    local total_setup_time=0
    local total_test_time=0
    local total_cleanup_time=0
    local successful_runs=0
    
    for ((i=1; i<=PERFORMANCE_ITERATIONS; i++)); do
        info "Container performance run $i/$PERFORMANCE_ITERATIONS..."
        
        # Setup phase
        local setup_start=$(get_timestamp_ms)
        
        if timeout "${TIMEOUT_MINUTES}m" "${SCRIPT_DIR}/4port-lan-setup.sh" setup >/dev/null 2>&1 && \
           timeout "${TIMEOUT_MINUTES}m" "${SCRIPT_DIR}/container-manager.sh" build >/dev/null 2>&1 && \
           timeout "${TIMEOUT_MINUTES}m" "${SCRIPT_DIR}/container-manager.sh" start >/dev/null 2>&1; then
            
            local setup_end=$(get_timestamp_ms)
            local setup_duration=$(calculate_duration "$setup_start" "$setup_end")
            
            # Wait for containers to be ready
            sleep 10
            
            # Test execution phase
            local test_start=$(get_timestamp_ms)
            
            if timeout "${TIMEOUT_MINUTES}m" "${CONTAINER_DIR}/tests/network/4port-lan-tests.sh" >/dev/null 2>&1; then
                local test_end=$(get_timestamp_ms)
                local test_duration=$(calculate_duration "$test_start" "$test_end")
                
                # Cleanup phase
                local cleanup_start=$(get_timestamp_ms)
                
                "${SCRIPT_DIR}/container-manager.sh" stop >/dev/null 2>&1 || true
                "${SCRIPT_DIR}/4port-lan-setup.sh" cleanup >/dev/null 2>&1 || true
                
                local cleanup_end=$(get_timestamp_ms)
                local cleanup_duration=$(calculate_duration "$cleanup_start" "$cleanup_end")
                
                # Record successful run
                total_setup_time=$(echo "$total_setup_time + $setup_duration" | bc)
                total_test_time=$(echo "$total_test_time + $test_duration" | bc)
                total_cleanup_time=$(echo "$total_cleanup_time + $cleanup_duration" | bc)
                successful_runs=$((successful_runs + 1))
                
                info "Run $i completed - Setup: ${setup_duration}s, Test: ${test_duration}s, Cleanup: ${cleanup_duration}s"
            else
                error "Test execution failed in run $i"
                # Cleanup on failure
                "${SCRIPT_DIR}/container-manager.sh" stop >/dev/null 2>&1 || true
                "${SCRIPT_DIR}/4port-lan-setup.sh" cleanup >/dev/null 2>&1 || true
            fi
        else
            error "Setup failed in run $i"
            # Cleanup on failure
            "${SCRIPT_DIR}/container-manager.sh" stop >/dev/null 2>&1 || true
            "${SCRIPT_DIR}/4port-lan-setup.sh" cleanup >/dev/null 2>&1 || true
        fi
    done
    
    if [ $successful_runs -gt 0 ]; then
        container_metrics["setup_time"]=$(echo "scale=3; $total_setup_time / $successful_runs" | bc)
        container_metrics["test_time"]=$(echo "scale=3; $total_test_time / $successful_runs" | bc)
        container_metrics["cleanup_time"]=$(echo "scale=3; $total_cleanup_time / $successful_runs" | bc)
        container_metrics["total_time"]=$(echo "scale=3; ${container_metrics[setup_time]} + ${container_metrics[test_time]} + ${container_metrics[cleanup_time]}" | bc)
        container_metrics["successful_runs"]=$successful_runs
        
        success "Container performance measurement completed"
        info "Average times - Setup: ${container_metrics[setup_time]}s, Test: ${container_metrics[test_time]}s, Cleanup: ${container_metrics[cleanup_time]}s"
        info "Total average time: ${container_metrics[total_time]}s"
    else
        error "No successful container test runs"
        return 1
    fi
}

# VM testing performance measurement (if available)
measure_vm_performance() {
    if [ "$VM_AVAILABLE" = false ]; then
        warn "VM testing not available, skipping VM performance measurement"
        return 0
    fi
    
    info "Measuring VM testing performance..."
    
    # Check if VM testing scripts exist
    local vm_test_script="${VM_DIR}/scripts/run-all-tests.sh"
    if [ ! -f "$vm_test_script" ]; then
        warn "VM test script not found: $vm_test_script"
        warn "VM performance measurement will be skipped"
        return 0
    fi
    
    local total_vm_time=0
    local successful_vm_runs=0
    
    for ((i=1; i<=PERFORMANCE_ITERATIONS; i++)); do
        info "VM performance run $i/$PERFORMANCE_ITERATIONS..."
        
        local vm_start=$(get_timestamp_ms)
        
        if timeout "$((TIMEOUT_MINUTES * 2))m" "$vm_test_script" --no-cleanup >/dev/null 2>&1; then
            local vm_end=$(get_timestamp_ms)
            local vm_duration=$(calculate_duration "$vm_start" "$vm_end")
            
            total_vm_time=$(echo "$total_vm_time + $vm_duration" | bc)
            successful_vm_runs=$((successful_vm_runs + 1))
            
            info "VM run $i completed in ${vm_duration}s"
            
            # Cleanup VM environment
            "${VM_DIR}/scripts/vm-manager.sh" cleanup >/dev/null 2>&1 || true
        else
            error "VM test execution failed in run $i"
            # Cleanup on failure
            "${VM_DIR}/scripts/vm-manager.sh" cleanup >/dev/null 2>&1 || true
        fi
    done
    
    if [ $successful_vm_runs -gt 0 ]; then
        vm_metrics["total_time"]=$(echo "scale=3; $total_vm_time / $successful_vm_runs" | bc)
        vm_metrics["successful_runs"]=$successful_vm_runs
        
        success "VM performance measurement completed"
        info "Average VM test time: ${vm_metrics[total_time]}s"
    else
        warn "No successful VM test runs"
    fi
}

# Calculate performance comparison
calculate_comparison() {
    info "Calculating performance comparison..."
    
    if [ -n "${container_metrics[total_time]:-}" ]; then
        comparison_results["container_total_time"]="${container_metrics[total_time]}"
        comparison_results["container_setup_time"]="${container_metrics[setup_time]}"
        comparison_results["container_test_time"]="${container_metrics[test_time]}"
        comparison_results["container_cleanup_time"]="${container_metrics[cleanup_time]}"
        
        if [ -n "${vm_metrics[total_time]:-}" ]; then
            comparison_results["vm_total_time"]="${vm_metrics[total_time]}"
            
            # Calculate speedup factor
            local speedup=$(echo "scale=3; ${vm_metrics[total_time]} / ${container_metrics[total_time]}" | bc)
            comparison_results["speedup_factor"]="$speedup"
            
            # Calculate time reduction percentage
            local time_reduction=$(echo "scale=1; (1 - ${container_metrics[total_time]} / ${vm_metrics[total_time]}) * 100" | bc)
            comparison_results["time_reduction_percent"]="$time_reduction"
            
            # Check if target is met (container should be <50% of VM time, i.e., >2x speedup)
            if [ "$(echo "$speedup >= 2.0" | bc)" -eq 1 ]; then
                comparison_results["target_met"]="true"
                success "Performance target MET: ${speedup}x speedup (${time_reduction}% faster)"
            else
                comparison_results["target_met"]="false"
                warn "Performance target NOT MET: ${speedup}x speedup (target: 2x)"
            fi
        else
            comparison_results["vm_total_time"]="N/A"
            comparison_results["speedup_factor"]="N/A"
            comparison_results["time_reduction_percent"]="N/A"
            comparison_results["target_met"]="unknown"
            warn "VM performance data not available for comparison"
        fi
    else
        error "Container performance data not available"
        return 1
    fi
}

# Generate performance report
generate_performance_report() {
    info "Generating performance comparison report..."
    
    # Create JSON report
    cat > "$RESULTS_FILE" << EOF
{
    "performance_comparison": {
        "timestamp": "$TIMESTAMP",
        "test_configuration": {
            "iterations": $PERFORMANCE_ITERATIONS,
            "timeout_minutes": $TIMEOUT_MINUTES,
            "warmup_iterations": $WARMUP_ITERATIONS
        }
    },
    "container_performance": {
        "total_time": "${comparison_results[container_total_time]:-N/A}",
        "setup_time": "${comparison_results[container_setup_time]:-N/A}",
        "test_time": "${comparison_results[container_test_time]:-N/A}",
        "cleanup_time": "${comparison_results[container_cleanup_time]:-N/A}",
        "successful_runs": "${container_metrics[successful_runs]:-0}"
    },
    "vm_performance": {
        "total_time": "${comparison_results[vm_total_time]:-N/A}",
        "successful_runs": "${vm_metrics[successful_runs]:-0}"
    },
    "comparison": {
        "speedup_factor": "${comparison_results[speedup_factor]:-N/A}",
        "time_reduction_percent": "${comparison_results[time_reduction_percent]:-N/A}",
        "target_met": "${comparison_results[target_met]:-unknown}",
        "target_description": "Container testing should be <50% of VM testing time (>2x speedup)"
    }
}
EOF

    # Print summary
    echo ""
    echo "=========================================="
    echo "Container vs VM Performance Comparison"
    echo "=========================================="
    echo "Container Testing:"
    echo "  Total Time: ${comparison_results[container_total_time]:-N/A}s"
    echo "  Setup Time: ${comparison_results[container_setup_time]:-N/A}s"
    echo "  Test Time: ${comparison_results[container_test_time]:-N/A}s"
    echo "  Cleanup Time: ${comparison_results[container_cleanup_time]:-N/A}s"
    echo ""
    echo "VM Testing:"
    echo "  Total Time: ${comparison_results[vm_total_time]:-N/A}s"
    echo ""
    echo "Performance Comparison:"
    echo "  Speedup Factor: ${comparison_results[speedup_factor]:-N/A}x"
    echo "  Time Reduction: ${comparison_results[time_reduction_percent]:-N/A}%"
    echo "  Target Met: ${comparison_results[target_met]:-unknown}"
    echo ""
    echo "Results file: $RESULTS_FILE"
    echo "Log file: $COMPARISON_LOG"
    echo "=========================================="
    
    if [ "${comparison_results[target_met]:-unknown}" = "true" ]; then
        success "Performance comparison completed successfully - Target achieved!"
        return 0
    elif [ "${comparison_results[target_met]:-unknown}" = "false" ]; then
        error "Performance target not met"
        return 1
    else
        warn "Performance comparison completed with limited data"
        return 0
    fi
}

# Main execution
main() {
    info "Starting container vs VM performance comparison..."
    
    # Check prerequisites
    check_prerequisites
    
    # Measure container performance
    measure_container_performance
    
    # Measure VM performance (if available)
    measure_vm_performance
    
    # Calculate comparison
    calculate_comparison
    
    # Generate report
    generate_performance_report
}

# Execute main function
main "$@"
