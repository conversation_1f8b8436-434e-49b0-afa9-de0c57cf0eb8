#!/bin/bash

# 4 Port LAN Setup Script for OpenWrt Rust Container Testing
# This script sets up Podman networks for 4 port LAN simulation

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_DIR="${SCRIPT_DIR}/../configs"
NETWORK_CONFIG="${CONFIG_DIR}/networks/4port-lan.json"
LOG_DIR="${SCRIPT_DIR}/../logs"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="${LOG_DIR}/4port-lan-setup_${TIMESTAMP}.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "${LOG_FILE}"
}

info() { log "INFO" "$@"; }
warn() { log "WARN" "${YELLOW}$*${NC}"; }
error() { log "ERROR" "${RED}$*${NC}"; }
success() { log "SUCCESS" "${GREEN}$*${NC}"; }

# Check prerequisites
check_prerequisites() {
    info "Checking prerequisites..."
    
    # Check if Podman is installed
    if ! command -v podman &> /dev/null; then
        error "Podman is not installed. Please install Podman first."
        exit 1
    fi
    
    # Check if jq is installed
    if ! command -v jq &> /dev/null; then
        error "jq is not installed. Please install jq for JSON processing."
        exit 1
    fi
    
    # Check if running as root (required for some network operations)
    if [[ $EUID -ne 0 ]] && [[ "$1" != "--rootless" ]]; then
        warn "Running as non-root user. Some network operations may require sudo."
        warn "Use --rootless flag for rootless container setup."
    fi
    
    # Create log directory
    mkdir -p "${LOG_DIR}"
    
    success "Prerequisites check completed"
}

# Create Podman networks
create_networks() {
    info "Creating Podman networks for 4 port LAN..."
    
    # Read network configuration
    if [[ ! -f "${NETWORK_CONFIG}" ]]; then
        error "Network configuration file not found: ${NETWORK_CONFIG}"
        exit 1
    fi
    
    # Extract network definitions
    local networks=$(jq -r '.networks | keys[]' "${NETWORK_CONFIG}")
    
    for network in $networks; do
        local network_name=$(jq -r ".networks.${network}.name" "${NETWORK_CONFIG}")
        local subnet=$(jq -r ".networks.${network}.subnet" "${NETWORK_CONFIG}")
        local gateway=$(jq -r ".networks.${network}.gateway" "${NETWORK_CONFIG}")
        
        info "Creating network: ${network_name} (${subnet})"
        
        # Check if network already exists
        if podman network exists "${network_name}" 2>/dev/null; then
            warn "Network ${network_name} already exists, skipping..."
            continue
        fi
        
        # Create the network
        if podman network create \
            --subnet="${subnet}" \
            --gateway="${gateway}" \
            --driver=bridge \
            "${network_name}"; then
            success "Created network: ${network_name}"
        else
            error "Failed to create network: ${network_name}"
            exit 1
        fi
    done
}

# Configure VLAN support
configure_vlans() {
    info "Configuring VLAN support..."
    
    # Check if VLAN utilities are available
    if ! command -v vconfig &> /dev/null && ! command -v ip &> /dev/null; then
        warn "VLAN utilities not found. VLAN configuration may be limited."
        return 0
    fi
    
    # Extract VLAN definitions
    local vlans=$(jq -r '.vlans | keys[]' "${NETWORK_CONFIG}")
    
    for vlan in $vlans; do
        local vlan_id=$(jq -r ".vlans.${vlan}.id" "${NETWORK_CONFIG}")
        local vlan_name=$(jq -r ".vlans.${vlan}.name" "${NETWORK_CONFIG}")
        
        info "Configuring VLAN: ${vlan_name} (ID: ${vlan_id})"
        
        # VLAN configuration will be handled by container network setup
        # This is a placeholder for future VLAN-specific configuration
        success "VLAN ${vlan_name} configuration prepared"
    done
}

# Set up firewall rules
setup_firewall() {
    info "Setting up firewall rules..."
    
    # Check if iptables is available
    if ! command -v iptables &> /dev/null; then
        warn "iptables not found. Firewall rules will be handled by Podman."
        return 0
    fi
    
    # Extract firewall rules
    local rules=$(jq -r '.firewall_rules | keys[]' "${NETWORK_CONFIG}")
    
    for rule in $rules; do
        local action=$(jq -r ".firewall_rules.${rule}.action" "${NETWORK_CONFIG}")
        local description=$(jq -r ".firewall_rules.${rule}.description" "${NETWORK_CONFIG}")
        
        info "Firewall rule: ${rule} - ${description} (${action})"
        # Firewall rules will be implemented in container runtime
    done
    
    success "Firewall rules configuration prepared"
}

# Validate network setup
validate_setup() {
    info "Validating network setup..."
    
    # List created networks
    info "Created Podman networks:"
    podman network ls --format "table {{.Name}} {{.Driver}} {{.Subnet}}"
    
    # Validate each network
    local networks=$(jq -r '.networks | keys[]' "${NETWORK_CONFIG}")
    local validation_failed=false
    
    for network in $networks; do
        local network_name=$(jq -r ".networks.${network}.name" "${NETWORK_CONFIG}")
        
        if podman network exists "${network_name}"; then
            success "Network ${network_name} validated"
        else
            error "Network ${network_name} validation failed"
            validation_failed=true
        fi
    done
    
    if [[ "${validation_failed}" == "true" ]]; then
        error "Network validation failed"
        exit 1
    fi
    
    success "Network setup validation completed"
}

# Clean up networks
cleanup_networks() {
    info "Cleaning up Podman networks..."
    
    local networks=$(jq -r '.networks | keys[]' "${NETWORK_CONFIG}")
    
    for network in $networks; do
        local network_name=$(jq -r ".networks.${network}.name" "${NETWORK_CONFIG}")
        
        if podman network exists "${network_name}"; then
            info "Removing network: ${network_name}"
            if podman network rm "${network_name}"; then
                success "Removed network: ${network_name}"
            else
                error "Failed to remove network: ${network_name}"
            fi
        else
            info "Network ${network_name} does not exist, skipping..."
        fi
    done
}

# Display help
show_help() {
    cat << EOF
4 Port LAN Setup Script for OpenWrt Rust Container Testing

Usage: $0 [COMMAND] [OPTIONS]

Commands:
    setup       Set up 4 port LAN networks (default)
    cleanup     Remove all created networks
    validate    Validate network configuration
    help        Show this help message

Options:
    --rootless  Run in rootless mode
    --verbose   Enable verbose output
    --dry-run   Show what would be done without executing

Examples:
    $0 setup                    # Set up networks
    $0 setup --rootless         # Set up networks in rootless mode
    $0 cleanup                  # Remove all networks
    $0 validate                 # Validate current setup

EOF
}

# Main function
main() {
    local command="${1:-setup}"
    local rootless_mode=false
    local verbose_mode=false
    local dry_run=false
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            setup|cleanup|validate|help)
                command="$1"
                shift
                ;;
            --rootless)
                rootless_mode=true
                shift
                ;;
            --verbose)
                verbose_mode=true
                set -x
                shift
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            *)
                error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # Execute command
    case "${command}" in
        setup)
            info "Starting 4 port LAN network setup..."
            check_prerequisites "${rootless_mode:+--rootless}"
            create_networks
            configure_vlans
            setup_firewall
            validate_setup
            success "4 port LAN network setup completed successfully!"
            ;;
        cleanup)
            info "Starting network cleanup..."
            check_prerequisites "${rootless_mode:+--rootless}"
            cleanup_networks
            success "Network cleanup completed!"
            ;;
        validate)
            info "Starting network validation..."
            check_prerequisites "${rootless_mode:+--rootless}"
            validate_setup
            ;;
        help)
            show_help
            ;;
        *)
            error "Unknown command: ${command}"
            show_help
            exit 1
            ;;
    esac
}

# Execute main function with all arguments
main "$@"
