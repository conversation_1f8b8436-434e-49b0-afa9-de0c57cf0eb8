#!/bin/bash

# Container Utilities for OpenWrt Rust Testing
# Common utility functions for container operations

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_DIR="/var/log/openwrt-rust"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}"
}

info() { log "INFO" "$@"; }
warn() { log "WARN" "$@"; }
error() { log "ERROR" "$@"; }
success() { log "SUCCESS" "$@"; }
debug() { log "DEBUG" "$@"; }

# Colored output functions
print_info() { echo -e "${BLUE}[INFO]${NC} $*"; }
print_warn() { echo -e "${YELLOW}[WARN]${NC} $*"; }
print_error() { echo -e "${RED}[ERROR]${NC} $*"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $*"; }
print_debug() { echo -e "${CYAN}[DEBUG]${NC} $*"; }

# Check if running in container
is_container() {
    [ -f /.dockerenv ] || [ -f /run/.containerenv ] || grep -q 'container' /proc/1/cgroup 2>/dev/null
}

# Get container runtime (docker/podman)
get_container_runtime() {
    if command -v podman &> /dev/null; then
        echo "podman"
    elif command -v docker &> /dev/null; then
        echo "docker"
    else
        echo "unknown"
    fi
}

# Wait for network interface to be available
wait_for_interface() {
    local interface=$1
    local timeout=${2:-30}
    local count=0
    
    info "Waiting for network interface: $interface"
    
    while [ $count -lt $timeout ]; do
        if ip link show "$interface" >/dev/null 2>&1; then
            success "Interface $interface is available"
            return 0
        fi
        sleep 1
        count=$((count + 1))
    done
    
    error "Interface $interface not available after ${timeout} seconds"
    return 1
}

# Wait for network connectivity
wait_for_connectivity() {
    local target=${1:-*******}
    local timeout=${2:-30}
    local count=0
    
    info "Waiting for network connectivity to $target"
    
    while [ $count -lt $timeout ]; do
        if ping -c 1 -W 1 "$target" >/dev/null 2>&1; then
            success "Network connectivity to $target established"
            return 0
        fi
        sleep 1
        count=$((count + 1))
    done
    
    error "Network connectivity to $target not available after ${timeout} seconds"
    return 1
}

# Get container IP address
get_container_ip() {
    local interface=${1:-eth0}
    
    if ip addr show "$interface" >/dev/null 2>&1; then
        ip addr show "$interface" | grep 'inet ' | awk '{print $2}' | cut -d'/' -f1 | head -n1
    else
        echo ""
    fi
}

# Get container network information
get_network_info() {
    info "Container Network Information:"
    echo "================================"
    
    # Interfaces
    echo "Network Interfaces:"
    ip addr show | grep -E '^[0-9]+:|inet ' | sed 's/^/  /'
    
    echo ""
    echo "Routing Table:"
    ip route show | sed 's/^/  /'
    
    echo ""
    echo "DNS Configuration:"
    if [ -f /etc/resolv.conf ]; then
        cat /etc/resolv.conf | sed 's/^/  /'
    fi
    
    echo ""
    echo "Network Statistics:"
    ss -tuln | head -10 | sed 's/^/  /'
}

# Test network connectivity
test_connectivity() {
    local targets=("*******" "*******" "google.com")
    local success_count=0
    
    info "Testing network connectivity..."
    
    for target in "${targets[@]}"; do
        if ping -c 1 -W 2 "$target" >/dev/null 2>&1; then
            success "Connectivity to $target: OK"
            success_count=$((success_count + 1))
        else
            error "Connectivity to $target: FAILED"
        fi
    done
    
    if [ $success_count -gt 0 ]; then
        success "Network connectivity test: $success_count/${#targets[@]} targets reachable"
        return 0
    else
        error "Network connectivity test: No targets reachable"
        return 1
    fi
}

# Monitor container resources
monitor_resources() {
    local duration=${1:-60}
    local interval=${2:-5}
    local iterations=$((duration / interval))
    
    info "Monitoring container resources for ${duration} seconds..."
    
    for ((i=1; i<=iterations; i++)); do
        echo "=== Resource Monitor - Iteration $i/$iterations ==="
        
        # CPU and Memory
        if command -v ps &> /dev/null; then
            echo "CPU and Memory Usage:"
            ps aux --sort=-%cpu | head -5 | sed 's/^/  /'
        fi
        
        # Memory info
        if [ -f /proc/meminfo ]; then
            echo "Memory Information:"
            grep -E '^(MemTotal|MemFree|MemAvailable|Buffers|Cached):' /proc/meminfo | sed 's/^/  /'
        fi
        
        # Network statistics
        echo "Network Statistics:"
        if [ -f /proc/net/dev ]; then
            awk 'NR>2 {print "  " $1 " RX: " $2 " bytes, TX: " $10 " bytes"}' /proc/net/dev
        fi
        
        echo ""
        
        if [ $i -lt $iterations ]; then
            sleep $interval
        fi
    done
}

# Generate container health report
health_report() {
    local report_file="${LOG_DIR}/health-report_${TIMESTAMP}.txt"
    mkdir -p "$(dirname "$report_file")"
    
    info "Generating container health report: $report_file"
    
    {
        echo "OpenWrt Rust Container Health Report"
        echo "Generated: $(date)"
        echo "========================================"
        echo ""
        
        echo "Container Information:"
        echo "  Runtime: $(get_container_runtime)"
        echo "  In Container: $(is_container && echo "Yes" || echo "No")"
        echo ""
        
        echo "System Information:"
        uname -a
        echo ""
        
        echo "Network Configuration:"
        get_network_info
        echo ""
        
        echo "Process Information:"
        ps aux | head -10
        echo ""
        
        echo "Disk Usage:"
        df -h
        echo ""
        
        echo "Memory Usage:"
        free -h
        echo ""
        
        echo "OpenWrt Rust Processes:"
        pgrep -f openwrt-rust | while read pid; do
            echo "  PID $pid: $(ps -p $pid -o comm= 2>/dev/null || echo 'Process not found')"
        done
        echo ""
        
        echo "Log Files:"
        find /var/log/openwrt-rust -name "*.log" -type f 2>/dev/null | while read logfile; do
            echo "  $logfile ($(wc -l < "$logfile" 2>/dev/null || echo 0) lines)"
        done
        
    } > "$report_file"
    
    success "Health report generated: $report_file"
    return 0
}

# Cleanup container resources
cleanup_container() {
    info "Cleaning up container resources..."
    
    # Stop OpenWrt Rust processes
    if pgrep -f openwrt-rust >/dev/null; then
        info "Stopping OpenWrt Rust processes..."
        pkill -TERM -f openwrt-rust || true
        sleep 5
        pkill -KILL -f openwrt-rust || true
    fi
    
    # Clean up network interfaces
    info "Cleaning up network interfaces..."
    
    # Remove VLAN interfaces
    for iface in $(ip link show | grep -o 'eth[0-9]*\.[0-9]*' || true); do
        info "Removing VLAN interface: $iface"
        ip link delete "$iface" 2>/dev/null || true
    done
    
    # Remove bridge interfaces
    for bridge in $(brctl show 2>/dev/null | awk 'NR>1 {print $1}' | grep -v '^$' || true); do
        if [[ "$bridge" =~ ^openwrt- ]]; then
            info "Removing bridge interface: $bridge"
            ip link set "$bridge" down 2>/dev/null || true
            brctl delbr "$bridge" 2>/dev/null || true
        fi
    done
    
    # Clean up temporary files
    info "Cleaning up temporary files..."
    rm -rf /tmp/openwrt-rust-* 2>/dev/null || true
    rm -rf /var/run/openwrt-rust/* 2>/dev/null || true
    
    success "Container cleanup completed"
}

# Show usage information
show_usage() {
    cat << 'EOF'
Container Utilities for OpenWrt Rust Testing

Usage: container-utils.sh [COMMAND] [OPTIONS]

Commands:
    wait-interface <interface> [timeout]  Wait for network interface
    wait-connectivity [target] [timeout]  Wait for network connectivity
    get-ip [interface]                    Get container IP address
    network-info                          Show network information
    test-connectivity                     Test network connectivity
    monitor [duration] [interval]         Monitor container resources
    health-report                         Generate health report
    cleanup                              Clean up container resources
    help                                 Show this help

Examples:
    container-utils.sh wait-interface eth0 30
    container-utils.sh get-ip eth0
    container-utils.sh network-info
    container-utils.sh test-connectivity
    container-utils.sh monitor 120 10
    container-utils.sh health-report
    container-utils.sh cleanup

EOF
}

# Main function
main() {
    local command=${1:-help}
    
    case "$command" in
        wait-interface)
            wait_for_interface "${2:-eth0}" "${3:-30}"
            ;;
        wait-connectivity)
            wait_for_connectivity "${2:-*******}" "${3:-30}"
            ;;
        get-ip)
            get_container_ip "${2:-eth0}"
            ;;
        network-info)
            get_network_info
            ;;
        test-connectivity)
            test_connectivity
            ;;
        monitor)
            monitor_resources "${2:-60}" "${3:-5}"
            ;;
        health-report)
            health_report
            ;;
        cleanup)
            cleanup_container
            ;;
        help|--help)
            show_usage
            ;;
        *)
            error "Unknown command: $command"
            show_usage
            exit 1
            ;;
    esac
}

# Execute main function if script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
