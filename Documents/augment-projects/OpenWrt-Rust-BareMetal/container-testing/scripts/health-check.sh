#!/bin/bash

# Health Check Script for OpenWrt Rust Containers
# Comprehensive health monitoring for container testing

set -euo pipefail

# Configuration
HEALTH_CHECK_TIMEOUT=${HEALTH_CHECK_TIMEOUT:-10}
HEALTH_CHECK_LOG="/var/log/openwrt-rust/health-check.log"
OPENWRT_RUST_HOME=${OPENWRT_RUST_HOME:-/opt/openwrt-rust}

# Logging
log_health() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "${timestamp} [${level}] ${message}" >> "${HEALTH_CHECK_LOG}" 2>/dev/null || true
}

# Health check functions
check_basic_system() {
    # Check if basic system commands are available
    command -v ip >/dev/null 2>&1 || return 1
    command -v ping >/dev/null 2>&1 || return 1
    command -v ps >/dev/null 2>&1 || return 1
    
    # Check if we can access basic system information
    [ -r /proc/version ] || return 1
    [ -r /proc/meminfo ] || return 1
    
    return 0
}

check_network_interfaces() {
    # Check if at least one network interface is up (excluding loopback)
    local interfaces=$(ip link show up | grep -v 'lo:' | grep -c 'state UP' || echo 0)
    
    if [ "$interfaces" -eq 0 ]; then
        log_health "ERROR" "No network interfaces are up"
        return 1
    fi
    
    log_health "INFO" "Found $interfaces active network interfaces"
    return 0
}

check_network_connectivity() {
    # Check basic network connectivity (internal)
    # Try to ping localhost
    if ! ping -c 1 -W 1 127.0.0.1 >/dev/null 2>&1; then
        log_health "ERROR" "Cannot ping localhost"
        return 1
    fi
    
    # Check if we can resolve DNS (if external connectivity is available)
    if ping -c 1 -W 2 ******* >/dev/null 2>&1; then
        log_health "INFO" "External connectivity available"
    else
        log_health "WARN" "No external connectivity (expected in isolated testing)"
    fi
    
    return 0
}

check_openwrt_rust_process() {
    # Check if OpenWrt Rust process is running (if expected)
    if [ -f /var/run/openwrt-rust/openwrt-rust.pid ]; then
        local pid=$(cat /var/run/openwrt-rust/openwrt-rust.pid)
        
        if ! kill -0 "$pid" 2>/dev/null; then
            log_health "ERROR" "OpenWrt Rust process not running (PID: $pid)"
            return 1
        fi
        
        log_health "INFO" "OpenWrt Rust process running (PID: $pid)"
    else
        # Process might not be started yet, which is okay
        log_health "INFO" "OpenWrt Rust process not started yet"
    fi
    
    return 0
}

check_filesystem_access() {
    # Check if we can read/write to required directories
    local dirs=(
        "/var/log/openwrt-rust"
        "/var/lib/openwrt-rust"
        "/var/run/openwrt-rust"
        "/etc/openwrt-rust"
    )
    
    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            log_health "WARN" "Directory does not exist: $dir"
            continue
        fi
        
        if [ ! -r "$dir" ]; then
            log_health "ERROR" "Cannot read directory: $dir"
            return 1
        fi
        
        # Test write access with a temporary file
        local test_file="${dir}/.health-check-test"
        if ! touch "$test_file" 2>/dev/null; then
            log_health "ERROR" "Cannot write to directory: $dir"
            return 1
        fi
        rm -f "$test_file" 2>/dev/null || true
    done
    
    log_health "INFO" "Filesystem access checks passed"
    return 0
}

check_memory_usage() {
    # Check if memory usage is reasonable
    if [ -r /proc/meminfo ]; then
        local mem_total=$(grep MemTotal /proc/meminfo | awk '{print $2}')
        local mem_available=$(grep MemAvailable /proc/meminfo | awk '{print $2}')
        
        if [ "$mem_total" -gt 0 ] && [ "$mem_available" -gt 0 ]; then
            local mem_usage_percent=$(( (mem_total - mem_available) * 100 / mem_total ))
            
            if [ "$mem_usage_percent" -gt 90 ]; then
                log_health "WARN" "High memory usage: ${mem_usage_percent}%"
            else
                log_health "INFO" "Memory usage: ${mem_usage_percent}%"
            fi
        fi
    fi
    
    return 0
}

check_disk_space() {
    # Check if disk space is sufficient
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [ "$disk_usage" -gt 90 ]; then
        log_health "ERROR" "Disk usage too high: ${disk_usage}%"
        return 1
    elif [ "$disk_usage" -gt 80 ]; then
        log_health "WARN" "Disk usage high: ${disk_usage}%"
    else
        log_health "INFO" "Disk usage: ${disk_usage}%"
    fi
    
    return 0
}

check_container_environment() {
    # Check container-specific environment
    if [ -f /.dockerenv ] || [ -f /run/.containerenv ]; then
        log_health "INFO" "Running in container environment"
    else
        log_health "WARN" "Not running in recognized container environment"
    fi
    
    # Check if required environment variables are set
    local required_vars=("OPENWRT_RUST_HOME")
    for var in "${required_vars[@]}"; do
        if [ -z "${!var:-}" ]; then
            log_health "WARN" "Environment variable not set: $var"
        else
            log_health "INFO" "Environment variable set: $var=${!var}"
        fi
    done
    
    return 0
}

# Main health check function
perform_health_check() {
    local exit_code=0
    local checks_passed=0
    local checks_total=0
    
    # Initialize log
    mkdir -p "$(dirname "$HEALTH_CHECK_LOG")" 2>/dev/null || true
    log_health "INFO" "Starting health check"
    
    # Define health checks
    local checks=(
        "check_basic_system:Basic system functionality"
        "check_network_interfaces:Network interfaces"
        "check_network_connectivity:Network connectivity"
        "check_openwrt_rust_process:OpenWrt Rust process"
        "check_filesystem_access:Filesystem access"
        "check_memory_usage:Memory usage"
        "check_disk_space:Disk space"
        "check_container_environment:Container environment"
    )
    
    # Run health checks
    for check_def in "${checks[@]}"; do
        local check_func=$(echo "$check_def" | cut -d: -f1)
        local check_desc=$(echo "$check_def" | cut -d: -f2)
        
        checks_total=$((checks_total + 1))
        
        log_health "INFO" "Running check: $check_desc"
        
        if timeout "$HEALTH_CHECK_TIMEOUT" "$check_func"; then
            log_health "INFO" "Check passed: $check_desc"
            checks_passed=$((checks_passed + 1))
        else
            log_health "ERROR" "Check failed: $check_desc"
            exit_code=1
        fi
    done
    
    # Summary
    log_health "INFO" "Health check completed: $checks_passed/$checks_total checks passed"
    
    if [ $exit_code -eq 0 ]; then
        log_health "INFO" "Overall health status: HEALTHY"
    else
        log_health "ERROR" "Overall health status: UNHEALTHY"
    fi
    
    return $exit_code
}

# Quick health check (for frequent monitoring)
quick_health_check() {
    # Basic checks only
    check_basic_system && \
    check_network_interfaces && \
    check_filesystem_access
}

# Show health status
show_health_status() {
    echo "OpenWrt Rust Container Health Status"
    echo "===================================="
    
    if perform_health_check; then
        echo "Status: HEALTHY ✓"
        exit 0
    else
        echo "Status: UNHEALTHY ✗"
        echo ""
        echo "Recent health check log:"
        if [ -f "$HEALTH_CHECK_LOG" ]; then
            tail -10 "$HEALTH_CHECK_LOG" | sed 's/^/  /'
        fi
        exit 1
    fi
}

# Main execution
main() {
    local mode=${1:-full}
    
    case "$mode" in
        "full")
            perform_health_check
            ;;
        "quick")
            quick_health_check
            ;;
        "status")
            show_health_status
            ;;
        "help"|"--help")
            cat << 'EOF'
Health Check Script for OpenWrt Rust Containers

Usage: health-check.sh [MODE]

Modes:
    full     Perform full health check (default)
    quick    Perform quick health check
    status   Show health status with details
    help     Show this help

Environment Variables:
    HEALTH_CHECK_TIMEOUT    Timeout for individual checks (default: 10)
    OPENWRT_RUST_HOME      OpenWrt Rust installation directory

Exit Codes:
    0    All health checks passed
    1    One or more health checks failed

EOF
            exit 0
            ;;
        *)
            echo "Unknown mode: $mode" >&2
            exit 1
            ;;
    esac
}

# Execute main function
main "$@"
