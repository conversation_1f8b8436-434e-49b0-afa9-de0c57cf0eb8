# OpenWrt Rust Container Testing Setup Guide

This guide provides detailed instructions for setting up the OpenWrt Rust container testing environment with 4 port LAN simulation.

## Prerequisites

### System Requirements

- **Operating System**: Linux (Ubuntu 20.04+ recommended) or macOS 11+
- **Memory**: 4GB minimum, 8GB recommended
- **Storage**: 10GB free space for container images and test data
- **Network**: Internet connection for downloading dependencies

### Required Software

#### Podman Installation

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install -y podman
```

**macOS:**
```bash
brew install podman
```

**Fedora/RHEL:**
```bash
sudo dnf install -y podman
```

#### Additional Dependencies

**Ubuntu/Debian:**
```bash
sudo apt install -y \
    jq \
    bc \
    bridge-utils \
    vlan \
    iperf3 \
    netcat-openbsd \
    curl \
    wget
```

**macOS:**
```bash
brew install jq bc iperf3 netcat curl wget
```

### Podman Configuration

1. **Initialize <PERSON>dman (if needed):**
   ```bash
   podman machine init
   podman machine start
   ```

2. **Verify <PERSON><PERSON> installation:**
   ```bash
   podman --version
   podman info
   ```

3. **Test basic functionality:**
   ```bash
   podman run --rm hello-world
   ```

## Initial Setup

### 1. Clone Repository

```bash
git clone <repository-url>
cd OpenWrt-Rust-BareMetal/container-testing
```

### 2. Set Permissions

```bash
chmod +x scripts/*.sh
chmod +x tests/*/*.sh
```

### 3. Verify Configuration Files

Check that all configuration files are present:

```bash
ls -la configs/podman/
ls -la configs/networks/
ls -la configs/images/
```

Expected files:
- `configs/podman/containers.conf`
- `configs/podman/registries.conf`
- `configs/podman/storage.conf`
- `configs/networks/4port-lan.json`
- `configs/images/*/Dockerfile`

## Network Setup

### 1. Configure 4 Port LAN Networks

```bash
./scripts/4port-lan-setup.sh setup
```

This command creates the following networks:
- `openwrt-wan` (*************/24) - WAN interface
- `openwrt-lan1` (***********/24) - LAN port 2 (VLAN 10)
- `openwrt-lan2` (***********/24) - LAN port 3 (VLAN 20)
- `openwrt-lan3` (***********/24) - LAN port 4 (VLAN 30)

### 2. Verify Network Configuration

```bash
./scripts/4port-lan-setup.sh validate
```

### 3. View Network Status

```bash
podman network ls
podman network inspect openwrt-wan
```

## Container Image Building

### 1. Build Base Images

```bash
./scripts/container-manager.sh build
```

This builds:
- `openwrt-rust-base:latest` - Base container with Rust toolchain
- `openwrt-rust-testing:latest` - Testing container with OpenWrt Rust implementation
- `network-tools:latest` - Network testing and diagnostic tools

### 2. Verify Images

```bash
podman images | grep openwrt-rust
```

### 3. Test Image Functionality

```bash
podman run --rm openwrt-rust-testing:latest --help
```

## Container Deployment

### 1. Start All Containers

```bash
./scripts/container-manager.sh start
```

This starts:
- Router container with 4 network interfaces
- Client containers on different LAN segments
- Network isolation and VLAN configuration

### 2. Check Container Status

```bash
./scripts/container-manager.sh status
```

### 3. View Container Logs

```bash
./scripts/container-manager.sh logs openwrt-rust-router
```

## Testing Setup

### 1. Run Basic Tests

```bash
./tests/network/4port-lan-tests.sh
```

### 2. Run Performance Tests

```bash
./tests/performance/network-performance-tests.sh
```

### 3. Run Integration Tests

```bash
./tests/integration/container-integration-tests.sh
```

### 4. Run All Tests

```bash
./scripts/run-all-tests.sh
```

## Configuration Customization

### Network Configuration

Edit `configs/networks/4port-lan.json` to customize:
- IP address ranges
- VLAN IDs
- Gateway addresses
- Network options

Example modification:
```json
{
  "networks": {
    "lan1": {
      "subnet": "************/24",
      "gateway": "************"
    }
  }
}
```

### Container Configuration

Edit `configs/podman/containers.conf` to customize:
- Resource limits
- Security settings
- Network options
- Storage configuration

### Image Customization

Modify Dockerfiles in `configs/images/` to:
- Add additional software packages
- Change base images
- Customize build processes
- Add application-specific configurations

## Troubleshooting Setup Issues

### Common Problems

#### 1. Podman Permission Issues

**Problem**: Permission denied errors when running Podman commands.

**Solution**:
```bash
# Add user to podman group (Linux)
sudo usermod -aG podman $USER
newgrp podman

# Or run in rootless mode
podman --remote info
```

#### 2. Network Creation Failures

**Problem**: Cannot create Podman networks.

**Solution**:
```bash
# Check CNI plugins
podman network ls
ls /usr/libexec/cni/

# Reset network configuration
podman system reset --force
./scripts/4port-lan-setup.sh setup
```

#### 3. Container Build Failures

**Problem**: Container images fail to build.

**Solution**:
```bash
# Check available space
df -h

# Clean up old images
podman image prune -a

# Check network connectivity
curl -I https://registry-1.docker.io/

# Rebuild with verbose output
podman build --no-cache -t test-image .
```

#### 4. Container Startup Issues

**Problem**: Containers fail to start or exit immediately.

**Solution**:
```bash
# Check container logs
podman logs container-name

# Run container interactively
podman run -it --rm openwrt-rust-testing:latest /bin/bash

# Check resource limits
podman stats
```

### Diagnostic Commands

```bash
# System information
podman info
podman version

# Network diagnostics
podman network ls
podman network inspect network-name

# Container diagnostics
podman ps -a
podman inspect container-name

# Image diagnostics
podman images
podman history image-name

# Resource usage
podman stats
podman system df
```

### Log Locations

- Container logs: `podman logs <container-name>`
- Test logs: `logs/`
- System logs: `/var/log/containers/` (Linux)
- Podman logs: `~/.local/share/containers/storage/` (rootless)

## Performance Optimization

### System Tuning

```bash
# Increase file descriptor limits
echo "* soft nofile 65536" | sudo tee -a /etc/security/limits.conf
echo "* hard nofile 65536" | sudo tee -a /etc/security/limits.conf

# Optimize network settings
echo 'net.core.rmem_max = 16777216' | sudo tee -a /etc/sysctl.conf
echo 'net.core.wmem_max = 16777216' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

### Container Optimization

```bash
# Use faster storage driver
podman info | grep -A5 "Storage Driver"

# Optimize image layers
podman build --squash -t optimized-image .

# Use resource limits
podman run --memory=2g --cpus=2 container-name
```

## Security Considerations

### Network Security

- Containers run in isolated network namespaces
- VLAN isolation prevents inter-segment communication
- Firewall rules control traffic flow
- No privileged containers by default

### Container Security

- Non-root user execution
- Read-only root filesystem where possible
- Minimal base images
- Regular security updates

### Access Control

```bash
# Run containers as non-root user
podman run --user 1000:1000 container-name

# Use security contexts
podman run --security-opt label=type:container_runtime_t container-name

# Limit capabilities
podman run --cap-drop=ALL --cap-add=NET_BIND_SERVICE container-name
```

## Next Steps

After successful setup:

1. **Explore Testing**: Run different test suites to validate functionality
2. **Customize Configuration**: Adapt settings for your specific use case
3. **Develop Tests**: Create additional test scenarios
4. **Monitor Performance**: Use built-in monitoring tools
5. **Scale Deployment**: Add more containers or networks as needed

## Support

For additional help:
- Check the troubleshooting section
- Review container and test logs
- Consult the main project documentation
- Compare with VM testing setup for reference

---

**Last Updated**: 2025-01-27  
**Version**: 1.0  
**Compatibility**: Podman 4.0+, Linux/macOS
