# OpenWrt Rust Container Testing Troubleshooting Guide

This guide provides comprehensive troubleshooting information for common issues encountered in the OpenWrt Rust container testing environment.

## Quick Diagnostic Commands

### System Health Check

```bash
# Run comprehensive health check
./scripts/container-utils.sh health-report

# Check container status
./scripts/container-manager.sh status

# Validate network configuration
./scripts/4port-lan-setup.sh validate

# Test basic connectivity
./scripts/container-utils.sh test-connectivity
```

### Log Collection

```bash
# View container logs
./scripts/container-manager.sh logs openwrt-rust-router

# Check system logs
journalctl -u podman --since "1 hour ago"

# Application logs
tail -f logs/*.log

# Generate diagnostic report
./scripts/generate-diagnostic-report.sh
```

## Common Issues and Solutions

### Container Issues

#### Issue: Container fails to start

**Symptoms:**
- Container exits immediately
- "Container not found" errors
- Startup timeouts

**Diagnosis:**
```bash
# Check container logs
./scripts/container-manager.sh logs openwrt-rust-router

# Inspect container configuration
podman inspect openwrt-rust-router

# Check image availability
podman images | grep openwrt-rust
```

**Solutions:**

1. **Rebuild container images:**
   ```bash
   ./scripts/container-manager.sh build --no-cache
   ```

2. **Check resource limits:**
   ```bash
   # Increase memory/CPU limits
   ./scripts/container-manager.sh start --memory 4g --cpus 4
   ```

3. **Verify network configuration:**
   ```bash
   ./scripts/4port-lan-setup.sh cleanup
   ./scripts/4port-lan-setup.sh setup
   ```

4. **Check permissions:**
   ```bash
   # Fix script permissions
   chmod +x scripts/*.sh
   
   # Check SELinux/AppArmor
   sudo setsebool -P container_manage_cgroup on
   ```

#### Issue: Container networking problems

**Symptoms:**
- Cannot reach containers
- Network isolation not working
- DNS resolution failures

**Diagnosis:**
```bash
# Check network status
podman network ls
podman network inspect openwrt-wan

# Test container connectivity
./scripts/container-utils.sh get-ip eth0
./scripts/container-utils.sh test-connectivity

# Check firewall rules
sudo iptables -L
```

**Solutions:**

1. **Reset network configuration:**
   ```bash
   ./scripts/4port-lan-setup.sh cleanup
   ./scripts/4port-lan-setup.sh setup
   ```

2. **Check CNI configuration:**
   ```bash
   # Verify CNI plugins
   ls /usr/libexec/cni/
   
   # Reset Podman networks
   podman system reset --force
   ```

3. **Firewall configuration:**
   ```bash
   # Allow container traffic
   sudo firewall-cmd --add-masquerade --permanent
   sudo firewall-cmd --reload
   ```

4. **DNS configuration:**
   ```bash
   # Check DNS settings
   cat /etc/resolv.conf
   
   # Test DNS resolution
   nslookup google.com
   ```

### Performance Issues

#### Issue: Slow container startup

**Symptoms:**
- Long container boot times
- Timeout errors during startup
- High resource usage

**Diagnosis:**
```bash
# Monitor resource usage
./scripts/container-utils.sh monitor 60 5

# Check system resources
free -h
df -h
top
```

**Solutions:**

1. **Optimize container images:**
   ```bash
   # Use multi-stage builds
   # Minimize image layers
   # Remove unnecessary packages
   ```

2. **Increase system resources:**
   ```bash
   # Add more memory/CPU
   # Use faster storage (SSD)
   # Optimize kernel parameters
   ```

3. **Container optimization:**
   ```bash
   # Use resource limits
   ./scripts/container-manager.sh start --memory 2g --cpus 2
   
   # Enable caching
   ./scripts/container-manager.sh build --cache-from openwrt-rust-base
   ```

#### Issue: Poor network performance

**Symptoms:**
- Low throughput
- High latency
- Packet loss

**Diagnosis:**
```bash
# Run performance tests
./tests/performance/network-performance-tests.sh

# Compare with VM performance
./scripts/performance-comparison.sh

# Check network configuration
./scripts/container-utils.sh network-info
```

**Solutions:**

1. **Network optimization:**
   ```bash
   # Optimize bridge settings
   echo 'net.bridge.bridge-nf-call-iptables=0' | sudo tee -a /etc/sysctl.conf
   sudo sysctl -p
   ```

2. **Container network settings:**
   ```bash
   # Use host networking for performance testing
   podman run --network host openwrt-rust-testing
   
   # Optimize MTU settings
   ip link set dev eth0 mtu 9000
   ```

3. **System tuning:**
   ```bash
   # Increase network buffers
   echo 'net.core.rmem_max = 16777216' | sudo tee -a /etc/sysctl.conf
   echo 'net.core.wmem_max = 16777216' | sudo tee -a /etc/sysctl.conf
   sudo sysctl -p
   ```

### Testing Issues

#### Issue: Tests fail intermittently

**Symptoms:**
- Random test failures
- Timing-related issues
- Inconsistent results

**Diagnosis:**
```bash
# Run tests with verbose output
./tests/network/4port-lan-tests.sh --verbose

# Check test logs
tail -f logs/4port-lan-tests_*.log

# Monitor system during tests
./scripts/container-utils.sh monitor 300 10
```

**Solutions:**

1. **Increase timeouts:**
   ```bash
   # Edit test configuration
   export TEST_TIMEOUT=300
   export PING_TIMEOUT=10
   ```

2. **Add retry logic:**
   ```bash
   # Implement test retries
   for i in {1..3}; do
       if ./tests/network/4port-lan-tests.sh; then
           break
       fi
       sleep 10
   done
   ```

3. **Stabilize test environment:**
   ```bash
   # Ensure clean state
   ./scripts/container-manager.sh stop
   ./scripts/4port-lan-setup.sh cleanup
   sleep 5
   ./scripts/4port-lan-setup.sh setup
   ./scripts/container-manager.sh start
   sleep 15
   ```

#### Issue: Test environment setup fails

**Symptoms:**
- Network setup errors
- Container build failures
- Permission denied errors

**Diagnosis:**
```bash
# Check prerequisites
./scripts/container-utils.sh check-prerequisites

# Verify system configuration
podman info
podman system df
```

**Solutions:**

1. **Fix permissions:**
   ```bash
   # Add user to podman group
   sudo usermod -aG podman $USER
   newgrp podman
   
   # Fix script permissions
   find scripts/ -name "*.sh" -exec chmod +x {} \;
   ```

2. **Clean up environment:**
   ```bash
   # Full cleanup and reset
   ./scripts/cleanup-all.sh
   podman system prune -a -f
   ```

3. **Reinstall dependencies:**
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install --reinstall podman jq bc bridge-utils
   ```

## Platform-Specific Issues

### Linux Issues

#### Issue: SELinux/AppArmor conflicts

**Symptoms:**
- Permission denied errors
- Container security violations
- Mount failures

**Solutions:**
```bash
# SELinux
sudo setsebool -P container_manage_cgroup on
sudo semanage fcontext -a -t container_file_t "/path/to/container/files(/.*)?"
sudo restorecon -R /path/to/container/files

# AppArmor
sudo aa-disable /usr/bin/podman
```

#### Issue: Systemd integration problems

**Symptoms:**
- Service startup failures
- Systemd socket errors
- User session issues

**Solutions:**
```bash
# Enable user services
systemctl --user enable podman.socket
systemctl --user start podman.socket

# Check systemd status
systemctl --user status podman
```

### macOS Issues

#### Issue: Podman machine problems

**Symptoms:**
- Machine startup failures
- Network connectivity issues
- Performance problems

**Solutions:**
```bash
# Reset Podman machine
podman machine stop
podman machine rm
podman machine init --memory 4096 --cpus 4
podman machine start

# Check machine status
podman machine list
podman machine ssh
```

#### Issue: File system performance

**Symptoms:**
- Slow file operations
- High I/O wait times
- Container startup delays

**Solutions:**
```bash
# Use volume mounts instead of bind mounts
# Optimize Docker Desktop settings
# Use faster storage backend
```

## Advanced Troubleshooting

### Debug Mode

Enable debug mode for detailed troubleshooting:

```bash
# Enable debug logging
export CONTAINER_TESTING_LOG_LEVEL=debug
export RUST_BACKTRACE=full

# Run with debug output
./scripts/container-manager.sh start --debug
./tests/network/4port-lan-tests.sh --debug
```

### Network Debugging

```bash
# Capture network traffic
sudo tcpdump -i any -w network-debug.pcap

# Analyze with Wireshark
wireshark network-debug.pcap

# Check routing tables
ip route show table all

# Monitor network interfaces
watch -n 1 'cat /proc/net/dev'
```

### Container Debugging

```bash
# Interactive debugging
./scripts/container-manager.sh exec openwrt-rust-router /bin/bash

# Inspect container internals
podman exec openwrt-rust-router ps aux
podman exec openwrt-rust-router netstat -tuln
podman exec openwrt-rust-router ip addr show

# Check container resources
podman stats openwrt-rust-router
```

### Performance Profiling

```bash
# Profile container performance
perf record -g ./scripts/container-manager.sh start
perf report

# Memory profiling
valgrind --tool=memcheck ./binary

# Network profiling
iperf3 -s &
iperf3 -c localhost -t 60
```

## Recovery Procedures

### Complete Environment Reset

```bash
#!/bin/bash
# Complete reset procedure

# Stop all containers
./scripts/container-manager.sh stop

# Remove all containers
podman rm -a -f

# Remove all images
podman rmi -a -f

# Clean up networks
./scripts/4port-lan-setup.sh cleanup
podman network prune -f

# Clean up volumes
podman volume prune -f

# System cleanup
podman system prune -a -f

# Restart from scratch
./scripts/4port-lan-setup.sh setup
./scripts/container-manager.sh build
./scripts/container-manager.sh start
```

### Backup and Restore

```bash
# Backup container state
podman commit openwrt-rust-router openwrt-rust-router-backup

# Export container
podman save -o openwrt-rust-backup.tar openwrt-rust-router-backup

# Restore container
podman load -i openwrt-rust-backup.tar
podman run openwrt-rust-router-backup
```

## Getting Help

### Log Collection for Support

```bash
# Generate comprehensive diagnostic report
./scripts/generate-diagnostic-report.sh

# Collect system information
./scripts/collect-system-info.sh

# Package logs for support
tar -czf support-logs.tar.gz logs/ /var/log/containers/
```

### Useful Resources

- **Project Documentation**: `docs/`
- **Container Logs**: `logs/`
- **System Logs**: `/var/log/containers/`
- **Podman Documentation**: https://docs.podman.io/
- **Container Networking**: https://github.com/containernetworking/cni

### Community Support

- Check existing issues in project repository
- Search troubleshooting documentation
- Review VM testing setup for comparison
- Consult Podman community resources

---

**Last Updated**: 2025-01-27  
**Version**: 1.0  
**Compatibility**: Podman 4.0+, Linux/macOS
