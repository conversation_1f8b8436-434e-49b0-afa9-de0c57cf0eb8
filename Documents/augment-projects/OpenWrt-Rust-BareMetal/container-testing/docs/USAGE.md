# OpenWrt Rust Container Testing Usage Guide

This guide provides comprehensive instructions for using the OpenWrt Rust container testing environment for development, testing, and validation workflows.

## Quick Start

### Basic Testing Workflow

```bash
# 1. Setup environment
cd container-testing
./scripts/4port-lan-setup.sh setup

# 2. Build and start containers
./scripts/container-manager.sh build
./scripts/container-manager.sh start

# 3. Run tests
./tests/network/4port-lan-tests.sh

# 4. Cleanup
./scripts/container-manager.sh stop
./scripts/4port-lan-setup.sh cleanup
```

### Automated Testing

```bash
# Run complete test suite
./scripts/run-all-tests.sh

# Run with specific options
./scripts/run-all-tests.sh --no-cleanup --verbose
```

## Container Management

### Building Container Images

```bash
# Build all images
./scripts/container-manager.sh build

# Build specific image
./scripts/container-manager.sh build --image openwrt-rust-testing

# Force rebuild (no cache)
./scripts/container-manager.sh build --no-cache
```

### Container Lifecycle

```bash
# Start all containers
./scripts/container-manager.sh start

# Start specific container
./scripts/container-manager.sh start --container openwrt-rust-router

# Check container status
./scripts/container-manager.sh status

# View container logs
./scripts/container-manager.sh logs openwrt-rust-router

# Execute commands in container
./scripts/container-manager.sh exec openwrt-rust-router "ip addr show"

# Stop all containers
./scripts/container-manager.sh stop

# Remove containers
./scripts/container-manager.sh remove
```

### Container Configuration

```bash
# List available containers
./scripts/container-manager.sh list

# Inspect container configuration
./scripts/container-manager.sh inspect openwrt-rust-router

# Monitor container resources
./scripts/container-manager.sh stats
```

## Network Management

### Network Setup and Configuration

```bash
# Setup 4 port LAN networks
./scripts/4port-lan-setup.sh setup

# Setup with specific options
./scripts/4port-lan-setup.sh setup --rootless

# Validate network configuration
./scripts/4port-lan-setup.sh validate

# Show network information
./scripts/4port-lan-setup.sh info

# Cleanup networks
./scripts/4port-lan-setup.sh cleanup
```

### Network Troubleshooting

```bash
# Check network connectivity
./scripts/container-utils.sh test-connectivity

# Get network information
./scripts/container-utils.sh network-info

# Monitor network interfaces
./scripts/container-utils.sh wait-interface eth0 30
```

## Testing Workflows

### Network Testing

```bash
# Basic network functionality tests
./tests/network/4port-lan-tests.sh

# Test specific scenarios
./tests/network/4port-lan-tests.sh --test connectivity
./tests/network/4port-lan-tests.sh --test vlan-isolation
./tests/network/4port-lan-tests.sh --test performance
```

### Performance Testing

```bash
# Network performance tests
./tests/performance/network-performance-tests.sh

# Performance comparison with VM testing
./scripts/performance-comparison.sh

# Custom performance tests
./tests/performance/network-performance-tests.sh --duration 60 --parallel 8
```

### Integration Testing

```bash
# End-to-end integration tests
./tests/integration/container-integration-tests.sh

# Service discovery tests
./tests/integration/container-integration-tests.sh --test service-discovery

# Container lifecycle tests
./tests/integration/container-integration-tests.sh --test lifecycle
```

## Development Workflows

### Development Environment Setup

```bash
# Setup development environment
./scripts/4port-lan-setup.sh setup
./scripts/container-manager.sh build --dev

# Start development containers
./scripts/container-manager.sh start --dev-mode

# Mount source code for live development
./scripts/container-manager.sh start --mount-source
```

### Code Changes and Testing

```bash
# 1. Make changes to OpenWrt Rust implementation
vim ../src/network/mod.rs

# 2. Rebuild containers with changes
./scripts/container-manager.sh build --incremental

# 3. Restart containers
./scripts/container-manager.sh restart

# 4. Run targeted tests
./tests/network/4port-lan-tests.sh --quick

# 5. Run full validation
./scripts/run-all-tests.sh --no-setup
```

### Debugging and Troubleshooting

```bash
# Interactive container access
./scripts/container-manager.sh exec openwrt-rust-router /bin/bash

# View detailed logs
./scripts/container-manager.sh logs openwrt-rust-router --follow

# Generate health report
./scripts/container-utils.sh health-report

# Monitor container resources
./scripts/container-utils.sh monitor 120 5
```

## Advanced Usage

### Custom Network Configurations

Edit `configs/networks/4port-lan.json` to customize network topology:

```json
{
  "networks": {
    "lan1": {
      "subnet": "************/24",
      "gateway": "************",
      "vlan": 100
    }
  }
}
```

Then apply changes:
```bash
./scripts/4port-lan-setup.sh cleanup
./scripts/4port-lan-setup.sh setup
```

### Container Image Customization

Modify Dockerfiles in `configs/images/` and rebuild:

```bash
# Edit Dockerfile
vim configs/images/openwrt-rust-testing/Dockerfile

# Rebuild specific image
./scripts/container-manager.sh build --image openwrt-rust-testing --no-cache
```

### Performance Tuning

```bash
# Optimize container resources
./scripts/container-manager.sh start --memory 4g --cpus 4

# Enable performance monitoring
./scripts/container-manager.sh start --enable-monitoring

# Run performance optimization
./scripts/performance-optimization.sh
```

## CI/CD Integration

### GitHub Actions Integration

The container testing integrates with GitHub Actions automatically:

```yaml
# Trigger container tests
git commit -m "feat: network improvements [comprehensive-test]"
git push
```

### Local CI/CD Simulation

```bash
# Simulate CI/CD pipeline locally
./scripts/ci-simulation.sh

# Run security checks
./scripts/security-validation.sh

# Generate CI/CD reports
./scripts/ci-reporting.sh
```

## Monitoring and Observability

### Real-time Monitoring

```bash
# Monitor all containers
./scripts/container-manager.sh monitor

# Monitor specific metrics
./scripts/container-utils.sh monitor --metrics cpu,memory,network

# Generate monitoring dashboard
./scripts/monitoring-dashboard.sh
```

### Log Management

```bash
# Centralized log collection
./scripts/log-collector.sh

# Log analysis
./scripts/log-analyzer.sh --timeframe 1h

# Export logs
./scripts/log-exporter.sh --format json
```

## Production Deployment

### Production Validation

```bash
# Run production readiness checks
./scripts/production-validation.sh

# Security compliance check
./scripts/security-compliance.sh

# Performance benchmarking
./scripts/production-benchmarks.sh
```

### Deployment Preparation

```bash
# Create deployment package
./scripts/create-deployment-package.sh

# Validate deployment package
./scripts/validate-deployment.sh

# Generate deployment documentation
./scripts/generate-deployment-docs.sh
```

## Best Practices

### Development Best Practices

1. **Incremental Testing**: Use `--no-setup` and `--no-cleanup` flags during development
2. **Resource Management**: Monitor container resource usage regularly
3. **Network Isolation**: Test VLAN isolation thoroughly before deployment
4. **Performance Validation**: Run performance tests after significant changes

### Testing Best Practices

1. **Test Isolation**: Each test should be independent and repeatable
2. **Cleanup**: Always cleanup resources after testing
3. **Logging**: Enable verbose logging for debugging
4. **Validation**: Validate test results and performance metrics

### Production Best Practices

1. **Security**: Run security scans before deployment
2. **Performance**: Validate performance targets are met
3. **Monitoring**: Implement comprehensive monitoring
4. **Documentation**: Maintain up-to-date documentation

## Common Use Cases

### Network Protocol Testing

```bash
# Test specific network protocols
./tests/network/protocol-tests.sh --protocol tcp
./tests/network/protocol-tests.sh --protocol udp
./tests/network/protocol-tests.sh --protocol icmp
```

### Load Testing

```bash
# Network load testing
./tests/performance/load-tests.sh --connections 1000
./tests/performance/load-tests.sh --throughput 1Gbps
```

### Regression Testing

```bash
# Run regression test suite
./tests/regression/regression-tests.sh

# Compare with baseline
./scripts/regression-comparison.sh --baseline v1.0.0
```

### Security Testing

```bash
# Network security tests
./tests/security/network-security-tests.sh

# Container security validation
./tests/security/container-security-tests.sh
```

## Environment Variables

### Configuration Variables

```bash
# Container testing configuration
export CONTAINER_TESTING_LOG_LEVEL=debug
export CONTAINER_TESTING_TIMEOUT=300
export CONTAINER_TESTING_PARALLEL=4

# Network configuration
export OPENWRT_NETWORK_MODE=4port-lan
export OPENWRT_VLAN_SUPPORT=enabled

# Performance configuration
export PERFORMANCE_ITERATIONS=5
export PERFORMANCE_TIMEOUT=600
```

### Runtime Variables

```bash
# Container runtime
export OPENWRT_RUST_MODE=testing
export OPENWRT_RUST_LOG_LEVEL=info
export OPENWRT_RUST_CONFIG_DIR=/etc/openwrt-rust

# Testing environment
export TEST_ENVIRONMENT=container
export TEST_PARALLEL_JOBS=4
export TEST_TIMEOUT=300
```

## Troubleshooting Quick Reference

### Common Issues

1. **Container startup fails**: Check logs with `./scripts/container-manager.sh logs`
2. **Network connectivity issues**: Validate with `./scripts/4port-lan-setup.sh validate`
3. **Performance issues**: Run `./scripts/performance-comparison.sh`
4. **Resource constraints**: Monitor with `./scripts/container-utils.sh monitor`

### Quick Fixes

```bash
# Reset environment
./scripts/container-manager.sh stop
./scripts/4port-lan-setup.sh cleanup
./scripts/4port-lan-setup.sh setup
./scripts/container-manager.sh start

# Force rebuild
./scripts/container-manager.sh build --no-cache --force

# Clean up resources
./scripts/cleanup-all.sh
```

---

**Last Updated**: 2025-01-27  
**Version**: 1.0  
**Compatibility**: Podman 4.0+, Linux/macOS
