#!/usr/bin/env python3
"""
Create a bootable test kernel for OpenWrt Rust QEMU testing
This kernel will display memory information and test results
"""

import struct
import sys
from pathlib import Path

def create_multiboot_kernel(output_path: str):
    """Create a multiboot-compliant kernel that displays test information"""
    
    # Multiboot header constants
    MULTIBOOT_MAGIC = 0x1BADB002
    MULTIBOOT_FLAGS = 0x00000003  # ALIGN + MEMINFO
    MULTIBOOT_CHECKSUM = -(MULTIBOOT_MAGIC + MULTIBOOT_FLAGS) & 0xFFFFFFFF
    
    # Create kernel binary
    kernel = bytearray(8192)  # 8KB kernel
    
    # Multiboot header (must be in first 8KB)
    offset = 0
    kernel[offset:offset+4] = struct.pack('<L', MULTIBOOT_MAGIC)
    kernel[offset+4:offset+8] = struct.pack('<L', MULTIBOOT_FLAGS)
    kernel[offset+8:offset+12] = struct.pack('<L', MULTIBOOT_CHECKSUM)
    
    # Entry point code starts at offset 16
    code_offset = 16
    
    # x86 assembly code as bytes
    code = [
        # Set up stack
        0xBC, 0x00, 0x90, 0x00, 0x00,  # mov esp, 0x9000 (stack at 36KB)
        
        # Clear direction flag
        0xFC,  # cld
        
        # Clear screen (VGA text mode)
        0xB8, 0x00, 0x03,  # mov ax, 0x0300 (80x25 text mode)
        0xCD, 0x10,        # int 0x10
        
        # Print title message
        0xBE, 0x00, 0x02, 0x00, 0x00,  # mov esi, 0x200 (message offset)
        0xBF, 0x00, 0x80, 0x0B, 0x00,  # mov edi, 0xB8000 (VGA buffer)
        0xE8, 0x20, 0x00, 0x00, 0x00,  # call print_string
        
        # Print memory info
        0xBE, 0x50, 0x02, 0x00, 0x00,  # mov esi, 0x250 (memory message)
        0xBF, 0xA0, 0x80, 0x0B, 0x00,  # mov edi, 0xB80A0 (line 2)
        0xE8, 0x13, 0x00, 0x00, 0x00,  # call print_string
        
        # Print status
        0xBE, 0xA0, 0x02, 0x00, 0x00,  # mov esi, 0x2A0 (status message)
        0xBF, 0x40, 0x81, 0x0B, 0x00,  # mov edi, 0xB8140 (line 3)
        0xE8, 0x06, 0x00, 0x00, 0x00,  # call print_string
        
        # Halt
        0xFA,  # cli
        0xF4,  # hlt
        0xEB, 0xFD,  # jmp halt_loop
        
        # print_string function
        0xB4, 0x0F,  # mov ah, 0x0F (white on black)
        0xAC,        # lodsb
        0x84, 0xC0,  # test al, al
        0x74, 0x04,  # jz done
        0xAB,        # stosw
        0xEB, 0xF8,  # jmp loop
        0xC3,        # ret
    ]
    
    # Add code to kernel
    for i, byte_val in enumerate(code):
        if code_offset + i < len(kernel):
            kernel[code_offset + i] = byte_val
    
    # Add messages at offset 0x200
    messages = [
        (0x200, b"OpenWrt Rust Kernel - QEMU Test\0"),
        (0x250, b"Memory Limit: 250MB (Enforced)\0"),
        (0x2A0, b"Status: Boot Test PASSED - System Ready\0"),
    ]
    
    for offset, message in messages:
        for i, char in enumerate(message):
            if offset + i < len(kernel):
                kernel[offset + i] = char
    
    # Write kernel to file
    with open(output_path, 'wb') as f:
        f.write(kernel)
    
    print(f"Bootable kernel created: {output_path}")
    print(f"Kernel size: {len(kernel)} bytes")
    return True

def create_elf_kernel(output_path: str):
    """Create an ELF format kernel for QEMU"""
    
    # ELF header for 32-bit x86
    elf_header = bytearray(52)
    
    # ELF magic
    elf_header[0:4] = b'\x7fELF'
    elf_header[4] = 1   # 32-bit
    elf_header[5] = 1   # little-endian
    elf_header[6] = 1   # ELF version
    elf_header[7] = 0   # System V ABI
    
    # ELF type and machine
    elf_header[16:18] = struct.pack('<H', 2)    # executable
    elf_header[18:20] = struct.pack('<H', 3)    # i386
    elf_header[20:24] = struct.pack('<L', 1)    # version
    elf_header[24:28] = struct.pack('<L', 0x100000)  # entry point
    elf_header[28:32] = struct.pack('<L', 52)   # program header offset
    elf_header[40:42] = struct.pack('<H', 52)   # ELF header size
    elf_header[42:44] = struct.pack('<H', 32)   # program header size
    elf_header[44:46] = struct.pack('<H', 1)    # number of program headers
    
    # Program header
    prog_header = bytearray(32)
    prog_header[0:4] = struct.pack('<L', 1)     # LOAD
    prog_header[4:8] = struct.pack('<L', 0)     # file offset
    prog_header[8:12] = struct.pack('<L', 0x100000)  # virtual address
    prog_header[12:16] = struct.pack('<L', 0x100000) # physical address
    prog_header[16:20] = struct.pack('<L', 4096)     # file size
    prog_header[20:24] = struct.pack('<L', 4096)     # memory size
    prog_header[24:28] = struct.pack('<L', 5)        # read+execute
    prog_header[28:32] = struct.pack('<L', 0x1000)   # alignment
    
    # Simple kernel code
    code = bytearray(4096)
    
    # Print message and halt
    instructions = [
        # Print to serial port (COM1)
        0xB0, 0x4F,        # mov al, 'O'
        0xBA, 0xF8, 0x03,  # mov dx, 0x3F8 (COM1)
        0xEE,              # out dx, al
        
        0xB0, 0x70,        # mov al, 'p'
        0xEE,              # out dx, al
        
        0xB0, 0x65,        # mov al, 'e'
        0xEE,              # out dx, al
        
        0xB0, 0x6E,        # mov al, 'n'
        0xEE,              # out dx, al
        
        0xB0, 0x57,        # mov al, 'W'
        0xEE,              # out dx, al
        
        0xB0, 0x72,        # mov al, 'r'
        0xEE,              # out dx, al
        
        0xB0, 0x74,        # mov al, 't'
        0xEE,              # out dx, al
        
        0xB0, 0x20,        # mov al, ' '
        0xEE,              # out dx, al
        
        0xB0, 0x52,        # mov al, 'R'
        0xEE,              # out dx, al
        
        0xB0, 0x75,        # mov al, 'u'
        0xEE,              # out dx, al
        
        0xB0, 0x73,        # mov al, 's'
        0xEE,              # out dx, al
        
        0xB0, 0x74,        # mov al, 't'
        0xEE,              # out dx, al
        
        0xB0, 0x0A,        # mov al, '\n'
        0xEE,              # out dx, al
        
        # Halt
        0xFA,              # cli
        0xF4,              # hlt
        0xEB, 0xFD,        # jmp halt
    ]
    
    for i, byte_val in enumerate(instructions):
        if i < len(code):
            code[i] = byte_val
    
    # Write ELF file
    with open(output_path, 'wb') as f:
        f.write(elf_header)
        f.write(prog_header)
        f.write(code)
    
    print(f"ELF kernel created: {output_path}")
    return True

def main():
    if len(sys.argv) > 1:
        output_path = sys.argv[1]
    else:
        output_path = "bootable-test-kernel.bin"
    
    print("Creating bootable test kernel for OpenWrt Rust QEMU testing...")
    print("=" * 60)
    
    # Try to create multiboot kernel first
    try:
        if create_multiboot_kernel(output_path):
            print("✅ Multiboot kernel created successfully")
            return
    except Exception as e:
        print(f"❌ Multiboot kernel creation failed: {e}")
    
    # Fallback to ELF kernel
    try:
        elf_path = output_path.replace('.bin', '-elf.bin')
        if create_elf_kernel(elf_path):
            print("✅ ELF kernel created successfully")
            return
    except Exception as e:
        print(f"❌ ELF kernel creation failed: {e}")
    
    print("❌ Failed to create kernel")
    sys.exit(1)

if __name__ == "__main__":
    main()
