{"rustc": 5357548097637079788, "features": "[\"default\", \"x86_64\"]", "declared_features": "[\"async\", \"bare-metal\", \"benchmarking\", \"bindgen\", \"comprehensive-testing\", \"cortex-m\", \"default\", \"documentation-generation\", \"embedded-hal-async\", \"ffi\", \"hardware-testing\", \"performance-monitoring\", \"podman-userspace\", \"profiling\", \"qemu-boot\", \"regression-testing\", \"security-audit\", \"security-hardening\", \"std\", \"stress-testing\", \"system-integration\", \"x86_64\"]", "target": 13013851398542838808, "profile": 402569047024343974, "path": 7826663193809773771, "deps": [[2313368913568865230, "spin", false, 6598579555639902892], [2783643944084398400, "linked_list_allocator", false, 1683637771645532479], [3572139036495447118, "volatile", false, *******************], [3945483610989673570, "x86_64", false, 2190958395323171933], [*******************, "build_script_build", false, 17638670695729330860], [5301752379562145233, "embedded_hal", false, 13176630152796032225], [6502365400774175331, "nom", false, 1275113051360561256], [6769372273416669584, "postcard", false, 3027980204824863550], [7896293946984509699, "bitflags", false, 13553334914179084641], [8785349741056605650, "embedded_storage", false, 17469718333212045349], [9396512774562930307, "nb", false, ******************], [9689903380558560274, "serde", false, 10994055619092104961], [12740221742494834345, "heapless", false, 7694977270382945036], [15635206729440195786, "smoltcp", false, 2101202429959280884], [17917672826516349275, "lazy_static", false, 867011440267125672], [18025426965865311582, "embedded_io", false, 1869649141450264683]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-none/release/.fingerprint/openwrt-rust-baremetal-2fc578811d37b943/dep-bin-simple-qemu-test", "checksum": false}}], "rustflags": ["-C", "link-arg=-Tlinker.ld", "-C", "link-arg=--nmagic", "-C", "link-arg=--gc-sections", "-C", "target-feature=-mmx,-sse,+soft-float"], "config": 2069994364910194474, "compile_kind": 5056187092498294466}