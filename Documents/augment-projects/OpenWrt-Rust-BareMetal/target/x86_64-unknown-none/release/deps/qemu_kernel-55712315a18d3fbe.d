/Users/<USER>/Documents/augment-projects/OpenWrt-Rust-BareMetal/target/x86_64-unknown-none/release/deps/qemu_kernel-55712315a18d3fbe: src/qemu_main.rs src/allocator.rs src/memory.rs src/interrupts_simple.rs src/gdt.rs src/syscall/mod.rs src/process/mod.rs src/task/mod.rs src/drivers/mod.rs src/drivers/hal.rs src/drivers/network.rs src/drivers/storage.rs src/drivers/gpio.rs src/drivers/power.rs src/drivers/hotplug.rs src/network/mod.rs src/network/device.rs src/network/protocols.rs src/network/interface_manager.rs src/network/wireless.rs src/network/firewall.rs src/filesystem/mod.rs src/filesystem/vfs.rs src/filesystem/jffs2.rs src/filesystem/overlay.rs src/filesystem/security.rs src/security/mod.rs src/security/audit.rs src/security/tamper_detection.rs src/security/crypto.rs src/security/access_control.rs src/security/memory_protection.rs src/security/runtime_monitor.rs src/security/policy_engine.rs src/testing.rs src/testing/memory_optimization_tests.rs src/testing/network_performance_tests.rs src/openwrt.rs src/openwrt/uci.rs src/openwrt/network_manager.rs src/openwrt/package_manager.rs src/openwrt/init_system.rs src/openwrt/web_interface.rs src/openwrt/hybrid_package_manager.rs src/openwrt/ipk_format.rs src/openwrt/network_operations.rs src/openwrt/security_framework.rs src/openwrt/opkg_compatibility.rs src/openwrt/package_translation.rs src/openwrt/dependency_resolver.rs src/openwrt/installation_workflows.rs src/openwrt/architecture.rs src/ipc/mod.rs src/ipc/message.rs src/ipc/shared_memory.rs src/ipc/channel.rs src/hybrid/mod.rs src/hybrid/process.rs src/hybrid/coordinator.rs src/hybrid/bridge.rs

/Users/<USER>/Documents/augment-projects/OpenWrt-Rust-BareMetal/target/x86_64-unknown-none/release/deps/qemu_kernel-55712315a18d3fbe.d: src/qemu_main.rs src/allocator.rs src/memory.rs src/interrupts_simple.rs src/gdt.rs src/syscall/mod.rs src/process/mod.rs src/task/mod.rs src/drivers/mod.rs src/drivers/hal.rs src/drivers/network.rs src/drivers/storage.rs src/drivers/gpio.rs src/drivers/power.rs src/drivers/hotplug.rs src/network/mod.rs src/network/device.rs src/network/protocols.rs src/network/interface_manager.rs src/network/wireless.rs src/network/firewall.rs src/filesystem/mod.rs src/filesystem/vfs.rs src/filesystem/jffs2.rs src/filesystem/overlay.rs src/filesystem/security.rs src/security/mod.rs src/security/audit.rs src/security/tamper_detection.rs src/security/crypto.rs src/security/access_control.rs src/security/memory_protection.rs src/security/runtime_monitor.rs src/security/policy_engine.rs src/testing.rs src/testing/memory_optimization_tests.rs src/testing/network_performance_tests.rs src/openwrt.rs src/openwrt/uci.rs src/openwrt/network_manager.rs src/openwrt/package_manager.rs src/openwrt/init_system.rs src/openwrt/web_interface.rs src/openwrt/hybrid_package_manager.rs src/openwrt/ipk_format.rs src/openwrt/network_operations.rs src/openwrt/security_framework.rs src/openwrt/opkg_compatibility.rs src/openwrt/package_translation.rs src/openwrt/dependency_resolver.rs src/openwrt/installation_workflows.rs src/openwrt/architecture.rs src/ipc/mod.rs src/ipc/message.rs src/ipc/shared_memory.rs src/ipc/channel.rs src/hybrid/mod.rs src/hybrid/process.rs src/hybrid/coordinator.rs src/hybrid/bridge.rs

src/qemu_main.rs:
src/allocator.rs:
src/memory.rs:
src/interrupts_simple.rs:
src/gdt.rs:
src/syscall/mod.rs:
src/process/mod.rs:
src/task/mod.rs:
src/drivers/mod.rs:
src/drivers/hal.rs:
src/drivers/network.rs:
src/drivers/storage.rs:
src/drivers/gpio.rs:
src/drivers/power.rs:
src/drivers/hotplug.rs:
src/network/mod.rs:
src/network/device.rs:
src/network/protocols.rs:
src/network/interface_manager.rs:
src/network/wireless.rs:
src/network/firewall.rs:
src/filesystem/mod.rs:
src/filesystem/vfs.rs:
src/filesystem/jffs2.rs:
src/filesystem/overlay.rs:
src/filesystem/security.rs:
src/security/mod.rs:
src/security/audit.rs:
src/security/tamper_detection.rs:
src/security/crypto.rs:
src/security/access_control.rs:
src/security/memory_protection.rs:
src/security/runtime_monitor.rs:
src/security/policy_engine.rs:
src/testing.rs:
src/testing/memory_optimization_tests.rs:
src/testing/network_performance_tests.rs:
src/openwrt.rs:
src/openwrt/uci.rs:
src/openwrt/network_manager.rs:
src/openwrt/package_manager.rs:
src/openwrt/init_system.rs:
src/openwrt/web_interface.rs:
src/openwrt/hybrid_package_manager.rs:
src/openwrt/ipk_format.rs:
src/openwrt/network_operations.rs:
src/openwrt/security_framework.rs:
src/openwrt/opkg_compatibility.rs:
src/openwrt/package_translation.rs:
src/openwrt/dependency_resolver.rs:
src/openwrt/installation_workflows.rs:
src/openwrt/architecture.rs:
src/ipc/mod.rs:
src/ipc/message.rs:
src/ipc/shared_memory.rs:
src/ipc/channel.rs:
src/hybrid/mod.rs:
src/hybrid/process.rs:
src/hybrid/coordinator.rs:
src/hybrid/bridge.rs:
