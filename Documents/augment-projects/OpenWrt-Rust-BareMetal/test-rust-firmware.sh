#!/bin/bash

# OpenWrt Rust Firmware Test Script
# Tests the Rust kernel with 250MB memory constraint

echo "=== OpenWrt Rust Firmware Test ==="
echo "Memory Limit: 250MB"
echo "Architecture: x86_64"
echo "Kernel: rust-test-kernel.bin"
echo ""

# Check if kernel exists
if [ ! -f "rust-test-kernel.bin" ]; then
    echo "Error: rust-test-kernel.bin not found!"
    echo "Please build the kernel first with:"
    echo "cargo build --bin simple-qemu-test --target x86_64-unknown-none --release"
    echo "cp target/x86_64-unknown-none/release/simple-qemu-test rust-test-kernel.bin"
    exit 1
fi

echo "Starting QEMU with OpenWrt Rust kernel..."
echo "Press Ctrl+A then X to exit QEMU"
echo ""

# Run QEMU with multiboot support
qemu-system-x86_64 \
    -kernel "rust-test-kernel.bin" \
    -m 250M \
    -smp 1 \
    -machine q35 \
    -cpu qemu64 \
    -nographic \
    -no-reboot \
    -no-shutdown \
    -serial stdio \
    -monitor none \
    -netdev user,id=net0,restrict=on \
    -device virtio-net-pci,netdev=net0 \
    -object memory-backend-ram,id=ram,size=250M \
    -numa node,memdev=ram \
    -enable-kvm 2>/dev/null || \
qemu-system-x86_64 \
    -kernel "rust-test-kernel.bin" \
    -m 250M \
    -smp 1 \
    -machine q35 \
    -cpu qemu64 \
    -nographic \
    -no-reboot \
    -no-shutdown \
    -serial stdio \
    -monitor none \
    -netdev user,id=net0,restrict=on \
    -device virtio-net-pci,netdev=net0 \
    -object memory-backend-ram,id=ram,size=250M \
    -numa node,memdev=ram

echo ""
echo "=== Test Completed ==="
