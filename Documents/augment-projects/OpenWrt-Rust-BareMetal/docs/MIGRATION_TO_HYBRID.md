# Migration Guide: Transitioning to Hybrid Package Management

## Overview

This guide provides comprehensive instructions for migrating from traditional OpenWrt package management to the new hybrid package management system. The migration process is designed to be seamless, with minimal downtime and full backward compatibility.

## Table of Contents

1. [Pre-Migration Assessment](#pre-migration-assessment)
2. [Migration Planning](#migration-planning)
3. [Automated Migration Process](#automated-migration-process)
4. [Manual Migration Steps](#manual-migration-steps)
5. [Package Conversion](#package-conversion)
6. [Validation and Testing](#validation-and-testing)
7. [Rollback Procedures](#rollback-procedures)
8. [Post-Migration Optimization](#post-migration-optimization)
9. [Troubleshooting](#troubleshooting)

## Pre-Migration Assessment

### System Requirements Check

Before beginning migration, verify your system meets the requirements:

```bash
# Run pre-migration assessment
./scripts/migration/pre-migration-check.sh

# Check system compatibility
opkg-rust doctor --pre-migration

# Verify available disk space (minimum 2GB recommended)
df -h /

# Check memory requirements (minimum 1GB available)
free -h
```

### Current System Analysis

```bash
# Generate current system report
opkg list-installed > current-packages.txt
opkg list-upgradable > upgradable-packages.txt

# Analyze package dependencies
./scripts/migration/analyze-dependencies.sh

# Check for custom configurations
./scripts/migration/check-custom-configs.sh
```

### Compatibility Assessment

```bash
# Check package compatibility with hybrid system
./scripts/migration/compatibility-check.sh current-packages.txt

# Identify packages requiring manual intervention
./scripts/migration/identify-manual-packages.sh

# Generate migration plan
./scripts/migration/generate-migration-plan.sh
```

## Migration Planning

### Migration Strategies

#### 1. Full System Migration (Recommended)
- Complete migration of all packages and configurations
- Minimal downtime approach
- Full validation and testing

#### 2. Incremental Migration
- Gradual migration of packages over time
- Lower risk but longer timeline
- Suitable for production systems

#### 3. Parallel System Migration
- Run both systems in parallel during transition
- Zero downtime approach
- Requires additional resources

### Creating Migration Plan

```bash
# Generate detailed migration plan
./scripts/migration/create-migration-plan.sh --strategy=full

# Customize migration plan
./scripts/migration/customize-plan.sh migration-plan.json

# Validate migration plan
./scripts/migration/validate-plan.sh migration-plan.json
```

### Backup Procedures

```bash
# Create full system backup
./scripts/migration/backup-system.sh --full

# Backup package database
./scripts/migration/backup-packages.sh

# Backup configurations
./scripts/migration/backup-configs.sh

# Verify backup integrity
./scripts/migration/verify-backup.sh
```

## Automated Migration Process

### Quick Migration (Recommended for most users)

```bash
# Run automated migration wizard
./scripts/migration/migrate-wizard.sh

# Follow interactive prompts:
# 1. System assessment
# 2. Backup creation
# 3. Migration execution
# 4. Validation
# 5. Cleanup
```

### Advanced Automated Migration

```bash
# Run migration with custom configuration
./scripts/migration/migrate-system.sh \
    --config=migration-config.json \
    --backup-dir=/backup \
    --log-level=verbose \
    --parallel-workers=4

# Monitor migration progress
./scripts/migration/monitor-migration.sh
```

### Migration Configuration

Create `migration-config.json`:

```json
{
    "migration": {
        "strategy": "full",
        "backup_enabled": true,
        "validation_enabled": true,
        "rollback_enabled": true
    },
    "packages": {
        "convert_custom": true,
        "preserve_configs": true,
        "update_dependencies": true
    },
    "repositories": {
        "add_hybrid_repos": true,
        "preserve_existing": true,
        "update_indexes": true
    },
    "security": {
        "verify_signatures": true,
        "trust_level": "medium",
        "sandbox_enabled": true
    },
    "performance": {
        "parallel_downloads": 4,
        "cache_enabled": true,
        "compression_enabled": true
    }
}
```

## Manual Migration Steps

### Step 1: Install Hybrid Package Manager

```bash
# Download hybrid package manager
wget https://releases.openwrt-rust.org/opkg-rust-latest.tar.gz

# Extract and install
tar -xzf opkg-rust-latest.tar.gz
cd opkg-rust
./install.sh

# Verify installation
opkg-rust --version
```

### Step 2: Configure Repositories

```bash
# Add hybrid repositories
opkg-rust repo add rust-main https://packages.openwrt-rust.org/main
opkg-rust repo add rust-community https://packages.openwrt-rust.org/community

# Import existing IPK repositories
./scripts/migration/import-ipk-repos.sh

# Update repository indexes
opkg-rust update
```

### Step 3: Export Current Package List

```bash
# Export installed packages
opkg list-installed | awk '{print $1}' > installed-packages.txt

# Export package configurations
./scripts/migration/export-configs.sh installed-packages.txt

# Export custom packages
./scripts/migration/export-custom-packages.sh
```

### Step 4: Convert Package Formats

```bash
# Convert custom packages to Rust native format
./scripts/migration/convert-packages.sh custom-packages/

# Validate converted packages
./scripts/migration/validate-converted.sh converted-packages/

# Sign converted packages
./scripts/migration/sign-packages.sh converted-packages/
```

### Step 5: Install Packages in Hybrid System

```bash
# Install base system packages first
opkg-rust install base-system kernel

# Install converted custom packages
opkg-rust install --local converted-packages/*.rust-pkg

# Install remaining packages
opkg-rust install --file installed-packages.txt
```

### Step 6: Migrate Configurations

```bash
# Import configurations
./scripts/migration/import-configs.sh

# Update configuration paths
./scripts/migration/update-config-paths.sh

# Validate configurations
./scripts/migration/validate-configs.sh
```

## Package Conversion

### Converting Custom Packages

#### From IPK to Rust Native

```bash
# Convert single package
./scripts/migration/convert-ipk-to-rust.sh package.ipk

# Convert multiple packages
./scripts/migration/batch-convert.sh packages/*.ipk

# Customize conversion
./scripts/migration/convert-with-config.sh package.ipk conversion-config.json
```

#### Conversion Configuration

Create `conversion-config.json`:

```json
{
    "conversion": {
        "target_format": "rust-native",
        "preserve_metadata": true,
        "update_dependencies": true,
        "optimize_binary": true
    },
    "metadata": {
        "maintainer": "Your Name <<EMAIL>>",
        "license": "GPL-2.0",
        "homepage": "https://example.com"
    },
    "dependencies": {
        "auto_resolve": true,
        "prefer_rust_native": true,
        "allow_cross_format": true
    }
}
```

### Package Validation

```bash
# Validate converted package
opkg-rust validate converted-package.rust-pkg

# Test package installation
opkg-rust install --test converted-package.rust-pkg

# Check package integrity
opkg-rust verify converted-package.rust-pkg
```

## Validation and Testing

### System Validation

```bash
# Run comprehensive system validation
./scripts/migration/validate-system.sh

# Check package integrity
opkg-rust verify --all

# Validate configurations
./scripts/migration/validate-all-configs.sh

# Test system functionality
./scripts/migration/test-system-functions.sh
```

### Package Testing

```bash
# Test package installation/removal
./scripts/migration/test-package-operations.sh

# Test dependency resolution
./scripts/migration/test-dependencies.sh

# Test cross-format compatibility
./scripts/migration/test-cross-format.sh
```

### Performance Testing

```bash
# Benchmark package operations
./scripts/migration/benchmark-operations.sh

# Compare performance with original system
./scripts/migration/compare-performance.sh

# Generate performance report
./scripts/migration/performance-report.sh
```

## Rollback Procedures

### Automatic Rollback

```bash
# Trigger automatic rollback
./scripts/migration/rollback.sh --auto

# Rollback to specific backup
./scripts/migration/rollback.sh --backup=backup-20250127

# Partial rollback (packages only)
./scripts/migration/rollback.sh --packages-only
```

### Manual Rollback

```bash
# Stop hybrid package manager
systemctl stop opkg-rust

# Restore original package database
./scripts/migration/restore-package-db.sh

# Restore configurations
./scripts/migration/restore-configs.sh

# Restart original package manager
systemctl start opkg
```

### Rollback Validation

```bash
# Validate rollback success
./scripts/migration/validate-rollback.sh

# Check system functionality
./scripts/migration/test-original-system.sh

# Generate rollback report
./scripts/migration/rollback-report.sh
```

## Post-Migration Optimization

### Performance Optimization

```bash
# Optimize package cache
opkg-rust optimize --cache

# Update repository indexes
opkg-rust update --optimize

# Configure performance settings
./scripts/migration/optimize-performance.sh
```

### Security Hardening

```bash
# Enable security features
opkg-rust config set verify_signatures true
opkg-rust config set min_trust_level medium

# Update security certificates
./scripts/migration/update-certificates.sh

# Run security audit
./scripts/migration/security-audit.sh
```

### System Cleanup

```bash
# Remove migration artifacts
./scripts/migration/cleanup.sh

# Remove old package manager (if desired)
./scripts/migration/remove-old-pm.sh --confirm

# Optimize disk usage
./scripts/migration/optimize-disk.sh
```

## Troubleshooting

### Common Migration Issues

#### Package Conversion Failures

```bash
# Check conversion logs
cat /var/log/migration/conversion.log

# Retry failed conversions
./scripts/migration/retry-failed-conversions.sh

# Manual conversion assistance
./scripts/migration/manual-conversion-helper.sh package.ipk
```

#### Dependency Resolution Issues

```bash
# Analyze dependency conflicts
./scripts/migration/analyze-conflicts.sh

# Resolve dependencies manually
./scripts/migration/resolve-deps-manual.sh

# Update dependency mappings
./scripts/migration/update-dep-mappings.sh
```

#### Configuration Migration Issues

```bash
# Check configuration compatibility
./scripts/migration/check-config-compat.sh

# Fix configuration paths
./scripts/migration/fix-config-paths.sh

# Merge configuration conflicts
./scripts/migration/merge-configs.sh
```

### Migration Support Tools

```bash
# Generate migration report
./scripts/migration/generate-report.sh

# Create support bundle
./scripts/migration/create-support-bundle.sh

# Run migration diagnostics
./scripts/migration/diagnose.sh --verbose
```

### Getting Help

1. **Documentation**: Check the comprehensive documentation
2. **Community Forum**: Post questions on the community forum
3. **Issue Tracker**: Report bugs on the issue tracker
4. **Professional Support**: Contact professional support services

### Emergency Procedures

```bash
# Emergency rollback
./scripts/migration/emergency-rollback.sh

# System recovery
./scripts/migration/system-recovery.sh

# Contact emergency support
./scripts/migration/emergency-contact.sh
```

## Migration Checklist

### Pre-Migration
- [ ] System requirements verified
- [ ] Current system analyzed
- [ ] Compatibility assessed
- [ ] Migration plan created
- [ ] Full system backup completed
- [ ] Rollback procedures tested

### During Migration
- [ ] Hybrid package manager installed
- [ ] Repositories configured
- [ ] Package list exported
- [ ] Custom packages converted
- [ ] Packages installed in hybrid system
- [ ] Configurations migrated

### Post-Migration
- [ ] System validation completed
- [ ] Package testing performed
- [ ] Performance benchmarked
- [ ] Security hardening applied
- [ ] System optimized
- [ ] Migration artifacts cleaned up

### Validation
- [ ] All packages functional
- [ ] Configurations working
- [ ] Performance acceptable
- [ ] Security measures active
- [ ] Rollback procedures verified
- [ ] Documentation updated

---

**Version**: 1.0  
**Last Updated**: January 27, 2025  
**Status**: Phase 11.6 - Documentation and Migration
