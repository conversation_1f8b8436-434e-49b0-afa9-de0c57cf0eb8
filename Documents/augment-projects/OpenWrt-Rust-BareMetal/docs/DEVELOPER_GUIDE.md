# OpenWrt Rust Developer Guide

## Overview

This guide provides comprehensive information for developers working on the OpenWrt Rust bare-metal implementation. It covers development setup, architecture patterns, API usage, and best practices.

## Table of Contents

1. [Development Environment Setup](#development-environment-setup)
2. [Architecture Overview](#architecture-overview)
3. [Core APIs](#core-apis)
4. [Development Workflow](#development-workflow)
5. [Testing Guidelines](#testing-guidelines)
6. [Performance Optimization](#performance-optimization)
7. [Security Considerations](#security-considerations)
8. [Troubleshooting](#troubleshooting)

## Development Environment Setup

### Prerequisites

- Rust toolchain 1.70+
- Cross-compilation targets for embedded systems
- OpenWrt development environment (for FFI integration)
- Hardware testing setup (optional)

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd OpenWrt-Rust-BareMetal

# Install Rust targets
rustup target add armv7-unknown-linux-gnueabihf
rustup target add mips-unknown-linux-gnu

# Build for development
cargo build

# Run tests
cargo test
```

### Cross-Compilation Setup

```bash
# Configure cross-compilation targets
cat > .cargo/config.toml << EOF
[target.armv7-unknown-linux-gnueabihf]
linker = "arm-linux-gnueabihf-gcc"

[target.mips-unknown-linux-gnu]
linker = "mips-linux-gnu-gcc"
EOF
```

## Architecture Overview

### Core Components

The OpenWrt Rust implementation consists of several key components:

1. **Memory Management** (`src/allocator.rs`, `src/memory.rs`)
   - Custom allocator optimized for embedded systems
   - Memory usage tracking and optimization
   - Fragmentation monitoring and mitigation

2. **Network Stack** (`src/network/`)
   - smoltcp-based TCP/IP implementation
   - Zero-copy packet processing
   - Network interface management
   - Wireless and firewall support

3. **Device Drivers** (`src/drivers/`)
   - Hardware abstraction layer (HAL)
   - Network, storage, GPIO, and power drivers
   - Hot-plug device support

4. **Filesystem** (`src/filesystem/`)
   - Virtual filesystem (VFS) layer
   - JFFS2 and overlay filesystem support
   - Flash-optimized operations

5. **Configuration Management** (`src/openwrt/`)
   - UCI-compatible configuration system
   - Transaction support and validation
   - Backup and restore functionality

6. **Security Framework** (`src/security/`)
   - Access control and audit logging
   - Tamper detection
   - Cryptographic operations

### Design Principles

- **Memory Safety**: Leverage Rust's ownership system to eliminate memory vulnerabilities
- **Performance**: Zero-cost abstractions and embedded-optimized implementations
- **Compatibility**: Maintain OpenWrt configuration and API compatibility
- **Modularity**: Clear separation of concerns and pluggable components

## Core APIs

### Memory Management

```rust
use crate::allocator;

// Get allocator statistics
let stats = allocator::get_stats();
println!("Memory usage: {}/{} bytes", stats.current_usage, stats.total_size);

// Allocate memory
let ptr = allocator::allocate(1024);
if let Some(ptr) = ptr {
    // Use memory
    allocator::deallocate(ptr, 1024);
}

// Optimize memory
allocator::optimize_memory()?;
```

### Network Operations

```rust
use crate::network;

// Get network statistics
let stats = network::get_statistics();
assert!(stats.initialized);

// Create virtual device for testing
let device = network::device::VirtualDevice::new();

// Inject packet
let packet = [0x01, 0x02, 0x03, 0x04];
device.inject_packet(&packet)?;

// Extract packet
if let Some(extracted) = device.extract_packet() {
    // Process packet
}

// Optimize network throughput
network::optimize_throughput()?;
```

### Configuration Management

```rust
use crate::openwrt::uci;

// List UCI packages
let packages = uci::list_packages();
for package in packages.iter() {
    println!("Package: {}", package);
}

// Get system configuration
let config = crate::openwrt::get_config();
println!("Hostname: {}", config.hostname);
```

### Device Driver Access

```rust
use crate::drivers;

// Get device manager
let manager = drivers::device_manager();

// List devices by type
let network_devices = manager.list_devices_by_type(drivers::DeviceType::Network);
let storage_devices = manager.list_devices_by_type(drivers::DeviceType::Storage);

println!("Network devices: {}", network_devices.len());
println!("Storage devices: {}", storage_devices.len());
```

### Performance Monitoring

```rust
use crate::system_integration;

// Update system metrics
system_integration::update_system_metrics()?;

// Get comprehensive metrics
let metrics = system_integration::get_comprehensive_metrics();
println!("System health: {}%", metrics.health_score);

// Get optimization history
let history = system_integration::get_optimization_history();
println!("Recent optimizations: {}", history.len());
```

## Development Workflow

### 1. Feature Development

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/new-feature
   ```

2. **Implement Feature**
   - Follow existing code patterns
   - Add comprehensive error handling
   - Include documentation comments

3. **Add Tests**
   ```rust
   #[cfg(test)]
   mod tests {
       use super::*;
       
       #[test]
       fn test_new_feature() {
           // Test implementation
       }
   }
   ```

4. **Run Tests**
   ```bash
   cargo test
   cargo test --features comprehensive-testing
   ```

### 2. Code Review Process

1. **Self Review**
   - Check for memory safety
   - Verify error handling
   - Ensure documentation is complete

2. **Automated Checks**
   - All tests must pass
   - No clippy warnings
   - Code coverage requirements met

3. **Peer Review**
   - Security review for unsafe code
   - Performance review for critical paths
   - API design review

### 3. Integration Testing

```bash
# Run comprehensive test suite
cargo test --features comprehensive-testing

# Run stress tests
cargo test --features stress-testing

# Run performance tests
cargo test --features performance-testing
```

## Testing Guidelines

### Test Categories

1. **Unit Tests**: Test individual components in isolation
2. **Integration Tests**: Test component interactions
3. **Stress Tests**: Test system behavior under load
4. **Hardware Tests**: Test on actual hardware (when safe)
5. **Regression Tests**: Ensure existing functionality remains intact
6. **Performance Tests**: Validate performance requirements

### Writing Tests

```rust
use crate::testing::{run_enhanced_test, TestCategory, TestSeverity};

fn test_my_feature() -> Result<(), &'static str> {
    // Test implementation
    let result = my_feature_function();
    
    if result.is_ok() {
        Ok(())
    } else {
        Err("Feature test failed")
    }
}

// Register test
run_enhanced_test(
    "my_feature_test",
    TestCategory::Unit,
    TestSeverity::High,
    test_my_feature
);
```

### Test Configuration

```rust
use crate::testing::TestConfig;

let config = TestConfig {
    enable_stress_tests: true,
    enable_hardware_tests: false, // Disabled for safety
    enable_performance_tests: true,
    max_execution_time_ms: 10000,
    memory_limit_bytes: 1024 * 1024,
    repeat_count: 1,
};
```

## Performance Optimization

### Memory Optimization

- Use memory pools for frequent allocations
- Monitor fragmentation ratio
- Implement custom allocators for specific use cases
- Profile memory usage patterns

### CPU Optimization

- Enable dynamic frequency scaling
- Optimize cache usage patterns
- Minimize interrupt latency
- Use SIMD instructions where appropriate

### Network Optimization

- Implement zero-copy packet processing
- Use packet batching for better throughput
- Optimize buffer sizes for target hardware
- Enable hardware offloading when available

### Measurement and Profiling

```rust
use crate::profiling;

// Record benchmark
profiling::benchmark::record_benchmark("operation_name", duration_us, "metadata");

// Get performance metrics
let cpu_metrics = crate::cpu_optimization::get_cpu_metrics();
let memory_metrics = crate::allocator::get_stats();
```

## Security Considerations

### Memory Safety

- Minimize `unsafe` code blocks
- Audit all `unsafe` code thoroughly
- Use `#[forbid(unsafe_code)]` in safe modules
- Implement comprehensive bounds checking

### Access Control

```rust
use crate::security;

// Check security status
let status = security::get_status();
assert!(status.initialized);

// Access control example
let access_status = security::access_control::get_status();
assert!(access_status.initialized);
```

### Audit Logging

```rust
use crate::security::audit;

// Log security event
audit::log_event("user_login", "user123", "success");

// Get audit status
let status = audit::get_status();
assert!(status.initialized);
```

## Troubleshooting

### Common Issues

1. **Compilation Errors**
   - Check Rust version compatibility
   - Verify feature flags are correct
   - Ensure all dependencies are available

2. **Memory Issues**
   - Check allocator statistics
   - Monitor fragmentation ratio
   - Verify proper cleanup in error paths

3. **Network Problems**
   - Verify network stack initialization
   - Check interface configuration
   - Monitor packet processing statistics

4. **Performance Issues**
   - Profile critical code paths
   - Check system health metrics
   - Verify optimization features are enabled

### Debugging Tools

```rust
// Get system diagnostics
let allocator_stats = crate::allocator::get_stats();
let network_stats = crate::network::get_statistics();
let system_metrics = crate::system_integration::get_comprehensive_metrics();

println!("Debug Info:");
println!("  Memory: {}/{} bytes", allocator_stats.current_usage, allocator_stats.total_size);
println!("  Network: {}", if network_stats.initialized { "OK" } else { "FAIL" });
println!("  Health: {}%", system_metrics.health_score);
```

### Getting Help

- Check the [API Documentation](API_REFERENCE.md)
- Review [Migration Guide](MIGRATION_GUIDE.md)
- See [Troubleshooting Guide](TROUBLESHOOTING.md)
- Contact the development team

---

**Document Version**: 1.0  
**Last Updated**: 2025-01-27  
**Compatible with**: Phase 5 Production Readiness Implementation
