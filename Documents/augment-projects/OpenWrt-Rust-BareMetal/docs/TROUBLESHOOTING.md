# OpenWrt Rust Troubleshooting Guide

## Overview

This guide provides solutions to common issues encountered when using the OpenWrt Rust implementation. It includes diagnostic procedures, common problems, and step-by-step solutions.

## Table of Contents

1. [Diagnostic Tools](#diagnostic-tools)
2. [Boot and Initialization Issues](#boot-and-initialization-issues)
3. [Memory Problems](#memory-problems)
4. [Network Issues](#network-issues)
5. [Performance Problems](#performance-problems)
6. [Configuration Issues](#configuration-issues)
7. [Hardware Problems](#hardware-problems)
8. [Security Issues](#security-issues)
9. [Development and Testing Issues](#development-and-testing-issues)

## Diagnostic Tools

### System Health Check

Run comprehensive system diagnostics:

```bash
# Check overall system health
./scripts/system_health_check.sh

# Get detailed system information
./scripts/get_system_info.sh

# Run diagnostic tests
cargo test --features comprehensive-testing
```

### Performance Monitoring

Monitor system performance in real-time:

```rust
use crate::system_integration;

// Get comprehensive metrics
let metrics = system_integration::get_comprehensive_metrics();
println!("System Health: {}%", metrics.health_score);
println!("Memory Usage: {}%", metrics.memory.usage_percent);
println!("CPU Usage: {}%", metrics.cpu.usage_percent);
```

### Memory Diagnostics

Check memory usage and fragmentation:

```rust
use crate::allocator;

let stats = allocator::get_stats();
println!("Memory Statistics:");
println!("  Total: {} bytes", stats.total_size);
println!("  Used: {} bytes", stats.current_usage);
println!("  Free: {} bytes", stats.total_size - stats.current_usage);
println!("  Fragmentation: {:.2}%", stats.fragmentation_ratio * 100.0);
```

### Network Diagnostics

Check network stack status:

```rust
use crate::network;

let stats = network::get_statistics();
println!("Network Status: {}", if stats.initialized { "OK" } else { "FAILED" });

let interface_stats = network::interface_manager::get_manager_statistics();
println!("Interfaces: {}", interface_stats.total_interfaces);
```

## Boot and Initialization Issues

### Problem: System Fails to Boot

**Symptoms:**
- System hangs during boot
- No output on console
- Boot loop

**Diagnostic Steps:**
1. Check boot logs
2. Verify hardware compatibility
3. Test with minimal configuration

**Solutions:**

```bash
# Check boot sequence
dmesg | grep -i "rust\|kernel\|boot"

# Verify memory initialization
grep -i "memory" /proc/bootlog

# Test with safe mode
./scripts/boot_safe_mode.sh
```

**Code-level debugging:**
```rust
// Add debug output to kernel initialization
fn init_kernel() {
    println!("Starting kernel initialization...");
    
    // Initialize allocator
    if let Err(e) = allocator::init() {
        println!("Allocator init failed: {}", e);
        panic!("Cannot continue without allocator");
    }
    println!("Allocator initialized successfully");
    
    // Continue with other subsystems...
}
```

### Problem: Kernel Panic During Boot

**Symptoms:**
- Panic message displayed
- System halts completely

**Solutions:**

```rust
// Improve panic handler
#[panic_handler]
fn panic(info: &PanicInfo) -> ! {
    println!("KERNEL PANIC: {}", info);
    
    // Try to save diagnostic information
    if let Ok(stats) = try_get_allocator_stats() {
        println!("Memory at panic: {}/{}", stats.current_usage, stats.total_size);
    }
    
    // Halt system safely
    loop {
        unsafe { asm!("hlt") };
    }
}
```

### Problem: Subsystem Initialization Failures

**Symptoms:**
- Specific subsystems not working
- Partial functionality

**Solutions:**

```rust
// Add comprehensive error handling
fn init_subsystems() -> Result<(), &'static str> {
    // Network initialization
    network::init().map_err(|e| {
        println!("Network init failed: {}", e);
        "Network initialization failed"
    })?;
    
    // Filesystem initialization
    filesystem::init().map_err(|e| {
        println!("Filesystem init failed: {}", e);
        "Filesystem initialization failed"
    })?;
    
    // Driver initialization
    drivers::init().map_err(|e| {
        println!("Driver init failed: {}", e);
        "Driver initialization failed"
    })?;
    
    Ok(())
}
```

## Memory Problems

### Problem: Out of Memory Errors

**Symptoms:**
- Allocation failures
- System becomes unresponsive
- Memory exhaustion warnings

**Diagnostic Steps:**

```rust
// Check memory usage patterns
fn diagnose_memory_usage() {
    let stats = allocator::get_stats();
    
    println!("Memory Diagnostics:");
    println!("  Total: {} KB", stats.total_size / 1024);
    println!("  Used: {} KB ({:.1}%)", 
             stats.current_usage / 1024,
             (stats.current_usage as f32 / stats.total_size as f32) * 100.0);
    println!("  Fragmentation: {:.2}%", stats.fragmentation_ratio * 100.0);
    println!("  Failed allocations: {}", stats.allocation_failures);
}
```

**Solutions:**

```rust
// Implement memory optimization
fn handle_memory_pressure() -> Result<(), &'static str> {
    let stats = allocator::get_stats();
    
    if stats.current_usage > (stats.total_size * 90 / 100) {
        println!("Memory pressure detected, optimizing...");
        
        // Trigger memory optimization
        allocator::optimize_memory()?;
        
        // Force garbage collection if available
        allocator::force_cleanup();
        
        // Check if optimization helped
        let new_stats = allocator::get_stats();
        if new_stats.current_usage < stats.current_usage {
            println!("Memory optimization successful");
            Ok(())
        } else {
            Err("Memory optimization failed")
        }
    } else {
        Ok(())
    }
}
```

### Problem: Memory Fragmentation

**Symptoms:**
- Large allocation failures despite available memory
- High fragmentation ratio

**Solutions:**

```rust
// Implement defragmentation
fn defragment_memory() -> Result<(), &'static str> {
    let initial_stats = allocator::get_stats();
    
    if initial_stats.fragmentation_ratio > 0.5 {
        println!("High fragmentation detected: {:.2}%", 
                 initial_stats.fragmentation_ratio * 100.0);
        
        // Trigger defragmentation
        allocator::defragment()?;
        
        let final_stats = allocator::get_stats();
        println!("Fragmentation after defrag: {:.2}%", 
                 final_stats.fragmentation_ratio * 100.0);
        
        Ok(())
    } else {
        Ok(())
    }
}
```

## Network Issues

### Problem: Network Interfaces Not Working

**Symptoms:**
- No network connectivity
- Interface not detected
- Packet loss

**Diagnostic Steps:**

```rust
// Check network interface status
fn diagnose_network_interfaces() {
    let manager_stats = network::interface_manager::get_manager_statistics();
    println!("Interface Manager Status:");
    println!("  Initialized: {}", manager_stats.initialized);
    println!("  Total interfaces: {}", manager_stats.total_interfaces);
    println!("  Active interfaces: {}", manager_stats.active_interfaces);
    
    // Check individual interfaces
    let device_manager = drivers::device_manager();
    let network_devices = device_manager.list_devices_by_type(drivers::DeviceType::Network);
    println!("Network devices detected: {}", network_devices.len());
}
```

**Solutions:**

```rust
// Reinitialize network stack
fn reinitialize_network() -> Result<(), &'static str> {
    println!("Reinitializing network stack...");
    
    // Reset network interfaces
    network::interface_manager::reset_interfaces()?;
    
    // Reload network drivers
    drivers::reload_network_drivers()?;
    
    // Restart network protocols
    network::protocols::restart()?;
    
    // Verify network functionality
    let stats = network::get_statistics();
    if stats.initialized {
        println!("Network reinitialization successful");
        Ok(())
    } else {
        Err("Network reinitialization failed")
    }
}
```

### Problem: Poor Network Performance

**Symptoms:**
- Low throughput
- High latency
- Packet drops

**Solutions:**

```rust
// Optimize network performance
fn optimize_network_performance() -> Result<(), &'static str> {
    println!("Optimizing network performance...");
    
    // Enable zero-copy processing
    network::enable_zero_copy()?;
    
    // Optimize buffer sizes
    network::optimize_buffer_sizes()?;
    
    // Enable packet batching
    network::enable_packet_batching()?;
    
    // Tune network parameters
    network::optimize_throughput()?;
    
    println!("Network optimization complete");
    Ok(())
}
```

## Performance Problems

### Problem: High CPU Usage

**Symptoms:**
- System sluggishness
- High CPU utilization
- Thermal throttling

**Diagnostic Steps:**

```rust
// Monitor CPU performance
fn monitor_cpu_performance() {
    let metrics = cpu_optimization::get_cpu_metrics();
    
    println!("CPU Performance:");
    println!("  Usage: {:.1}%", metrics.usage_percent);
    println!("  Frequency: {} MHz", metrics.frequency_mhz);
    println!("  Temperature: {}°C", metrics.temperature_celsius);
    println!("  Interrupt latency: {} μs", metrics.interrupt_latency_us);
}
```

**Solutions:**

```rust
// Optimize CPU performance
fn optimize_cpu_performance() -> Result<(), &'static str> {
    let metrics = cpu_optimization::get_cpu_metrics();
    
    if metrics.usage_percent > 80.0 {
        println!("High CPU usage detected, optimizing...");
        
        // Enable dynamic frequency scaling
        cpu_optimization::enable_dynamic_scaling()?;
        
        // Optimize cache usage
        cpu_optimization::optimize_cache()?;
        
        // Reduce interrupt latency
        cpu_optimization::optimize_interrupts()?;
        
        Ok(())
    } else {
        Ok(())
    }
}
```

### Problem: System Becomes Unresponsive

**Symptoms:**
- System hangs
- No response to input
- Watchdog timeouts

**Solutions:**

```rust
// Implement system health monitoring
fn monitor_system_health() {
    let metrics = system_integration::get_comprehensive_metrics();
    
    if metrics.health_score < 50 {
        println!("System health critical: {}%", metrics.health_score);
        
        // Trigger emergency optimization
        emergency_system_optimization();
    }
}

fn emergency_system_optimization() {
    // Free up memory
    let _ = allocator::emergency_cleanup();
    
    // Reduce CPU load
    let _ = cpu_optimization::emergency_throttle();
    
    // Prioritize critical processes
    let _ = process::prioritize_critical();
}
```

## Configuration Issues

### Problem: UCI Configuration Not Loading

**Symptoms:**
- Configuration changes not applied
- Default settings used
- UCI errors

**Solutions:**

```bash
# Validate UCI configuration
uci validate

# Check configuration syntax
uci show | head -20

# Reset configuration if corrupted
uci revert
uci commit
```

**Code-level debugging:**

```rust
// Debug UCI configuration loading
fn debug_uci_loading() {
    let packages = openwrt::uci::list_packages();
    println!("UCI packages found: {}", packages.len());
    
    for package in packages.iter() {
        println!("Package: {}", package);
        // Add detailed package validation here
    }
}
```

## Hardware Problems

### Problem: Hardware Not Detected

**Symptoms:**
- Missing devices
- Driver load failures
- Hardware errors

**Diagnostic Steps:**

```rust
// Check hardware detection
fn check_hardware_detection() {
    let device_manager = drivers::device_manager();
    
    println!("Hardware Detection:");
    println!("  Total devices: {}", device_manager.device_count());
    
    let network_devices = device_manager.list_devices_by_type(drivers::DeviceType::Network);
    let storage_devices = device_manager.list_devices_by_type(drivers::DeviceType::Storage);
    let gpio_devices = device_manager.list_devices_by_type(drivers::DeviceType::Gpio);
    
    println!("  Network devices: {}", network_devices.len());
    println!("  Storage devices: {}", storage_devices.len());
    println!("  GPIO devices: {}", gpio_devices.len());
}
```

**Solutions:**

```rust
// Reinitialize hardware detection
fn reinitialize_hardware() -> Result<(), &'static str> {
    println!("Reinitializing hardware detection...");
    
    // Reset device manager
    drivers::reset_device_manager()?;
    
    // Reload all drivers
    drivers::reload_all_drivers()?;
    
    // Re-scan for devices
    drivers::rescan_devices()?;
    
    Ok(())
}
```

## Security Issues

### Problem: Security Framework Not Working

**Symptoms:**
- Security features disabled
- Audit logging failures
- Access control bypassed

**Solutions:**

```rust
// Check security framework status
fn check_security_status() {
    let status = security::get_status();
    println!("Security Status: {}", if status.initialized { "OK" } else { "FAILED" });
    
    let audit_status = security::audit::get_status();
    println!("Audit Status: {}", if audit_status.initialized { "OK" } else { "FAILED" });
    
    let access_status = security::access_control::get_status();
    println!("Access Control: {}", if access_status.initialized { "OK" } else { "FAILED" });
}

// Reinitialize security framework
fn reinitialize_security() -> Result<(), &'static str> {
    security::reinit()?;
    security::audit::reinit()?;
    security::access_control::reinit()?;
    Ok(())
}
```

## Development and Testing Issues

### Problem: Tests Failing

**Symptoms:**
- Test suite failures
- Compilation errors
- Runtime test errors

**Solutions:**

```bash
# Run tests with verbose output
cargo test -- --nocapture

# Run specific test category
cargo test --features comprehensive-testing

# Check test configuration
cargo test --features stress-testing -- --test-threads=1
```

**Debug test failures:**

```rust
// Add detailed test debugging
fn debug_test_failure() {
    let stats = testing::calculate_enhanced_stats();
    
    println!("Test Results:");
    println!("  Total: {}", stats.total_tests);
    println!("  Passed: {}", stats.passed_tests);
    println!("  Failed: {}", stats.failed_tests);
    println!("  Critical failures: {}", stats.critical_failures);
    
    // Get detailed test results
    let results = testing::get_test_results();
    for result in results.iter() {
        if !result.passed {
            println!("FAILED: {} - {}", result.name, result.message);
            if let Some(details) = result.error_details {
                println!("  Details: {}", details);
            }
        }
    }
}
```

## Emergency Procedures

### System Recovery

If the system becomes completely unresponsive:

```bash
# Hardware reset
# Boot from recovery partition
# Restore from backup

# Emergency diagnostic mode
./scripts/emergency_diagnostic.sh

# Safe mode boot
./scripts/boot_safe_mode.sh
```

### Data Recovery

```bash
# Mount filesystem read-only
mount -o ro /dev/mtdblock0 /mnt/recovery

# Extract critical data
tar -czf emergency_backup.tar.gz /mnt/recovery/etc/config/

# Verify data integrity
./scripts/verify_data_integrity.sh
```

---

**Document Version**: 1.0  
**Last Updated**: 2025-01-27  
**Compatible with**: Phase 5 Production Readiness Implementation
