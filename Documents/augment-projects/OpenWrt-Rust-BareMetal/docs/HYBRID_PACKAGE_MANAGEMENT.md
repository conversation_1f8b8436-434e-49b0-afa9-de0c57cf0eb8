# Hybrid Package Management System

## Overview

The OpenWrt Rust implementation features a revolutionary hybrid package management system that seamlessly integrates official OpenWrt packages with custom Rust-based packages. This system provides access to the entire OpenWrt ecosystem while maintaining the performance and security benefits of the Rust implementation.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Supported Package Formats](#supported-package-formats)
3. [Package Installation](#package-installation)
4. [Dependency Management](#dependency-management)
5. [Repository Configuration](#repository-configuration)
6. [Security Model](#security-model)
7. [Performance Considerations](#performance-considerations)
8. [Migration from Legacy Systems](#migration-from-legacy-systems)
9. [Troubleshooting](#troubleshooting)
10. [API Reference](#api-reference)

## Architecture Overview

The hybrid package management system consists of several key components:

```
┌─────────────────────────────────────────────────────────────────┐
│                    Unified Package Interface                   │
├─────────────────────────────────────────────────────────────────┤
│                  Dependency Resolution Engine                  │
├─────────────────────────────────────────────────────────────────┤
│              Package Format Compatibility Layer               │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  Rust Native    │  │   IPK Handler   │  │   APK Handler   │ │
│  │   Packages      │  │  (OpenWrt)      │  │ (Future-proof)  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                    Network Operations Layer                    │
├─────────────────────────────────────────────────────────────────┤
│                      Security Framework                        │
└─────────────────────────────────────────────────────────────────┘
```

### Key Features

- **Unified Interface**: Single command-line interface for all package formats
- **Cross-Format Dependencies**: Packages can depend on packages from different formats
- **Security Isolation**: Each package format operates in its own security context
- **Performance Optimization**: Intelligent caching and parallel operations
- **Backward Compatibility**: Full compatibility with existing OpenWrt packages

## Supported Package Formats

### 1. Rust Native Packages

**Format**: JSON-based metadata with Rust binary
**Extension**: `.rust-pkg`
**Security**: Highest trust level, direct integration

```json
{
    "name": "rust-network-manager",
    "version": "1.0.0",
    "description": "High-performance network manager in Rust",
    "dependencies": ["libc >= 2.28"],
    "provides": ["network-manager"],
    "format": "rust-native"
}
```

### 2. IPK Packages (OpenWrt Standard)

**Format**: AR archive with control files
**Extension**: `.ipk`
**Security**: Medium trust level, sandboxed execution

```
Package: luci-app-firewall
Version: git-23.093.47327-2073b5a
Depends: luci-base, firewall4
Architecture: all
Description: Firewall configuration module
```

### 3. APK Packages (Future Support)

**Format**: Alpine Package Keeper format
**Extension**: `.apk`
**Security**: Medium trust level, sandboxed execution

```
C:Q1abcd1234567890abcd1234567890abcd1234567890
P:example-package
V:1.0.0-r0
A:x86_64
S:1024
I:2048
T:Example package description
```

## Package Installation

### Basic Installation

```bash
# Install a Rust native package
opkg-rust install rust-network-manager

# Install an IPK package
opkg-rust install luci-app-firewall

# Install multiple packages
opkg-rust install rust-network-manager luci-app-firewall
```

### Advanced Installation Options

```bash
# Install with specific version
opkg-rust install rust-network-manager=1.0.0

# Install from specific repository
opkg-rust install --repo=custom rust-network-manager

# Install with dependency resolution disabled
opkg-rust install --no-deps rust-network-manager

# Install in test mode (dry run)
opkg-rust install --test rust-network-manager
```

### Batch Installation

```bash
# Install from package list file
opkg-rust install --file packages.txt

# Install all packages from repository
opkg-rust install --repo=custom --all
```

## Dependency Management

### Cross-Format Dependencies

The hybrid system supports dependencies across different package formats:

```json
{
    "name": "rust-web-interface",
    "dependencies": [
        "rust-http-server >= 2.0.0",
        "luci-base >= 1.0.0",
        "openssl >= 1.1.1"
    ]
}
```

### Dependency Resolution Algorithm

1. **Parse Dependencies**: Extract all dependencies from target package
2. **Format Detection**: Identify the format of each dependency
3. **Availability Check**: Verify availability in configured repositories
4. **Conflict Detection**: Check for package conflicts and provides relationships
5. **Resolution Order**: Determine optimal installation order
6. **Security Validation**: Verify signatures and trust levels

### Conflict Resolution

```bash
# List conflicts
opkg-rust conflicts rust-network-manager

# Resolve conflicts interactively
opkg-rust install --interactive rust-network-manager

# Force installation (override conflicts)
opkg-rust install --force rust-network-manager
```

## Repository Configuration

### Repository Types

1. **Rust Native Repositories**: JSON-based package indexes
2. **IPK Repositories**: Traditional OpenWrt package feeds
3. **APK Repositories**: Alpine-style repositories (future)

### Configuration File

Location: `/etc/opkg-rust/repositories.conf`

```ini
# Rust native repository
src rust-main https://packages.openwrt-rust.org/main
src rust-community https://packages.openwrt-rust.org/community

# IPK repositories
src/gz openwrt_core https://downloads.openwrt.org/releases/packages-24.10/x86_64/base
src/gz openwrt_packages https://downloads.openwrt.org/releases/packages-24.10/x86_64/packages

# APK repositories (future)
src apk-main https://packages.openwrt-rust.org/apk/main
```

### Repository Management

```bash
# List configured repositories
opkg-rust repo list

# Add repository
opkg-rust repo add rust-testing https://packages.openwrt-rust.org/testing

# Remove repository
opkg-rust repo remove rust-testing

# Update repository indexes
opkg-rust update

# Update specific repository
opkg-rust update rust-main
```

## Security Model

### Trust Levels

1. **High Trust**: Rust native packages with verified signatures
2. **Medium Trust**: IPK/APK packages with valid signatures
3. **Low Trust**: Unsigned packages (disabled by default)

### Security Features

- **Package Signing**: All packages must be cryptographically signed
- **Certificate Validation**: Full certificate chain validation
- **Sandbox Execution**: Non-native packages run in sandboxed environments
- **Permission Model**: Fine-grained permission control for package operations
- **Audit Logging**: Comprehensive logging of all package operations

### Security Configuration

```bash
# Enable/disable signature verification
opkg-rust config set verify_signatures true

# Set minimum trust level
opkg-rust config set min_trust_level medium

# Configure certificate authorities
opkg-rust config set ca_bundle /etc/ssl/certs/ca-certificates.crt
```

## Performance Considerations

### Optimization Features

- **Parallel Downloads**: Concurrent package downloads
- **Delta Updates**: Only download changed portions of packages
- **Compression**: Intelligent compression for package storage
- **Caching**: Multi-level caching for metadata and packages
- **Lazy Loading**: Load package information on demand

### Performance Tuning

```bash
# Set parallel download workers
opkg-rust config set parallel_downloads 4

# Configure cache size
opkg-rust config set cache_size 512MB

# Enable compression
opkg-rust config set compress_cache true

# Set network timeout
opkg-rust config set network_timeout 30
```

### Performance Monitoring

```bash
# Show performance statistics
opkg-rust stats

# Monitor real-time performance
opkg-rust monitor

# Generate performance report
opkg-rust report --performance
```

## Migration from Legacy Systems

### Automatic Migration

```bash
# Migrate from opkg
opkg-rust migrate --from=opkg

# Migrate specific packages
opkg-rust migrate --packages="luci-base,firewall4"

# Dry run migration
opkg-rust migrate --test --from=opkg
```

### Manual Migration

1. **Export Package List**: `opkg list-installed > packages.txt`
2. **Convert Format**: `opkg-rust convert packages.txt`
3. **Install Packages**: `opkg-rust install --file converted-packages.txt`
4. **Verify Installation**: `opkg-rust verify --all`

### Migration Validation

```bash
# Validate migration
opkg-rust validate-migration

# Compare with original system
opkg-rust compare --baseline=opkg

# Generate migration report
opkg-rust report --migration
```

## Troubleshooting

### Common Issues

#### Package Not Found
```bash
# Update repository indexes
opkg-rust update

# Search for package
opkg-rust search package-name

# Check repository configuration
opkg-rust repo list
```

#### Dependency Conflicts
```bash
# Show dependency tree
opkg-rust depends package-name

# List conflicts
opkg-rust conflicts package-name

# Resolve interactively
opkg-rust install --interactive package-name
```

#### Signature Verification Failures
```bash
# Check certificate configuration
opkg-rust config get ca_bundle

# Verify package signature manually
opkg-rust verify package-name

# Temporarily disable verification (not recommended)
opkg-rust install --no-verify package-name
```

### Diagnostic Tools

```bash
# System health check
opkg-rust doctor

# Detailed diagnostics
opkg-rust diagnose --verbose

# Generate support bundle
opkg-rust support-bundle
```

## API Reference

### Core Functions

#### Package Installation
```rust
use openwrt_rust::package_manager::HybridPackageManager;

let mut pm = HybridPackageManager::new()?;
pm.install("rust-network-manager").await?;
```

#### Repository Management
```rust
use openwrt_rust::package_manager::Repository;

let repo = Repository::new("https://packages.example.com")?;
pm.add_repository(repo)?;
```

#### Dependency Resolution
```rust
let deps = pm.resolve_dependencies("package-name").await?;
for dep in deps {
    println!("Dependency: {} ({})", dep.name, dep.format);
}
```

### Configuration API

```rust
use openwrt_rust::package_manager::Config;

let mut config = Config::load()?;
config.set("verify_signatures", true)?;
config.save()?;
```

### Security API

```rust
use openwrt_rust::package_manager::Security;

let security = Security::new()?;
let verified = security.verify_package("package.ipk").await?;
```

## Best Practices

### Package Development

1. **Use Semantic Versioning**: Follow semver for all package versions
2. **Minimize Dependencies**: Keep dependency lists as small as possible
3. **Test Cross-Format**: Test packages with dependencies from different formats
4. **Document APIs**: Provide comprehensive API documentation
5. **Security First**: Always sign packages and validate dependencies

### Repository Management

1. **Regular Updates**: Update repository indexes regularly
2. **Mirror Repositories**: Use local mirrors for better performance
3. **Backup Configurations**: Backup repository configurations
4. **Monitor Health**: Monitor repository health and availability
5. **Security Scanning**: Regularly scan repositories for vulnerabilities

### System Administration

1. **Regular Maintenance**: Perform regular system maintenance
2. **Monitor Performance**: Monitor package management performance
3. **Security Updates**: Apply security updates promptly
4. **Backup System**: Backup system state before major changes
5. **Documentation**: Maintain up-to-date documentation

---

**Version**: 1.0  
**Last Updated**: January 27, 2025  
**Status**: Phase 11.6 - Documentation and Migration
