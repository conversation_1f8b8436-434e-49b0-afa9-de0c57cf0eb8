# Developer Workflows for Hybrid Package Management

## Overview

This guide provides comprehensive workflows for developers working with the hybrid package management system, covering development, testing, deployment, and maintenance of both Rust native and cross-format packages.

## Table of Contents

1. [Development Environment Setup](#development-environment-setup)
2. [Package Development Workflows](#package-development-workflows)
3. [Cross-Format Development](#cross-format-development)
4. [Testing Workflows](#testing-workflows)
5. [Deployment and Release](#deployment-and-release)
6. [Maintenance and Updates](#maintenance-and-updates)
7. [IDE Integration](#ide-integration)
8. [Automation and CI/CD](#automation-and-cicd)

## Development Environment Setup

### Quick Setup Script

```bash
#!/bin/bash
# setup-dev-environment.sh

# Clone repository
git clone https://github.com/openwrt/openwrt-rust-baremetal.git
cd openwrt-rust-baremetal

# Install Rust toolchain
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# Install development tools
rustup component add rustfmt clippy rust-analyzer

# Install system dependencies
sudo apt-get update
sudo apt-get install -y build-essential pkg-config libssl-dev \
    qemu-system-x86 podman jq bc

# Set up testing environments
./vm-testing/scripts/vm-setup.sh
./container-testing/scripts/container-manager.sh setup
./phase11-testing/run-all-tests.sh setup

# Configure development environment
cp .env.example .env
echo "Development environment setup complete!"
```

### IDE Configuration

#### VS Code Setup

Create `.vscode/settings.json`:

```json
{
    "rust-analyzer.cargo.features": ["development"],
    "rust-analyzer.checkOnSave.command": "clippy",
    "rust-analyzer.rustfmt.rangeFormatting.enable": true,
    "files.associations": {
        "*.rs": "rust",
        "*.toml": "toml"
    },
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.fixAll.rust-analyzer": true
    }
}
```

Create `.vscode/tasks.json`:

```json
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "Build",
            "type": "cargo",
            "command": "build",
            "group": "build"
        },
        {
            "label": "Test",
            "type": "cargo",
            "command": "test",
            "group": "test"
        },
        {
            "label": "Run Hybrid Tests",
            "type": "shell",
            "command": "./phase11-testing/run-all-tests.sh",
            "group": "test"
        }
    ]
}
```

## Package Development Workflows

### Workflow 1: New Rust Native Package

#### Step 1: Create Package Structure

```bash
# Create new package
cargo new --bin my-network-tool
cd my-network-tool

# Add OpenWrt dependencies
cat >> Cargo.toml << 'EOF'
[dependencies]
openwrt-rust = { path = "../openwrt-rust-framework" }
serde = { version = "1.0", features = ["derive"] }
tokio = { version = "1.0", features = ["rt-multi-thread", "net"] }
EOF
```

#### Step 2: Implement Core Functionality

```rust
// src/main.rs
use openwrt_rust::prelude::*;
use openwrt_rust::network::NetworkInterface;
use std::error::Error;

#[tokio::main]
async fn main() -> Result<(), Box<dyn Error>> {
    // Initialize OpenWrt integration
    let mut network = NetworkInterface::new("eth0")?;
    
    // Implement your tool logic
    network.configure_ip("*************/24").await?;
    
    println!("Network tool configured successfully");
    Ok(())
}
```

#### Step 3: Add Package Metadata

```json
// package.json
{
    "name": "my-network-tool",
    "version": "1.0.0",
    "description": "Advanced network configuration tool",
    "author": "Developer Name <<EMAIL>>",
    "license": "GPL-2.0",
    "dependencies": ["libc >= 2.28"],
    "provides": ["network-tool"],
    "format": "rust-native",
    "category": "network"
}
```

#### Step 4: Development Testing

```bash
# Build and test locally
cargo build
cargo test

# Test with hybrid package manager
opkg-rust install --local target/release/my-network-tool

# Run integration tests
./scripts/test-package.sh my-network-tool
```

### Workflow 2: Cross-Format Package Development

#### Scenario: Rust Package with IPK Dependencies

```rust
// Cargo.toml
[dependencies]
openwrt-rust = { path = "../openwrt-rust-framework" }
# Note: IPK dependencies declared in package.json
```

```json
// package.json
{
    "name": "rust-web-interface",
    "version": "1.0.0",
    "dependencies": [
        "rust-http-server >= 2.0.0",
        "luci-base >= 1.0.0",
        "openssl >= 1.1.1"
    ],
    "cross_format_deps": {
        "luci-base": {
            "format": "ipk",
            "provides": ["web-interface-base"]
        }
    }
}
```

#### Integration Code

```rust
// src/luci_integration.rs
use openwrt_rust::ffi::luci;
use std::ffi::CString;

pub struct LuciIntegration {
    context: *mut luci::LuciContext,
}

impl LuciIntegration {
    pub fn new() -> Result<Self, Box<dyn std::error::Error>> {
        let context = unsafe {
            luci::luci_init()
        };
        
        if context.is_null() {
            return Err("Failed to initialize LuCI integration".into());
        }
        
        Ok(LuciIntegration { context })
    }
    
    pub fn register_module(&self, name: &str) -> Result<(), Box<dyn std::error::Error>> {
        let c_name = CString::new(name)?;
        let result = unsafe {
            luci::luci_register_module(self.context, c_name.as_ptr())
        };
        
        if result != 0 {
            return Err("Failed to register LuCI module".into());
        }
        
        Ok(())
    }
}
```

## Cross-Format Development

### Best Practices

#### 1. Dependency Management

```bash
# Check cross-format dependencies
opkg-rust depends my-package --cross-format

# Test dependency resolution
opkg-rust install my-package --test --verbose

# Validate compatibility
opkg-rust compat-check my-package
```

#### 2. Interface Design

```rust
// Define clear interfaces for cross-format interaction
pub trait WebInterface {
    fn register_handler(&mut self, path: &str, handler: Box<dyn Handler>);
    fn start_server(&self) -> Result<(), ServerError>;
}

// Implement for both Rust and FFI backends
impl WebInterface for RustWebServer { /* ... */ }
impl WebInterface for LuciWebServer { /* ... */ }
```

#### 3. Configuration Management

```rust
// Use unified configuration approach
use openwrt_rust::config::{Config, ConfigValue};

let config = Config::load("my-package")?;
let web_port: u16 = config.get("web_port")?.try_into()?;
let luci_enabled: bool = config.get("luci_integration")?.try_into()?;
```

## Testing Workflows

### Local Testing

```bash
# Unit tests
cargo test

# Integration tests
cargo test --test integration

# Package-specific tests
./scripts/test-package.sh my-package

# Cross-format compatibility tests
./phase11-testing/package-compatibility/format-validation-suite.sh
```

### VM Testing

```bash
# Start VM testing environment
cd vm-testing
./scripts/vm-start.sh

# Deploy package to VM
./scripts/deploy-package.sh my-package

# Run tests in VM
./scripts/vm-test.sh my-package

# Collect results
./scripts/collect-results.sh
```

### Container Testing

```bash
# Start container environment
cd container-testing
./scripts/container-manager.sh start

# Test package in container
./scripts/test-in-container.sh my-package

# Performance testing
./scripts/performance-test.sh my-package
```

### Comprehensive Testing

```bash
# Run full test suite
./phase11-testing/run-all-tests.sh

# Test specific scenarios
./phase11-testing/integration-testing/integration-test-runner.sh

# Performance validation
./phase11-testing/performance-validation/performance-test-suite.sh

# Security testing
./phase11-testing/security-validation/security-test-framework.sh
```

## Deployment and Release

### Pre-Release Checklist

```bash
# Code quality checks
cargo fmt --check
cargo clippy -- -D warnings
cargo audit

# Comprehensive testing
./phase11-testing/run-all-tests.sh

# Documentation update
cargo doc --no-deps
./scripts/update-docs.sh

# Version bump
./scripts/bump-version.sh 1.1.0
```

### Package Building

```bash
# Build release version
cargo build --release

# Create package archive
./scripts/package/create-package.sh my-package

# Sign package
./scripts/package/sign-package.sh my-package.rust-pkg

# Validate package
opkg-rust validate my-package.rust-pkg
```

### Repository Submission

```bash
# Submit to testing repository
./scripts/submit-package.sh my-package.rust-pkg --repo=testing

# After testing, submit to main repository
./scripts/submit-package.sh my-package.rust-pkg --repo=main

# Update package index
./scripts/update-package-index.sh
```

## Maintenance and Updates

### Update Workflow

```bash
# Check for updates
opkg-rust list-upgradable

# Update dependencies
cargo update

# Test with new dependencies
cargo test
./phase11-testing/run-all-tests.sh

# Update package version
./scripts/bump-version.sh 1.1.1

# Release update
./scripts/release-update.sh
```

### Security Updates

```bash
# Security audit
cargo audit

# Check for vulnerabilities
./scripts/security-scan.sh my-package

# Apply security patches
./scripts/apply-security-patches.sh

# Emergency release
./scripts/emergency-release.sh my-package
```

## IDE Integration

### Rust Analyzer Configuration

```toml
# .cargo/config.toml
[build]
target-dir = "target"

[env]
OPENWRT_TARGET = "x86_64-unknown-linux-gnu"
OPENWRT_SYSROOT = "/opt/openwrt-sysroot"

[target.x86_64-unknown-linux-gnu]
linker = "x86_64-linux-gnu-gcc"
```

### Debug Configuration

```json
// .vscode/launch.json
{
    "version": "0.2.0",
    "configurations": [
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug Package",
            "cargo": {
                "args": ["build", "--bin=my-package"],
                "filter": {
                    "name": "my-package",
                    "kind": "bin"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}",
            "environment": [
                {
                    "name": "OPENWRT_ENV",
                    "value": "development"
                }
            ]
        }
    ]
}
```

## Automation and CI/CD

### GitHub Actions Workflow

```yaml
# .github/workflows/package-ci.yml
name: Package CI

on:
  push:
    paths: ['packages/**']
  pull_request:
    paths: ['packages/**']

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Rust
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        
    - name: Build package
      run: cargo build --release
      
    - name: Run tests
      run: cargo test
      
    - name: Run hybrid tests
      run: ./phase11-testing/run-all-tests.sh
      
    - name: Package validation
      run: ./scripts/validate-package.sh
```

### Automated Testing

```bash
# scripts/automated-test.sh
#!/bin/bash

set -euo pipefail

# Build package
cargo build --release

# Run unit tests
cargo test

# Run integration tests
./phase11-testing/integration-testing/integration-test-runner.sh

# Performance validation
./phase11-testing/performance-validation/performance-test-suite.sh

# Security validation
./phase11-testing/security-validation/security-test-framework.sh

# Generate report
./scripts/generate-test-report.sh
```

### Release Automation

```bash
# scripts/automated-release.sh
#!/bin/bash

VERSION=$1

# Validate version
./scripts/validate-version.sh "$VERSION"

# Update version
./scripts/bump-version.sh "$VERSION"

# Run full test suite
./phase11-testing/run-all-tests.sh

# Build and package
cargo build --release
./scripts/package/create-package.sh

# Sign and validate
./scripts/package/sign-package.sh
./scripts/package/validate-package.sh

# Submit to repository
./scripts/submit-package.sh --repo=main

# Create GitHub release
./scripts/create-github-release.sh "$VERSION"
```

## Development Tips

### Performance Optimization

```rust
// Use appropriate data structures
use std::collections::HashMap;
use heapless::FnvIndexMap; // For no-std environments

// Profile critical paths
#[cfg(feature = "profiling")]
use pprof;

// Optimize for embedded targets
#[cfg(target_arch = "mips")]
fn optimized_function() {
    // MIPS-specific optimizations
}
```

### Error Handling

```rust
// Use custom error types
#[derive(Debug, thiserror::Error)]
pub enum PackageError {
    #[error("Network error: {0}")]
    Network(#[from] std::io::Error),
    
    #[error("Configuration error: {message}")]
    Config { message: String },
}

// Implement proper error propagation
pub fn install_package(name: &str) -> Result<(), PackageError> {
    let config = load_config()
        .map_err(|e| PackageError::Config { 
            message: format!("Failed to load config: {}", e) 
        })?;
    
    // Implementation
    Ok(())
}
```

### Logging and Debugging

```rust
// Use structured logging
use tracing::{info, warn, error, debug};

#[tracing::instrument]
pub async fn install_package(name: &str) -> Result<(), PackageError> {
    info!("Installing package: {}", name);
    
    // Implementation with debug logging
    debug!("Checking dependencies for {}", name);
    
    Ok(())
}
```

---

**Version**: 1.0  
**Last Updated**: January 27, 2025  
**Status**: Phase 11.6 - Documentation and Migration
