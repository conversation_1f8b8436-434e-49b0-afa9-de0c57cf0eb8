# Hybrid Package Management - Troubleshooting Guide

## Overview

This guide provides comprehensive troubleshooting procedures for the hybrid package management system, covering common issues, automated diagnostics, and resolution strategies for mixed package environments.

## Table of Contents

1. [Quick Diagnostics](#quick-diagnostics)
2. [Package Installation Issues](#package-installation-issues)
3. [Dependency Resolution Problems](#dependency-resolution-problems)
4. [Cross-Format Compatibility Issues](#cross-format-compatibility-issues)
5. [Repository Configuration Problems](#repository-configuration-problems)
6. [Security and Signature Issues](#security-and-signature-issues)
7. [Performance Problems](#performance-problems)
8. [Migration Issues](#migration-issues)
9. [Automated Diagnostic Tools](#automated-diagnostic-tools)
10. [Emergency Recovery Procedures](#emergency-recovery-procedures)

## Quick Diagnostics

### System Health Check

Run the comprehensive system health check:

```bash
# Quick system health check
opkg-rust doctor

# Detailed diagnostics
opkg-rust doctor --verbose

# Generate diagnostic report
opkg-rust doctor --report
```

### Common Quick Fixes

```bash
# Update repository indexes
opkg-rust update

# Clear package cache
opkg-rust clean

# Rebuild package database
opkg-rust rebuild-db

# Reset configuration to defaults
opkg-rust config reset
```

## Package Installation Issues

### Issue: Package Not Found

**Symptoms:**
- Error: "Package 'package-name' not found"
- Package appears in search but installation fails

**Diagnosis:**
```bash
# Check repository configuration
opkg-rust repo list

# Search for package in all repositories
opkg-rust search package-name --all-repos

# Check package availability by format
opkg-rust search package-name --format=rust-native
opkg-rust search package-name --format=ipk
```

**Solutions:**

1. **Update Repository Indexes:**
   ```bash
   opkg-rust update
   opkg-rust search package-name
   ```

2. **Check Repository Configuration:**
   ```bash
   # List configured repositories
   opkg-rust repo list
   
   # Add missing repository
   opkg-rust repo add repo-name https://repository-url
   ```

3. **Verify Package Format:**
   ```bash
   # Check if package exists in different format
   opkg-rust search package-name --all-formats
   
   # Install with specific format
   opkg-rust install package-name --format=ipk
   ```

### Issue: Installation Fails with Errors

**Symptoms:**
- Installation starts but fails partway through
- Error messages about file conflicts or permissions

**Diagnosis:**
```bash
# Check installation logs
opkg-rust log --installation

# Verify package integrity
opkg-rust verify package-name

# Check available disk space
df -h /

# Check permissions
opkg-rust check-permissions
```

**Solutions:**

1. **Resolve File Conflicts:**
   ```bash
   # List conflicting files
   opkg-rust conflicts package-name
   
   # Force installation (use with caution)
   opkg-rust install package-name --force
   
   # Resolve conflicts interactively
   opkg-rust install package-name --interactive
   ```

2. **Fix Permission Issues:**
   ```bash
   # Fix package manager permissions
   sudo opkg-rust fix-permissions
   
   # Reset package database permissions
   sudo chmod -R 644 /var/lib/opkg-rust/
   sudo chmod 755 /var/lib/opkg-rust/
   ```

## Dependency Resolution Problems

### Issue: Unresolvable Dependencies

**Symptoms:**
- Error: "Cannot resolve dependencies for package-name"
- Circular dependency errors
- Cross-format dependency conflicts

**Diagnosis:**
```bash
# Show dependency tree
opkg-rust depends package-name --tree

# Check for circular dependencies
opkg-rust depends package-name --check-circular

# Analyze cross-format dependencies
opkg-rust depends package-name --cross-format
```

**Solutions:**

1. **Resolve Cross-Format Dependencies:**
   ```bash
   # Enable cross-format dependency resolution
   opkg-rust config set allow_cross_format true
   
   # Install dependencies manually
   opkg-rust install dependency1 dependency2
   opkg-rust install package-name
   ```

2. **Handle Circular Dependencies:**
   ```bash
   # Break circular dependency
   opkg-rust install package-a --no-deps
   opkg-rust install package-b --no-deps
   opkg-rust install package-c
   ```

3. **Update Dependency Mappings:**
   ```bash
   # Update dependency database
   opkg-rust update-deps
   
   # Rebuild dependency cache
   opkg-rust rebuild-deps
   ```

### Issue: Version Conflicts

**Symptoms:**
- Error: "Version conflict for package-name"
- Multiple versions of same package required

**Diagnosis:**
```bash
# Show version conflicts
opkg-rust conflicts package-name --versions

# List installed versions
opkg-rust list-installed --versions

# Check version requirements
opkg-rust depends package-name --versions
```

**Solutions:**

1. **Resolve Version Conflicts:**
   ```bash
   # Upgrade conflicting packages
   opkg-rust upgrade conflicting-package
   
   # Install specific version
   opkg-rust install package-name=1.2.3
   
   # Allow multiple versions (if supported)
   opkg-rust install package-name --allow-multiple
   ```

## Cross-Format Compatibility Issues

### Issue: Format Incompatibility

**Symptoms:**
- Rust native package cannot use IPK dependency
- IPK package fails to recognize Rust native provides

**Diagnosis:**
```bash
# Check format compatibility
opkg-rust compat-check package-name

# List format mappings
opkg-rust format-mappings

# Test cross-format integration
opkg-rust test-integration package-name
```

**Solutions:**

1. **Update Format Mappings:**
   ```bash
   # Update compatibility database
   opkg-rust update-compat
   
   # Add manual mapping
   opkg-rust map-package rust-package ipk-equivalent
   ```

2. **Use Compatibility Layer:**
   ```bash
   # Enable compatibility layer
   opkg-rust config set compat_layer true
   
   # Install with compatibility wrapper
   opkg-rust install package-name --compat
   ```

### Issue: Provides/Conflicts Resolution

**Symptoms:**
- Multiple packages provide same functionality
- Conflicts between different format packages

**Diagnosis:**
```bash
# List provides relationships
opkg-rust provides functionality-name

# Check conflicts
opkg-rust conflicts --provides functionality-name

# Show package relationships
opkg-rust relationships package-name
```

**Solutions:**

1. **Resolve Provides Conflicts:**
   ```bash
   # Choose preferred provider
   opkg-rust prefer rust-package over ipk-package
   
   # Install specific provider
   opkg-rust install package-name --provider=rust-native
   ```

## Repository Configuration Problems

### Issue: Repository Access Failures

**Symptoms:**
- Cannot connect to repository
- SSL/TLS certificate errors
- Authentication failures

**Diagnosis:**
```bash
# Test repository connectivity
opkg-rust repo test repo-name

# Check SSL certificates
opkg-rust repo check-ssl repo-name

# Verify authentication
opkg-rust repo auth-test repo-name
```

**Solutions:**

1. **Fix Connectivity Issues:**
   ```bash
   # Update repository URL
   opkg-rust repo update repo-name https://new-url
   
   # Configure proxy if needed
   opkg-rust config set proxy http://proxy:8080
   
   # Test with different DNS
   opkg-rust repo test repo-name --dns=*******
   ```

2. **Resolve SSL Issues:**
   ```bash
   # Update CA certificates
   sudo opkg-rust update-ca-certs
   
   # Disable SSL verification (temporary)
   opkg-rust repo config repo-name ssl_verify false
   
   # Use specific CA bundle
   opkg-rust config set ca_bundle /path/to/ca-bundle.crt
   ```

## Security and Signature Issues

### Issue: Signature Verification Failures

**Symptoms:**
- Error: "Package signature verification failed"
- Untrusted package warnings
- Certificate validation errors

**Diagnosis:**
```bash
# Check signature status
opkg-rust verify package-name

# List trusted certificates
opkg-rust certs list

# Check certificate validity
opkg-rust certs check certificate-name
```

**Solutions:**

1. **Update Certificates:**
   ```bash
   # Update certificate store
   sudo opkg-rust certs update
   
   # Import new certificate
   opkg-rust certs import certificate.pem
   
   # Trust specific certificate
   opkg-rust certs trust certificate-name
   ```

2. **Handle Unsigned Packages:**
   ```bash
   # Temporarily allow unsigned packages
   opkg-rust config set allow_unsigned true
   
   # Install specific unsigned package
   opkg-rust install package-name --allow-unsigned
   
   # Sign package manually
   opkg-rust sign package-name --key=signing-key
   ```

## Performance Problems

### Issue: Slow Package Operations

**Symptoms:**
- Package installation takes very long
- Repository updates are slow
- High CPU/memory usage during operations

**Diagnosis:**
```bash
# Monitor performance
opkg-rust monitor --real-time

# Generate performance report
opkg-rust perf-report

# Check resource usage
opkg-rust stats --resources
```

**Solutions:**

1. **Optimize Performance:**
   ```bash
   # Increase parallel downloads
   opkg-rust config set parallel_downloads 8
   
   # Enable compression
   opkg-rust config set compress_cache true
   
   # Optimize cache settings
   opkg-rust config set cache_size 1GB
   ```

2. **Reduce Resource Usage:**
   ```bash
   # Limit memory usage
   opkg-rust config set max_memory 512MB
   
   # Reduce parallel operations
   opkg-rust config set parallel_downloads 2
   
   # Clean unnecessary cache
   opkg-rust clean --aggressive
   ```

## Migration Issues

### Issue: Migration Failures

**Symptoms:**
- Migration wizard fails partway through
- Packages missing after migration
- Configuration not preserved

**Diagnosis:**
```bash
# Check migration status
./scripts/migration/check-migration-status.sh

# Validate migrated packages
./scripts/migration/validate-migration.sh

# Compare with original system
./scripts/migration/compare-systems.sh
```

**Solutions:**

1. **Resume Failed Migration:**
   ```bash
   # Resume from last checkpoint
   ./scripts/migration/resume-migration.sh
   
   # Retry specific step
   ./scripts/migration/retry-step.sh step-number
   
   # Force complete migration
   ./scripts/migration/force-complete.sh
   ```

2. **Fix Missing Packages:**
   ```bash
   # Identify missing packages
   ./scripts/migration/find-missing.sh
   
   # Install missing packages
   ./scripts/migration/install-missing.sh
   
   # Restore from backup
   ./scripts/migration/restore-packages.sh
   ```

## Automated Diagnostic Tools

### System Doctor

```bash
# Comprehensive system check
opkg-rust doctor

# Check specific component
opkg-rust doctor --component=repositories
opkg-rust doctor --component=packages
opkg-rust doctor --component=security

# Generate detailed report
opkg-rust doctor --report --output=/tmp/diagnostic-report.json
```

### Package Validator

```bash
# Validate all installed packages
opkg-rust validate --all

# Validate specific package
opkg-rust validate package-name

# Deep validation with integrity check
opkg-rust validate package-name --deep
```

### Performance Profiler

```bash
# Profile package operations
opkg-rust profile install package-name

# Monitor system performance
opkg-rust monitor --duration=60s

# Generate performance baseline
opkg-rust baseline --create
```

## Emergency Recovery Procedures

### System Recovery

```bash
# Emergency system recovery
./scripts/emergency/system-recovery.sh

# Restore from backup
./scripts/emergency/restore-backup.sh backup-path

# Reset to factory defaults
./scripts/emergency/factory-reset.sh
```

### Package Database Recovery

```bash
# Rebuild package database
opkg-rust rebuild-db --force

# Restore package database from backup
opkg-rust restore-db backup-file

# Reinitialize package system
opkg-rust init --force
```

### Configuration Recovery

```bash
# Reset configuration to defaults
opkg-rust config reset --all

# Restore configuration from backup
opkg-rust config restore backup-file

# Repair corrupted configuration
opkg-rust config repair
```

## Getting Help

### Support Channels

1. **Documentation**: Check comprehensive documentation
2. **Community Forum**: Post questions on community forum
3. **Issue Tracker**: Report bugs on GitHub issue tracker
4. **Professional Support**: Contact professional support services

### Creating Support Requests

```bash
# Generate support bundle
opkg-rust support-bundle

# Create diagnostic report
opkg-rust diagnose --comprehensive

# Export system information
opkg-rust sysinfo --export
```

### Useful Information to Include

- System information (`opkg-rust sysinfo`)
- Error messages and logs
- Steps to reproduce the issue
- Expected vs actual behavior
- Recent changes to the system

---

**Version**: 1.0  
**Last Updated**: January 27, 2025  
**Status**: Phase 11.6 - Documentation and Migration
