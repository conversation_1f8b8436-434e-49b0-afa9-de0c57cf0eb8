# OpenWrt Rust API Reference

## Overview

This document provides a comprehensive reference for all public APIs in the OpenWrt Rust implementation. APIs are organized by module and include function signatures, parameters, return values, and usage examples.

## Table of Contents

1. [Memory Management APIs](#memory-management-apis)
2. [Network Stack APIs](#network-stack-apis)
3. [Device Driver APIs](#device-driver-apis)
4. [Filesystem APIs](#filesystem-apis)
5. [Configuration Management APIs](#configuration-management-apis)
6. [Security Framework APIs](#security-framework-apis)
7. [Performance Monitoring APIs](#performance-monitoring-apis)
8. [Testing Framework APIs](#testing-framework-apis)

## Memory Management APIs

### `allocator` Module

#### `get_stats() -> AllocatorStats`

Returns current allocator statistics.

**Returns:**
- `AllocatorStats`: Structure containing memory usage information

**Example:**
```rust
let stats = crate::allocator::get_stats();
println!("Memory usage: {}/{} bytes", stats.current_usage, stats.total_size);
```

#### `allocate(size: usize) -> Option<*mut u8>`

Allocates memory of the specified size.

**Parameters:**
- `size`: Number of bytes to allocate

**Returns:**
- `Some(*mut u8)`: Pointer to allocated memory on success
- `None`: If allocation fails

**Example:**
```rust
let ptr = crate::allocator::allocate(1024);
if let Some(ptr) = ptr {
    // Use memory
    crate::allocator::deallocate(ptr, 1024);
}
```

#### `deallocate(ptr: *mut u8, size: usize)`

Deallocates previously allocated memory.

**Parameters:**
- `ptr`: Pointer to memory to deallocate
- `size`: Size of the memory block

**Example:**
```rust
crate::allocator::deallocate(ptr, 1024);
```

#### `optimize_memory() -> Result<(), &'static str>`

Optimizes memory allocation and reduces fragmentation.

**Returns:**
- `Ok(())`: On successful optimization
- `Err(&'static str)`: Error message on failure

#### `is_memory_critical() -> bool`

Checks if memory usage is at critical levels.

**Returns:**
- `true`: If memory is critically low
- `false`: If memory usage is acceptable

### `memory` Module

#### `get_memory_usage() -> MemoryUsage`

Returns detailed memory usage information.

**Returns:**
- `MemoryUsage`: Structure with total, allocated, and free memory

**Example:**
```rust
let usage = crate::memory::get_memory_usage();
println!("Free memory: {} bytes", usage.free_bytes);
```

## Network Stack APIs

### `network` Module

#### `get_statistics() -> NetworkStatistics`

Returns network stack statistics.

**Returns:**
- `NetworkStatistics`: Network initialization and operation status

#### `optimize_throughput() -> Result<(), &'static str>`

Optimizes network throughput performance.

**Returns:**
- `Ok(())`: On successful optimization
- `Err(&'static str)`: Error message on failure

### `network::device` Module

#### `VirtualDevice::new() -> VirtualDevice`

Creates a new virtual network device for testing.

**Returns:**
- `VirtualDevice`: New virtual device instance

#### `VirtualDevice::inject_packet(&self, packet: &[u8]) -> Result<(), &'static str>`

Injects a packet into the virtual device.

**Parameters:**
- `packet`: Byte slice containing packet data

**Returns:**
- `Ok(())`: On successful injection
- `Err(&'static str)`: Error message on failure

#### `VirtualDevice::extract_packet(&self) -> Option<Vec<u8>>`

Extracts a packet from the virtual device.

**Returns:**
- `Some(Vec<u8>)`: Packet data if available
- `None`: If no packet is available

### `network::protocols` Module

#### `get_statistics() -> ProtocolStatistics`

Returns network protocol statistics.

**Returns:**
- `ProtocolStatistics`: Protocol initialization and operation status

### `network::interface_manager` Module

#### `get_manager_statistics() -> InterfaceManagerStatistics`

Returns interface manager statistics.

**Returns:**
- `InterfaceManagerStatistics`: Interface management status and counts

## Device Driver APIs

### `drivers` Module

#### `device_manager() -> &'static DeviceManager`

Returns reference to the global device manager.

**Returns:**
- `&'static DeviceManager`: Reference to device manager

#### `DeviceManager::device_count(&self) -> usize`

Returns the total number of registered devices.

**Returns:**
- `usize`: Number of devices

#### `DeviceManager::list_devices_by_type(&self, device_type: DeviceType) -> Vec<DeviceId>`

Lists devices of a specific type.

**Parameters:**
- `device_type`: Type of devices to list

**Returns:**
- `Vec<DeviceId>`: List of device identifiers

**Example:**
```rust
let manager = crate::drivers::device_manager();
let network_devices = manager.list_devices_by_type(crate::drivers::DeviceType::Network);
```

### `drivers::hal` Module

#### `get_hal() -> &'static HardwareAbstractionLayer`

Returns reference to the hardware abstraction layer.

**Returns:**
- `&'static HardwareAbstractionLayer`: Reference to HAL

#### `HardwareAbstractionLayer::is_initialized(&self) -> bool`

Checks if the HAL is properly initialized.

**Returns:**
- `bool`: True if initialized, false otherwise

## Filesystem APIs

### `filesystem` Module

#### `get_statistics() -> FilesystemStatistics`

Returns filesystem statistics.

**Returns:**
- `FilesystemStatistics`: Filesystem initialization and operation status

### `filesystem::vfs` Module

#### `get_statistics() -> VfsStatistics`

Returns virtual filesystem statistics.

**Returns:**
- `VfsStatistics`: VFS operation status

### `filesystem::jffs2` Module

#### `get_statistics() -> Jffs2Statistics`

Returns JFFS2 filesystem statistics.

**Returns:**
- `Jffs2Statistics`: JFFS2 operation status

### `filesystem::overlay` Module

#### `get_statistics() -> OverlayStatistics`

Returns overlay filesystem statistics.

**Returns:**
- `OverlayStatistics`: Overlay filesystem status

## Configuration Management APIs

### `openwrt` Module

#### `get_status() -> SystemStatus`

Returns current system status.

**Returns:**
- `SystemStatus`: Enumeration of system states

#### `get_config() -> SystemConfig`

Returns current system configuration.

**Returns:**
- `SystemConfig`: Structure containing system configuration

### `openwrt::uci` Module

#### `list_packages() -> Vec<&'static str>`

Lists available UCI packages.

**Returns:**
- `Vec<&'static str>`: List of package names

**Example:**
```rust
let packages = crate::openwrt::uci::list_packages();
for package in packages.iter() {
    println!("Package: {}", package);
}
```

## Security Framework APIs

### `security` Module

#### `get_status() -> SecurityStatus`

Returns security framework status.

**Returns:**
- `SecurityStatus`: Security initialization and operation status

### `security::access_control` Module

#### `get_status() -> AccessControlStatus`

Returns access control status.

**Returns:**
- `AccessControlStatus`: Access control operation status

### `security::audit` Module

#### `get_status() -> AuditStatus`

Returns audit logging status.

**Returns:**
- `AuditStatus`: Audit system operation status

#### `log_event(event_type: &str, subject: &str, result: &str)`

Logs a security event.

**Parameters:**
- `event_type`: Type of security event
- `subject`: Subject of the event (user, process, etc.)
- `result`: Result of the event (success, failure, etc.)

### `security::tamper_detection` Module

#### `get_status() -> TamperDetectionStatus`

Returns tamper detection status.

**Returns:**
- `TamperDetectionStatus`: Tamper detection operation status

## Performance Monitoring APIs

### `system_integration` Module

#### `update_system_metrics() -> Result<(), &'static str>`

Updates system performance metrics.

**Returns:**
- `Ok(())`: On successful update
- `Err(&'static str)`: Error message on failure

#### `get_comprehensive_metrics() -> SystemMetrics`

Returns comprehensive system metrics.

**Returns:**
- `SystemMetrics`: Structure containing all system performance metrics

#### `get_optimization_history() -> Vec<OptimizationRecord>`

Returns history of system optimizations.

**Returns:**
- `Vec<OptimizationRecord>`: List of optimization records

### `cpu_optimization` Module

#### `get_cpu_metrics() -> CpuMetrics`

Returns CPU performance metrics.

**Returns:**
- `CpuMetrics`: CPU usage, frequency, temperature, and latency information

#### `update_metrics() -> Result<(), &'static str>`

Updates CPU performance metrics.

**Returns:**
- `Ok(())`: On successful update
- `Err(&'static str)`: Error message on failure

## Testing Framework APIs

### `testing` Module

#### `run_production_readiness_tests() -> EnhancedTestStats`

Runs the complete Phase 5 production readiness test suite.

**Returns:**
- `EnhancedTestStats`: Comprehensive test results and statistics

#### `run_comprehensive_test_suite() -> (TestStats, EnhancedTestStats)`

Runs both legacy and enhanced test suites.

**Returns:**
- `(TestStats, EnhancedTestStats)`: Results from both test frameworks

#### `configure_enhanced_testing(config: TestConfig)`

Configures the enhanced testing framework.

**Parameters:**
- `config`: Test configuration structure

#### `run_enhanced_test<F>(name: &'static str, category: TestCategory, severity: TestSeverity, test_fn: F) -> bool`

Runs an individual enhanced test.

**Parameters:**
- `name`: Test name
- `category`: Test category (Unit, Integration, etc.)
- `severity`: Test severity (Critical, High, etc.)
- `test_fn`: Test function to execute

**Returns:**
- `bool`: True if test passed, false if failed

### Test Configuration

#### `TestConfig` Structure

```rust
pub struct TestConfig {
    pub enable_stress_tests: bool,
    pub enable_hardware_tests: bool,
    pub enable_performance_tests: bool,
    pub max_execution_time_ms: u64,
    pub memory_limit_bytes: usize,
    pub repeat_count: usize,
}
```

### Test Categories and Severity

#### `TestCategory` Enumeration

- `Unit`: Unit tests for individual components
- `Integration`: Integration tests for component interactions
- `Stress`: Stress tests for system limits
- `Hardware`: Hardware-specific tests
- `Regression`: Regression tests for existing functionality
- `Performance`: Performance validation tests
- `Security`: Security-focused tests
- `Compatibility`: Compatibility tests

#### `TestSeverity` Enumeration

- `Critical`: Critical functionality tests
- `High`: High-priority tests
- `Medium`: Medium-priority tests
- `Low`: Low-priority tests
- `Info`: Informational tests

## Error Handling

All APIs follow consistent error handling patterns:

- Functions that can fail return `Result<T, &'static str>`
- Error messages are descriptive and actionable
- Critical errors are logged through the audit system
- Memory allocation failures are handled gracefully

## Thread Safety

- All public APIs are thread-safe
- Internal synchronization uses spin locks for no_std compatibility
- Shared state is protected by appropriate synchronization primitives

## Performance Considerations

- APIs are designed for minimal overhead
- Zero-copy operations where possible
- Embedded-optimized implementations
- Configurable performance thresholds

---

**Document Version**: 1.0  
**Last Updated**: 2025-01-27  
**Compatible with**: Phase 5 Production Readiness Implementation
