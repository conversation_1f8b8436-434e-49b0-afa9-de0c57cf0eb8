# Community Contribution Guide - Hybrid Package Management

## Welcome Contributors!

Thank you for your interest in contributing to the OpenWrt Rust implementation and its hybrid package management system. This guide will help you get started with contributing to the project, whether you're developing packages, improving documentation, or enhancing the core system.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Contribution Types](#contribution-types)
3. [Development Environment Setup](#development-environment-setup)
4. [Package Development](#package-development)
5. [Core System Contributions](#core-system-contributions)
6. [Documentation Contributions](#documentation-contributions)
7. [Testing and Quality Assurance](#testing-and-quality-assurance)
8. [Submission Guidelines](#submission-guidelines)
9. [Community Standards](#community-standards)
10. [Resources and Support](#resources-and-support)

## Getting Started

### Prerequisites

- **Rust Knowledge**: Familiarity with Rust programming language
- **OpenWrt Experience**: Basic understanding of OpenWrt system
- **Git Proficiency**: Experience with Git version control
- **Linux Environment**: Development environment with Linux or WSL

### Quick Start Checklist

- [ ] Fork the repository
- [ ] Set up development environment
- [ ] Read the code of conduct
- [ ] Join the community channels
- [ ] Choose your first contribution

## Contribution Types

### 1. Package Development

**What**: Create new packages for the hybrid package management system

**Skills Needed**: Rust programming, package management concepts

**Examples**:
- Network utilities in Rust
- System monitoring tools
- Security applications
- IoT device drivers

### 2. Core System Development

**What**: Improve the hybrid package management core functionality

**Skills Needed**: Advanced Rust, systems programming, package management

**Examples**:
- Performance optimizations
- New package format support
- Security enhancements
- Cross-platform compatibility

### 3. Documentation

**What**: Improve documentation, guides, and tutorials

**Skills Needed**: Technical writing, markdown, understanding of the system

**Examples**:
- API documentation
- User guides
- Troubleshooting guides
- Tutorial content

### 4. Testing and QA

**What**: Develop tests, find bugs, improve quality assurance

**Skills Needed**: Testing frameworks, debugging, system administration

**Examples**:
- Unit tests
- Integration tests
- Performance benchmarks
- Bug reports and fixes

### 5. Community Support

**What**: Help other users and contributors

**Skills Needed**: Communication, problem-solving, system knowledge

**Examples**:
- Forum support
- Issue triage
- Mentoring new contributors
- Community events

## Development Environment Setup

### 1. Clone the Repository

```bash
# Fork the repository on GitHub first, then clone your fork
git clone https://github.com/YOUR_USERNAME/openwrt-rust-baremetal.git
cd openwrt-rust-baremetal

# Add upstream remote
git remote add upstream https://github.com/openwrt/openwrt-rust-baremetal.git
```

### 2. Install Development Dependencies

```bash
# Install Rust toolchain
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# Install required components
rustup component add rustfmt clippy

# Install system dependencies (Ubuntu/Debian)
sudo apt-get update
sudo apt-get install build-essential pkg-config libssl-dev

# Install OpenWrt build dependencies
sudo apt-get install git-core build-essential libssl-dev libncurses5-dev \
    unzip gawk zlib1g-dev file wget python3-distutils python3-minimal
```

### 3. Build the Project

```bash
# Build the project
cargo build

# Run tests
cargo test

# Check code formatting
cargo fmt --check

# Run linter
cargo clippy
```

### 4. Set Up Testing Environment

```bash
# Set up VM testing environment
cd vm-testing
./scripts/vm-setup.sh

# Set up container testing environment
cd container-testing
./scripts/container-manager.sh setup

# Run Phase 11.5 testing framework
cd phase11-testing
./run-all-tests.sh
```

## Package Development

### Creating a New Rust Native Package

#### 1. Package Structure

```
my-package/
├── Cargo.toml
├── src/
│   ├── main.rs
│   └── lib.rs
├── package.json          # Package metadata
├── README.md
├── LICENSE
└── tests/
    └── integration_tests.rs
```

#### 2. Package Metadata (package.json)

```json
{
    "name": "my-awesome-package",
    "version": "1.0.0",
    "description": "An awesome package for OpenWrt",
    "author": "Your Name <<EMAIL>>",
    "license": "GPL-2.0",
    "homepage": "https://github.com/your-username/my-awesome-package",
    "repository": "https://github.com/your-username/my-awesome-package.git",
    "dependencies": [
        "libc >= 2.28",
        "openssl >= 1.1.1"
    ],
    "provides": ["my-awesome-package"],
    "conflicts": [],
    "architecture": "all",
    "category": "utilities",
    "format": "rust-native",
    "openwrt": {
        "min_version": "23.05",
        "supported_targets": ["x86_64", "arm", "mips"]
    }
}
```

#### 3. Cargo.toml Configuration

```toml
[package]
name = "my-awesome-package"
version = "1.0.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
license = "GPL-2.0"
description = "An awesome package for OpenWrt"
homepage = "https://github.com/your-username/my-awesome-package"
repository = "https://github.com/your-username/my-awesome-package"

[dependencies]
# OpenWrt Rust framework
openwrt-rust = { path = "../openwrt-rust-framework" }

# Standard dependencies
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }

[dev-dependencies]
tokio-test = "0.4"

[[bin]]
name = "my-awesome-package"
path = "src/main.rs"
```

#### 4. Integration with OpenWrt Framework

```rust
// src/main.rs
use openwrt_rust::prelude::*;
use openwrt_rust::config::ConfigManager;
use openwrt_rust::network::NetworkManager;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize OpenWrt integration
    let config = ConfigManager::new()?;
    let network = NetworkManager::new()?;
    
    // Your package logic here
    println!("My Awesome Package is running!");
    
    Ok(())
}
```

#### 5. Testing Your Package

```rust
// tests/integration_tests.rs
use my_awesome_package::*;

#[tokio::test]
async fn test_package_functionality() {
    // Test your package functionality
    assert!(true);
}

#[test]
fn test_configuration() {
    // Test configuration handling
    assert!(true);
}
```

### Package Submission Process

#### 1. Prepare Your Package

```bash
# Format code
cargo fmt

# Run linter
cargo clippy

# Run tests
cargo test

# Build package
cargo build --release

# Create package archive
./scripts/package/create-package.sh my-awesome-package
```

#### 2. Submit for Review

```bash
# Create feature branch
git checkout -b feature/my-awesome-package

# Commit changes
git add .
git commit -m "Add my-awesome-package

- Implements awesome functionality
- Includes comprehensive tests
- Follows OpenWrt Rust conventions"

# Push to your fork
git push origin feature/my-awesome-package

# Create pull request on GitHub
```

## Core System Contributions

### Areas for Contribution

1. **Package Manager Core**
   - Dependency resolution improvements
   - Performance optimizations
   - New package format support

2. **Security Framework**
   - Signature verification enhancements
   - Sandboxing improvements
   - Vulnerability scanning

3. **Network Stack**
   - Protocol implementations
   - Performance optimizations
   - Hardware driver support

4. **Build System**
   - Cross-compilation improvements
   - Build optimization
   - CI/CD enhancements

### Development Guidelines

#### Code Style

```rust
// Use descriptive names
fn calculate_package_dependencies(package: &Package) -> Vec<Dependency> {
    // Implementation
}

// Document public APIs
/// Calculates dependencies for a given package
/// 
/// # Arguments
/// * `package` - The package to analyze
/// 
/// # Returns
/// Vector of dependencies required by the package
pub fn calculate_dependencies(package: &Package) -> Vec<Dependency> {
    // Implementation
}

// Use error handling
fn risky_operation() -> Result<String, PackageError> {
    // Implementation that might fail
}
```

#### Testing Requirements

```rust
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_dependency_calculation() {
        let package = create_test_package();
        let deps = calculate_dependencies(&package);
        assert_eq!(deps.len(), 2);
    }

    #[tokio::test]
    async fn test_async_operation() {
        let result = async_operation().await;
        assert!(result.is_ok());
    }
}
```

## Documentation Contributions

### Documentation Types

1. **API Documentation**: Rust doc comments
2. **User Guides**: Markdown documentation
3. **Tutorials**: Step-by-step guides
4. **Troubleshooting**: Problem-solution guides

### Writing Guidelines

#### Structure

```markdown
# Title

## Overview
Brief description of the topic

## Prerequisites
What users need to know/have before starting

## Step-by-Step Instructions
1. First step
2. Second step
3. Third step

## Examples
Practical examples with code

## Troubleshooting
Common issues and solutions

## See Also
Links to related documentation
```

#### Code Examples

```markdown
# Always include complete, runnable examples

```bash
# Install the package
opkg-rust install my-package

# Configure the package
opkg-rust config my-package set option value
```

```rust
// Rust code example
use openwrt_rust::prelude::*;

fn main() {
    println!("Hello, OpenWrt!");
}
```
```

## Testing and Quality Assurance

### Testing Framework

```bash
# Run all tests
cargo test

# Run specific test suite
cargo test --package openwrt-rust-core

# Run integration tests
cargo test --test integration

# Run with coverage
cargo tarpaulin --out Html
```

### Quality Checks

```bash
# Format check
cargo fmt --check

# Linting
cargo clippy -- -D warnings

# Security audit
cargo audit

# Dependency check
cargo outdated
```

### Performance Testing

```bash
# Run performance tests
cargo bench

# Profile performance
cargo flamegraph --bin my-package

# Memory usage analysis
valgrind --tool=massif target/release/my-package
```

## Submission Guidelines

### Pull Request Process

1. **Fork and Branch**
   ```bash
   git checkout -b feature/description
   ```

2. **Make Changes**
   - Follow coding standards
   - Add tests
   - Update documentation

3. **Test Thoroughly**
   ```bash
   cargo test
   cargo clippy
   cargo fmt --check
   ```

4. **Commit with Good Messages**
   ```
   Add hybrid package format support

   - Implement APK package format handler
   - Add comprehensive test suite
   - Update documentation
   - Maintain backward compatibility

   Fixes #123
   ```

5. **Submit Pull Request**
   - Clear title and description
   - Reference related issues
   - Include testing information

### Review Process

1. **Automated Checks**: CI/CD pipeline runs tests
2. **Code Review**: Maintainers review code
3. **Testing**: Manual testing if needed
4. **Approval**: Approved by maintainers
5. **Merge**: Merged into main branch

## Community Standards

### Code of Conduct

- Be respectful and inclusive
- Focus on constructive feedback
- Help others learn and grow
- Maintain professional communication

### Communication Channels

- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General questions and discussions
- **Discord**: Real-time chat and support
- **Mailing List**: Development discussions

### Recognition

Contributors are recognized through:
- Contributor list in README
- Release notes mentions
- Community highlights
- Maintainer nominations

## Resources and Support

### Documentation

- [API Reference](API_REFERENCE.md)
- [Developer Guide](DEVELOPER_GUIDE.md)
- [Hybrid Package Management](HYBRID_PACKAGE_MANAGEMENT.md)
- [Troubleshooting](HYBRID_TROUBLESHOOTING.md)

### Tools and Templates

- Package template repository
- Development environment scripts
- Testing framework
- Documentation templates

### Getting Help

1. **Read Documentation**: Check existing docs first
2. **Search Issues**: Look for similar problems
3. **Ask Questions**: Use GitHub Discussions
4. **Join Community**: Connect with other contributors

### Mentorship Program

New contributors can request mentorship:
- Pair programming sessions
- Code review guidance
- Project direction advice
- Career development support

---

**Thank you for contributing to OpenWrt Rust!**

Your contributions help make OpenWrt more secure, performant, and maintainable for everyone.

**Version**: 1.0  
**Last Updated**: January 27, 2025  
**Status**: Phase 11.6 - Documentation and Migration
