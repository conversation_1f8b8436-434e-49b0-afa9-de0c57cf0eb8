#!/bin/bash

# Simple QEMU test for OpenWrt Rust bare-metal firmware
# This script provides the complete QEMU command line for testing

set -euo pipefail

# Configuration
MEMORY_LIMIT="250M"
ARCHITECTURE="x86_64"
QEMU_BINARY="qemu-system-x86_64"
TIMEOUT=10

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if QEMU is available
check_qemu() {
    if ! command -v "$QEMU_BINARY" >/dev/null 2>&1; then
        log_error "QEMU not found: $QEMU_BINARY"
        log_info "Please install QEMU:"
        log_info "  macOS: brew install qemu"
        log_info "  Ubuntu/Debian: sudo apt-get install qemu-system-x86"
        exit 1
    fi
    log_success "QEMU found: $QEMU_BINARY"
}

# Show the complete QEMU command line
show_qemu_command() {
    local kernel_path="$1"
    
    echo ""
    echo "=========================================="
    echo "OpenWrt Rust Bare-Metal QEMU Test Setup"
    echo "=========================================="
    echo ""
    echo "Complete QEMU command line for testing:"
    echo ""
    
    # The complete QEMU command with 250MB memory limit
    cat << EOF
$QEMU_BINARY \\
  -kernel "$kernel_path" \\
  -m $MEMORY_LIMIT \\
  -smp 1 \\
  -machine pc-q35-2.0 \\
  -cpu qemu64 \\
  -nographic \\
  -no-reboot \\
  -no-shutdown \\
  -serial stdio \\
  -monitor none \\
  -netdev user,id=net0,restrict=on \\
  -device virtio-net-pci,netdev=net0 \\
  -object memory-backend-ram,id=ram,size=$MEMORY_LIMIT \\
  -numa node,memdev=ram \\
  -append "console=ttyS0 panic=1 oops=panic"
EOF
    
    echo ""
    echo "Key parameters:"
    echo "  -m $MEMORY_LIMIT          : Memory limit enforced"
    echo "  -machine pc-q35-2.0      : Modern PC machine type"
    echo "  -cpu qemu64              : Generic x86_64 CPU"
    echo "  -nographic               : No graphics, console only"
    echo "  -serial stdio            : Serial output to terminal"
    echo "  -netdev user,restrict=on : Restricted network access"
    echo "  -numa node,memdev=ram    : NUMA memory configuration"
    echo ""
}

# Run the actual QEMU test
run_qemu_test() {
    local kernel_path="$1"
    
    log_info "Starting QEMU test with $MEMORY_LIMIT memory limit..."
    
    # Create log directory
    mkdir -p qemu-logs
    local log_file="qemu-logs/qemu-simple-test-$(date +%Y%m%d-%H%M%S).log"
    
    # The actual QEMU command
    timeout $TIMEOUT $QEMU_BINARY \
        -kernel "$kernel_path" \
        -m $MEMORY_LIMIT \
        -smp 1 \
        -machine pc-q35-2.0 \
        -cpu qemu64 \
        -nographic \
        -no-reboot \
        -no-shutdown \
        -serial stdio \
        -monitor none \
        -netdev user,id=net0,restrict=on \
        -device virtio-net-pci,netdev=net0 \
        -object memory-backend-ram,id=ram,size=$MEMORY_LIMIT \
        -numa node,memdev=ram \
        -append "console=ttyS0 panic=1 oops=panic" \
        2>&1 | tee "$log_file" || {
        
        local exit_code=$?
        if [[ $exit_code -eq 124 ]]; then
            log_success "QEMU test completed (timeout after ${TIMEOUT}s)"
        else
            log_warning "QEMU exited with code: $exit_code"
        fi
        
        log_info "Log saved to: $log_file"
        return 0
    }
}

# Create a minimal test kernel if needed
create_minimal_kernel() {
    local kernel_path="test-kernel-minimal.bin"
    
    if [[ ! -f "$kernel_path" ]]; then
        log_info "Creating minimal test kernel..."
        
        # Create a very simple kernel that just halts
        printf '\xfa\xf4' > "$kernel_path"  # cli; hlt instructions
        
        log_success "Minimal kernel created: $kernel_path"
    fi
    
    echo "$kernel_path"
}

# Try to find an existing kernel
find_kernel() {
    local kernel_candidates=(
        "target/x86_64-unknown-none/release/qemu-kernel"
        "target/x86_64-unknown-none/debug/qemu-kernel"
        "test-kernel.bin"
        "kernel.bin"
    )

    for kernel in "${kernel_candidates[@]}"; do
        if [[ -f "$kernel" ]]; then
            log_info "Found kernel: $kernel" >&2
            echo "$kernel"
            return 0
        fi
    done

    # No kernel found, create minimal one
    create_minimal_kernel
}

# Show usage
show_usage() {
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  -h, --help       Show this help message"
    echo "  -k, --kernel     Specify kernel path"
    echo "  -m, --memory     Set memory limit (default: 250M)"
    echo "  -t, --timeout    Set test timeout (default: 10s)"
    echo "  -c, --command    Show command only (don't run)"
    echo "  -r, --run        Run the test"
    echo ""
    echo "Examples:"
    echo "  $0 -c                    # Show QEMU command"
    echo "  $0 -r                    # Run QEMU test"
    echo "  $0 -k kernel.bin -r      # Run with specific kernel"
    echo "  $0 -m 128M -t 30 -r      # Run with 128MB, 30s timeout"
}

# Main function
main() {
    local kernel_path=""
    local show_command_only=false
    local run_test=false
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_usage
                exit 0
                ;;
            -k|--kernel)
                kernel_path="$2"
                shift 2
                ;;
            -m|--memory)
                MEMORY_LIMIT="$2"
                shift 2
                ;;
            -t|--timeout)
                TIMEOUT="$2"
                shift 2
                ;;
            -c|--command)
                show_command_only=true
                shift
                ;;
            -r|--run)
                run_test=true
                shift
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Check QEMU availability
    check_qemu
    
    # Find or specify kernel
    if [[ -z "$kernel_path" ]]; then
        kernel_path=$(find_kernel)
    elif [[ ! -f "$kernel_path" ]]; then
        log_error "Kernel not found: $kernel_path"
        exit 1
    fi
    
    # Show command
    if [[ "$show_command_only" == true ]] || [[ "$run_test" == false ]]; then
        show_qemu_command "$kernel_path"
    fi
    
    # Run test if requested
    if [[ "$run_test" == true ]]; then
        echo ""
        run_qemu_test "$kernel_path"
    fi
    
    # If neither -c nor -r specified, show both
    if [[ "$show_command_only" == false ]] && [[ "$run_test" == false ]]; then
        echo ""
        echo "Use -c to show command only, or -r to run the test"
        echo "Example: $0 -r"
    fi
}

# Execute main function
main "$@"
