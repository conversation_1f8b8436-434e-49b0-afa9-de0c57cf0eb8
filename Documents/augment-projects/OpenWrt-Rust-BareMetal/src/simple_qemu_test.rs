//! Simple QEMU Test Kernel for OpenWrt Rust
//!
//! This is a minimal kernel that tests basic functionality in QEMU

#![no_std]
#![no_main]

use core::panic::PanicInfo;

// Multiboot header for QEMU compatibility
#[repr(C, packed)]
struct MultibootHeader {
    magic: u32,
    flags: u32,
    checksum: u32,
}

#[used]
#[link_section = ".multiboot"]
static MULTIBOOT_HEADER: MultibootHeader = MultibootHeader {
    magic: 0x1BADB002,
    flags: 0,
    checksum: 0u32.wrapping_sub(0x1BADB002),
};

/// Kernel entry point
#[no_mangle]
pub extern "C" fn _start() -> ! {
    // Print early boot message
    early_print("OpenWrt Rust Kernel - QEMU Test\n");
    early_print("Memory Limit: 250MB\n");
    early_print("Architecture: x86_64\n");
    early_print("Status: BOOT SUCCESS\n");
    
    // Simple test loop
    let mut counter = 0u32;
    loop {
        counter = counter.wrapping_add(1);
        
        // Print periodic status every million iterations
        if counter % 1000000 == 0 {
            early_print("Kernel running... ");
            print_number(counter / 1000000);
            early_print("\n");
        }
        
        // Simple CPU yield
        unsafe {
            core::arch::asm!("pause");
        }
        
        // Exit after 10 iterations for testing
        if counter >= 10000000 {
            early_print("Test completed successfully!\n");
            early_print("OpenWrt Rust Kernel - TEST PASSED\n");
            break;
        }
    }
    
    // Halt the system
    loop {
        unsafe {
            core::arch::asm!("hlt");
        }
    }
}

/// Early print function for boot messages
fn early_print(s: &str) {
    // Simple serial output for early boot messages
    for byte in s.bytes() {
        unsafe {
            // Write to serial port (0x3F8)
            core::arch::asm!(
                "out dx, al",
                in("dx") 0x3F8u16,
                in("al") byte,
            );
        }
    }
}

/// Print a number in decimal
fn print_number(mut num: u32) {
    if num == 0 {
        early_print("0");
        return;
    }
    
    let mut digits = [0u8; 10];
    let mut count = 0;
    
    while num > 0 {
        digits[count] = (num % 10) as u8 + b'0';
        num /= 10;
        count += 1;
    }
    
    // Print digits in reverse order
    for i in (0..count).rev() {
        unsafe {
            core::arch::asm!(
                "out dx, al",
                in("dx") 0x3F8u16,
                in("al") digits[i],
            );
        }
    }
}

/// Panic handler for no_std environment
#[panic_handler]
fn panic(info: &PanicInfo) -> ! {
    early_print("KERNEL PANIC: ");
    if let Some(location) = info.location() {
        early_print("at ");
        early_print(location.file());
        early_print("\n");
    }
    
    early_print("System halted.\n");
    
    loop {
        unsafe {
            core::arch::asm!("hlt");
        }
    }
}
