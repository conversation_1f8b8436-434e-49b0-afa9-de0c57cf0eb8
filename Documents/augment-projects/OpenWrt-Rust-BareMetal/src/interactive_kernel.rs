//! Interactive OpenWrt Rust Kernel for QEMU
//!
//! This kernel provides an interactive console for testing OpenWrt functionality

#![no_std]
#![no_main]

use core::panic::PanicInfo;

// Multiboot header for QEMU compatibility
#[repr(C, packed)]
struct MultibootHeader {
    magic: u32,
    flags: u32,
    checksum: u32,
}

#[used]
#[link_section = ".multiboot"]
static MULTIBOOT_HEADER: MultibootHeader = MultibootHeader {
    magic: 0x1BADB002,
    flags: 0,
    checksum: 0u32.wrapping_sub(0x1BADB002),
};

/// Serial port base address (COM1)
const SERIAL_PORT: u16 = 0x3F8;

/// Write a byte to the serial port
fn serial_write_byte(byte: u8) {
    unsafe {
        core::arch::asm!(
            "out dx, al",
            in("dx") SERIAL_PORT,
            in("al") byte,
            options(nomem, nostack, preserves_flags)
        );
    }
}

/// Write a string to serial port
fn serial_write_str(s: &str) {
    for byte in s.bytes() {
        serial_write_byte(byte);
    }
}

/// Write a formatted string with newline
fn serial_println(s: &str) {
    serial_write_str(s);
    serial_write_str("\r\n");
}

/// Read a byte from serial port (non-blocking)
fn serial_read_byte() -> Option<u8> {
    unsafe {
        // Check if data is available (LSR bit 0)
        let mut status: u8;
        core::arch::asm!(
            "in al, dx",
            out("al") status,
            in("dx") SERIAL_PORT + 5, // Line Status Register
            options(nomem, nostack, preserves_flags)
        );
        
        if status & 1 != 0 {
            // Data available, read it
            let mut data: u8;
            core::arch::asm!(
                "in al, dx",
                out("al") data,
                in("dx") SERIAL_PORT,
                options(nomem, nostack, preserves_flags)
            );
            Some(data)
        } else {
            None
        }
    }
}

/// Initialize serial port
fn init_serial() {
    unsafe {
        // Disable interrupts
        core::arch::asm!(
            "out dx, al",
            in("dx") SERIAL_PORT + 1,
            in("al") 0x00u8,
            options(nomem, nostack, preserves_flags)
        );
        
        // Set baud rate divisor (115200 baud)
        core::arch::asm!(
            "out dx, al",
            in("dx") SERIAL_PORT + 3,
            in("al") 0x80u8, // Enable DLAB
            options(nomem, nostack, preserves_flags)
        );
        
        core::arch::asm!(
            "out dx, al",
            in("dx") SERIAL_PORT + 0,
            in("al") 0x01u8, // Divisor low byte
            options(nomem, nostack, preserves_flags)
        );
        
        core::arch::asm!(
            "out dx, al",
            in("dx") SERIAL_PORT + 1,
            in("al") 0x00u8, // Divisor high byte
            options(nomem, nostack, preserves_flags)
        );
        
        // 8 bits, no parity, one stop bit
        core::arch::asm!(
            "out dx, al",
            in("dx") SERIAL_PORT + 3,
            in("al") 0x03u8,
            options(nomem, nostack, preserves_flags)
        );
        
        // Enable FIFO, clear them, with 14-byte threshold
        core::arch::asm!(
            "out dx, al",
            in("dx") SERIAL_PORT + 2,
            in("al") 0xC7u8,
            options(nomem, nostack, preserves_flags)
        );
        
        // IRQs enabled, RTS/DSR set
        core::arch::asm!(
            "out dx, al",
            in("dx") SERIAL_PORT + 4,
            in("al") 0x0Bu8,
            options(nomem, nostack, preserves_flags)
        );
    }
}

/// Simple delay function
fn delay() {
    for _ in 0..1000000 {
        unsafe {
            core::arch::asm!("pause", options(nomem, nostack, preserves_flags));
        }
    }
}

/// Interactive shell loop
fn interactive_shell() {
    serial_println("");
    serial_println("=== OpenWrt Rust Interactive Shell ===");
    serial_println("Commands:");
    serial_println("  help    - Show this help");
    serial_println("  status  - Show system status");
    serial_println("  memory  - Show memory info");
    serial_println("  test    - Run system test");
    serial_println("  reboot  - Restart system");
    serial_println("  exit    - Exit shell");
    serial_println("");
    
    let mut input_buffer = [0u8; 64];
    let mut buffer_pos = 0;
    
    loop {
        serial_write_str("openwrt-rust> ");
        
        // Read command
        buffer_pos = 0;
        loop {
            if let Some(byte) = serial_read_byte() {
                match byte {
                    b'\r' | b'\n' => {
                        serial_println("");
                        break;
                    }
                    b'\x08' | b'\x7f' => { // Backspace
                        if buffer_pos > 0 {
                            buffer_pos -= 1;
                            serial_write_str("\x08 \x08");
                        }
                    }
                    b' '..=b'~' => { // Printable characters
                        if buffer_pos < input_buffer.len() - 1 {
                            input_buffer[buffer_pos] = byte;
                            buffer_pos += 1;
                            serial_write_byte(byte);
                        }
                    }
                    _ => {} // Ignore other characters
                }
            }
            delay(); // Small delay to prevent busy waiting
        }
        
        // Null-terminate the command
        input_buffer[buffer_pos] = 0;
        
        // Process command
        let command = core::str::from_utf8(&input_buffer[..buffer_pos]).unwrap_or("");
        
        match command {
            "help" => {
                serial_println("Available commands:");
                serial_println("  help    - Show this help");
                serial_println("  status  - Show system status");
                serial_println("  memory  - Show memory info");
                serial_println("  test    - Run system test");
                serial_println("  reboot  - Restart system");
                serial_println("  exit    - Exit shell");
            }
            "status" => {
                serial_println("System Status:");
                serial_println("  Kernel: OpenWrt Rust v1.0.0");
                serial_println("  Memory: 250MB limit enforced");
                serial_println("  Architecture: x86_64");
                serial_println("  Machine: QEMU Q35");
                serial_println("  Status: Running");
            }
            "memory" => {
                serial_println("Memory Information:");
                serial_println("  Total RAM: 250MB");
                serial_println("  Kernel Memory: ~4KB");
                serial_println("  Available: ~249MB");
                serial_println("  Memory Protection: Enabled");
            }
            "test" => {
                serial_println("Running system tests...");
                serial_println("  [OK] Serial communication");
                serial_println("  [OK] Memory access");
                serial_println("  [OK] CPU instructions");
                serial_println("  [OK] Input handling");
                serial_println("All tests passed!");
            }
            "reboot" => {
                serial_println("Rebooting system...");
                delay();
                // Trigger a reboot by triple fault
                unsafe {
                    core::arch::asm!(
                        "cli",
                        "mov rax, 0",
                        "mov cr3, rax",
                        options(nomem, nostack)
                    );
                }
            }
            "exit" => {
                serial_println("Exiting shell...");
                break;
            }
            "" => {
                // Empty command, just continue
            }
            _ => {
                serial_write_str("Unknown command: ");
                serial_println(command);
                serial_println("Type 'help' for available commands.");
            }
        }
    }
}

/// Kernel entry point
#[no_mangle]
pub extern "C" fn _start() -> ! {
    // Initialize serial port
    init_serial();
    
    // Boot messages
    serial_println("");
    serial_println("==========================================");
    serial_println("    OpenWrt Rust Interactive Kernel");
    serial_println("==========================================");
    serial_println("");
    serial_println("Boot Information:");
    serial_println("  Version: 1.0.0");
    serial_println("  Memory Limit: 250MB");
    serial_println("  Architecture: x86_64");
    serial_println("  Build: Interactive QEMU Test");
    serial_println("");
    serial_println("Initializing system...");
    
    // Small delay to show boot process
    delay();
    serial_println("  [OK] Serial console initialized");
    delay();
    serial_println("  [OK] Memory management ready");
    delay();
    serial_println("  [OK] CPU features detected");
    delay();
    serial_println("  [OK] System ready for interaction");
    serial_println("");
    
    // Start interactive shell
    interactive_shell();
    
    // After shell exits, halt
    serial_println("");
    serial_println("System halted. Press Ctrl+C to exit QEMU.");
    
    loop {
        unsafe {
            core::arch::asm!("hlt", options(nomem, nostack, preserves_flags));
        }
    }
}

/// Panic handler
#[panic_handler]
fn panic(info: &PanicInfo) -> ! {
    serial_println("");
    serial_println("KERNEL PANIC!");
    if let Some(location) = info.location() {
        serial_write_str("Location: ");
        serial_write_str(location.file());
        serial_write_str(":");
        // Note: We can't easily format numbers without std, so just show file
        serial_println("");
    }
    serial_write_str("Message: ");
    serial_println("Panic occurred");
    
    loop {
        unsafe {
            core::arch::asm!("hlt", options(nomem, nostack, preserves_flags));
        }
    }
}
