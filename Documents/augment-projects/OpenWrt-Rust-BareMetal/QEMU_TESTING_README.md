# OpenWrt Rust Bare-Metal QEMU Testing Guide

This guide provides comprehensive instructions for testing the OpenWrt Rust bare-metal firmware in QEMU with memory constraints.

## Overview

The QEMU testing environment is designed to:
- Test firmware with a strict 250MB memory limit
- Support multiple architectures (x86_64, ARM, MIPS)
- Provide automated testing scenarios
- Generate detailed logs for analysis
- Prevent memory overflow issues during testing

## Quick Start

### Prerequisites

1. **Install QEMU**:
   ```bash
   # macOS
   brew install qemu
   
   # Ubuntu/Debian
   sudo apt-get install qemu-system-x86 qemu-system-arm qemu-system-mips
   
   # CentOS/RHEL
   sudo yum install qemu-kvm qemu-system-x86
   ```

2. **Python 3.6+** (for advanced runner)

### Basic Usage

1. **Simple Test Run**:
   ```bash
   ./qemu-test-setup.sh
   ```

2. **Custom Memory Limit**:
   ```bash
   ./qemu-test-setup.sh -m 128M
   ```

3. **Advanced Testing**:
   ```bash
   ./qemu-runner.py -c default -t 60
   ```

## Testing Tools

### 1. qemu-test-setup.sh

Basic shell script for quick QEMU testing.

**Usage**:
```bash
./qemu-test-setup.sh [options]

Options:
  -h, --help     Show help message
  -m, --memory   Set memory limit (default: 250M)
  -a, --arch     Set architecture (default: x86_64)
  -k, --kernel   Specify kernel path
```

**Examples**:
```bash
# Default test with 250MB memory
./qemu-test-setup.sh

# Test with 128MB memory
./qemu-test-setup.sh -m 128M

# Test with custom kernel
./qemu-test-setup.sh -k /path/to/kernel.bin

# Test ARM architecture
./qemu-test-setup.sh -a aarch64 -m 200M
```

### 2. qemu-runner.py

Advanced Python-based test runner with configuration management.

**Usage**:
```bash
./qemu-runner.py [options]

Options:
  -c, --config      QEMU configuration to use (default: default)
  -k, --kernel      Path to kernel binary
  -t, --timeout     Test timeout in seconds (default: 30)
  -s, --scenario    Test scenario to validate
  --list-configs    List available configurations
  --list-scenarios  List available test scenarios
```

**Examples**:
```bash
# List available configurations
./qemu-runner.py --list-configs

# Run default configuration
./qemu-runner.py -c default

# Run debug configuration with custom timeout
./qemu-runner.py -c debug -t 120

# Run stress test scenario
./qemu-runner.py -c stress_test -s stress_test

# Test with custom kernel
./qemu-runner.py -k target/x86_64-unknown-none/release/kernel
```

## Configuration

### QEMU Configurations

The `qemu-config.json` file defines multiple test configurations:

1. **default**: Standard testing with 250MB memory
2. **minimal**: Lightweight testing with 128MB memory
3. **debug**: Debug configuration with GDB support
4. **arm64**: ARM64 cross-architecture testing
5. **stress_test**: Maximum stress testing

### Memory Constraints

- **Default Limit**: 250MB
- **Minimum Limit**: 64MB
- **Maximum Limit**: 250MB (enforced)

### Supported Architectures

- **x86_64**: Primary development target
- **aarch64**: ARM 64-bit
- **arm**: ARM 32-bit
- **mips**: MIPS big-endian
- **mipsel**: MIPS little-endian

## Test Scenarios

### Available Scenarios

1. **boot_test**: Basic boot verification
2. **memory_test**: Memory allocation testing
3. **network_test**: Network interface testing
4. **stress_test**: System stress testing

### Custom Scenarios

Add new scenarios to `qemu-config.json`:

```json
{
  "name": "custom_test",
  "description": "Custom test description",
  "timeout": 60,
  "expected_output": ["expected", "output", "strings"]
}
```

## Building the Firmware

### For QEMU Testing

1. **Build for x86_64**:
   ```bash
   cargo build --bin qemu-kernel --target x86_64-unknown-none --release
   ```

2. **Build for ARM64**:
   ```bash
   cargo build --bin qemu-kernel --target aarch64-unknown-none --release
   ```

### Troubleshooting Build Issues

If compilation fails, the test scripts will create a minimal test kernel automatically.

## QEMU Command Examples

### Basic x86_64 Test
```bash
qemu-system-x86_64 \
  -kernel test-kernel.bin \
  -m 250M \
  -smp 1 \
  -machine pc-q35-2.0 \
  -cpu qemu64 \
  -nographic \
  -no-reboot \
  -no-shutdown \
  -serial stdio \
  -monitor none \
  -append "console=ttyS0 panic=1 oops=panic"
```

### ARM64 Test
```bash
qemu-system-aarch64 \
  -kernel test-kernel.bin \
  -m 250M \
  -smp 1 \
  -machine virt \
  -cpu cortex-a57 \
  -nographic \
  -no-reboot \
  -no-shutdown \
  -serial stdio \
  -monitor none \
  -append "console=ttyAMA0"
```

### Debug Configuration
```bash
qemu-system-x86_64 \
  -kernel test-kernel.bin \
  -m 250M \
  -smp 1 \
  -machine pc-q35-2.0 \
  -cpu qemu64 \
  -nographic \
  -no-reboot \
  -no-shutdown \
  -serial stdio \
  -monitor unix:/tmp/qemu-monitor.sock,server,nowait \
  -s -S \
  -append "console=ttyS0 panic=1 oops=panic debug"
```

## Logs and Output

### Log Location
All test logs are saved to `qemu-logs/` directory with timestamps:
- `qemu-test-YYYYMMDD-HHMMSS.log`
- `qemu-{config}-YYYYMMDD-HHMMSS.log`

### Log Analysis
Logs contain:
- QEMU startup parameters
- Kernel boot messages
- System output
- Error messages
- Test results

## Memory Constraint Validation

The testing environment enforces strict memory limits:

1. **Validation**: All configurations are validated against the 250MB limit
2. **Enforcement**: QEMU is configured with exact memory limits
3. **Monitoring**: Memory usage is tracked during testing
4. **Prevention**: Tests fail if memory limits are exceeded

## Integration with CI/CD

### GitHub Actions Example
```yaml
- name: Run QEMU Tests
  run: |
    ./qemu-runner.py -c default -t 60
    ./qemu-runner.py -c minimal -t 30
    ./qemu-runner.py -c stress_test -t 120
```

### Jenkins Pipeline
```groovy
stage('QEMU Testing') {
    steps {
        sh './qemu-test-setup.sh -m 250M'
        sh './qemu-runner.py --list-configs'
        sh './qemu-runner.py -c default -s boot_test'
    }
}
```

## Troubleshooting

### Common Issues

1. **QEMU Not Found**:
   - Install QEMU for your platform
   - Ensure QEMU binaries are in PATH

2. **Kernel Build Fails**:
   - Scripts will create minimal test kernel
   - Check Rust toolchain installation
   - Verify target architecture support

3. **Memory Limit Exceeded**:
   - Reduce memory allocation
   - Check system available memory
   - Use minimal configuration

4. **Test Timeout**:
   - Increase timeout value
   - Check kernel boot process
   - Review QEMU logs

### Debug Mode

Enable debug mode for detailed output:
```bash
./qemu-runner.py -c debug -t 120
```

Connect GDB for debugging:
```bash
gdb -ex "target remote localhost:1234" kernel.bin
```

## Performance Optimization

### Tips for Better Performance

1. **Use KVM** (Linux only):
   - Enable KVM acceleration when available
   - Requires hardware virtualization support

2. **Optimize Memory**:
   - Use minimal configuration for basic tests
   - Allocate only required memory

3. **CPU Configuration**:
   - Match CPU type to host architecture
   - Use appropriate SMP settings

## Security Considerations

1. **Network Isolation**: User-mode networking with restrictions
2. **File System**: No host file system access by default
3. **Memory Protection**: Strict memory limits enforced
4. **Process Isolation**: QEMU runs in isolated environment

## Contributing

To add new configurations or scenarios:

1. Edit `qemu-config.json`
2. Test with `qemu-runner.py`
3. Update documentation
4. Submit pull request

## Support

For issues and questions:
- Check logs in `qemu-logs/` directory
- Review configuration in `qemu-config.json`
- Test with minimal configuration first
- Report issues with full log output
