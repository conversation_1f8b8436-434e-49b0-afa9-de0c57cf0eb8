# OpenWrt-Rust Hybrid Build System

## Overview

This directory contains the hybrid build system integration that enables seamless building of both Rust components and official OpenWrt packages. The system bridges the OpenWrt SDK with Rust cross-compilation capabilities to create a unified build environment.

## Architecture

```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                           Hybrid Build System Architecture                         │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                              Build Orchestration Layer                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                    │
│  │ Build Manager   │  │ Dependency      │  │ Target          │                    │
│  │                 │  │ Resolver        │  │ Coordinator     │                    │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                    │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                            Cross-Compilation Layer                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                    │
│  │ Rust Toolchain  │  │ OpenWrt         │  │ Toolchain       │                    │
│  │ Manager         │  │ Toolchain       │  │ Integration     │                    │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                    │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                              Package Build Layer                                   │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                    │
│  │ Rust Package    │  │ OpenWrt Package │  │ Hybrid Package  │                    │
│  │ Builder         │  │ Builder         │  │ Builder         │                    │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                    │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                            OpenWrt SDK Integration                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                    │
│  │ SDK Manager     │  │ Makefile        │  │ Package         │                    │
│  │                 │  │ Integration     │  │ Templates       │                    │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                    │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

## Directory Structure

```
build-system/
├── README.md                           # This file
├── scripts/                            # Build automation scripts
│   ├── build-manager.sh               # Main build orchestration
│   ├── rust-toolchain-setup.sh        # Rust cross-compilation setup
│   ├── openwrt-sdk-integration.sh     # OpenWrt SDK integration
│   └── package-builder.sh             # Package building automation
├── makefiles/                          # Hybrid Makefile system
│   ├── rust-package.mk                # Rust package build rules
│   ├── hybrid-package.mk              # Hybrid package build rules
│   └── toolchain-integration.mk       # Toolchain integration rules
├── templates/                          # Package templates
│   ├── rust-package-template/         # Template for Rust packages
│   ├── hybrid-package-template/       # Template for hybrid packages
│   └── openwrt-integration-template/  # Template for OpenWrt integration
├── toolchain/                          # Toolchain configuration
│   ├── rust-targets.toml              # Rust target configurations
│   ├── cross-compile-config.sh        # Cross-compilation configuration
│   └── toolchain-validation.sh        # Toolchain validation scripts
├── ci-cd/                              # CI/CD integration
│   ├── github-actions/                # GitHub Actions workflows
│   ├── build-matrix.yml               # Build matrix configuration
│   └── artifact-management.sh         # Build artifact management
└── validation/                         # Build validation
    ├── build-tests.sh                 # Build system tests
    ├── package-validation.sh          # Package validation tests
    └── integration-tests.sh           # Integration tests
```

## Key Features

### 1. Unified Build Orchestration
- **Single Entry Point**: One command to build entire hybrid system
- **Dependency Management**: Automatic resolution of build dependencies
- **Parallel Building**: Concurrent building of independent components
- **Target Coordination**: Multi-architecture build coordination

### 2. Cross-Compilation Integration
- **Rust Toolchain Management**: Automatic setup of Rust cross-compilation
- **OpenWrt Toolchain Integration**: Seamless integration with OpenWrt toolchain
- **Target Configuration**: Support for all OpenWrt target architectures
- **Toolchain Validation**: Automatic validation of toolchain setup

### 3. Package Building System
- **Rust Package Support**: Native building of Rust packages for OpenWrt
- **OpenWrt Package Integration**: Building of official OpenWrt packages
- **Hybrid Packages**: Packages combining Rust and C components
- **Package Templates**: Standardized templates for different package types

### 4. OpenWrt SDK Integration
- **SDK Management**: Automatic download and setup of OpenWrt SDK
- **Makefile Integration**: Seamless integration with OpenWrt Makefile system
- **Package Format Support**: Support for IPK and APK package formats
- **Build Environment**: Consistent build environment across platforms

## Usage

### Quick Start

1. **Initialize Build System**:
   ```bash
   ./scripts/build-manager.sh init
   ```

2. **Setup Toolchains**:
   ```bash
   ./scripts/rust-toolchain-setup.sh --target mips-unknown-linux-musl
   ./scripts/openwrt-sdk-integration.sh --version 23.05.0 --target ath79
   ```

3. **Build Hybrid System**:
   ```bash
   ./scripts/build-manager.sh build --target ath79 --profile hybrid
   ```

### Advanced Usage

#### Building Specific Components

```bash
# Build only Rust components
./scripts/build-manager.sh build --component rust --target mips-unknown-linux-musl

# Build only OpenWrt packages
./scripts/build-manager.sh build --component openwrt --target ath79

# Build hybrid packages
./scripts/build-manager.sh build --component hybrid --target ath79
```

#### Cross-Compilation Setup

```bash
# Setup Rust cross-compilation for specific target
./scripts/rust-toolchain-setup.sh \
    --target mips-unknown-linux-musl \
    --sysroot /path/to/openwrt/staging_dir \
    --linker mips-openwrt-linux-musl-gcc

# Validate toolchain setup
./toolchain/toolchain-validation.sh --target mips-unknown-linux-musl
```

#### Package Development

```bash
# Create new Rust package
./scripts/package-builder.sh create \
    --type rust \
    --name my-rust-package \
    --template rust-package-template

# Create hybrid package
./scripts/package-builder.sh create \
    --type hybrid \
    --name my-hybrid-package \
    --template hybrid-package-template
```

## Supported Targets

### OpenWrt Architectures
- **MIPS**: mips_24kc, mips_4kec, mipsel_24kc, mipsel_74kc
- **ARM**: arm_cortex-a7, arm_cortex-a9, arm_cortex-a15, aarch64_cortex-a53
- **x86**: i386, x86_64

### Rust Targets
- **MIPS**: mips-unknown-linux-musl, mipsel-unknown-linux-musl
- **ARM**: arm-unknown-linux-musleabi, armv7-unknown-linux-musleabihf, aarch64-unknown-linux-musl
- **x86**: i686-unknown-linux-musl, x86_64-unknown-linux-musl

## Build Profiles

### Development Profile
- **Debug Builds**: Rust components built with debug information
- **Fast Compilation**: Optimized for development speed
- **Local Testing**: Includes testing and validation tools

### Production Profile
- **Release Builds**: Optimized Rust components
- **Size Optimization**: Minimized binary sizes
- **Security Hardening**: Enhanced security features enabled

### Hybrid Profile
- **Mixed Optimization**: Balanced between development and production
- **Component Selection**: Selective building of components
- **Testing Integration**: Comprehensive testing enabled

## Configuration

### Build Configuration

The build system uses TOML configuration files for flexible setup:

```toml
# build-config.toml
[build]
profile = "hybrid"
parallel_jobs = 4
verbose = true

[targets]
primary = "mips_24kc"
secondary = ["arm_cortex-a7", "x86_64"]

[rust]
toolchain = "stable"
features = ["std", "alloc"]
optimization = "size"

[openwrt]
version = "23.05.0"
sdk_path = "./openwrt-sdk"
package_arch = "mips_24kc"
```

### Environment Variables

```bash
# Build system configuration
export OPENWRT_RUST_BUILD_PROFILE=hybrid
export OPENWRT_RUST_TARGET=mips_24kc
export OPENWRT_RUST_PARALLEL_JOBS=4

# Toolchain configuration
export RUST_TARGET=mips-unknown-linux-musl
export OPENWRT_SDK_PATH=./openwrt-sdk
export CROSS_COMPILE=mips-openwrt-linux-musl-

# Package configuration
export PACKAGE_OUTPUT_DIR=./packages
export PACKAGE_CACHE_DIR=./cache
export PACKAGE_VALIDATION=true
```

## Integration Points

### CI/CD Integration
- **GitHub Actions**: Automated building and testing
- **Build Matrix**: Multi-target build validation
- **Artifact Management**: Automated package publishing
- **Quality Gates**: Automated quality and security checks

### Development Workflow
- **IDE Integration**: Support for popular development environments
- **Debugging Support**: Cross-debugging capabilities
- **Testing Framework**: Comprehensive testing integration
- **Documentation Generation**: Automatic documentation building

### Deployment Integration
- **Package Repositories**: Integration with package repositories
- **Update Mechanisms**: Automated update deployment
- **Rollback Support**: Safe deployment with rollback capabilities
- **Monitoring Integration**: Build and deployment monitoring

## Performance Characteristics

### Build Performance
- **Parallel Building**: Up to 4x speedup with parallel jobs
- **Incremental Builds**: Only rebuild changed components
- **Caching**: Aggressive caching of build artifacts
- **Cross-Compilation**: Optimized cross-compilation workflows

### Resource Usage
- **Memory**: ~2-4GB RAM for full builds
- **Disk**: ~10-20GB for complete toolchain and cache
- **CPU**: Scales with available cores (recommended 4+ cores)
- **Network**: Minimal after initial setup

## Troubleshooting

### Common Issues

1. **Toolchain Setup Failures**
   - Verify OpenWrt SDK download and extraction
   - Check Rust toolchain installation
   - Validate cross-compilation environment

2. **Build Failures**
   - Check dependency resolution
   - Verify target architecture compatibility
   - Review build logs for specific errors

3. **Package Integration Issues**
   - Validate package format compatibility
   - Check dependency declarations
   - Verify installation scripts

### Debug Mode

Enable debug mode for detailed build information:

```bash
export OPENWRT_RUST_DEBUG=1
./scripts/build-manager.sh build --verbose --debug
```

## Contributing

### Development Setup
1. Clone repository with build system
2. Run initialization script
3. Setup development environment
4. Run validation tests

### Testing
- Unit tests for build scripts
- Integration tests for toolchain setup
- End-to-end tests for package building
- Performance benchmarks

---

**Build System Version**: 1.0
**OpenWrt Compatibility**: 23.05.0+
**Rust Compatibility**: 1.70.0+
**Last Updated**: 2025-01-27
