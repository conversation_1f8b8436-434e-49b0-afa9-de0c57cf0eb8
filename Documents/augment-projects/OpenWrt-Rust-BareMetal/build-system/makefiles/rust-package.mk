# OpenWrt Rust Package Build Rules
# Makefile template for building Rust packages in OpenWrt environment
# Phase 11.3 - Build System Integration

# Include OpenWrt package infrastructure
include $(TOPDIR)/rules.mk

# Rust package configuration
RUST_PKG_NAME ?= $(PKG_NAME)
RUST_PKG_VERSION ?= $(PKG_VERSION)
RUST_PKG_RELEASE ?= $(PKG_RELEASE)

# Rust build configuration
RUST_TARGET ?= $(call rust_target_from_arch,$(ARCH))
RUST_PROFILE ?= release
RUST_FEATURES ?= std,alloc
RUST_TARGET_DIR ?= $(PKG_BUILD_DIR)/target

# Cross-compilation configuration
RUST_CROSS_COMPILE ?= $(TARGET_CROSS)
RUST_SYSROOT ?= $(STAGING_DIR)
RUST_LINKER ?= $(TARGET_CC)

# Cargo configuration
CARGO_HOME ?= $(STAGING_DIR_HOST)/.cargo
CARGO_TARGET_DIR ?= $(RUST_TARGET_DIR)
CARGO_BUILD_ARGS ?= --target $(RUST_TARGET) --$(RUST_PROFILE)

# Environment variables for Rust compilation
RUST_ENV_VARS := \
	CARGO_HOME="$(CARGO_HOME)" \
	CARGO_TARGET_DIR="$(CARGO_TARGET_DIR)" \
	RUSTFLAGS="-C linker=$(RUST_LINKER) -C target-feature=+crt-static" \
	PKG_CONFIG_ALLOW_CROSS=1 \
	PKG_CONFIG_PATH="$(STAGING_DIR)/usr/lib/pkgconfig" \
	CC_$(subst -,_,$(RUST_TARGET))="$(TARGET_CC)" \
	AR_$(subst -,_,$(RUST_TARGET))="$(TARGET_AR)" \
	CARGO_TARGET_$(shell echo $(RUST_TARGET) | tr '[:lower:]' '[:upper:]' | tr '-' '_')_LINKER="$(TARGET_CC)"

# Function to convert OpenWrt architecture to Rust target
define rust_target_from_arch
$(strip \
	$(if $(filter mips mips_24kc mips_4kec,$(1)),mips-unknown-linux-musl, \
	$(if $(filter mipsel mipsel_24kc mipsel_74kc,$(1)),mipsel-unknown-linux-musl, \
	$(if $(filter arm arm_cortex-a7 arm_cortex-a9,$(1)),arm-unknown-linux-musleabi, \
	$(if $(filter arm_cortex-a15,$(1)),armv7-unknown-linux-musleabihf, \
	$(if $(filter aarch64 aarch64_cortex-a53 aarch64_cortex-a72,$(1)),aarch64-unknown-linux-musl, \
	$(if $(filter i386,$(1)),i686-unknown-linux-musl, \
	$(if $(filter x86_64,$(1)),x86_64-unknown-linux-musl, \
	$(error Unsupported architecture: $(1)))))))))
endef

# Default Rust package build steps
define Build/Prepare/Rust
	$(call Build/Prepare/Default)
	# Setup Rust toolchain
	$(RUST_ENV_VARS) rustup target add $(RUST_TARGET) || true
	# Create cargo configuration
	mkdir -p $(PKG_BUILD_DIR)/.cargo
	echo '[target.$(RUST_TARGET)]' > $(PKG_BUILD_DIR)/.cargo/config.toml
	echo 'linker = "$(RUST_LINKER)"' >> $(PKG_BUILD_DIR)/.cargo/config.toml
	echo 'rustflags = ["-C", "target-feature=+crt-static"]' >> $(PKG_BUILD_DIR)/.cargo/config.toml
endef

define Build/Configure/Rust
	# Rust packages typically don't need separate configure step
	# Configuration is handled through Cargo.toml and environment variables
	true
endef

define Build/Compile/Rust
	cd $(PKG_BUILD_DIR) && \
	$(RUST_ENV_VARS) \
	cargo build $(CARGO_BUILD_ARGS) \
		$(if $(RUST_FEATURES),--features "$(RUST_FEATURES)") \
		$(if $(RUST_NO_DEFAULT_FEATURES),--no-default-features) \
		$(if $(RUST_ALL_FEATURES),--all-features) \
		$(if $(RUST_BINS),--bins) \
		$(if $(RUST_BIN),--bin $(RUST_BIN)) \
		$(if $(RUST_EXAMPLES),--examples) \
		$(if $(RUST_EXAMPLE),--example $(RUST_EXAMPLE)) \
		$(if $(RUST_TESTS),--tests) \
		$(if $(RUST_BENCHES),--benches) \
		$(if $(RUST_ALL_TARGETS),--all-targets) \
		$(RUST_EXTRA_ARGS)
endef

define Build/Install/Rust
	# Install Rust binaries
	$(if $(RUST_PKG_BINS), \
		$(foreach bin,$(RUST_PKG_BINS), \
			$(INSTALL_DIR) $(1)/usr/bin && \
			$(INSTALL_BIN) $(RUST_TARGET_DIR)/$(RUST_TARGET)/$(RUST_PROFILE)/$(bin) $(1)/usr/bin/$(bin); \
		) \
	)
	# Install Rust libraries
	$(if $(RUST_PKG_LIBS), \
		$(foreach lib,$(RUST_PKG_LIBS), \
			$(INSTALL_DIR) $(1)/usr/lib && \
			$(INSTALL_DATA) $(RUST_TARGET_DIR)/$(RUST_TARGET)/$(RUST_PROFILE)/$(lib) $(1)/usr/lib/$(lib); \
		) \
	)
	# Install configuration files
	$(if $(RUST_PKG_CONFIGS), \
		$(foreach config,$(RUST_PKG_CONFIGS), \
			$(INSTALL_DIR) $(1)/etc && \
			$(INSTALL_CONF) $(PKG_BUILD_DIR)/$(config) $(1)/etc/$(notdir $(config)); \
		) \
	)
	# Install data files
	$(if $(RUST_PKG_DATA), \
		$(foreach data,$(RUST_PKG_DATA), \
			$(INSTALL_DIR) $(1)/usr/share/$(RUST_PKG_NAME) && \
			$(INSTALL_DATA) $(PKG_BUILD_DIR)/$(data) $(1)/usr/share/$(RUST_PKG_NAME)/$(notdir $(data)); \
		) \
	)
endef

# Rust package testing
define Build/Test/Rust
	cd $(PKG_BUILD_DIR) && \
	$(RUST_ENV_VARS) \
	cargo test $(CARGO_BUILD_ARGS) \
		$(if $(RUST_FEATURES),--features "$(RUST_FEATURES)") \
		$(if $(RUST_NO_DEFAULT_FEATURES),--no-default-features) \
		$(if $(RUST_ALL_FEATURES),--all-features) \
		$(RUST_TEST_ARGS)
endef

# Rust package documentation
define Build/Doc/Rust
	cd $(PKG_BUILD_DIR) && \
	$(RUST_ENV_VARS) \
	cargo doc $(CARGO_BUILD_ARGS) \
		$(if $(RUST_FEATURES),--features "$(RUST_FEATURES)") \
		$(if $(RUST_NO_DEFAULT_FEATURES),--no-default-features) \
		$(if $(RUST_ALL_FEATURES),--all-features) \
		--no-deps \
		$(RUST_DOC_ARGS)
endef

# Rust package cleanup
define Build/Clean/Rust
	cd $(PKG_BUILD_DIR) && \
	$(RUST_ENV_VARS) \
	cargo clean
	rm -rf $(PKG_BUILD_DIR)/.cargo
endef

# Package information template for Rust packages
define Package/$(PKG_NAME)/Default
  SECTION:=lang-rust
  CATEGORY:=Languages
  SUBMENU:=Rust
  TITLE:=$(RUST_PKG_NAME) - Rust package
  URL:=https://crates.io/crates/$(RUST_PKG_NAME)
  DEPENDS:=+libc +libpthread $(RUST_PKG_DEPENDS)
  PKGARCH:=$(ARCH)
endef

# Default package description
define Package/$(PKG_NAME)/description
  $(RUST_PKG_DESCRIPTION)
endef

# Default package installation
define Package/$(PKG_NAME)/install
	$(call Build/Install/Rust,$(1))
endef

# Rust package validation
define Package/$(PKG_NAME)/postinst
#!/bin/sh
# Validate Rust package installation
if [ -z "$${IPKG_INSTROOT}" ]; then
	# Validate binaries
	$(foreach bin,$(RUST_PKG_BINS), \
		if [ -x "/usr/bin/$(bin)" ]; then \
			echo "Rust binary $(bin) installed successfully"; \
		else \
			echo "Error: Rust binary $(bin) not found or not executable"; \
			exit 1; \
		fi; \
	)
	# Validate libraries
	$(foreach lib,$(RUST_PKG_LIBS), \
		if [ -f "/usr/lib/$(lib)" ]; then \
			echo "Rust library $(lib) installed successfully"; \
		else \
			echo "Error: Rust library $(lib) not found"; \
			exit 1; \
		fi; \
	)
fi
endef

# Rust package removal
define Package/$(PKG_NAME)/prerm
#!/bin/sh
# Pre-removal script for Rust package
if [ -z "$${IPKG_INSTROOT}" ]; then
	# Stop any running services
	$(if $(RUST_PKG_SERVICES), \
		$(foreach service,$(RUST_PKG_SERVICES), \
			/etc/init.d/$(service) stop 2>/dev/null || true; \
		) \
	)
fi
endef

# Build rules assignment
Build/Prepare ?= $(Build/Prepare/Rust)
Build/Configure ?= $(Build/Configure/Rust)
Build/Compile ?= $(Build/Compile/Rust)
Build/Install ?= $(Build/Install/Rust)

# Optional build rules
ifdef RUST_ENABLE_TESTS
Build/Test := $(Build/Test/Rust)
endif

ifdef RUST_ENABLE_DOCS
Build/Doc := $(Build/Doc/Rust)
endif

# Clean rule
Build/Clean := $(Build/Clean/Rust)

# Rust-specific variables that can be overridden
RUST_PKG_BINS ?=
RUST_PKG_LIBS ?=
RUST_PKG_CONFIGS ?=
RUST_PKG_DATA ?=
RUST_PKG_SERVICES ?=
RUST_PKG_DEPENDS ?=
RUST_PKG_DESCRIPTION ?= Rust package for OpenWrt

# Additional Cargo arguments
RUST_EXTRA_ARGS ?=
RUST_TEST_ARGS ?=
RUST_DOC_ARGS ?=

# Feature flags
RUST_NO_DEFAULT_FEATURES ?=
RUST_ALL_FEATURES ?=
RUST_BINS ?=
RUST_BIN ?=
RUST_EXAMPLES ?=
RUST_EXAMPLE ?=
RUST_TESTS ?=
RUST_BENCHES ?=
RUST_ALL_TARGETS ?=

# Enable testing and documentation by default in development builds
ifeq ($(CONFIG_DEVEL),y)
RUST_ENABLE_TESTS ?= 1
RUST_ENABLE_DOCS ?= 1
endif

# Optimization flags based on build profile
ifeq ($(RUST_PROFILE),release)
CARGO_BUILD_ARGS += --release
RUSTFLAGS += -C opt-level=s -C lto=fat
endif

ifeq ($(RUST_PROFILE),debug)
RUSTFLAGS += -C opt-level=0 -C debug-assertions=on
endif

# Size optimization for embedded targets
ifeq ($(CONFIG_TARGET_OPTIMIZATION_SIZE),y)
RUSTFLAGS += -C opt-level=z -C panic=abort
endif

# Security hardening
ifeq ($(CONFIG_PKG_FORTIFY_SOURCE_2),y)
RUSTFLAGS += -C force-frame-pointers=yes
endif

# Export Rust environment
export RUST_ENV_VARS
export CARGO_HOME
export CARGO_TARGET_DIR
export RUSTFLAGS

# Include standard OpenWrt package infrastructure
include $(INCLUDE_DIR)/package.mk

# Rust package template
define RustPackage
  define Package/$(1)
    $(call Package/$(1)/Default)
  endef

  define Package/$(1)/description
    $(call Package/$(1)/description)
  endef

  define Package/$(1)/install
    $(call Package/$(1)/install,$$(1))
  endef

  $$(eval $$(call BuildPackage,$(1)))
endef
