#!/bin/bash

# OpenWrt SDK Integration Script
# Downloads, configures, and integrates OpenWrt SDK with Rust build system
# Phase 11.3 - Build System Integration

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_SYSTEM_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
PROJECT_ROOT="$(cd "$BUILD_SYSTEM_ROOT/.." && pwd)"
SDK_CACHE_DIR="$PROJECT_ROOT/build-cache/openwrt-sdk"
SDK_CONFIG_DIR="$BUILD_SYSTEM_ROOT/toolchain"

# Default configuration
DEFAULT_OPENWRT_VERSION="23.05.0"
DEFAULT_TARGET="ath79"
DEFAULT_SUBTARGET="generic"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# OpenWrt target architectures and their details
declare -A OPENWRT_TARGETS=(
    ["ath79"]="mips_24kc"
    ["ramips"]="mipsel_24kc"
    ["bcm27xx"]="arm_cortex-a7"
    ["bcm63xx"]="mips_mips32"
    ["x86"]="x86_64"
    ["mediatek"]="aarch64_cortex-a53"
    ["rockchip"]="aarch64_cortex-a72"
    ["ipq40xx"]="arm_cortex-a7"
    ["ipq806x"]="arm_cortex-a15"
    ["mvebu"]="arm_cortex-a9"
)

# SDK download URLs
OPENWRT_SDK_BASE_URL="https://downloads.openwrt.org/releases"

# Usage information
usage() {
    cat << EOF
OpenWrt SDK Integration Script

Usage: $0 [options]

Options:
    --init                  Initialize SDK integration system
    --setup                 Setup SDK for target
    --download              Download SDK for target
    --configure             Configure SDK
    --validate              Validate SDK setup
    --clean                 Clean SDK cache
    --list                  List available targets
    --version VERSION       OpenWrt version [default: $DEFAULT_OPENWRT_VERSION]
    --target TARGET         Target platform [default: $DEFAULT_TARGET]
    --subtarget SUBTARGET   Target subtarget [default: $DEFAULT_SUBTARGET]
    --arch ARCH             Target architecture (auto-detected)
    --help                  Show this help message

Supported Targets:
$(for target in "${!OPENWRT_TARGETS[@]}"; do echo "    $target (${OPENWRT_TARGETS[$target]})"; done | sort)

Examples:
    $0 --init
    $0 --setup --target ath79 --version 23.05.0
    $0 --download --target ramips --subtarget mt7621
    $0 --validate --target x86
    $0 --list

EOF
}

# Initialize SDK integration system
init_sdk_system() {
    log_info "Initializing OpenWrt SDK integration system..."
    
    # Create necessary directories
    mkdir -p "$SDK_CACHE_DIR"
    mkdir -p "$SDK_CONFIG_DIR"
    
    # Create SDK configuration
    create_sdk_config
    
    # Check required tools
    check_required_tools
    
    log_success "SDK integration system initialized"
}

# Create SDK configuration
create_sdk_config() {
    local config_file="$SDK_CONFIG_DIR/openwrt-sdk-config.toml"
    
    if [[ ! -f "$config_file" ]]; then
        log_info "Creating SDK configuration..."
        cat > "$config_file" << EOF
# OpenWrt SDK Configuration

[sdk]
version = "$DEFAULT_OPENWRT_VERSION"
base_url = "$OPENWRT_SDK_BASE_URL"
cache_dir = "$SDK_CACHE_DIR"

[targets]
default = "$DEFAULT_TARGET"

[build]
parallel_jobs = 4
verbose = false
clean_build = false

EOF
        log_info "Created SDK configuration: $config_file"
    fi
}

# Check required tools
check_required_tools() {
    local required_tools=("wget" "tar" "make" "gcc" "git")
    local missing_tools=()
    
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        log_info "Please install the missing tools and try again"
        exit 1
    fi
    
    log_info "All required tools are available"
}

# Setup SDK for target
setup_sdk() {
    local version="$1"
    local target="$2"
    local subtarget="$3"
    
    log_info "Setting up OpenWrt SDK for $target/$subtarget (version $version)"
    
    # Download SDK if not present
    download_sdk "$version" "$target" "$subtarget"
    
    # Configure SDK
    configure_sdk "$version" "$target" "$subtarget"
    
    # Validate SDK setup
    validate_sdk_setup "$version" "$target" "$subtarget"
    
    log_success "SDK setup completed for $target/$subtarget"
}

# Download SDK
download_sdk() {
    local version="$1"
    local target="$2"
    local subtarget="$3"
    local arch="${OPENWRT_TARGETS[$target]:-unknown}"
    
    local sdk_name="openwrt-sdk-${version}-${target}-${subtarget}_gcc-11.2.0_musl.Linux-x86_64"
    local sdk_archive="${sdk_name}.tar.xz"
    local sdk_url="${OPENWRT_SDK_BASE_URL}/${version}/targets/${target}/${subtarget}/${sdk_archive}"
    local sdk_dir="$SDK_CACHE_DIR/$version-$target-$subtarget"
    
    # Check if SDK already exists
    if [[ -d "$sdk_dir" ]]; then
        log_info "SDK already exists: $sdk_dir"
        return 0
    fi
    
    log_info "Downloading OpenWrt SDK..."
    log_info "URL: $sdk_url"
    
    # Create temporary download directory
    local temp_dir="$SDK_CACHE_DIR/temp-$$"
    mkdir -p "$temp_dir"
    
    # Download SDK
    if wget -q --show-progress -O "$temp_dir/$sdk_archive" "$sdk_url"; then
        log_success "SDK downloaded successfully"
    else
        log_error "Failed to download SDK from: $sdk_url"
        rm -rf "$temp_dir"
        exit 1
    fi
    
    # Extract SDK
    log_info "Extracting SDK..."
    cd "$temp_dir"
    if tar -xf "$sdk_archive"; then
        log_success "SDK extracted successfully"
    else
        log_error "Failed to extract SDK"
        rm -rf "$temp_dir"
        exit 1
    fi
    
    # Move SDK to final location
    local extracted_dir=$(find . -maxdepth 1 -type d -name "openwrt-sdk-*" | head -1)
    if [[ -n "$extracted_dir" ]]; then
        mv "$extracted_dir" "$sdk_dir"
        log_success "SDK installed to: $sdk_dir"
    else
        log_error "Could not find extracted SDK directory"
        rm -rf "$temp_dir"
        exit 1
    fi
    
    # Cleanup
    rm -rf "$temp_dir"
}

# Configure SDK
configure_sdk() {
    local version="$1"
    local target="$2"
    local subtarget="$3"
    local sdk_dir="$SDK_CACHE_DIR/$version-$target-$subtarget"
    
    log_info "Configuring OpenWrt SDK..."
    
    if [[ ! -d "$sdk_dir" ]]; then
        log_error "SDK directory not found: $sdk_dir"
        exit 1
    fi
    
    cd "$sdk_dir"
    
    # Update feeds
    log_info "Updating feeds..."
    if [[ -f "scripts/feeds" ]]; then
        ./scripts/feeds update -a
        ./scripts/feeds install -a
    fi
    
    # Create basic configuration
    log_info "Creating SDK configuration..."
    if [[ -f "Config.in" ]]; then
        make defconfig
    fi
    
    # Setup environment
    setup_sdk_environment "$sdk_dir"
    
    log_success "SDK configuration completed"
}

# Setup SDK environment
setup_sdk_environment() {
    local sdk_dir="$1"
    local env_file="$sdk_dir/sdk-env.sh"
    
    log_info "Setting up SDK environment..."
    
    cat > "$env_file" << EOF
#!/bin/bash
# OpenWrt SDK Environment Setup
# Source this file to setup SDK environment

export OPENWRT_SDK_PATH="$sdk_dir"
export STAGING_DIR="\$OPENWRT_SDK_PATH/staging_dir"
export PATH="\$STAGING_DIR/host/bin:\$PATH"
export PKG_CONFIG_PATH="\$STAGING_DIR/target-*/usr/lib/pkgconfig"
export CROSS_COMPILE="\$(find \$STAGING_DIR -name "*-gcc" | head -1 | sed 's/-gcc$//')-"

# Rust integration
export CARGO_TARGET_DIR="\$OPENWRT_SDK_PATH/rust-target"
export RUSTFLAGS="-C linker=\$CROSS_COMPILE\gcc"

echo "OpenWrt SDK environment configured"
echo "SDK Path: \$OPENWRT_SDK_PATH"
echo "Cross Compiler: \$CROSS_COMPILE\gcc"
EOF
    
    chmod +x "$env_file"
    log_info "SDK environment script created: $env_file"
}

# Validate SDK setup
validate_sdk_setup() {
    local version="$1"
    local target="$2"
    local subtarget="$3"
    local sdk_dir="$SDK_CACHE_DIR/$version-$target-$subtarget"
    
    log_info "Validating SDK setup..."
    
    # Check SDK directory
    if [[ ! -d "$sdk_dir" ]]; then
        log_error "SDK directory not found: $sdk_dir"
        return 1
    fi
    
    # Check essential files
    local essential_files=("Makefile" "rules.mk" "include/toplevel.mk")
    for file in "${essential_files[@]}"; do
        if [[ ! -f "$sdk_dir/$file" ]]; then
            log_error "Essential file missing: $file"
            return 1
        fi
    done
    
    # Check staging directory
    if [[ ! -d "$sdk_dir/staging_dir" ]]; then
        log_error "Staging directory not found"
        return 1
    fi
    
    # Check toolchain
    local toolchain_dir=$(find "$sdk_dir/staging_dir" -name "toolchain-*" -type d | head -1)
    if [[ -z "$toolchain_dir" ]]; then
        log_error "Toolchain not found in staging directory"
        return 1
    fi
    
    # Test basic make functionality
    cd "$sdk_dir"
    if make help &>/dev/null; then
        log_success "SDK make functionality validated"
    else
        log_warning "SDK make functionality test failed"
    fi
    
    log_success "SDK validation completed"
}

# List available targets
list_targets() {
    log_info "Available OpenWrt targets:"
    echo
    printf "%-15s %-20s %-30s\n" "Target" "Architecture" "Description"
    printf "%-15s %-20s %-30s\n" "------" "------------" "-----------"
    
    for target in $(printf '%s\n' "${!OPENWRT_TARGETS[@]}" | sort); do
        local arch="${OPENWRT_TARGETS[$target]}"
        printf "%-15s %-20s %-30s\n" "$target" "$arch" "OpenWrt $target platform"
    done
    
    echo
    log_info "Installed SDKs:"
    if [[ -d "$SDK_CACHE_DIR" ]]; then
        find "$SDK_CACHE_DIR" -maxdepth 1 -type d -name "*-*-*" | sed 's|.*/||' | sed 's/^/  /' | sort
    else
        echo "  None"
    fi
}

# Clean SDK cache
clean_sdk_cache() {
    log_info "Cleaning SDK cache..."
    
    if [[ -d "$SDK_CACHE_DIR" ]]; then
        rm -rf "$SDK_CACHE_DIR"
        mkdir -p "$SDK_CACHE_DIR"
        log_success "SDK cache cleaned"
    else
        log_info "SDK cache directory does not exist"
    fi
}

# Get SDK path for target
get_sdk_path() {
    local version="$1"
    local target="$2"
    local subtarget="$3"
    local sdk_dir="$SDK_CACHE_DIR/$version-$target-$subtarget"
    
    if [[ -d "$sdk_dir" ]]; then
        echo "$sdk_dir"
    else
        log_error "SDK not found for $target/$subtarget (version $version)"
        exit 1
    fi
}

# Main function
main() {
    local command=""
    local version="$DEFAULT_OPENWRT_VERSION"
    local target="$DEFAULT_TARGET"
    local subtarget="$DEFAULT_SUBTARGET"
    local arch=""
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --init)
                command="init"
                shift
                ;;
            --setup)
                command="setup"
                shift
                ;;
            --download)
                command="download"
                shift
                ;;
            --configure)
                command="configure"
                shift
                ;;
            --validate)
                command="validate"
                shift
                ;;
            --clean)
                command="clean"
                shift
                ;;
            --list)
                command="list"
                shift
                ;;
            --version)
                version="$2"
                shift 2
                ;;
            --target)
                target="$2"
                shift 2
                ;;
            --subtarget)
                subtarget="$2"
                shift 2
                ;;
            --arch)
                arch="$2"
                shift 2
                ;;
            --help)
                usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done
    
    # Auto-detect architecture if not specified
    if [[ -z "$arch" ]]; then
        arch="${OPENWRT_TARGETS[$target]:-unknown}"
        if [[ "$arch" == "unknown" ]]; then
            log_error "Unknown target: $target"
            log_info "Use --list to see available targets"
            exit 1
        fi
    fi
    
    # Execute command
    case "$command" in
        "init")
            init_sdk_system
            ;;
        "setup")
            setup_sdk "$version" "$target" "$subtarget"
            ;;
        "download")
            download_sdk "$version" "$target" "$subtarget"
            ;;
        "configure")
            configure_sdk "$version" "$target" "$subtarget"
            ;;
        "validate")
            validate_sdk_setup "$version" "$target" "$subtarget"
            ;;
        "clean")
            clean_sdk_cache
            ;;
        "list")
            list_targets
            ;;
        "")
            log_error "No command specified"
            usage
            exit 1
            ;;
        *)
            log_error "Unknown command: $command"
            usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
