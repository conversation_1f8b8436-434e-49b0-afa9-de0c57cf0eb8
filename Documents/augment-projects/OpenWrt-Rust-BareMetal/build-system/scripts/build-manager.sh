#!/bin/bash

# OpenWrt-Rust Hybrid Build Manager
# Main orchestration script for building hybrid OpenWrt-Rust systems
# Phase 11.3 - Build System Integration

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_SYSTEM_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
PROJECT_ROOT="$(cd "$BUILD_SYSTEM_ROOT/.." && pwd)"
BUILD_CONFIG_FILE="$BUILD_SYSTEM_ROOT/build-config.toml"
BUILD_CACHE_DIR="$PROJECT_ROOT/build-cache"
BUILD_OUTPUT_DIR="$PROJECT_ROOT/build-output"

# Default configuration
DEFAULT_PROFILE="hybrid"
DEFAULT_TARGET="mips_24kc"
DEFAULT_PARALLEL_JOBS="4"
DEFAULT_OPENWRT_VERSION="23.05.0"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [[ "${OPENWRT_RUST_DEBUG:-0}" == "1" ]]; then
        echo -e "${PURPLE}[DEBUG]${NC} $1"
    fi
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# Build configuration
BUILD_PROFILE="${OPENWRT_RUST_BUILD_PROFILE:-$DEFAULT_PROFILE}"
BUILD_TARGET="${OPENWRT_RUST_TARGET:-$DEFAULT_TARGET}"
PARALLEL_JOBS="${OPENWRT_RUST_PARALLEL_JOBS:-$DEFAULT_PARALLEL_JOBS}"
VERBOSE="${OPENWRT_RUST_VERBOSE:-0}"
DEBUG="${OPENWRT_RUST_DEBUG:-0}"

# Usage information
usage() {
    cat << EOF
OpenWrt-Rust Hybrid Build Manager

Usage: $0 <command> [options]

Commands:
    init                Initialize build system
    build               Build hybrid system
    clean               Clean build artifacts
    validate            Validate build environment
    package             Build specific packages
    toolchain           Manage toolchains
    help                Show this help message

Build Options:
    --profile PROFILE   Build profile (development|production|hybrid) [default: $DEFAULT_PROFILE]
    --target TARGET     Target architecture [default: $DEFAULT_TARGET]
    --component COMP    Component to build (rust|openwrt|hybrid|all) [default: all]
    --jobs JOBS         Number of parallel jobs [default: $DEFAULT_PARALLEL_JOBS]
    --verbose           Enable verbose output
    --debug             Enable debug output
    --clean             Clean before building

Toolchain Options:
    --setup             Setup toolchains
    --validate          Validate toolchain setup
    --list              List available targets

Package Options:
    --name NAME         Package name
    --type TYPE         Package type (rust|openwrt|hybrid)
    --template TEMPLATE Package template

Examples:
    $0 init
    $0 build --profile production --target mips_24kc
    $0 build --component rust --target arm_cortex-a7
    $0 package --name my-package --type rust
    $0 toolchain --setup --target mips_24kc

Environment Variables:
    OPENWRT_RUST_BUILD_PROFILE    Build profile
    OPENWRT_RUST_TARGET          Target architecture
    OPENWRT_RUST_PARALLEL_JOBS   Number of parallel jobs
    OPENWRT_RUST_VERBOSE         Enable verbose output (1/0)
    OPENWRT_RUST_DEBUG           Enable debug output (1/0)

EOF
}

# Initialize build system
init_build_system() {
    log_step "Initializing OpenWrt-Rust Hybrid Build System"
    
    # Create necessary directories
    log_info "Creating build directories..."
    mkdir -p "$BUILD_CACHE_DIR"
    mkdir -p "$BUILD_OUTPUT_DIR"
    mkdir -p "$BUILD_OUTPUT_DIR/packages"
    mkdir -p "$BUILD_OUTPUT_DIR/images"
    mkdir -p "$BUILD_OUTPUT_DIR/logs"
    
    # Create build configuration if it doesn't exist
    if [[ ! -f "$BUILD_CONFIG_FILE" ]]; then
        log_info "Creating default build configuration..."
        create_default_config
    fi
    
    # Initialize git submodules if needed
    if [[ -f "$PROJECT_ROOT/.gitmodules" ]]; then
        log_info "Initializing git submodules..."
        cd "$PROJECT_ROOT"
        git submodule update --init --recursive
    fi
    
    # Setup toolchains
    log_info "Setting up toolchains..."
    "$SCRIPT_DIR/rust-toolchain-setup.sh" --init
    "$SCRIPT_DIR/openwrt-sdk-integration.sh" --init
    
    log_success "Build system initialized successfully"
}

# Create default build configuration
create_default_config() {
    cat > "$BUILD_CONFIG_FILE" << EOF
# OpenWrt-Rust Hybrid Build Configuration

[build]
profile = "$DEFAULT_PROFILE"
parallel_jobs = $DEFAULT_PARALLEL_JOBS
verbose = false
debug = false

[targets]
primary = "$DEFAULT_TARGET"
secondary = ["arm_cortex-a7", "x86_64"]

[rust]
toolchain = "stable"
features = ["std", "alloc"]
optimization = "size"
target_dir = "target"

[openwrt]
version = "$DEFAULT_OPENWRT_VERSION"
sdk_path = "./openwrt-sdk"
package_arch = "$DEFAULT_TARGET"
feeds = ["base", "packages", "luci"]

[packages]
output_dir = "./build-output/packages"
cache_dir = "./build-cache/packages"
validation = true

[ci]
enabled = true
matrix_targets = ["mips_24kc", "arm_cortex-a7", "x86_64"]
artifact_retention = 30
EOF
    log_info "Created default configuration: $BUILD_CONFIG_FILE"
}

# Build hybrid system
build_hybrid_system() {
    local component="${1:-all}"
    local clean_build="${2:-false}"
    
    log_step "Building OpenWrt-Rust Hybrid System"
    log_info "Profile: $BUILD_PROFILE"
    log_info "Target: $BUILD_TARGET"
    log_info "Component: $component"
    log_info "Parallel Jobs: $PARALLEL_JOBS"
    
    # Clean if requested
    if [[ "$clean_build" == "true" ]]; then
        log_info "Cleaning build artifacts..."
        clean_build_artifacts
    fi
    
    # Validate build environment
    log_info "Validating build environment..."
    validate_build_environment
    
    # Setup build environment
    setup_build_environment
    
    # Build components based on selection
    case "$component" in
        "rust")
            build_rust_components
            ;;
        "openwrt")
            build_openwrt_components
            ;;
        "hybrid")
            build_hybrid_components
            ;;
        "all")
            build_rust_components
            build_openwrt_components
            build_hybrid_components
            ;;
        *)
            log_error "Unknown component: $component"
            exit 1
            ;;
    esac
    
    # Generate build report
    generate_build_report
    
    log_success "Build completed successfully"
}

# Setup build environment
setup_build_environment() {
    log_info "Setting up build environment..."
    
    # Export build variables
    export OPENWRT_RUST_BUILD_PROFILE="$BUILD_PROFILE"
    export OPENWRT_RUST_TARGET="$BUILD_TARGET"
    export OPENWRT_RUST_PARALLEL_JOBS="$PARALLEL_JOBS"
    export OPENWRT_RUST_VERBOSE="$VERBOSE"
    export OPENWRT_RUST_DEBUG="$DEBUG"
    
    # Setup Rust environment
    export CARGO_TARGET_DIR="$BUILD_CACHE_DIR/rust-target"
    export RUSTFLAGS="-C target-cpu=native"
    
    # Setup OpenWrt environment
    export OPENWRT_SDK_PATH="$BUILD_CACHE_DIR/openwrt-sdk"
    export STAGING_DIR="$OPENWRT_SDK_PATH/staging_dir"
    
    # Create build timestamp
    export BUILD_TIMESTAMP="$(date +%Y%m%d_%H%M%S)"
    export BUILD_ID="$BUILD_PROFILE-$BUILD_TARGET-$BUILD_TIMESTAMP"
    
    log_debug "Build environment setup completed"
}

# Build Rust components
build_rust_components() {
    log_step "Building Rust components"
    
    # Setup Rust toolchain for target
    "$SCRIPT_DIR/rust-toolchain-setup.sh" --target "$BUILD_TARGET"
    
    # Build Rust packages
    log_info "Building Rust packages..."
    cd "$PROJECT_ROOT"
    
    case "$BUILD_PROFILE" in
        "development")
            cargo build --target "$(get_rust_target)" --features "std,alloc"
            ;;
        "production")
            cargo build --release --target "$(get_rust_target)" --features "std,alloc"
            ;;
        "hybrid")
            cargo build --target "$(get_rust_target)" --features "std,alloc"
            ;;
    esac
    
    log_success "Rust components built successfully"
}

# Build OpenWrt components
build_openwrt_components() {
    log_step "Building OpenWrt components"
    
    # Setup OpenWrt SDK
    "$SCRIPT_DIR/openwrt-sdk-integration.sh" --setup --target "$BUILD_TARGET"
    
    # Build OpenWrt packages
    log_info "Building OpenWrt packages..."
    cd "$OPENWRT_SDK_PATH"
    
    # Build base packages
    make package/base-files/compile V=s
    make package/kernel/compile V=s
    
    # Build additional packages based on profile
    case "$BUILD_PROFILE" in
        "development")
            make package/devel/compile V=s
            ;;
        "production")
            make package/network/compile V=s
            make package/utils/compile V=s
            ;;
        "hybrid")
            make package/network/compile V=s
            make package/utils/compile V=s
            ;;
    esac
    
    log_success "OpenWrt components built successfully"
}

# Build hybrid components
build_hybrid_components() {
    log_step "Building hybrid components"
    
    # Build hybrid packages that combine Rust and OpenWrt components
    log_info "Building hybrid packages..."
    
    # Use hybrid package builder
    "$SCRIPT_DIR/package-builder.sh" build \
        --type hybrid \
        --target "$BUILD_TARGET" \
        --profile "$BUILD_PROFILE"
    
    log_success "Hybrid components built successfully"
}

# Get Rust target for OpenWrt architecture
get_rust_target() {
    case "$BUILD_TARGET" in
        "mips_24kc"|"mips_4kec")
            echo "mips-unknown-linux-musl"
            ;;
        "mipsel_24kc"|"mipsel_74kc")
            echo "mipsel-unknown-linux-musl"
            ;;
        "arm_cortex-a7"|"arm_cortex-a9")
            echo "arm-unknown-linux-musleabi"
            ;;
        "arm_cortex-a15")
            echo "armv7-unknown-linux-musleabihf"
            ;;
        "aarch64_cortex-a53")
            echo "aarch64-unknown-linux-musl"
            ;;
        "i386")
            echo "i686-unknown-linux-musl"
            ;;
        "x86_64")
            echo "x86_64-unknown-linux-musl"
            ;;
        *)
            log_error "Unsupported target: $BUILD_TARGET"
            exit 1
            ;;
    esac
}

# Validate build environment
validate_build_environment() {
    log_info "Validating build environment..."
    
    # Check required tools
    local required_tools=("cargo" "rustc" "make" "gcc" "git")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "Required tool not found: $tool"
            exit 1
        fi
    done
    
    # Validate toolchain setup
    "$BUILD_SYSTEM_ROOT/toolchain/toolchain-validation.sh" --target "$BUILD_TARGET"
    
    log_success "Build environment validation passed"
}

# Clean build artifacts
clean_build_artifacts() {
    log_info "Cleaning build artifacts..."
    
    # Clean Rust artifacts
    if [[ -d "$BUILD_CACHE_DIR/rust-target" ]]; then
        rm -rf "$BUILD_CACHE_DIR/rust-target"
    fi
    
    # Clean OpenWrt artifacts
    if [[ -d "$BUILD_CACHE_DIR/openwrt-build" ]]; then
        rm -rf "$BUILD_CACHE_DIR/openwrt-build"
    fi
    
    # Clean output directory
    if [[ -d "$BUILD_OUTPUT_DIR" ]]; then
        rm -rf "$BUILD_OUTPUT_DIR"
        mkdir -p "$BUILD_OUTPUT_DIR"
    fi
    
    log_success "Build artifacts cleaned"
}

# Generate build report
generate_build_report() {
    local report_file="$BUILD_OUTPUT_DIR/build-report-$BUILD_ID.json"
    
    log_info "Generating build report..."
    
    cat > "$report_file" << EOF
{
  "build_info": {
    "id": "$BUILD_ID",
    "timestamp": "$BUILD_TIMESTAMP",
    "profile": "$BUILD_PROFILE",
    "target": "$BUILD_TARGET",
    "parallel_jobs": $PARALLEL_JOBS
  },
  "environment": {
    "rust_version": "$(rustc --version)",
    "cargo_version": "$(cargo --version)",
    "system": "$(uname -a)"
  },
  "artifacts": {
    "packages": "$(find "$BUILD_OUTPUT_DIR/packages" -name "*.ipk" 2>/dev/null | wc -l)",
    "images": "$(find "$BUILD_OUTPUT_DIR/images" -name "*.bin" 2>/dev/null | wc -l)"
  },
  "status": "completed"
}
EOF
    
    log_success "Build report generated: $report_file"
}

# Main command processing
main() {
    local command="${1:-help}"
    shift || true
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --profile)
                BUILD_PROFILE="$2"
                shift 2
                ;;
            --target)
                BUILD_TARGET="$2"
                shift 2
                ;;
            --component)
                COMPONENT="$2"
                shift 2
                ;;
            --jobs)
                PARALLEL_JOBS="$2"
                shift 2
                ;;
            --verbose)
                VERBOSE=1
                shift
                ;;
            --debug)
                DEBUG=1
                shift
                ;;
            --clean)
                CLEAN_BUILD=true
                shift
                ;;
            --help)
                usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done
    
    # Execute command
    case "$command" in
        "init")
            init_build_system
            ;;
        "build")
            build_hybrid_system "${COMPONENT:-all}" "${CLEAN_BUILD:-false}"
            ;;
        "clean")
            clean_build_artifacts
            ;;
        "validate")
            validate_build_environment
            ;;
        "package")
            "$SCRIPT_DIR/package-builder.sh" "$@"
            ;;
        "toolchain")
            "$SCRIPT_DIR/rust-toolchain-setup.sh" "$@"
            ;;
        "help"|*)
            usage
            ;;
    esac
}

# Run main function
main "$@"
