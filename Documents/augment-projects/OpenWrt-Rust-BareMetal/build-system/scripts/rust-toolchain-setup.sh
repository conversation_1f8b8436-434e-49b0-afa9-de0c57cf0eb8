#!/bin/bash

# Rust Toolchain Setup for OpenWrt Cross-Compilation
# Configures Rust cross-compilation for OpenWrt target architectures
# Phase 11.3 - Build System Integration

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_SYSTEM_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
PROJECT_ROOT="$(cd "$BUILD_SYSTEM_ROOT/.." && pwd)"
TOOLCHAIN_CONFIG_DIR="$BUILD_SYSTEM_ROOT/toolchain"
RUST_TOOLCHAIN_DIR="$PROJECT_ROOT/.rust-toolchains"

# Default configuration
DEFAULT_RUST_VERSION="stable"
DEFAULT_TARGET="mips-unknown-linux-musl"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Supported Rust targets for OpenWrt architectures
declare -A RUST_TARGETS=(
    ["mips_24kc"]="mips-unknown-linux-musl"
    ["mips_4kec"]="mips-unknown-linux-musl"
    ["mipsel_24kc"]="mipsel-unknown-linux-musl"
    ["mipsel_74kc"]="mipsel-unknown-linux-musl"
    ["arm_cortex-a7"]="arm-unknown-linux-musleabi"
    ["arm_cortex-a9"]="arm-unknown-linux-musleabi"
    ["arm_cortex-a15"]="armv7-unknown-linux-musleabihf"
    ["aarch64_cortex-a53"]="aarch64-unknown-linux-musl"
    ["i386"]="i686-unknown-linux-musl"
    ["x86_64"]="x86_64-unknown-linux-musl"
)

# Cross-compilation linkers for targets
declare -A TARGET_LINKERS=(
    ["mips-unknown-linux-musl"]="mips-openwrt-linux-musl-gcc"
    ["mipsel-unknown-linux-musl"]="mipsel-openwrt-linux-musl-gcc"
    ["arm-unknown-linux-musleabi"]="arm-openwrt-linux-muslgnueabi-gcc"
    ["armv7-unknown-linux-musleabihf"]="arm-openwrt-linux-muslgnueabihf-gcc"
    ["aarch64-unknown-linux-musl"]="aarch64-openwrt-linux-musl-gcc"
    ["i686-unknown-linux-musl"]="i486-openwrt-linux-musl-gcc"
    ["x86_64-unknown-linux-musl"]="x86_64-openwrt-linux-musl-gcc"
)

# Usage information
usage() {
    cat << EOF
Rust Toolchain Setup for OpenWrt Cross-Compilation

Usage: $0 [options]

Options:
    --init                  Initialize Rust toolchain system
    --target TARGET         Setup toolchain for specific target
    --openwrt-target ARCH   Setup for OpenWrt architecture
    --list                  List available targets
    --validate              Validate toolchain setup
    --version VERSION       Rust version to use [default: $DEFAULT_RUST_VERSION]
    --sysroot PATH          OpenWrt sysroot path
    --linker LINKER         Cross-compilation linker
    --help                  Show this help message

Supported OpenWrt Architectures:
$(for arch in "${!RUST_TARGETS[@]}"; do echo "    $arch -> ${RUST_TARGETS[$arch]}"; done | sort)

Examples:
    $0 --init
    $0 --target mips-unknown-linux-musl
    $0 --openwrt-target mips_24kc
    $0 --list
    $0 --validate --target mips-unknown-linux-musl

EOF
}

# Initialize Rust toolchain system
init_toolchain_system() {
    log_info "Initializing Rust toolchain system..."
    
    # Create toolchain directories
    mkdir -p "$RUST_TOOLCHAIN_DIR"
    mkdir -p "$TOOLCHAIN_CONFIG_DIR"
    
    # Check if rustup is installed
    if ! command -v rustup &> /dev/null; then
        log_error "rustup is not installed. Please install rustup first."
        log_info "Visit: https://rustup.rs/"
        exit 1
    fi
    
    # Install stable toolchain if not present
    if ! rustup toolchain list | grep -q "stable"; then
        log_info "Installing stable Rust toolchain..."
        rustup toolchain install stable
    fi
    
    # Set stable as default
    rustup default stable
    
    # Create cargo configuration directory
    mkdir -p "$PROJECT_ROOT/.cargo"
    
    log_success "Rust toolchain system initialized"
}

# Setup toolchain for specific target
setup_target_toolchain() {
    local rust_target="$1"
    local openwrt_sysroot="${2:-}"
    local custom_linker="${3:-}"
    
    log_info "Setting up Rust toolchain for target: $rust_target"
    
    # Install target if not present
    if ! rustup target list --installed | grep -q "$rust_target"; then
        log_info "Installing Rust target: $rust_target"
        rustup target add "$rust_target"
    else
        log_info "Rust target already installed: $rust_target"
    fi
    
    # Determine linker
    local linker="$custom_linker"
    if [[ -z "$linker" ]]; then
        linker="${TARGET_LINKERS[$rust_target]:-}"
        if [[ -z "$linker" ]]; then
            log_warning "No default linker configured for target: $rust_target"
            return 0
        fi
    fi
    
    # Create target-specific cargo configuration
    create_cargo_config "$rust_target" "$linker" "$openwrt_sysroot"
    
    # Validate toolchain setup
    validate_target_toolchain "$rust_target"
    
    log_success "Toolchain setup completed for: $rust_target"
}

# Create cargo configuration for target
create_cargo_config() {
    local rust_target="$1"
    local linker="$2"
    local sysroot="$3"
    
    local cargo_config="$PROJECT_ROOT/.cargo/config.toml"
    
    log_info "Creating cargo configuration for $rust_target..."
    
    # Create base configuration if it doesn't exist
    if [[ ! -f "$cargo_config" ]]; then
        cat > "$cargo_config" << EOF
# OpenWrt-Rust Cross-Compilation Configuration
# Generated by rust-toolchain-setup.sh

[build]
target-dir = "target"

EOF
    fi
    
    # Add target-specific configuration
    local target_section="[target.$rust_target]"
    
    # Remove existing target section if present
    if grep -q "^\[target\.$rust_target\]" "$cargo_config"; then
        # Create temporary file without the target section
        awk "
        /^\[target\.$rust_target\]/ { skip=1; next }
        /^\[/ && skip { skip=0 }
        !skip { print }
        " "$cargo_config" > "$cargo_config.tmp"
        mv "$cargo_config.tmp" "$cargo_config"
    fi
    
    # Add new target configuration
    cat >> "$cargo_config" << EOF

$target_section
linker = "$linker"
EOF
    
    # Add sysroot if provided
    if [[ -n "$sysroot" ]]; then
        cat >> "$cargo_config" << EOF
rustflags = [
    "-C", "link-arg=--sysroot=$sysroot",
    "-C", "target-feature=+crt-static"
]
EOF
    else
        cat >> "$cargo_config" << EOF
rustflags = [
    "-C", "target-feature=+crt-static"
]
EOF
    fi
    
    log_info "Cargo configuration updated: $cargo_config"
}

# Setup toolchain for OpenWrt architecture
setup_openwrt_target() {
    local openwrt_arch="$1"
    local openwrt_sysroot="${2:-}"
    
    # Get corresponding Rust target
    local rust_target="${RUST_TARGETS[$openwrt_arch]:-}"
    if [[ -z "$rust_target" ]]; then
        log_error "Unsupported OpenWrt architecture: $openwrt_arch"
        log_info "Supported architectures:"
        for arch in "${!RUST_TARGETS[@]}"; do
            echo "  $arch"
        done | sort
        exit 1
    fi
    
    log_info "Setting up toolchain for OpenWrt architecture: $openwrt_arch"
    log_info "Rust target: $rust_target"
    
    # Setup the Rust target
    setup_target_toolchain "$rust_target" "$openwrt_sysroot"
}

# Validate target toolchain
validate_target_toolchain() {
    local rust_target="$1"
    
    log_info "Validating toolchain for: $rust_target"
    
    # Check if target is installed
    if ! rustup target list --installed | grep -q "$rust_target"; then
        log_error "Target not installed: $rust_target"
        return 1
    fi
    
    # Check if linker is available
    local linker="${TARGET_LINKERS[$rust_target]:-}"
    if [[ -n "$linker" ]] && ! command -v "$linker" &> /dev/null; then
        log_warning "Cross-compilation linker not found: $linker"
        log_info "Make sure OpenWrt toolchain is installed and in PATH"
    fi
    
    # Test compilation
    test_target_compilation "$rust_target"
    
    log_success "Toolchain validation passed for: $rust_target"
}

# Test target compilation
test_target_compilation() {
    local rust_target="$1"
    local test_dir="$RUST_TOOLCHAIN_DIR/test-$rust_target"
    
    log_info "Testing compilation for: $rust_target"
    
    # Create test project
    mkdir -p "$test_dir"
    cd "$test_dir"
    
    # Create simple test program
    cat > "main.rs" << 'EOF'
#![no_std]
#![no_main]

use core::panic::PanicInfo;

#[no_mangle]
pub extern "C" fn _start() -> ! {
    loop {}
}

#[panic_handler]
fn panic(_info: &PanicInfo) -> ! {
    loop {}
}
EOF
    
    # Try to compile
    if rustc --target "$rust_target" --crate-type bin main.rs -o test-binary 2>/dev/null; then
        log_success "Test compilation successful for: $rust_target"
        rm -f test-binary main.rs
    else
        log_warning "Test compilation failed for: $rust_target"
        log_info "This may be normal if cross-compilation linker is not available"
    fi
    
    cd - > /dev/null
    rm -rf "$test_dir"
}

# List available targets
list_targets() {
    log_info "Available OpenWrt architectures and corresponding Rust targets:"
    echo
    printf "%-20s %-30s %-30s\n" "OpenWrt Arch" "Rust Target" "Linker"
    printf "%-20s %-30s %-30s\n" "------------" "-----------" "------"
    
    for arch in $(printf '%s\n' "${!RUST_TARGETS[@]}" | sort); do
        local rust_target="${RUST_TARGETS[$arch]}"
        local linker="${TARGET_LINKERS[$rust_target]:-N/A}"
        printf "%-20s %-30s %-30s\n" "$arch" "$rust_target" "$linker"
    done
    
    echo
    log_info "Installed Rust targets:"
    rustup target list --installed | sed 's/^/  /'
}

# Validate all configured toolchains
validate_all_toolchains() {
    log_info "Validating all configured toolchains..."
    
    local cargo_config="$PROJECT_ROOT/.cargo/config.toml"
    if [[ ! -f "$cargo_config" ]]; then
        log_warning "No cargo configuration found"
        return 0
    fi
    
    # Extract configured targets from cargo config
    local configured_targets
    configured_targets=$(grep -o '^\[target\.[^]]*\]' "$cargo_config" | sed 's/\[target\.\(.*\)\]/\1/' || true)
    
    if [[ -z "$configured_targets" ]]; then
        log_info "No targets configured in cargo config"
        return 0
    fi
    
    local validation_failed=false
    while IFS= read -r target; do
        if [[ -n "$target" ]]; then
            log_info "Validating target: $target"
            if ! validate_target_toolchain "$target"; then
                validation_failed=true
            fi
        fi
    done <<< "$configured_targets"
    
    if [[ "$validation_failed" == "true" ]]; then
        log_error "Some toolchain validations failed"
        exit 1
    else
        log_success "All toolchain validations passed"
    fi
}

# Main function
main() {
    local command=""
    local target=""
    local openwrt_target=""
    local rust_version="$DEFAULT_RUST_VERSION"
    local sysroot=""
    local linker=""
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --init)
                command="init"
                shift
                ;;
            --target)
                command="setup-target"
                target="$2"
                shift 2
                ;;
            --openwrt-target)
                command="setup-openwrt"
                openwrt_target="$2"
                shift 2
                ;;
            --list)
                command="list"
                shift
                ;;
            --validate)
                command="validate"
                shift
                ;;
            --version)
                rust_version="$2"
                shift 2
                ;;
            --sysroot)
                sysroot="$2"
                shift 2
                ;;
            --linker)
                linker="$2"
                shift 2
                ;;
            --help)
                usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done
    
    # Execute command
    case "$command" in
        "init")
            init_toolchain_system
            ;;
        "setup-target")
            if [[ -z "$target" ]]; then
                log_error "Target not specified"
                usage
                exit 1
            fi
            setup_target_toolchain "$target" "$sysroot" "$linker"
            ;;
        "setup-openwrt")
            if [[ -z "$openwrt_target" ]]; then
                log_error "OpenWrt target not specified"
                usage
                exit 1
            fi
            setup_openwrt_target "$openwrt_target" "$sysroot"
            ;;
        "list")
            list_targets
            ;;
        "validate")
            if [[ -n "$target" ]]; then
                validate_target_toolchain "$target"
            else
                validate_all_toolchains
            fi
            ;;
        "")
            log_error "No command specified"
            usage
            exit 1
            ;;
        *)
            log_error "Unknown command: $command"
            usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
