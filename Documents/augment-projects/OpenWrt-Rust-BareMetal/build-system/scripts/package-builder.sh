#!/bin/bash

# Package Builder Script for OpenWrt-Rust Hybrid System
# Builds packages for Rust, OpenWrt, and hybrid components
# Phase 11.3 - Build System Integration

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_SYSTEM_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
PROJECT_ROOT="$(cd "$BUILD_SYSTEM_ROOT/.." && pwd)"
TEMPLATES_DIR="$BUILD_SYSTEM_ROOT/templates"
PACKAGES_OUTPUT_DIR="$PROJECT_ROOT/build-output/packages"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Package types
PACKAGE_TYPES=("rust" "openwrt" "hybrid")

# Usage information
usage() {
    cat << EOF
Package Builder for OpenWrt-Rust Hybrid System

Usage: $0 <command> [options]

Commands:
    create              Create new package from template
    build               Build existing package
    install             Install package to target
    validate            Validate package structure
    list                List available packages
    clean               Clean package build artifacts

Create Options:
    --name NAME         Package name (required)
    --type TYPE         Package type: rust|openwrt|hybrid (required)
    --template TEMPLATE Template to use
    --description DESC  Package description
    --version VERSION   Package version [default: 1.0.0]
    --author AUTHOR     Package author
    --license LICENSE   Package license [default: GPL-2.0]

Build Options:
    --target TARGET     Target architecture
    --profile PROFILE   Build profile: development|production|hybrid
    --features FEATURES Rust features to enable
    --parallel JOBS     Number of parallel jobs
    --verbose           Enable verbose output
    --clean             Clean before building

Install Options:
    --destination DIR   Installation destination
    --force             Force installation

Examples:
    $0 create --name my-rust-app --type rust --description "My Rust application"
    $0 build --name my-rust-app --target mips_24kc --profile production
    $0 install --name my-rust-app --destination /tmp/openwrt-root
    $0 validate --name my-rust-app
    $0 list --type rust

EOF
}

# Create new package from template
create_package() {
    local name="$1"
    local type="$2"
    local template="${3:-${type}-package-template}"
    local description="${4:-Package created with OpenWrt-Rust build system}"
    local version="${5:-1.0.0}"
    local author="${6:-OpenWrt-Rust Developer}"
    local license="${7:-GPL-2.0}"
    
    log_info "Creating new $type package: $name"
    
    # Validate package type
    if [[ ! " ${PACKAGE_TYPES[*]} " =~ " $type " ]]; then
        log_error "Invalid package type: $type"
        log_info "Supported types: ${PACKAGE_TYPES[*]}"
        exit 1
    fi
    
    # Check if template exists
    local template_dir="$TEMPLATES_DIR/$template"
    if [[ ! -d "$template_dir" ]]; then
        log_error "Template not found: $template"
        log_info "Available templates:"
        ls -1 "$TEMPLATES_DIR" | sed 's/^/  /'
        exit 1
    fi
    
    # Create package directory
    local package_dir="$PROJECT_ROOT/packages/$name"
    if [[ -d "$package_dir" ]]; then
        log_error "Package already exists: $name"
        exit 1
    fi
    
    mkdir -p "$package_dir"
    
    # Copy template files
    log_info "Copying template files..."
    cp -r "$template_dir"/* "$package_dir/"
    
    # Process template variables
    process_template_variables "$package_dir" "$name" "$type" "$description" "$version" "$author" "$license"
    
    # Initialize package based on type
    case "$type" in
        "rust")
            initialize_rust_package "$package_dir" "$name" "$description" "$version" "$author" "$license"
            ;;
        "openwrt")
            initialize_openwrt_package "$package_dir" "$name" "$description" "$version" "$author" "$license"
            ;;
        "hybrid")
            initialize_hybrid_package "$package_dir" "$name" "$description" "$version" "$author" "$license"
            ;;
    esac
    
    log_success "Package created successfully: $package_dir"
    log_info "Next steps:"
    log_info "  1. Edit the package configuration"
    log_info "  2. Implement the package functionality"
    log_info "  3. Build the package: $0 build --name $name"
}

# Process template variables
process_template_variables() {
    local package_dir="$1"
    local name="$2"
    local type="$3"
    local description="$4"
    local version="$5"
    local author="$6"
    local license="$7"
    local timestamp="$(date -Iseconds)"
    
    log_info "Processing template variables..."
    
    # Find all template files
    local template_files
    template_files=$(find "$package_dir" -type f -name "*.template" -o -name "*.toml" -o -name "Makefile*" -o -name "*.mk" -o -name "*.rs" -o -name "*.c" -o -name "*.h" -o -name "*.md")
    
    # Process each file
    while IFS= read -r file; do
        if [[ -f "$file" ]]; then
            sed -i.bak \
                -e "s/{{PACKAGE_NAME}}/$name/g" \
                -e "s/{{PACKAGE_TYPE}}/$type/g" \
                -e "s/{{PACKAGE_DESCRIPTION}}/$description/g" \
                -e "s/{{PACKAGE_VERSION}}/$version/g" \
                -e "s/{{PACKAGE_AUTHOR}}/$author/g" \
                -e "s/{{PACKAGE_LICENSE}}/$license/g" \
                -e "s/{{TIMESTAMP}}/$timestamp/g" \
                "$file"
            rm -f "$file.bak"
        fi
    done <<< "$template_files"
    
    # Rename template files
    find "$package_dir" -name "*.template" | while read -r template_file; do
        local new_name="${template_file%.template}"
        mv "$template_file" "$new_name"
    done
}

# Initialize Rust package
initialize_rust_package() {
    local package_dir="$1"
    local name="$2"
    local description="$3"
    local version="$4"
    local author="$5"
    local license="$6"
    
    log_info "Initializing Rust package..."
    
    # Create Cargo.toml if it doesn't exist
    if [[ ! -f "$package_dir/Cargo.toml" ]]; then
        cat > "$package_dir/Cargo.toml" << EOF
[package]
name = "$name"
version = "$version"
edition = "2021"
description = "$description"
authors = ["$author"]
license = "$license"
repository = "https://github.com/openwrt-rust/$name"

[dependencies]
# Add your dependencies here

[features]
default = ["std"]
std = []
alloc = []

[[bin]]
name = "$name"
path = "src/main.rs"

[profile.release]
opt-level = "s"
lto = true
codegen-units = 1
panic = "abort"
strip = true
EOF
    fi
    
    # Create src directory and main.rs
    mkdir -p "$package_dir/src"
    if [[ ! -f "$package_dir/src/main.rs" ]]; then
        cat > "$package_dir/src/main.rs" << EOF
//! $description
//! 
//! OpenWrt-Rust package: $name

fn main() {
    println!("Hello from $name!");
    println!("This is a Rust package for OpenWrt");
}
EOF
    fi
    
    # Create OpenWrt Makefile
    create_rust_makefile "$package_dir" "$name" "$description" "$version"
}

# Initialize OpenWrt package
initialize_openwrt_package() {
    local package_dir="$1"
    local name="$2"
    local description="$3"
    local version="$4"
    local author="$5"
    local license="$6"
    
    log_info "Initializing OpenWrt package..."
    
    # Create OpenWrt Makefile
    cat > "$package_dir/Makefile" << EOF
include \$(TOPDIR)/rules.mk

PKG_NAME:=$name
PKG_VERSION:=$version
PKG_RELEASE:=1

PKG_SOURCE:=\$(PKG_NAME)-\$(PKG_VERSION).tar.gz
PKG_SOURCE_URL:=https://github.com/openwrt-packages/\$(PKG_NAME)/archive/
PKG_HASH:=skip

PKG_LICENSE:=$license
PKG_LICENSE_FILES:=LICENSE
PKG_MAINTAINER:=$author

include \$(INCLUDE_DIR)/package.mk

define Package/$name
  SECTION:=utils
  CATEGORY:=Utilities
  TITLE:=$description
  URL:=https://github.com/openwrt-packages/$name
  DEPENDS:=+libc
endef

define Package/$name/description
  $description
endef

define Build/Configure
endef

define Build/Compile
	\$(MAKE) -C \$(PKG_BUILD_DIR) \\
		CC="\$(TARGET_CC)" \\
		CFLAGS="\$(TARGET_CFLAGS)" \\
		LDFLAGS="\$(TARGET_LDFLAGS)"
endef

define Package/$name/install
	\$(INSTALL_DIR) \$(1)/usr/bin
	\$(INSTALL_BIN) \$(PKG_BUILD_DIR)/$name \$(1)/usr/bin/
endef

\$(eval \$(call BuildPackage,$name))
EOF
    
    # Create source directory
    mkdir -p "$package_dir/src"
    
    # Create simple C program
    cat > "$package_dir/src/main.c" << EOF
/*
 * $description
 * OpenWrt package: $name
 */

#include <stdio.h>
#include <stdlib.h>

int main(int argc, char *argv[]) {
    printf("Hello from %s!\\n", "$name");
    printf("This is an OpenWrt package\\n");
    return 0;
}
EOF
    
    # Create simple Makefile for C compilation
    cat > "$package_dir/src/Makefile" << EOF
CC ?= gcc
CFLAGS ?= -Wall -Wextra -O2
TARGET = $name

all: \$(TARGET)

\$(TARGET): main.c
	\$(CC) \$(CFLAGS) -o \$(TARGET) main.c

clean:
	rm -f \$(TARGET)

.PHONY: all clean
EOF
}

# Initialize hybrid package
initialize_hybrid_package() {
    local package_dir="$1"
    local name="$2"
    local description="$3"
    local version="$4"
    local author="$5"
    local license="$6"
    
    log_info "Initializing hybrid package..."
    
    # Initialize both Rust and C components
    initialize_rust_package "$package_dir" "$name" "$description" "$version" "$author" "$license"
    
    # Create C library component
    mkdir -p "$package_dir/c-lib"
    cat > "$package_dir/c-lib/helper.c" << EOF
/*
 * C helper library for $name
 * Part of hybrid OpenWrt-Rust package
 */

#include "helper.h"
#include <stdio.h>

void c_helper_function(const char* message) {
    printf("C helper: %s\\n", message);
}

int c_helper_calculate(int a, int b) {
    return a + b;
}
EOF
    
    cat > "$package_dir/c-lib/helper.h" << EOF
/*
 * C helper library header for $name
 * Part of hybrid OpenWrt-Rust package
 */

#ifndef HELPER_H
#define HELPER_H

void c_helper_function(const char* message);
int c_helper_calculate(int a, int b);

#endif /* HELPER_H */
EOF
    
    # Create build.rs for Rust-C integration
    cat > "$package_dir/build.rs" << EOF
use std::env;
use std::path::PathBuf;

fn main() {
    // Build C library
    cc::Build::new()
        .file("c-lib/helper.c")
        .include("c-lib")
        .compile("helper");

    // Tell cargo to tell rustc to link the helper library
    println!("cargo:rustc-link-lib=static=helper");

    // Tell cargo to invalidate the built crate whenever the C source changes
    println!("cargo:rerun-if-changed=c-lib/helper.c");
    println!("cargo:rerun-if-changed=c-lib/helper.h");
}
EOF
    
    # Update Cargo.toml for hybrid build
    cat >> "$package_dir/Cargo.toml" << EOF

[build-dependencies]
cc = "1.0"
EOF
    
    # Update main.rs for hybrid functionality
    cat > "$package_dir/src/main.rs" << EOF
//! $description
//! 
//! Hybrid OpenWrt-Rust package: $name
//! Combines Rust and C components

use std::ffi::CString;

extern "C" {
    fn c_helper_function(message: *const i8);
    fn c_helper_calculate(a: i32, b: i32) -> i32;
}

fn main() {
    println!("Hello from hybrid package: $name!");
    
    // Call C helper function
    let message = CString::new("Hello from Rust calling C!").unwrap();
    unsafe {
        c_helper_function(message.as_ptr());
    }
    
    // Use C calculation function
    let result = unsafe { c_helper_calculate(10, 20) };
    println!("C calculation result: {}", result);
    
    println!("Hybrid package demonstration complete");
}
EOF
}

# Create Rust Makefile for OpenWrt
create_rust_makefile() {
    local package_dir="$1"
    local name="$2"
    local description="$3"
    local version="$4"
    
    cat > "$package_dir/Makefile" << EOF
include \$(TOPDIR)/rules.mk
include \$(BUILD_SYSTEM_ROOT)/makefiles/rust-package.mk

PKG_NAME:=$name
PKG_VERSION:=$version
PKG_RELEASE:=1

PKG_SOURCE:=\$(PKG_NAME)-\$(PKG_VERSION).tar.gz
PKG_SOURCE_URL:=https://github.com/openwrt-rust/\$(PKG_NAME)/archive/
PKG_HASH:=skip

PKG_LICENSE:=GPL-2.0
PKG_LICENSE_FILES:=LICENSE
PKG_MAINTAINER:=OpenWrt-Rust Team

# Rust package configuration
RUST_PKG_NAME:=$name
RUST_PKG_DESCRIPTION:=$description
RUST_PKG_BINS:=$name
RUST_FEATURES:=std,alloc

include \$(INCLUDE_DIR)/package.mk

define Package/$name
  \$(call Package/$name/Default)
  TITLE:=$description
  URL:=https://github.com/openwrt-rust/$name
endef

define Package/$name/description
  $description
  
  This package is built with the OpenWrt-Rust hybrid build system.
endef

\$(eval \$(call RustPackage,$name))
EOF
}

# Build package
build_package() {
    local name="$1"
    local target="${2:-mips_24kc}"
    local profile="${3:-hybrid}"
    local features="${4:-}"
    local parallel="${5:-4}"
    local verbose="${6:-false}"
    local clean="${7:-false}"
    
    log_info "Building package: $name"
    log_info "Target: $target, Profile: $profile"
    
    local package_dir="$PROJECT_ROOT/packages/$name"
    if [[ ! -d "$package_dir" ]]; then
        log_error "Package not found: $name"
        exit 1
    fi
    
    # Determine package type
    local package_type
    if [[ -f "$package_dir/Cargo.toml" ]]; then
        if [[ -d "$package_dir/c-lib" ]]; then
            package_type="hybrid"
        else
            package_type="rust"
        fi
    else
        package_type="openwrt"
    fi
    
    log_info "Package type: $package_type"
    
    # Setup build environment
    export OPENWRT_RUST_TARGET="$target"
    export OPENWRT_RUST_BUILD_PROFILE="$profile"
    export OPENWRT_RUST_PARALLEL_JOBS="$parallel"
    export OPENWRT_RUST_VERBOSE="$verbose"
    
    # Build based on package type
    case "$package_type" in
        "rust")
            build_rust_package "$package_dir" "$name" "$target" "$profile" "$features" "$clean"
            ;;
        "openwrt")
            build_openwrt_package "$package_dir" "$name" "$target" "$profile" "$clean"
            ;;
        "hybrid")
            build_hybrid_package "$package_dir" "$name" "$target" "$profile" "$features" "$clean"
            ;;
    esac
    
    log_success "Package built successfully: $name"
}

# Build Rust package
build_rust_package() {
    local package_dir="$1"
    local name="$2"
    local target="$3"
    local profile="$4"
    local features="$5"
    local clean="$6"
    
    log_info "Building Rust package..."
    
    cd "$package_dir"
    
    # Setup Rust toolchain
    "$BUILD_SYSTEM_ROOT/scripts/rust-toolchain-setup.sh" --openwrt-target "$target"
    
    # Clean if requested
    if [[ "$clean" == "true" ]]; then
        cargo clean
    fi
    
    # Build package
    local rust_target
    rust_target=$("$BUILD_SYSTEM_ROOT/scripts/build-manager.sh" get-rust-target "$target")
    
    local cargo_args="--target $rust_target"
    if [[ "$profile" == "production" ]]; then
        cargo_args="$cargo_args --release"
    fi
    if [[ -n "$features" ]]; then
        cargo_args="$cargo_args --features $features"
    fi
    
    cargo build $cargo_args
    
    # Create package
    create_package_archive "$package_dir" "$name" "$target"
}

# Build OpenWrt package
build_openwrt_package() {
    local package_dir="$1"
    local name="$2"
    local target="$3"
    local profile="$4"
    local clean="$5"
    
    log_info "Building OpenWrt package..."
    
    # Setup OpenWrt SDK
    "$BUILD_SYSTEM_ROOT/scripts/openwrt-sdk-integration.sh" --setup --target "$target"
    
    # Build using OpenWrt build system
    local sdk_path
    sdk_path=$("$BUILD_SYSTEM_ROOT/scripts/openwrt-sdk-integration.sh" get-sdk-path "$target")
    
    cd "$sdk_path"
    
    # Copy package to SDK
    cp -r "$package_dir" "package/$name"
    
    # Build package
    make package/"$name"/compile V=s
    
    # Copy built package to output
    mkdir -p "$PACKAGES_OUTPUT_DIR"
    find bin/packages -name "${name}_*.ipk" -exec cp {} "$PACKAGES_OUTPUT_DIR/" \;
}

# Build hybrid package
build_hybrid_package() {
    local package_dir="$1"
    local name="$2"
    local target="$3"
    local profile="$4"
    local features="$5"
    local clean="$6"
    
    log_info "Building hybrid package..."
    
    # Build as Rust package with C integration
    build_rust_package "$package_dir" "$name" "$target" "$profile" "$features" "$clean"
}

# Create package archive
create_package_archive() {
    local package_dir="$1"
    local name="$2"
    local target="$3"
    
    log_info "Creating package archive..."
    
    mkdir -p "$PACKAGES_OUTPUT_DIR"
    
    # Create simple tar.gz archive for now
    # In a full implementation, this would create proper IPK packages
    cd "$package_dir"
    tar -czf "$PACKAGES_OUTPUT_DIR/${name}_${target}.tar.gz" .
    
    log_info "Package archive created: $PACKAGES_OUTPUT_DIR/${name}_${target}.tar.gz"
}

# List packages
list_packages() {
    local type_filter="${1:-}"
    
    log_info "Available packages:"
    
    if [[ ! -d "$PROJECT_ROOT/packages" ]]; then
        log_info "No packages found"
        return 0
    fi
    
    local packages_found=false
    for package_dir in "$PROJECT_ROOT/packages"/*; do
        if [[ -d "$package_dir" ]]; then
            local package_name=$(basename "$package_dir")
            local package_type="unknown"
            
            # Determine package type
            if [[ -f "$package_dir/Cargo.toml" ]]; then
                if [[ -d "$package_dir/c-lib" ]]; then
                    package_type="hybrid"
                else
                    package_type="rust"
                fi
            elif [[ -f "$package_dir/Makefile" ]]; then
                package_type="openwrt"
            fi
            
            # Filter by type if specified
            if [[ -z "$type_filter" || "$package_type" == "$type_filter" ]]; then
                printf "  %-20s %-10s %s\n" "$package_name" "$package_type" "$(get_package_description "$package_dir")"
                packages_found=true
            fi
        fi
    done
    
    if [[ "$packages_found" == "false" ]]; then
        log_info "No packages found matching criteria"
    fi
}

# Get package description
get_package_description() {
    local package_dir="$1"
    
    # Try to extract description from various sources
    if [[ -f "$package_dir/Cargo.toml" ]]; then
        grep '^description' "$package_dir/Cargo.toml" | cut -d'"' -f2 2>/dev/null || echo "Rust package"
    elif [[ -f "$package_dir/Makefile" ]]; then
        grep 'TITLE:=' "$package_dir/Makefile" | cut -d'=' -f2 2>/dev/null || echo "OpenWrt package"
    else
        echo "Package"
    fi
}

# Main function
main() {
    local command="${1:-help}"
    shift || true
    
    # Parse command line arguments
    local name=""
    local type=""
    local template=""
    local description=""
    local version="1.0.0"
    local author="OpenWrt-Rust Developer"
    local license="GPL-2.0"
    local target="mips_24kc"
    local profile="hybrid"
    local features=""
    local parallel="4"
    local verbose="false"
    local clean="false"
    local destination=""
    local force="false"
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --name)
                name="$2"
                shift 2
                ;;
            --type)
                type="$2"
                shift 2
                ;;
            --template)
                template="$2"
                shift 2
                ;;
            --description)
                description="$2"
                shift 2
                ;;
            --version)
                version="$2"
                shift 2
                ;;
            --author)
                author="$2"
                shift 2
                ;;
            --license)
                license="$2"
                shift 2
                ;;
            --target)
                target="$2"
                shift 2
                ;;
            --profile)
                profile="$2"
                shift 2
                ;;
            --features)
                features="$2"
                shift 2
                ;;
            --parallel)
                parallel="$2"
                shift 2
                ;;
            --verbose)
                verbose="true"
                shift
                ;;
            --clean)
                clean="true"
                shift
                ;;
            --destination)
                destination="$2"
                shift 2
                ;;
            --force)
                force="true"
                shift
                ;;
            --help)
                usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done
    
    # Execute command
    case "$command" in
        "create")
            if [[ -z "$name" || -z "$type" ]]; then
                log_error "Package name and type are required for create command"
                usage
                exit 1
            fi
            create_package "$name" "$type" "$template" "$description" "$version" "$author" "$license"
            ;;
        "build")
            if [[ -z "$name" ]]; then
                log_error "Package name is required for build command"
                usage
                exit 1
            fi
            build_package "$name" "$target" "$profile" "$features" "$parallel" "$verbose" "$clean"
            ;;
        "list")
            list_packages "$type"
            ;;
        "help"|*)
            usage
            ;;
    esac
}

# Run main function
main "$@"
