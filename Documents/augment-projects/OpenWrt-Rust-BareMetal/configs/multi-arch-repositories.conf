# Multi-Architecture Repository Configuration
# Official OpenWrt Package Repository Integration
# Base URL: https://downloads.openwrt.org/releases/packages-24.10/

# x86 Architectures
src/gz openwrt_x86_64_base https://downloads.openwrt.org/releases/packages-24.10/x86_64/base
src/gz openwrt_x86_64_packages https://downloads.openwrt.org/releases/packages-24.10/x86_64/packages
src/gz openwrt_x86_64_luci https://downloads.openwrt.org/releases/packages-24.10/x86_64/luci

src/gz openwrt_i386_pentium4_base https://downloads.openwrt.org/releases/packages-24.10/i386_pentium4/base
src/gz openwrt_i386_pentium4_packages https://downloads.openwrt.org/releases/packages-24.10/i386_pentium4/packages

src/gz openwrt_i386_pentium_mmx_base https://downloads.openwrt.org/releases/packages-24.10/i386_pentium-mmx/base
src/gz openwrt_i386_pentium_mmx_packages https://downloads.openwrt.org/releases/packages-24.10/i386_pentium-mmx/packages

# ARM 64-bit Architectures
src/gz openwrt_aarch64_cortex_a53_base https://downloads.openwrt.org/releases/packages-24.10/aarch64_cortex-a53/base
src/gz openwrt_aarch64_cortex_a53_packages https://downloads.openwrt.org/releases/packages-24.10/aarch64_cortex-a53/packages

src/gz openwrt_aarch64_cortex_a72_base https://downloads.openwrt.org/releases/packages-24.10/aarch64_cortex-a72/base
src/gz openwrt_aarch64_cortex_a72_packages https://downloads.openwrt.org/releases/packages-24.10/aarch64_cortex-a72/packages

src/gz openwrt_aarch64_cortex_a76_base https://downloads.openwrt.org/releases/packages-24.10/aarch64_cortex-a76/base
src/gz openwrt_aarch64_cortex_a76_packages https://downloads.openwrt.org/releases/packages-24.10/aarch64_cortex-a76/packages

src/gz openwrt_aarch64_generic_base https://downloads.openwrt.org/releases/packages-24.10/aarch64_generic/base
src/gz openwrt_aarch64_generic_packages https://downloads.openwrt.org/releases/packages-24.10/aarch64_generic/packages

# ARM 32-bit Architectures
src/gz openwrt_arm_cortex_a7_base https://downloads.openwrt.org/releases/packages-24.10/arm_cortex-a7/base
src/gz openwrt_arm_cortex_a7_packages https://downloads.openwrt.org/releases/packages-24.10/arm_cortex-a7/packages

src/gz openwrt_arm_cortex_a7_neon_vfpv4_base https://downloads.openwrt.org/releases/packages-24.10/arm_cortex-a7_neon-vfpv4/base
src/gz openwrt_arm_cortex_a7_neon_vfpv4_packages https://downloads.openwrt.org/releases/packages-24.10/arm_cortex-a7_neon-vfpv4/packages

src/gz openwrt_arm_cortex_a9_base https://downloads.openwrt.org/releases/packages-24.10/arm_cortex-a9/base
src/gz openwrt_arm_cortex_a9_packages https://downloads.openwrt.org/releases/packages-24.10/arm_cortex-a9/packages

src/gz openwrt_arm_cortex_a15_neon_vfpv4_base https://downloads.openwrt.org/releases/packages-24.10/arm_cortex-a15_neon-vfpv4/base
src/gz openwrt_arm_cortex_a15_neon_vfpv4_packages https://downloads.openwrt.org/releases/packages-24.10/arm_cortex-a15_neon-vfpv4/packages

# MIPS Architectures
src/gz openwrt_mips_24kc_base https://downloads.openwrt.org/releases/packages-24.10/mips_24kc/base
src/gz openwrt_mips_24kc_packages https://downloads.openwrt.org/releases/packages-24.10/mips_24kc/packages

src/gz openwrt_mipsel_24kc_base https://downloads.openwrt.org/releases/packages-24.10/mipsel_24kc/base
src/gz openwrt_mipsel_24kc_packages https://downloads.openwrt.org/releases/packages-24.10/mipsel_24kc/packages

src/gz openwrt_mips_4kec_base https://downloads.openwrt.org/releases/packages-24.10/mips_4kec/base
src/gz openwrt_mips_4kec_packages https://downloads.openwrt.org/releases/packages-24.10/mips_4kec/packages

src/gz openwrt_mipsel_74kc_base https://downloads.openwrt.org/releases/packages-24.10/mipsel_74kc/base
src/gz openwrt_mipsel_74kc_packages https://downloads.openwrt.org/releases/packages-24.10/mipsel_74kc/packages

# MIPS 64-bit Architectures
src/gz openwrt_mips64_mips64r2_base https://downloads.openwrt.org/releases/packages-24.10/mips64_mips64r2/base
src/gz openwrt_mips64_mips64r2_packages https://downloads.openwrt.org/releases/packages-24.10/mips64_mips64r2/packages

src/gz openwrt_mips64el_mips64r2_base https://downloads.openwrt.org/releases/packages-24.10/mips64el_mips64r2/base
src/gz openwrt_mips64el_mips64r2_packages https://downloads.openwrt.org/releases/packages-24.10/mips64el_mips64r2/packages

# PowerPC Architectures
src/gz openwrt_powerpc64_e5500_base https://downloads.openwrt.org/releases/packages-24.10/powerpc64_e5500/base
src/gz openwrt_powerpc64_e5500_packages https://downloads.openwrt.org/releases/packages-24.10/powerpc64_e5500/packages

src/gz openwrt_powerpc_464fp_base https://downloads.openwrt.org/releases/packages-24.10/powerpc_464fp/base
src/gz openwrt_powerpc_464fp_packages https://downloads.openwrt.org/releases/packages-24.10/powerpc_464fp/packages

src/gz openwrt_powerpc_8548_base https://downloads.openwrt.org/releases/packages-24.10/powerpc_8548/base
src/gz openwrt_powerpc_8548_packages https://downloads.openwrt.org/releases/packages-24.10/powerpc_8548/packages

# RISC-V Architecture
src/gz openwrt_riscv64_riscv64_base https://downloads.openwrt.org/releases/packages-24.10/riscv64_riscv64/base
src/gz openwrt_riscv64_riscv64_packages https://downloads.openwrt.org/releases/packages-24.10/riscv64_riscv64/packages

# LoongArch Architecture
src/gz openwrt_loongarch64_generic_base https://downloads.openwrt.org/releases/packages-24.10/loongarch64_generic/base
src/gz openwrt_loongarch64_generic_packages https://downloads.openwrt.org/releases/packages-24.10/loongarch64_generic/packages

# Architecture Detection and Auto-Configuration
# The package manager will automatically select the appropriate repositories
# based on the detected target architecture

# Priority Configuration
# Higher priority repositories will be checked first
# Priority: 1 (highest) to 100 (lowest)

# Primary architectures (commonly used)
option priority_x86_64 1
option priority_aarch64_cortex_a53 2
option priority_arm_cortex_a7 3
option priority_mips_24kc 4
option priority_mipsel_24kc 5

# Secondary architectures
option priority_aarch64_cortex_a72 10
option priority_aarch64_cortex_a76 11
option priority_arm_cortex_a9 12
option priority_mips_4kec 13

# Specialized architectures
option priority_riscv64_riscv64 20
option priority_loongarch64_generic 21
option priority_powerpc64_e5500 22

# Configuration Options
option check_signature 1
option verify_ssl 1
option timeout 30
option retries 3
option cache_packages 1
option cache_duration 3600

# Mirror Configuration (for redundancy and performance)
# Primary mirror: downloads.openwrt.org
# Backup mirrors can be added here

# Example backup mirror configuration:
# src/gz openwrt_x86_64_base_mirror1 https://mirror1.openwrt.org/releases/packages-24.10/x86_64/base
# src/gz openwrt_x86_64_base_mirror2 https://mirror2.openwrt.org/releases/packages-24.10/x86_64/base
