#!/bin/bash

echo "=========================================="
echo "OpenWrt Rust Firmware + Packages Test"
echo "=========================================="
echo ""

# Test Rust firmware compilation
echo "🔧 Testing Rust firmware compilation..."
cargo build --bin simple-qemu-test --target x86_64-unknown-none --release

if [ $? -eq 0 ]; then
    echo "✅ Rust firmware compiled successfully"
else
    echo "❌ Rust firmware compilation failed"
    exit 1
fi

# Test essential packages
echo ""
echo "📦 Testing essential packages..."

# Test jq (JSON processing)
if command -v jq >/dev/null 2>&1; then
    echo '{"firmware": "OpenWrt Rust", "status": "testing"}' | jq '.firmware'
    echo "✅ jq JSON processing works"
else
    echo "⚠️  jq not available"
fi

# Test curl (HTTP client)
if command -v curl >/dev/null 2>&1; then
    echo "✅ curl HTTP client available"
else
    echo "⚠️  curl not available"
fi

# Test network tools
if command -v ip >/dev/null 2>&1; then
    echo "✅ ip networking tools available"
else
    echo "⚠️  ip tools not available"
fi

echo ""
echo "🎉 Firmware + Packages integration test completed!"
