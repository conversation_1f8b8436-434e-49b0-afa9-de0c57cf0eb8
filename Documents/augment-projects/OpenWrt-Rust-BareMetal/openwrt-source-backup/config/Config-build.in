# SPDX-License-Identifier: GPL-2.0-only
#
# Copyright (C) 2006-2013 OpenWrt.org
# Copyright (C) 2016 LEDE Project

config EXPERIMENTAL
	bool "Enable experimental features by default"
	help
	  Set this option to build with latest bleeding edge features
	  which may or may not work as expected.
	  If you would like to help the development of OpenWrt, you are
	  encouraged to set this option and provide feedback (both
	  positive and negative). But do so only if you know how to
	  recover your device in case of flashing potentially non-working
	  firmware.

	  If you plan to use this build in production, say NO!

menu "Global build settings"

	config JSON_OVERVIEW_IMAGE_INFO
		bool "Create JSON info file overview per target"
		default y
		help
		  Create a JSON info file called profiles.json in the target
		  directory containing machine readable list of built profiles
		  and resulting images.

	config JSON_CYCLONEDX_SBOM
		bool "Create CycloneDX SBOM JSON"
		default BUILDBOT
		help
		  Create a JSON files *.bom.cdx.json in the build
		  directory containing Software Bill Of Materials in CycloneDX
		  format.

	config ALL_NONSHARED
		bool "Select all target specific packages by default"
		select ALL_KMODS
		default BUILDBOT

	config ALL_KMODS
		bool "Select all kernel module packages by default"

	config ALL
		bool "Select all userspace packages by default"
		select ALL_KMODS
		select ALL_NONSHARED

	config BUILDBOT
		bool "Set build defaults for automatic builds (e.g. via buildbot)"
		help
		  This option changes several defaults to be more suitable for
		  automatic builds. This includes the following changes:
		  - Deleting build directories after compiling (to save space)
		  - Enabling per-device rootfs support
		  ...

	config SIGNED_PACKAGES
		bool "Cryptographically signed package lists"
		default y

	config SIGNATURE_CHECK
		bool "Enable signature checking in opkg"
		default SIGNED_PACKAGES

	config DOWNLOAD_CHECK_CERTIFICATE
		bool "Enable TLS certificate verification during package download"
		default y

	config USE_APK
		imply PACKAGE_apk-mbedtls
		bool "Use APK instead of OPKG to build distribution"
		default y

	comment "General build options"

	config TESTING_KERNEL
		bool "Use the testing kernel version"
		depends on HAS_TESTING_KERNEL
		default EXPERIMENTAL
		help
		  If the target supports a newer kernel version than the default,
		  you can use this config option to enable it


	config DISPLAY_SUPPORT
		bool "Show packages that require graphics support (local or remote)"

	config BUILD_PATENTED
		bool "Compile with support for patented functionality"
		help
		  When this option is disabled, software which provides patented functionality
		  will not be built.  In case software provides optional support for patented
		  functionality, this optional support will get disabled for this package.

	config BUILD_NLS
		bool "Compile with full language support"
		help
		  When this option is enabled, packages are built with the full versions of
		  iconv and GNU gettext instead of the default OpenWrt stubs. If uClibc is
		  used, it is also built with locale support.

	config SHADOW_PASSWORDS
		bool
		default y

	config CLEAN_IPKG
		bool
		prompt "Remove ipkg/opkg status data files in final images"
		help
		  This removes all ipkg/opkg status data files from the target directory
		  before building the root filesystem.

	config IPK_FILES_CHECKSUMS
		bool
		prompt "Record files checksums in package metadata"
		depends on !USE_APK
		help
		  This makes file checksums part of package metadata. It increases size
		  but provides you with pkg_check command to check for flash corruptions.

	config INCLUDE_CONFIG
		bool "Include build configuration in firmware" if DEVEL
		help
		  If enabled, buildinfo files will be stored in /etc/build.* of firmware.

	config REPRODUCIBLE_DEBUG_INFO
		bool "Make debug information reproducible"
		default BUILDBOT
		help
		  This strips the local build path out of debug information. This has the
		  advantage of making it reproducible, but the disadvantage of making local
		  debugging using ./scripts/remote-gdb harder, since the debug data will
		  no longer point to the full path on the build host.

	config COLLECT_KERNEL_DEBUG
		bool
		prompt "Collect kernel debug information"
		select KERNEL_DEBUG_INFO
		default BUILDBOT
		help
		  This collects debugging symbols from the kernel and all compiled modules.
		  Useful for release builds, so that kernel issues can be debugged offline
		  later.

	menu "Kernel build options"

	source "config/Config-kernel.in"

	endmenu

	comment "Package build options"

	config DEBUG
		bool
		prompt "Compile packages with debugging info"
		help
		  Adds -g3 to the CFLAGS.

	config USE_GC_SECTIONS
		bool
		prompt "Dead code and data elimination for all packages (EXPERIMENTAL)"
		help
		  Places functions and data items into its own sections to use the linker's
		  garbage collection capabilites.
		  Packages can choose to opt-out via setting PKG_BUILD_FLAGS:=no-gc-sections

	config USE_LTO
		bool
		prompt "Use the link-time optimizer for all packages (EXPERIMENTAL)"
		help
		  Adds LTO flags to the CFLAGS and LDFLAGS.
		  Packages can choose to opt-out via setting PKG_BUILD_FLAGS:=no-lto

	config MOLD
		depends on (aarch64 || arm || i386 || i686 || loongarch64 || m68k || powerpc || powerpc64 || riscv64 || sh4 || x86_64)
		depends on !HOST_OS_MACOS
		def_bool $(shell, ./config/check-hostcxx.sh 10 2 12)

	config USE_MOLD
		bool
		prompt "Use the mold linker for all packages"
		depends on MOLD
		help
		  Link packages with mold, a modern linker
		  Packages can opt-out via setting PKG_BUILD_FLAGS:=no-mold

	config IPV6
		def_bool y

	comment "Stripping options"

	choice
		prompt "Binary stripping method"
		default USE_STRIP   if USE_GLIBC
		default USE_SSTRIP
		help
		  Select the binary stripping method you wish to use.

		config NO_STRIP
			bool "none"
			help
			  This will install unstripped binaries (useful for native
			  compiling/debugging).

		config USE_STRIP
			bool "strip"
			help
			  This will install binaries stripped using strip from binutils.

		config USE_SSTRIP
			bool "sstrip"
			depends on !USE_GLIBC
			help
			  This will install binaries stripped using sstrip.
	endchoice

	config STRIP_ARGS
		string
		prompt "Strip arguments"
		depends on USE_STRIP
		default "--strip-unneeded --remove-section=.comment --remove-section=.note" if DEBUG
		default "--strip-all"
		help
		  Specifies arguments passed to the strip command when stripping binaries.

	config SSTRIP_DISCARD_TRAILING_ZEROES
		bool "Strip trailing zero bytes"
		depends on USE_SSTRIP && !USE_MOLD
		default y
		help
		  Use sstrip's -z option to discard trailing zero bytes

	config STRIP_KERNEL_EXPORTS
		bool "Strip unnecessary exports from the kernel image"
		depends on BROKEN
		help
		  Reduces kernel size by stripping unused kernel exports from the kernel
		  image.  Note that this might make the kernel incompatible with any kernel
		  modules that were not selected at the time the kernel image was created.

	config USE_MKLIBS
		bool "Strip unnecessary functions from libraries"
		help
		  Reduces libraries to only those functions that are necessary for using all
		  selected packages (including those selected as <M>).  Note that this will
		  make the system libraries incompatible with most of the packages that are
		  not selected during the build process.

	comment "Hardening build options"

	config PKG_CHECK_FORMAT_SECURITY
		bool
		prompt "Enable gcc format-security"
		default y
		help
		  Add -Wformat -Werror=format-security to the CFLAGS.  You can disable
		  this per package by adding PKG_CHECK_FORMAT_SECURITY:=0 in the package
		  Makefile.

	choice
		prompt "User space ASLR PIE compilation"
		default PKG_ASLR_PIE_NONE if ((SMALL_FLASH || LOW_MEMORY_FOOTPRINT) && !SDK)
		default PKG_ASLR_PIE_REGULAR
		help
		  Add -fPIC to CFLAGS and -specs=hardened-build-ld to LDFLAGS.
		  This enables package build as Position Independent Executables (PIE)
		  to protect against "return-to-text" attacks. This belongs to the
		  feature of Address Space Layout Randomisation (ASLR), which is
		  implemented by the kernel and the ELF loader by randomising the
		  location of memory allocations. This makes memory addresses harder
		  to predict when an attacker is attempting a memory-corruption exploit.
		  You can disable this per package by adding PKG_ASLR_PIE:=0 in the package
		  Makefile.
		  Be ware that ASLR increases the binary size.
		config PKG_ASLR_PIE_NONE
			bool "None"
			help
			  PIE is deactivated for all applications
		config PKG_ASLR_PIE_REGULAR
			bool "Regular"
			help
			  PIE is activated for some binaries, mostly network exposed applications
		config PKG_ASLR_PIE_ALL
			bool "All"
			select BUSYBOX_DEFAULT_PIE
			help
			  PIE is activated for all applications
	endchoice

	choice
		prompt "User space Stack-Smashing Protection"
		default PKG_CC_STACKPROTECTOR_REGULAR
		help
		  Enable GCC Stack Smashing Protection (SSP) for userspace applications
		config PKG_CC_STACKPROTECTOR_NONE
			bool "None"
			help
				No stack smashing protection.
		config PKG_CC_STACKPROTECTOR_REGULAR
			bool "Regular"
			help
				Protects functions with vulnerable objects.
				This includes functions with buffers larger than 8 bytes or calls to alloca.
		config PKG_CC_STACKPROTECTOR_STRONG
			bool "Strong"
			help
				Like Regular, but also protects functions with
				local arrays or references to local frame addresses.
		config PKG_CC_STACKPROTECTOR_ALL
			bool "All"
			help
				Protects all functions.
	endchoice

	choice
		prompt "Kernel space Stack-Smashing Protection"
		default KERNEL_CC_STACKPROTECTOR_REGULAR
		help
		  Enable GCC Stack-Smashing Protection (SSP) for the kernel
		config KERNEL_CC_STACKPROTECTOR_NONE
			bool "None"
			help
				No stack smashing protection.
		config KERNEL_CC_STACKPROTECTOR_REGULAR
			bool "Regular"
			help
				Protects functions with vulnerable objects.
				This includes functions with buffers larger than 8 bytes or calls to alloca.
		config KERNEL_CC_STACKPROTECTOR_STRONG
			bool "Strong"
			help
				Like Regular, but also protects functions with
				local arrays or references to local frame addresses.
	endchoice

	config KERNEL_STACKPROTECTOR
		bool
		default KERNEL_CC_STACKPROTECTOR_REGULAR || KERNEL_CC_STACKPROTECTOR_STRONG

	config KERNEL_STACKPROTECTOR_STRONG
		bool
		default KERNEL_CC_STACKPROTECTOR_STRONG

	choice
		prompt "Enable buffer-overflows detection (FORTIFY_SOURCE)"
		default PKG_FORTIFY_SOURCE_1
		help
		  Enable the _FORTIFY_SOURCE macro which introduces additional
		  checks to detect buffer-overflows in the following standard library
		  functions: memcpy, mempcpy, memmove, memset, strcpy, stpcpy,
		  strncpy, strcat, strncat, sprintf, vsprintf, snprintf, vsnprintf,
		  gets.  "Conservative" (_FORTIFY_SOURCE set to 1) only introduces
		  checks that shouldn't change the behavior of conforming programs,
		  while "aggressive" (_FORTIFY_SOURCES set to 2) some more checking is
		  added, but some conforming programs might fail.
		config PKG_FORTIFY_SOURCE_NONE
			bool "None"
		config PKG_FORTIFY_SOURCE_1
			bool "Conservative"
		config PKG_FORTIFY_SOURCE_2
			bool "Aggressive"
	endchoice

	choice
		prompt "Enable RELRO protection"
		default PKG_RELRO_FULL
		help
		  Enable a link-time protection known as RELRO (Relocation Read Only)
		  which helps to protect from certain type of exploitation techniques
		  altering the content of some ELF sections. "Partial" RELRO makes the
		  .dynamic section not writeable after initialization, introducing
		  almost no performance penalty, while "full" RELRO also marks the GOT
		  as read-only at the cost of initializing all of it at startup.
		config PKG_RELRO_NONE
			bool "None"
		config PKG_RELRO_PARTIAL
			bool "Partial"
		config PKG_RELRO_FULL
			bool "Full"
	endchoice

	config TARGET_ROOTFS_SECURITY_LABELS
		bool
		select KERNEL_SQUASHFS_XATTR
		select KERNEL_EXT4_FS_SECURITY
		select KERNEL_F2FS_FS_SECURITY
		select KERNEL_UBIFS_FS_SECURITY
		select KERNEL_JFFS2_FS_SECURITY

	config SELINUX
		bool "Enable SELinux"
		select KERNEL_SECURITY_SELINUX
		select TARGET_ROOTFS_SECURITY_LABELS
		select PACKAGE_procd-selinux
		select PACKAGE_busybox-selinux
		help
		  This option enables SELinux kernel features, applies security labels
		  in squashfs rootfs and selects the selinux-variants of busybox and procd.

		  Selecting this option results in about 0.5MiB of additional flash space
		  usage accounting for increased kernel and rootfs size.

	choice
		prompt "default SELinux type"
		depends on TARGET_ROOTFS_SECURITY_LABELS
		default SELINUXTYPE_dssp
		help
		  Select SELinux policy to be installed and used for applying rootfs labels.

		config SELINUXTYPE_targeted
			bool "targeted"
			select PACKAGE_refpolicy
			help
			  SELinux Reference Policy (refpolicy)

		config SELINUXTYPE_dssp
			bool "dssp"
			select PACKAGE_selinux-policy
			help
			  Defensec SELinux Security Policy -- OpenWrt edition

	endchoice

	config USE_SECCOMP
		bool "Enable SECCOMP"
		select KERNEL_SECCOMP
		depends on (aarch64 || arm || armeb || mips || mipsel || mips64 || mips64el || i386 || loongarch64 || powerpc || x86_64)
		depends on !TARGET_uml
		default y
		help
		  This option enables seccomp kernel features to safely
		  execute untrusted bytecode and selects the seccomp-variants
		  of procd

endmenu
