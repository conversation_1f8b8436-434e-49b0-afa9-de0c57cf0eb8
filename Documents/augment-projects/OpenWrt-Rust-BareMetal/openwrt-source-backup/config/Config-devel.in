# SPDX-License-Identifier: GPL-2.0-only
#
# Copyright (C) 2006-2013 OpenWrt.org

menuconfig DEVEL
	bool "Advanced configuration options (for developers)"

	config BROKEN
		bool "Show broken platforms / packages / devices" if DEVEL

	config BINARY_FOLDER
		string "Binary folder" if DEVE<PERSON>
		default ""
		help
		  Store built firmware images and filesystem images in this directory.
		  If not set, uses './bin/$(BOARD)'

	config DOWNLOAD_TOOL_CUSTOM
		string "Use custom download tool" if DEVEL
		default ""
		help
		  Use and force custom download tool instead of relying on autoselection
		  between curl if available and wget as a fallback.

		  download.pl supports 3 tools officially aria2c, curl and wget.
		  If one of the tool is used in this config, download.pl will use the
		  default args to make use of them.

		  If the provided string is different than aria2c, curl or wget, the command
		  is used as is and the download url will be appended at the end of such command.

	config DOWNLOAD_FOLDER
		string "Download folder" if DEVEL
		default ""
		help
		  Store downloaded source bundles in this directory.
		  If not set then defaults to './dl', which is removed by operations such as
		  'git clean -xdf' or 'make distclean'.
		  This option is useful if you have a low bandwidth Internet connection, and by
		  setting a path outside the OpenWrt tree downloads will be saved.

	config LOCALMIRROR
		string "Local mirror for source packages" if DEVEL
		default ""

	config AUTOREBUILD
		bool "Automatic rebuild of packages" if DEVEL
		default y
		help
		  Automatically rebuild packages when their files change.

	config AUTOREMOVE
		bool "Automatic removal of build directories" if DEVEL
		help
		  Automatically delete build directories after make target completed.
		  This allows you to symlink build_dir into a scratch location, e.g. a ramdisk,
		  which does not have enough space to keep a complete build_dir.

	config BUILD_ALL_HOST_TOOLS
		bool "Compile all host tools" if DEVEL
		help
		  Compile all host host tools even if not needed. This is needed to prepare a
		  universal precompiled host tools archive to use in another buildroot.

	menuconfig OPTIMIZE_HOST_TOOLS
		bool "Host Tools compile options" if DEVEL

		if OPTIMIZE_HOST_TOOLS

		config HOST_FLAGS_OPT
			string "Host Tools optimization flags"
			default "-O2"
			help
			  Compiler flags which are used to build host tools.

			  E.g.: "-O2", "-O3 -fno-tree-vectorize".

			  Default is "-O2".

		config HOST_TOOLS_STRIP
			bool "Strip Host Tools"
			help
			  Instructs compiler/linker to use flags from HOST_FLAGS_STRIP
			  in order to reduce binary size of host tools.

		config HOST_FLAGS_STRIP
			string "Host Tools compiler/linker flags for stripping symbols"
			depends on HOST_TOOLS_STRIP
			default "-Wl,-s"
			help
			  Compiler flags which are used to strip symbols from host tools.

			  Each flag should be prefixed with "-Wl," string
			  because compiler (GCC) passes this value to linker.

			  Default is "-Wl,-s" which means "strip all symbols" - specifically,
			  debug symbols and other symbols not needed for relocation processing.

		comment "Host Tools miscellaneous flags"

		config HOST_EXTRA_CFLAGS
			string "Host Tools extra CFLAGS"
			default ""

		config HOST_EXTRA_CXXFLAGS
			string "Host Tools extra CXXFLAGS"
			default ""

		config HOST_EXTRA_CPPFLAGS
			string "Host Tools extra CPPFLAGS"
			default ""

		config HOST_EXTRA_LDFLAGS
			string "Host Tools extra LDFLAGS"
			default ""

		endif

	config BUILD_SUFFIX
		string "Build suffix to append to the target BUILD_DIR variable" if DEVEL
		default ""
		help
		  Build suffix to append to the BUILD_DIR variable, i.e: './build_dir/{target-build-dir}_$(BUILD_SUFFIX)'.
		  This allows you to switch to a different .config whilst retaining all the build
		  objects generated by the first .config

	config TARGET_ROOTFS_DIR
		string "Override the default TARGET_ROOTFS_DIR variable" if DEVEL
		default ""
		help
		  Override the default TARGET_ROOTFS_DIR variable content $(BUILD_DIR) with
		  custom path.  Use this option to re-define the location of the target
		  root filesystem directory.

	config CCACHE
		bool "Use ccache" if DEVEL
		help
		  Compiler cache; see https://ccache.samba.org/

	config CCACHE_DIR
		string "Set ccache directory" if CCACHE
		default ""
		help
		  Store ccache in this directory.
		  If not set, uses './.ccache'

	config KERNEL_CFLAGS
		string "Kernel extra CFLAGS" if DEVEL
		default "-falign-functions=32" if TARGET_bcm53xx
		default ""

	config EXTERNAL_KERNEL_TREE
		string "Use external kernel tree" if DEVEL
		default ""

	config KERNEL_GIT_CLONE_URI
		string "Enter git repository to clone" if DEVEL
		default ""
		help
		  Enter the full git repository path i.e.:
		  git://git.kernel.org/pub/scm/linux/kernel/git/torvalds/linux-2.6.git
		  This will create a git clone of the kernel in your build directory.

	config KERNEL_GIT_LOCAL_REPOSITORY
		string "Enter path to local reference repository" if DEVEL
		depends on (KERNEL_GIT_CLONE_URI != "")
		default ""
		help
		  Enter a full pathname to a local reference git repository.
		  In this instance, the --reference option of git clone will
		  be used thus creating a quick local clone of your repo.

	config KERNEL_GIT_REF
		string "Enter git ref at which to checkout" if DEVEL
		depends on (KERNEL_GIT_CLONE_URI != "")
		default ""
		help
		  Enter the git ref at which to checkout the git repository
		  after it is cloned, and before making it a tar-ball.
		  It can be a git hash or a branch name.
		  If unused, the clone's repository HEAD will be checked-out.

	config KERNEL_GIT_MIRROR_HASH
		string "Enter hash of Git kernel tree source checkout tarball" if DEVEL
		depends on (KERNEL_GIT_CLONE_URI != "")
		default ""

	config BUILD_LOG
		bool "Enable log files during build process" if DEVEL
		help
		  If enabled, log files will be written to the ./log directory.

	config BUILD_LOG_DIR
		string "Log folder" if DEVEL
		default ""
		help
		  Store build logs in this directory.
		  If not set, uses './logs'

	config SRC_TREE_OVERRIDE
		bool "Enable package source tree override" if DEVEL
		help
		  If enabled, you can force a package to use a git tree as source
		  code instead of the normal tarball. Create a symlink 'git-src'
		  in the package directory, pointing to the .git tree that you want
		  to pull the source code from.

	config EXTRA_OPTIMIZATION
		string "Additional compiler options" if DEVEL
		default "-fno-caller-saves -fno-plt" if !CONFIG_EXTERNAL_TOOLCHAIN && !arc
		default "-fno-caller-saves"
		help
		  Extra target-independent optimizations to use when building for the target.
