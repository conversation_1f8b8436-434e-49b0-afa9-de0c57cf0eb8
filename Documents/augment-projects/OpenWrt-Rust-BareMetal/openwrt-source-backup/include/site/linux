ac_atomic_add=yes
ac_atomic_sub=yes
ac_cv_c_gettext_without_libintl=yes
ac_cv_c_long_double=no
ac_cv_conv_longlong_to_float=yes
ac_cv_file__dev_zero=yes
ac_cv_func___va_copy=no
ac_cv_func__exit=yes
ac_cv_func_bcopy=yes
ac_cv_func_bzero=yes
ac_cv_func_bcmp=yes
ac_cv_func_creal=yes
ac_cv_func_cimag=yes
ac_cv_func_fchmod=yes
ac_cv_func_getaddrinfo=yes
ac_cv_func_getcwd=yes
ac_cv_func_getdomainname=yes
ac_cv_func_getpgrp_void=yes
ac_cv_func_getpwuid_r=yes
ac_cv_func_gettimeofday=yes
ac_cv_func_index=yes
ac_cv_func_lstat_dereferences_slashed_symlink=yes
ac_cv_func_lstat_empty_string_bug=no
ac_cv_func_lstat=yes
ac_cv_func_malloc_0_nonnull=yes
ac_cv_func_malloc_works=yes
ac_cv_func_memcmp_clean=yes
ac_cv_func_memcmp_working=yes
ac_cv_func_posix_getgrgid_r=yes
ac_cv_func_posix_getpwuid_r=yes
ac_cv_func_psignal=yes
ac_cv_func_pthread_key_delete=yes
ac_cv_func_realloc_0_nonnull=yes
ac_cv_func_realloc_works=yes
ac_cv_func_rename=yes
ac_cv_func_rindex=yes
ac_cv_func_setlocale=yes
ac_cv_func_setgrent_void=yes
ac_cv_func_setpgrp_void=yes
ac_cv_func_setresuid=yes
ac_cv_func_setvbuf_reversed=no
ac_cv_func_stat_empty_string_bug=no
ac_cv_func_stat_ignores_trailing_slash=no
ac_cv_func_strerror=yes
ac_cv_func_strftime=yes
ac_cv_func_utimes=yes
ac_cv_func___adjtimex=yes
ac_cv_func_va_copy=no
ac_cv_func_vsnprintf=yes
ac_cv_have_accrights_in_msghdr=no
ac_cv_have_broken_snprintf=no
ac_cv_have_control_in_msghdr=yes
ac_cv_have_decl_sys_siglist=no
ac_cv_have_openpty_ctty_bug=yes
ac_cv_have_space_d_name_in_struct_dirent=yes
ac_cv_header_netinet_sctp_uio_h=no
ac_cv_int64_t=yes
ac_cv_lbl_unaligned_fail=no
ac_cv_linux_kernel_pppoe=yes
ac_cv_linux_vers=2
ac_cv_pack_bitfields_reversed=yes
ac_cv_path_LDCONFIG=
ac_cv_regexec_segfault_emptystr=no
ac_cv_sctp=no
ac_cv_sys_restartable_syscalls=yes
ac_cv_time_r_type=POSIX
ac_cv_type_suseconds_t=yes
ac_cv_size_t=yes
ac_cv_ssize_t=yes
ac_cv_uchar=no
ac_cv_uint=yes
ac_cv_uint64_t=yes
ac_cv_uintptr_t=yes
ac_cv_ulong=yes
ac_cv_ushort=yes
ac_cv_va_copy=C99
ac_cv_va_val_copy=yes
as_cv_unaligned_access=yes
ac_cv_func_malloc_0_nonnull=yes
ac_cv_func_realloc_0_nonnull=yes
