fdtaddr=0x8f000000
loadaddr=0x88000000
fdt_high=0xa0000000
initrd_high=0xffffffffffffffff
hwconfig=fsl_ddr:bank_intlv=auto
mc_init=mmc read ******** 5000 1800;mmc read ******** 7000 800;fsl_mc start mc ******** ********;mmc read ******** 6800 800;fsl_mc apply dpl ********
sd_boot=ext4load mmc 0:1 $loadaddr fitImage;bootm $loadaddr
bootargs=root=/dev/mmcblk0p2 rw rootwait rootfstype=squashfs,f2fs noinitrd earlycon=uart8250,mmio,0x21c0500 console=ttyS0,115200
bootcmd=echo starting openwrt ...;run mc_init;run sd_boot
bootdelay=3
fsl_bootcmd_mcinitcmd_set=y
