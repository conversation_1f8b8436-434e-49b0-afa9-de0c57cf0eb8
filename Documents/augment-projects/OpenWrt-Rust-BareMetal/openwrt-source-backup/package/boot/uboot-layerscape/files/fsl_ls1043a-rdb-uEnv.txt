fdtaddr=0x8f000000
loadaddr=0x88000000
bootm_size=0x10000000
hwconfig=fsl_ddr:bank_intlv=auto
nor_boot=cp.b 60f00000 $fdtaddr 100000;cp.b ******** $loadaddr 1000000;bootm $loadaddr - $fdtaddr
bootargs=root=/dev/mtdblock8 rootfstype=squashfs,jffs2 noinitrd earlycon=uart8250,mmio,0x21c0500 console=ttyS0,115200 mtdparts=********.nor:1m(bl2),4m(fip),1m(u-boot-env),3m(reserved-1),256k(fman),5888k(reserved-2),1m(dtb),16m(kernel),32m(rootfs),49m@0xf00000(firmware)
bootcmd=echo starting openwrt ...;run nor_boot
bootdelay=3
fsl_bootcmd_mcinitcmd_set=y
