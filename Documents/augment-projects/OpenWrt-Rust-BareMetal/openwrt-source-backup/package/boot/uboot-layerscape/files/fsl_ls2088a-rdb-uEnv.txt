fdtaddr=0x8f000000
loadaddr=0x88000000
fdt_high=0xa0000000
initrd_high=0xffffffffffffffff
hwconfig=fsl_ddr:bank_intlv=auto
mc_init=fsl_mc start mc 580a00000 580e00000;fsl_mc apply dpl 580d00000
nor_boot=cp.b 580f00000 $fdtaddr 100000;cp.b ********* $loadaddr 1000000;bootm $loadaddr - $fdtaddr
bootargs=root=/dev/mtdblock9 rootfstype=squashfs,jffs2 noinitrd earlycon=uart8250,mmio,0x21c0500 console=ttyS1,115200 mtdparts=*********.nor:1m(bl2),4m(fip),1m(u-boot-env),4m(reserved-1),3m(mc),1m(dpl),1m(dpc),1m(dtb),16m(kernel),32m(rootfs),49m@0xf00000(firmware)
bootcmd=echo starting openwrt ...;run mc_init;run nor_boot
bootdelay=3
fsl_bootcmd_mcinitcmd_set=y
