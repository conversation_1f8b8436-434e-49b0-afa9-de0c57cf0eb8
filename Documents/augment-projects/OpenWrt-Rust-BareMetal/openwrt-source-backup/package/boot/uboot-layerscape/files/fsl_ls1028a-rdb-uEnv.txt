fdtaddr=0x8f000000
loadaddr=0x81000000
bootm_size=0x10000000
hwconfig=fsl_ddr:bank_intlv=auto
qspi_boot=sf probe 0:0;sf read $loadaddr 1000000 1000000;bootm $loadaddr
bootargs=rootfstype=squashfs,jffs2 noinitrd earlycon=uart8250,mmio,0x21c0500 console=ttyS0,115200 mtdparts=20c0000.spi:1m(bl2),4m(fip),1m(u-boot-env),128k(secure-boot-headers),48m@0x1000000(firmware)
bootcmd=echo starting openwrt ...;run qspi_boot
bootdelay=3
fsl_bootcmd_mcinitcmd_set=y
