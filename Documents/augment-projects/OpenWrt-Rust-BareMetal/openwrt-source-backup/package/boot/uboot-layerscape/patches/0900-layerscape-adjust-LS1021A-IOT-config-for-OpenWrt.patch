From 54a19a8c97608c71b440639c878f2f57b5add95d Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <paweld<PERSON><PERSON><PERSON>@gmail.com>
Date: Mon, 24 Oct 2022 14:19:38 +0200
Subject: [PATCH] layerscape: adjust LS1021A-IOT config for OpenWrt

Two configs are required:
  - FIT
  - Ext4load

Let's enable it. U-boot is now bigger than 512K. Let's enlarge it to
768K. Envs start at 1M, so it will fit.

Signed-off-by: <PERSON><PERSON><PERSON> <paweldem<PERSON><EMAIL>>
---
 configs/ls1021aiot_sdcard_defconfig | 7 +++++--
 1 file changed, 5 insertions(+), 2 deletions(-)

--- a/configs/ls1021aiot_sdcard_defconfig
+++ b/configs/ls1021aiot_sdcard_defconfig
@@ -14,7 +14,7 @@ CONFIG_SYS_I2C_MXC_I2C3=y
 CONFIG_DM_GPIO=y
 CONFIG_DEFAULT_DEVICE_TREE="ls1021a-iot-duart"
 CONFIG_SPL_TEXT_BASE=0x10000000
-CONFIG_SYS_MONITOR_LEN=524288
+CONFIG_SYS_MONITOR_LEN=786432
 CONFIG_SPL_MMC=y
 CONFIG_SPL_SERIAL=y
 CONFIG_SPL_STACK=0x1001d000
@@ -43,7 +43,7 @@ CONFIG_SPL_MAX_SIZE=0x1a000
 CONFIG_SPL_PAD_TO=0x1c000
 CONFIG_SPL_HAS_BSS_LINKER_SECTION=y
 CONFIG_SPL_BSS_START_ADDR=0x80100000
-CONFIG_SPL_BSS_MAX_SIZE=0x80000
+CONFIG_SPL_BSS_MAX_SIZE=0xc0000
 CONFIG_SPL_FSL_PBL=y
 # CONFIG_SPL_SHARES_INIT_SP_ADDR is not set
 CONFIG_SPL_SYS_MALLOC=y
@@ -67,8 +67,11 @@ CONFIG_CMD_MII=y
 # CONFIG_CMD_MDIO is not set
 CONFIG_CMD_PING=y
 CONFIG_CMD_EXT2=y
+CONFIG_CMD_EXT4=y
 CONFIG_CMD_FAT=y
 # CONFIG_SPL_EFI_PARTITION is not set
+CONFIG_FIT=y
+CONFIG_FIT_VERBOSE=y
 CONFIG_OF_CONTROL=y
 CONFIG_ENV_OVERWRITE=y
 CONFIG_ENV_IS_IN_MMC=y
