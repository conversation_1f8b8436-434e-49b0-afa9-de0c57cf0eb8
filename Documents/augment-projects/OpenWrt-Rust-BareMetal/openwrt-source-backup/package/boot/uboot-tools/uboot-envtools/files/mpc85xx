[ -e /etc/config/ubootenv ] && exit 0

touch /etc/config/ubootenv

. /lib/uboot-envtools.sh
. /lib/functions.sh

board=$(board_name)

case "$board" in
enterasys,ws-ap3715i)
	ubootenv_add_uci_config "$(find_mtd_part 'cfg1')" "0x0" "0x10000" "0x10000"
	ubootenv_add_uci_config "$(find_mtd_part 'cfg2')" "0x0" "0x10000" "0x10000"
	;;
extreme-networks,ws-ap3825i)
	ubootenv_add_uci_config "$(find_mtd_part 'cfg1')" "0x0" "0x10000" "0x20000"
	ubootenv_add_uci_config "$(find_mtd_part 'cfg2')" "0x0" "0x10000" "0x20000"
	;;
ocedo,panda)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x20000" "0x20000"
	ubootenv_add_uci_config "/dev/mtd2" "0x0" "0x20000" "0x20000"
	;;
watchguard,firebox-t10)
	ubootenv_add_uci_config "$(find_mtd_part 'u-boot-env')" "0x0" "0x2000" "0x10000"
	;;
aerohive,hiveap-330)
	ubootenv_add_uci_config "$(find_mtd_part 'u-boot-env')" "0x0" "0x20000" "0x10000"
	;;
esac

config_load ubootenv
config_foreach ubootenv_add_app_config ubootenv

exit 0
