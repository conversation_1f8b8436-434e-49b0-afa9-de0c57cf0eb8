#
# Copyright (C) 2021 OpenWrt.org
#

[ -e /etc/config/ubootenv ] && exit 0

touch /etc/config/ubootenv

. /lib/uboot-envtools.sh
. /lib/functions.sh

board=$(board_name)

case "$board" in
bananapi,bpi-r2)
	. /lib/upgrade/common.sh
	bootdev="$(fitblk_get_bootdev)"
	ubootenv_add_uci_config "/dev/${bootdev%p[0-9]*}p1" "0xb0000" "0x10000" "0x10000" "1"
	;;
unielec,u7623-02)
	ubootenv_add_uci_config "/dev/mmcblk0p1" "0xc0000" "0x10000" "0x10000" "1"
	;;
esac

config_load ubootenv
config_foreach ubootenv_add_app_config ubootenv

exit 0
