#
# Copyright (C) 2016 LEDE
#

[ -e /etc/config/ubootenv ] && exit 0

touch /etc/config/ubootenv

. /lib/uboot-envtools.sh
. /lib/functions.sh

board=$(board_name)

ubootenv_mtdinfo () {
	UBOOTENV_PART=$(cat /proc/mtd | grep -i APPSBLENV)
	mtd_dev=$(echo $UBOOTENV_PART | awk '{print $1}' | sed 's/:$//')
	mtd_size=$(echo $UBOOTENV_PART | awk '{print "0x"$2}')
	mtd_erase=$(echo $UBOOTENV_PART | awk '{print "0x"$3}')
	nor_flash=$(find /sys/bus/spi/devices/*/mtd -name ${mtd_dev})

	if [ -n "$nor_flash" ]; then
		ubootenv_size=$mtd_size
	else
		# size is fixed to 0x40000 in u-boot
		ubootenv_size=0x40000
	fi

	sectors=$(( $ubootenv_size / $mtd_erase ))
	sectors=$(printf "0x%x" $sectors )
	echo /dev/$mtd_dev 0x0 $ubootenv_size $mtd_erase $sectors
}

case "$board" in
arris,tr4400-v2|\
askey,rt4230w-rev6)
	ubootenv_add_uci_config "/dev/mtd9" "0x0" "0x40000" "0x20000"
	;;
edgecore,ecw5410)
	ubootenv_add_uci_config "/dev/mtd11" "0x0" "0x10000" "0x10000"
	;;
extreme,ap3935)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x10000" "0x10000"
	ubootenv_add_uci_config "/dev/mtd3" "0x0" "0x10000" "0x10000"
	;;
ignitenet,ss-w2-ac2600|\
qcom,ipq8064-ap148|\
qcom,ipq8064-db149)
	ubootenv_add_uci_config $(ubootenv_mtdinfo)
	;;
linksys,ea7500-v1|\
linksys,ea8500)
	ubootenv_add_uci_config "/dev/mtd10" "0x0" "0x20000" "0x20000"
	;;
netgear,r7800)
	ubootenv_add_uci_config "/dev/mtd2" "0x0" "0x040000" "0x20000"
	;;
nokia,ac400i)
	ubootenv_add_uci_config "/dev/mtd20" "0x0" "0x040000" "0x20000"
	;;
ubnt,unifi-ac-hd|\
zyxel,nbg6817)
	ubootenv_add_uci_config "/dev/mtdblock9" "0x0" "0x10000" "0x10000"
	;;
esac

config_load ubootenv
config_foreach ubootenv_add_app_config ubootenv

exit 0
