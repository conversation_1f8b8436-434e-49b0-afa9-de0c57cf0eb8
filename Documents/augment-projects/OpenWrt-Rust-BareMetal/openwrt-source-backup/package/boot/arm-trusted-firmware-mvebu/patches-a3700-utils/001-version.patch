diff --git a/wtmi/sys_init/Makefile b/wtmi/sys_init/Makefile
--- a/wtmi/sys_init/Makefile
+++ b/wtmi/sys_init/Makefile
@@ -51,8 +51,8 @@ ECHO     = @echo
 SED      = @sed
 
 LOCAL_VERSION_STRING	?= -armada
-BUILD_STRING		:= $(shell git log -n 1 --pretty=format:"%h" && (git diff-index --quiet HEAD || echo -dirty))
-VERSION_STRING		:= $(LOCAL_VERSION_STRING)-$(BUILD_STRING)
+A3700_UTILS_COMMIT_ID	?= $(shell git log -n 1 --pretty=format:"%h" && (git diff-index --quiet HEAD || echo -dirty))
+VERSION_STRING		:= $(LOCAL_VERSION_STRING)-$(A3700_UTILS_COMMIT_ID)
 
 CPUOPTS  = -mthumb -mcpu=cortex-m3 -mlittle-endian
 BINPATH  = build
