--- a/<PERSON><PERSON><PERSON>
+++ b/Makefile
@@ -28,7 +28,7 @@
 	@cp -f ${MV_DDR_PATH}/a3700_tool $(TIM_DDR_PATH)/ddr_tool
 
 $(TIM_DDR_PATH)/ddr_tool.verstr: $(MV_DDR_PATH)/a3700_tool
-	@echo mv_ddr-$(shell sed 's/^mv_ddr-//' $(MV_DDR_PATH)/localversion 2>/dev/null || echo 'unknown')$(if $(shell git -C $(MV_DDR_PATH) rev-parse --git-dir 2>/dev/null),-g$(shell git -C $(MV_DDR_PATH) rev-parse --verify --quiet --short HEAD 2>/dev/null)$(shell git -C $(MV_DDR_PATH) diff-index --quiet HEAD || echo -d)) > $@
+	@echo mv_ddr-$(shell sed 's/^mv_ddr-//' $(MV_DDR_PATH)/localversion 2>/dev/null || echo 'unknown')-g$(MV_DDR_COMMIT_ID) > $@
 
 mv_ddr: $(TIM_DDR_PATH)/ddr_tool $(TIM_DDR_PATH)/ddr_tool.verstr
 

