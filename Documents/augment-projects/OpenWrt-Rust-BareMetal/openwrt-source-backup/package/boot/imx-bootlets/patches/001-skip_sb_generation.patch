--- a/<PERSON><PERSON><PERSON>
+++ b/Makefile
@@ -37,13 +37,13 @@ ifeq "$(DFT_IMAGE)" "$(wildcard $(DFT_IM
 	elftosb -z -c ./uboot.bd -o i$(ARCH)_uboot.sb
 	elftosb -z -f imx28 -c ./uboot_ivt.bd -o i$(ARCH)_ivt_uboot.sb
 else
-	@echo "by using the pre-built kernel"
-	elftosb -z -c ./linux.bd -o i$(ARCH)_linux.sb
-	elftosb -z -f imx28 -c  ./linux_ivt.bd -o i$(ARCH)_ivt_linux.sb
+	@echo "... not generating any image for now."
+	#elftosb -z -c ./linux.bd -o i$(ARCH)_linux.sb
+	#elftosb -z -f imx28 -c  ./linux_ivt.bd -o i$(ARCH)_ivt_linux.sb
 
-	@echo "generating U-Boot boot stream image"
-	elftosb -z -c ./uboot.bd -o i$(ARCH)_uboot.sb
-	elftosb -z -f imx28 -c ./uboot_ivt.bd -o i$(ARCH)_ivt_uboot.sb
+	#@echo "generating U-Boot boot stream image"
+	#elftosb -z -c ./uboot.bd -o i$(ARCH)_uboot.sb
+	#elftosb -z -f imx28 -c ./uboot_ivt.bd -o i$(ARCH)_ivt_uboot.sb
 endif
 	#@echo "generating kernel bootstream file sd_mmc_bootstream.raw"
 	#Please use cfimager to burn xxx_linux.sb. The below way will no
