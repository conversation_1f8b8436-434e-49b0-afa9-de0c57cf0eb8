--- a/include/grub/util/install.h
+++ b/include/grub/util/install.h
@@ -199,13 +199,13 @@ grub_install_get_image_target (const cha
 void
 grub_util_bios_setup (const char *dir,
 		      const char *boot_file, const char *core_file,
-		      const char *dest, int force,
+		      const char *root, const char *dest, int force,
 		      int fs_probe, int allow_floppy,
 		      int add_rs_codes, int warn_short_mbr_gap);
 void
 grub_util_sparc_setup (const char *dir,
 		       const char *boot_file, const char *core_file,
-		       const char *dest, int force,
+		       const char *root, const char *dest, int force,
 		       int fs_probe, int allow_floppy,
 		       int add_rs_codes, int warn_short_mbr_gap);
 
--- a/util/grub-install.c
+++ b/util/grub-install.c
@@ -1770,7 +1770,7 @@ main (int argc, char *argv[])
 	if (install_bootsector)
 	  {
 	    grub_util_bios_setup (platdir, "boot.img", "core.img",
-				  install_drive, force,
+				  NULL, install_drive, force,
 				  fs_probe, allow_floppy, add_rs_codes,
 				  !grub_install_is_short_mbrgap_supported ());
 
@@ -1801,7 +1801,7 @@ main (int argc, char *argv[])
 	if (install_bootsector)
 	  {
 	    grub_util_sparc_setup (platdir, "boot.img", "core.img",
-				   install_drive, force,
+				   NULL, install_drive, force,
 				   fs_probe, allow_floppy,
 				   0 /* unused */, 0 /* unused */ );
 
--- a/util/grub-setup.c
+++ b/util/grub-setup.c
@@ -87,6 +87,8 @@ static struct argp_option options[] = {
    N_("install even if problems are detected"), 0},
   {"skip-fs-probe",'s',0,      0,
    N_("do not probe for filesystems in DEVICE"), 0},
+  {"root-device", 'r', N_("DEVICE"), 0,
+   N_("use DEVICE as the root device"), 0},
   {"verbose",     'v', 0,      0, N_("print verbose messages."), 0},
   {"allow-floppy", 'a', 0,      0,
    /* TRANSLATORS: The potential breakage isn't limited to floppies but it's
@@ -130,6 +132,7 @@ struct arguments
   char *core_file;
   char *dir;
   char *dev_map;
+  char *root_dev;
   int  force;
   int  fs_probe;
   int allow_floppy;
@@ -178,6 +181,13 @@ argp_parser (int key, char *arg, struct
         arguments->dev_map = xstrdup (arg);
         break;
 
+      case 'r':
+        if (arguments->root_dev)
+          free (arguments->root_dev);
+
+        arguments->root_dev = xstrdup (arg);
+        break;
+
       case 'f':
         arguments->force = 1;
         break;
@@ -313,7 +323,7 @@ main (int argc, char *argv[])
   GRUB_SETUP_FUNC (arguments.dir ? : DEFAULT_DIRECTORY,
 		   arguments.boot_file ? : DEFAULT_BOOT_FILE,
 		   arguments.core_file ? : DEFAULT_CORE_FILE,
-		   dest_dev, arguments.force,
+		   arguments.root_dev, dest_dev, arguments.force,
 		   arguments.fs_probe, arguments.allow_floppy,
 		   arguments.add_rs_codes, 0);
 
--- a/util/setup.c
+++ b/util/setup.c
@@ -252,14 +252,13 @@ identify_partmap (grub_disk_t disk __att
 void
 SETUP (const char *dir,
        const char *boot_file, const char *core_file,
-       const char *dest, int force,
+       const char *root, const char *dest, int force,
        int fs_probe, int allow_floppy,
        int add_rs_codes __attribute__ ((unused)), /* unused on sparc64 */
        int warn_small)
 {
   char *core_path;
   char *boot_img, *core_img, *boot_path;
-  char *root = 0;
   size_t boot_size, core_size;
   grub_uint16_t core_sectors;
   grub_device_t root_dev = 0, dest_dev, core_dev;
@@ -311,7 +310,10 @@ SETUP (const char *dir,
 
   core_dev = dest_dev;
 
-  {
+  if (root)
+    root_dev = grub_device_open(root);
+
+  if (!root_dev) {
     char **root_devices = grub_guess_root_devices (dir);
     char **cur;
     int found = 0;
@@ -324,6 +326,8 @@ SETUP (const char *dir,
 	char *drive;
 	grub_device_t try_dev;
 
+	if (root_dev)
+	  break;
 	drive = grub_util_get_grub_dev (*cur);
 	if (!drive)
 	  continue;
