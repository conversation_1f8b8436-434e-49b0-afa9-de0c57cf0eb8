--- a/tools/fiptool/fiptool.c
+++ b/tools/fiptool/fiptool.c
@@ -19,6 +19,9 @@
 #include <stdio.h>
 #include <stdlib.h>
 #include <string.h>
+#include <unistd.h>
+
+#define uuid_t fiptool_uuid_t
 
 #include "fiptool.h"
 #include "tbbr_config.h"
--- a/tools/fiptool/fiptool_platform.h
+++ b/tools/fiptool/fiptool_platform.h
@@ -12,6 +12,7 @@
 #ifndef FIPTOOL_PLATFORM_H
 #define FIPTOOL_PLATFORM_H
 
+#define _DARWIN_C_SOURCE
 #ifndef _MSC_VER
 
 /* Not Visual Studio, so include Posix Headers. */
