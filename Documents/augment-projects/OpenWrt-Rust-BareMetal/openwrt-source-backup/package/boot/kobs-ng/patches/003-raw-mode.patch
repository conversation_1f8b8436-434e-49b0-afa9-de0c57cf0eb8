The downstream Freescale vendor kernel has a patch that allows determining
if raw NAND flash mode is provided via a debugfs file. This is not present
in upstream kernels, but the raw access support was added in the 3.19
kernel, so we will check the kernel version if we can't find the file.
--- a/src/mtd.c
+++ b/src/mtd.c
@@ -34,6 +34,7 @@
 #include <errno.h>
 #include <sys/types.h>
 #include <sys/ioctl.h>
+#include <sys/utsname.h>
 
 #include "mtd.h"
 #include "rand.h"
@@ -808,15 +809,27 @@ struct mtd_data *mtd_open(const struct m
 	md->cfg = *cfg;
 
 	/* check if use new raw access mode */
+	/* by looking for debugfs from fsl patch */
+	md->raw_mode_flag = 0;
 	fp = fopen("/sys/kernel/debug/gpmi-nand/raw_mode", "r");
 	if (!fp) {
-		md->raw_mode_flag = 0;
-		vp(md, "mtd: use legacy raw access mode\n");
+		/* fallback to kernel version: raw access added in 3.19 */
+		struct utsname uts;
+		if (!uname(&uts)) {
+			int major = 0, minor = 0;
+			sscanf(uts.release, "%d.%d", &major, &minor);
+			vp(md, "mtd: Linux %d.%d\n", major, minor);
+			if ((major << 8 | minor) > (3 << 8 | 18))
+				md->raw_mode_flag = 1;
+		}
 	} else {
 		fclose(fp);
 		md->raw_mode_flag = 1;
-		vp(md, "mtd: use new bch layout raw access mode\n");
 	}
+	if (md->raw_mode_flag)
+		vp(md, "mtd: use new bch layout raw access mode\n");
+	else
+		vp(md, "mtd: use legacy raw access mode\n");
 
 	if (plat_config_data->m_u32UseMultiBootArea) {
 
