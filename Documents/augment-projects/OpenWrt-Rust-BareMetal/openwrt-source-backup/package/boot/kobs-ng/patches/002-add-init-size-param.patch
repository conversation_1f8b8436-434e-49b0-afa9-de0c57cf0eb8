Add --chip_0_size param to override the size of the mtd partition which is
required if the SPL does not occupy the entire partition. For Gateworks
Ventana boards the 'uboot' partition contains both the SPL and uboot.
--- a/src/main.c
+++ b/src/main.c
@@ -94,6 +94,7 @@ void usage(void)
 	"  [KOBS] boot structures config options\n"
 	"    --chip_0_device_path=<path> .......... Device of boot (default /dev/mtd0)\n"
 	"    --chip_1_device_path=<path> .......... The second chip in case of multichip NAND\n"
+	"    --chip_0_size=<size> ................. Override size of chip_0 device\n"
 	"    --search_exponent=<value> ............ NCB field (default 2)\n"
 	"    --data_setup_time=<value> ............ NCB field (default 80)\n"
 	"    --data_hold_time=<value> ............. NCB field (default 60)\n"
--- a/src/mtd.c
+++ b/src/mtd.c
@@ -876,6 +876,11 @@ struct mtd_data *mtd_open(const struct m
 			goto out;
 		}
 
+		/* override MTD size */
+		if (md->cfg.chip_0_size) {
+			miu->size = md->cfg.chip_0_size;
+		}
+
 		/* verify it's a nand */
 		if (miu->type != MTD_NANDFLASH
 			&& miu->type != MTD_MLCNANDFLASH) {
@@ -3385,7 +3390,7 @@ static const struct {
 } mtd_int_args[] = {
 	ARG_IGNORE(chip_count),
 	ARG_IGNORE(chip_0_offset),
-	ARG_IGNORE(chip_0_size),
+	ARG(chip_0_size),
 	ARG_IGNORE(chip_1_offset),
 	ARG_IGNORE(chip_1_size),
 	ARG(search_exponent),
@@ -3578,7 +3583,7 @@ void mtd_cfg_dump(struct mtd_config *cfg
 //	Pd(chip_count);
 	Ps(chip_0_device_path);
 //	Pd(chip_0_offset);
-//	Pd(chip_0_size);
+	Pd(chip_0_size);
 	Ps(chip_1_device_path);
 //	Pd(chip_1_offset);
 //	Pd(chip_1_size);
