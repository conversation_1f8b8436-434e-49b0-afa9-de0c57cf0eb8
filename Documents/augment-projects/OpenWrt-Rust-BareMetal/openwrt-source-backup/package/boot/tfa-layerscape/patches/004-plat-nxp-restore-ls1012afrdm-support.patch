From 8c6a66feb721f18c930c7df03d1fbb7304107af6 Mon Sep 17 00:00:00 2001
From: Woj<PERSON>ch Dubowik <<EMAIL>>
Date: Thu, 20 Apr 2023 16:21:25 +0200
Subject: [PATCH] tfa-layerscape: Restore ls1012afrdm support

Signed-off-by: Wojciech Dubowik <<EMAIL>>
---
 plat/nxp/soc-ls1012a/ls1012afrdm/ddr_init.c   | 34 +++++++
 plat/nxp/soc-ls1012a/ls1012afrdm/plat_def.h   | 83 +++++++++++++++++++
 plat/nxp/soc-ls1012a/ls1012afrdm/platform.mk  | 25 +++++
 .../soc-ls1012a/ls1012afrdm/platform_def.h    | 13 +++
 plat/nxp/soc-ls1012a/ls1012afrdm/policy.h     | 16 ++++
 5 files changed, 180 insertions(+)
 create mode 100644 plat/nxp/soc-ls1012a/ls1012afrdm/ddr_init.c
 create mode 100644 plat/nxp/soc-ls1012a/ls1012afrdm/plat_def.h
 create mode 100644 plat/nxp/soc-ls1012a/ls1012afrdm/platform.mk
 create mode 100644 plat/nxp/soc-ls1012a/ls1012afrdm/platform_def.h
 create mode 100644 plat/nxp/soc-ls1012a/ls1012afrdm/policy.h

--- /dev/null
+++ b/plat/nxp/soc-ls1012a/ls1012afrdm/ddr_init.c
@@ -0,0 +1,34 @@
+/*
+ * Copyright 2018-2022 NXP
+ *
+ * SPDX-License-Identifier: BSD-3-Clause
+ */
+
+#include <common/debug.h>
+#include <fsl_mmdc.h>
+
+#include <platform_def.h>
+
+long long init_ddr(void)
+{
+	static const struct fsl_mmdc_info mparam = {
+		.mdctl = U(0x04180000),
+		.mdpdc = U(0x00030035),
+		.mdotc = U(0x12554000),
+		.mdcfg0 = U(0xbabf7954),
+		.mdcfg1 = U(0xdb328f64),
+		.mdcfg2 = U(0x01ff00db),
+		.mdmisc = U(0x00001680),
+		.mdref = U(0x0f3c8000),
+		.mdrwd = U(0x00002000),
+		.mdor = U(0x00bf1023),
+		.mdasp = U(0x0000003f),
+		.mpodtctrl = U(0x0000022a),
+		.mpzqhwctrl = U(0xa1390003),
+	};
+
+	mmdc_init(&mparam, NXP_DDR_ADDR);
+	NOTICE("DDR Init Done\n");
+
+	return NXP_DRAM0_SIZE;
+}
--- /dev/null
+++ b/plat/nxp/soc-ls1012a/ls1012afrdm/plat_def.h
@@ -0,0 +1,83 @@
+/*
+ * Copyright 2022 NXP
+ *
+ * SPDX-License-Identifier: BSD-3-Clause
+ */
+
+#ifndef PLAT_DEF_H
+#define PLAT_DEF_H
+
+#include <arch.h>
+/*
+ * Required without TBBR.
+ * To include the defines for DDR PHY
+ * Images.
+ */
+#include <tbbr_img_def.h>
+
+#include <policy.h>
+#include <soc.h>
+
+/* DDR Related definition */
+#define PLAT_DEF_DRAM0_SIZE	0x20000000	/* 512 MB */
+
+#define NXP_SYSCLK_FREQ		125000000
+#define NXP_DDRCLK_FREQ		100000000
+
+/* UART related definition */
+#define NXP_CONSOLE_ADDR	NXP_UART_ADDR
+#define NXP_CONSOLE_BAUDRATE	115200
+
+#define NXP_SPD_EEPROM0         0x51
+
+/* Size of cacheable stacks */
+#if defined(IMAGE_BL2)
+#if defined(TRUSTED_BOARD_BOOT)
+#define PLATFORM_STACK_SIZE	0x2000
+#else
+#define PLATFORM_STACK_SIZE	0x1000
+#endif
+#elif defined(IMAGE_BL31)
+#define PLATFORM_STACK_SIZE	0x1000
+#endif
+
+/* SD block buffer */
+#define NXP_SD_BLOCK_BUF_SIZE	(0x00100000)
+#define NXP_SD_BLOCK_BUF_ADDR	ULL(0x80000000)
+
+#define BL2_LIMIT		(NXP_OCRAM_ADDR + NXP_OCRAM_SIZE)
+
+/* IO defines as needed by IO driver framework */
+#define MAX_IO_DEVICES		3
+#define MAX_IO_BLOCK_DEVICES	1
+#define MAX_IO_HANDLES		4
+
+/*
+ * FIP image defines - Offset at which FIP Image would be present
+ * Image would include Bl31 , Bl33 and Bl32 (optional)
+ */
+#ifdef POLICY_FUSE_PROVISION
+#define MAX_FIP_DEVICES		2
+#endif
+
+#ifndef MAX_FIP_DEVICES
+#define MAX_FIP_DEVICES		1
+#endif
+
+/*
+ * ID of the secure physical generic timer interrupt used by the BL32.
+ */
+#define BL32_IRQ_SEC_PHY_TIMER	29
+
+/*
+ * Define properties of Group 1 Secure and Group 0 interrupts as per GICv3
+ * terminology. On a GICv2 system or mode, the lists will be merged and treated
+ * as Group 0 interrupts.
+ */
+#define PLAT_LS_G1S_IRQ_PROPS(grp) \
+	INTR_PROP_DESC(BL32_IRQ_SEC_PHY_TIMER, GIC_HIGHEST_SEC_PRIORITY, grp, \
+			GIC_INTR_CFG_LEVEL)
+
+#define PLAT_LS_G0_IRQ_PROPS(grp)
+
+#endif
--- /dev/null
+++ b/plat/nxp/soc-ls1012a/ls1012afrdm/platform.mk
@@ -0,0 +1,25 @@
+#
+# Copyright 2018-2022 NXP
+#
+# SPDX-License-Identifier: BSD-3-Clause
+#
+
+# board-specific build parameters
+BOOT_MODE	:= qspi
+BOARD		:= ls1012afrdm
+
+# DDR Compilation Configs
+DDRC_NUM_CS     :=      1
+
+# On-Board Flash Details
+QSPI_FLASH_SZ   :=      0x4000000
+
+BL2_SOURCES	+=	${BOARD_PATH}/ddr_init.c
+
+SUPPORTED_BOOT_MODE	:=	qspi
+
+# Adding platform board build info
+include plat/nxp/common/plat_make_helper/plat_common_def.mk
+
+# Adding SoC build info
+include plat/nxp/soc-ls1012a/soc.mk
--- /dev/null
+++ b/plat/nxp/soc-ls1012a/ls1012afrdm/platform_def.h
@@ -0,0 +1,13 @@
+/*
+ * Copyright 2022 NXP
+ *
+ * SPDX-License-Identifier: BSD-3-Clause
+ */
+
+#ifndef PLATFORM_DEF_H
+#define PLATFORM_DEF_H
+
+#include <plat_def.h>
+#include <plat_default_def.h>
+
+#endif /* PLATFORM_DEF_H */
--- /dev/null
+++ b/plat/nxp/soc-ls1012a/ls1012afrdm/policy.h
@@ -0,0 +1,16 @@
+/*
+ * Copyright 2018-2022 NXP
+ *
+ * SPDX-License-Identifier: BSD-3-Clause
+ */
+
+#ifndef POLICY_H
+#define	POLICY_H
+
+/*
+ * Set this to 0x0 to leave the default SMMU page size in sACR
+ * Set this to 0x1 to change the SMMU page size to 64K
+ */
+#define POLICY_SMMU_PAGESZ_64K 0x0
+
+#endif /* POLICY_H */
