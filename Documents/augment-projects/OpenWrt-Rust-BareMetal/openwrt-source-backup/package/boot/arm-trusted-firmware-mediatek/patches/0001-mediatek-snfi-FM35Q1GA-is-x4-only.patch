From fb2a2b669ec9bbf5c448d4b56499bc83de075c93 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Thu, 29 Feb 2024 18:01:08 +0000
Subject: [PATCH 1/3] mediatek: snfi: FM35Q1GA is x4-only

Dont allow x2 read and cache read operations on FM35Q1GA.

Signed-off-by: <PERSON> <<EMAIL>>
---
 plat/mediatek/apsoc_common/drivers/snfi/mtk-snand-ids.c | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

--- a/plat/mediatek/apsoc_common/drivers/snfi/mtk-snand-ids.c
+++ b/plat/mediatek/apsoc_common/drivers/snfi/mtk-snand-ids.c
@@ -423,7 +423,7 @@ static const struct snand_flash_info sna
 
 	SNAND_INFO("FM35Q1GA", SNA<PERSON>_ID(SNAND_ID_DYMMY, 0xe5, 0x71),
 		   SNAND_MEMORG_1G_2K_64,
-		   &snand_cap_read_from_cache_x4,
+		   &snand_cap_read_from_cache_x4_only,
 		   &snand_cap_program_load_x4),
 
 	SNAND_INFO("PN26G01A", SNAND_ID(SNAND_ID_DYMMY, 0xa1, 0xe1),
