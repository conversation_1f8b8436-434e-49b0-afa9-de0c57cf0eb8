From 94802b344195d3574701ca6ab5122f6b7615a6eb Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sun, 11 Aug 2024 23:12:33 +0100
Subject: [PATCH] mediatek: snfi: fix return code when reading

Return 0 on succesful read, which may contain correctable bitflips.

Fixes: #10
Signed-off-by: <PERSON> <<EMAIL>>
---
 plat/mediatek/apsoc_common/bl2/bl2_dev_snfi_init.c | 4 +++-
 1 file changed, 3 insertions(+), 1 deletion(-)

--- a/plat/mediatek/apsoc_common/bl2/bl2_dev_snfi_init.c
+++ b/plat/mediatek/apsoc_common/bl2/bl2_dev_snfi_init.c
@@ -30,9 +30,14 @@ static int snfi_mtd_read_page(struct nan
 	int ret;
 
 	ret = mtk_snand_read_page(snf, addr, (void *)buffer, NULL, false);
-	if (ret == -EBADMSG || ret > 0)
+	if (ret == -EBADMSG)
 		ret = 0;
 
+	if (ret > 0) {
+		NOTICE("corrected %d bitflips while reading page %u\n", ret, page);
+		ret = 0;
+	}
+
 	return ret;
 }
 
