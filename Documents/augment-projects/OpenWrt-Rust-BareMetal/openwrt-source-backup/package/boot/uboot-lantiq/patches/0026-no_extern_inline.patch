From b11c5d1dc29e81326d1215011d19377737082aeb Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Wed, 1 Jul 2015 16:36:43 +0200
Subject: [PATCH] MIPS: change 'extern inline' to 'static inline'

The kernel changed it a long time ago. Also this is now broken
on gcc-5.x.

Reported-by: <PERSON> <<EMAIL>>
Signed-off-by: <PERSON> <<EMAIL>>
---
 arch/mips/include/asm/io.h     | 12 ++++++------
 arch/mips/include/asm/system.h |  6 +++---
 2 files changed, 9 insertions(+), 9 deletions(-)

--- a/arch/mips/include/asm/io.h
+++ b/arch/mips/include/asm/io.h
@@ -118,7 +118,7 @@ static inline void set_io_port_base(unsi
  * Change virtual addresses to physical addresses and vv.
  * These are trivial on the 1:1 Linux/MIPS mapping
  */
-extern inline phys_addr_t virt_to_phys(volatile void * address)
+static inline phys_addr_t virt_to_phys(volatile void * address)
 {
 #ifndef CONFIG_64BIT
 	return CPHYSADDR(address);
@@ -127,7 +127,7 @@ extern inline phys_addr_t virt_to_phys(v
 #endif
 }
 
-extern inline void * phys_to_virt(unsigned long address)
+static inline void * phys_to_virt(unsigned long address)
 {
 #ifndef CONFIG_64BIT
 	return (void *)KSEG0ADDR(address);
@@ -139,7 +139,7 @@ extern inline void * phys_to_virt(unsign
 /*
  * IO bus memory addresses are also 1:1 with the physical address
  */
-extern inline unsigned long virt_to_bus(volatile void * address)
+static inline unsigned long virt_to_bus(volatile void * address)
 {
 #ifndef CONFIG_64BIT
 	return CPHYSADDR(address);
@@ -148,7 +148,7 @@ extern inline unsigned long virt_to_bus(
 #endif
 }
 
-extern inline void * bus_to_virt(unsigned long address)
+static inline void * bus_to_virt(unsigned long address)
 {
 #ifndef CONFIG_64BIT
 	return (void *)KSEG0ADDR(address);
@@ -166,12 +166,12 @@ extern unsigned long isa_slot_offset;
 extern void * __ioremap(unsigned long offset, unsigned long size, unsigned long flags);
 
 #if 0
-extern inline void *ioremap(unsigned long offset, unsigned long size)
+static inline void *ioremap(unsigned long offset, unsigned long size)
 {
 	return __ioremap(offset, size, _CACHE_UNCACHED);
 }
 
-extern inline void *ioremap_nocache(unsigned long offset, unsigned long size)
+static inline void *ioremap_nocache(unsigned long offset, unsigned long size)
 {
 	return __ioremap(offset, size, _CACHE_UNCACHED);
 }
--- a/arch/mips/include/asm/system.h
+++ b/arch/mips/include/asm/system.h
@@ -23,7 +23,7 @@
 #include <linux/kernel.h>
 #endif
 
-extern __inline__ void
+static __inline__ void
 __sti(void)
 {
 	__asm__ __volatile__(
@@ -47,7 +47,7 @@ __sti(void)
  * R4000/R4400 need three nops, the R4600 two nops and the R10000 needs
  * no nops at all.
  */
-extern __inline__ void
+static __inline__ void
 __cli(void)
 {
 	__asm__ __volatile__(
@@ -208,7 +208,7 @@ do { \
  * For 32 and 64 bit operands we can take advantage of ll and sc.
  * FIXME: This doesn't work for R3000 machines.
  */
-extern __inline__ unsigned long xchg_u32(volatile int * m, unsigned long val)
+static __inline__ unsigned long xchg_u32(volatile int * m, unsigned long val)
 {
 #ifdef CONFIG_CPU_HAS_LLSC
 	unsigned long dummy;
