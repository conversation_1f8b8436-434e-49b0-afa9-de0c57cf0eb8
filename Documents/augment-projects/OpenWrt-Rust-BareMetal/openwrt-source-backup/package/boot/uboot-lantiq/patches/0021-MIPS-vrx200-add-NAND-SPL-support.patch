From 7361581a1baaec43058f5b9350c32c7ac4e58064 Mon Sep 17 00:00:00 2001
From: <PERSON> <daniel.schwier<PERSON><EMAIL>>
Date: Mon, 12 Aug 2013 00:11:16 +0200
Subject: MIPS: vrx200: add NAND SPL support

Signed-off-by: <PERSON> <daniel.schwier<PERSON><EMAIL>>

--- a/arch/mips/cpu/mips32/vrx200/config.mk
+++ b/arch/mips/cpu/mips32/vrx200/config.mk
@@ -27,4 +27,9 @@ ALL-y += $(obj)u-boot.ltq.norspl
 ALL-$(CONFIG_SPL_LZO_SUPPORT) += $(obj)u-boot.ltq.lzo.norspl
 ALL-$(CONFIG_SPL_LZMA_SUPPORT) += $(obj)u-boot.ltq.lzma.norspl
 endif
+ifdef CONFIG_SYS_BOOT_NANDSPL
+ALL-y += $(obj)u-boot.ltq.nandspl
+ALL-$(CONFIG_SPL_LZO_SUPPORT) += $(obj)u-boot.ltq.lzo.nandspl
+ALL-$(CONFIG_SPL_LZMA_SUPPORT) += $(obj)u-boot.ltq.lzma.nandspl
+endif
 endif
--- a/arch/mips/include/asm/arch-vrx200/config.h
+++ b/arch/mips/include/asm/arch-vrx200/config.h
@@ -168,7 +168,7 @@
 #define CONFIG_SYS_TEXT_BASE		0xB0000000
 #endif
 
-#if defined(CONFIG_SYS_BOOT_SFSPL)
+#if defined(CONFIG_SYS_BOOT_SFSPL) || defined(CONFIG_SYS_BOOT_NANDSPL)
 #define CONFIG_SYS_TEXT_BASE		0x80100000
 #define CONFIG_SPL_TEXT_BASE		0xBE220000
 #endif
