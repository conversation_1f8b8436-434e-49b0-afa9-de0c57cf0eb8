From 65f1f160139c2bac83650c9c7c4aee4e5fd74c7c Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sun, 2 May 2021 02:03:05 +0200
Subject: [PATCH] MIPS: lantiq: danube: fix SPL boot

On danube we only have 0x6800 bytes of usable SRAM. Everything behind
can't be written to and a SPL u-boot locks up during boot.

Signed-off-by: <PERSON> <<EMAIL>>
Reviewed-by: <PERSON> <<EMAIL>>
---
 arch/mips/include/asm/arch-danube/config.h | 4 ++--
 1 file changed, 2 insertions(+), 2 deletions(-)

--- a/arch/mips/include/asm/arch-danube/config.h
+++ b/arch/mips/include/asm/arch-danube/config.h
@@ -61,7 +61,7 @@
 
 /* SRAM */
 #define CONFIG_SYS_SRAM_BASE		0xBE1A0000
-#define CONFIG_SYS_SRAM_SIZE		0x10000
+#define CONFIG_SYS_SRAM_SIZE		0x6800
 
 /* ASC/UART driver and console */
 #define CONFIG_LANTIQ_SERIAL
@@ -117,7 +117,7 @@
 #define CONFIG_CMD_NET
 #endif
 
-#define CONFIG_SPL_MAX_SIZE		(32 * 1024)
+#define CONFIG_SPL_MAX_SIZE		(18 * 1024)
 #define CONFIG_SPL_BSS_MAX_SIZE		(8 * 1024)
 #define CONFIG_SPL_STACK_MAX_SIZE	(8 * 1024)
 #define CONFIG_SPL_MALLOC_MAX_SIZE	(32 * 1024)
