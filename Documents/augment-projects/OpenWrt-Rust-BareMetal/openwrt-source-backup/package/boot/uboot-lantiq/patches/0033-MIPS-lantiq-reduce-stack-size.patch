From ad739ffebf689abdbcddbe4e1b0bf847d7931a92 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 20 Jan 2017 13:59:53 +0100
Subject: [PATCH] MIPS: lantiq: reduce stack size

On lantiq a lot of stuff expects to be loaded to and executed at
0x80002000, including our own second stage bootloader.

For all build u-boots, the initial stack pointer is at 0x80008000. After
loading data to 0x80002000, every further stack operation corrupts the
loaded code.

Set the initial stack pointer to 0x80002000, to not overwrite code
loaded in memory. A stack of 0x2000 bytes has been proven as enough in
all done tests.

Signed-off-by: <PERSON> <<EMAIL>>
---
 arch/mips/include/asm/arch-arx100/config.h | 2 +-
 arch/mips/include/asm/arch-danube/config.h | 2 +-
 arch/mips/include/asm/arch-vrx200/config.h | 2 +-
 3 files changed, 3 insertions(+), 3 deletions(-)

--- a/arch/mips/include/asm/arch-arx100/config.h
+++ b/arch/mips/include/asm/arch-arx100/config.h
@@ -66,7 +66,7 @@
 #define CONFIG_SYS_MEMTEST_END		0x82000000
 #define CONFIG_SYS_LOAD_ADDR		0x81000000
 #define CONFIG_SYS_LOAD_SIZE		(2 * 1024 * 1024)
-#define CONFIG_SYS_INIT_SP_OFFSET	(32 * 1024)
+#define CONFIG_SYS_INIT_SP_OFFSET	0x2000
 
 /* SRAM */
 #define CONFIG_SYS_SRAM_BASE		0xBE1A0000
--- a/arch/mips/include/asm/arch-danube/config.h
+++ b/arch/mips/include/asm/arch-danube/config.h
@@ -57,7 +57,7 @@
 #define CONFIG_SYS_MEMTEST_END		0x82000000
 #define CONFIG_SYS_LOAD_ADDR		0x81000000
 #define CONFIG_SYS_LOAD_SIZE		(2 * 1024 * 1024)
-#define CONFIG_SYS_INIT_SP_OFFSET	0x4000
+#define CONFIG_SYS_INIT_SP_OFFSET	0x2000
 
 /* SRAM */
 #define CONFIG_SYS_SRAM_BASE		0xBE1A0000
--- a/arch/mips/include/asm/arch-vrx200/config.h
+++ b/arch/mips/include/asm/arch-vrx200/config.h
@@ -69,7 +69,7 @@
 #define CONFIG_SYS_MEMTEST_END		0x82000000
 #define CONFIG_SYS_LOAD_ADDR		0x81000000
 #define CONFIG_SYS_LOAD_SIZE		(2 * 1024 * 1024)
-#define CONFIG_SYS_INIT_SP_OFFSET	(32 * 1024)
+#define CONFIG_SYS_INIT_SP_OFFSET	0x2000
 
 /* SRAM */
 #define CONFIG_SYS_SRAM_BASE		0xBE220000
