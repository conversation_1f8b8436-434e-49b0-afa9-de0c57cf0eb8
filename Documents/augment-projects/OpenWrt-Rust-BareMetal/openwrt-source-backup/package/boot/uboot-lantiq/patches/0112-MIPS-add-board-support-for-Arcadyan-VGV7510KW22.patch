--- /dev/null
+++ b/board/arcadyan/vgv7510kw22/Makefile
@@ -0,0 +1,27 @@
+#
+# Copyright (C) 2000-2011 <PERSON>, DENX Software Engineering, <EMAIL>
+#
+# SPDX-License-Identifier:	GPL-2.0+
+#
+
+include $(TOPDIR)/config.mk
+
+LIB	= $(obj)lib$(BOARD).o
+
+COBJS	= $(BOARD).o
+
+SRCS	:= $(SOBJS:.o=.S) $(COBJS:.o=.c)
+OBJS	:= $(addprefix $(obj),$(COBJS))
+SOBJS	:= $(addprefix $(obj),$(SOBJS))
+
+$(LIB):	$(obj).depend $(OBJS) $(SOBJS)
+	$(call cmd_link_o_target, $(OBJS) $(SOBJS))
+
+#########################################################################
+
+# defines $(obj).depend target
+include $(SRCTREE)/rules.mk
+
+sinclude $(obj).depend
+
+#########################################################################
--- /dev/null
+++ b/board/arcadyan/vgv7510kw22/vgv7510kw22.c
@@ -0,0 +1,116 @@
+/*
+ * Copyright (C) 2015 Martin Blumenstingl <<EMAIL>>
+ *
+ * SPDX-License-Identifier:	GPL-2.0+
+ */
+
+#include <common.h>
+#include <asm/gpio.h>
+#include <asm/lantiq/eth.h>
+#include <asm/lantiq/chipid.h>
+#include <asm/lantiq/cpu.h>
+#include <asm/arch/gphy.h>
+
+#if defined(CONFIG_SYS_BOOT_RAM)
+#define do_gpio_init	1
+#define do_pll_init	0
+#define do_dcdc_init	1
+#elif defined(CONFIG_SYS_BOOT_NOR)
+#define do_gpio_init	1
+#define do_pll_init	1
+#define do_dcdc_init	1
+#else
+#define do_gpio_init	0
+#define do_pll_init	0
+#define do_dcdc_init	1
+#endif
+
+#define GPIO_POWER_GREEN	14
+#define GPIO_POWER_RED	28
+
+static void gpio_init(void)
+{
+	/* Turn on the green power LED */
+	gpio_direction_output(GPIO_POWER_GREEN, 0);
+
+	/* Turn off the red power LED */
+	gpio_direction_output(GPIO_POWER_RED, 1);
+}
+
+int board_early_init_f(void)
+{
+	if (do_gpio_init)
+		gpio_init();
+
+	if (do_pll_init)
+		ltq_pll_init();
+
+	if (do_dcdc_init)
+		ltq_dcdc_init(0x7F);
+
+	return 0;
+}
+
+int checkboard(void)
+{
+	puts("Board: " CONFIG_BOARD_NAME "\n");
+	ltq_chip_print_info();
+
+	return 0;
+}
+
+void show_boot_progress(int arg)
+{
+	if (!do_gpio_init)
+		return;
+
+	if (arg >= 0) {
+		/* Success - turn off the red power LED and turn on the green power LED */
+		gpio_set_value(GPIO_POWER_RED, 1);
+		gpio_set_value(GPIO_POWER_GREEN, 0);
+	} else {
+		/* Failure - turn off green power LED and turn on red power LED */
+		gpio_set_value(GPIO_POWER_GREEN, 1);
+		gpio_set_value(GPIO_POWER_RED, 0);
+	}
+
+	return;
+}
+
+static const struct ltq_eth_port_config eth_port_config[] = {
+	/* unused */
+	{ 0, 0x0, LTQ_ETH_PORT_NONE, PHY_INTERFACE_MODE_NONE },
+	/* unused */
+	{ 1, 0x0, LTQ_ETH_PORT_NONE, PHY_INTERFACE_MODE_NONE },
+	/* Internal GPHY0 with 10/100 firmware for LAN port 2 */
+	{ 2, 0x11, LTQ_ETH_PORT_PHY, PHY_INTERFACE_MODE_MII },
+	/* Internal GPHY0 with 10/100 firmware for LAN port 1 */
+	{ 3, 0x12, LTQ_ETH_PORT_PHY, PHY_INTERFACE_MODE_MII },
+	/* Internal GPHY1 with 10/100 firmware for LAN port 4 */
+	{ 4, 0x13, LTQ_ETH_PORT_PHY, PHY_INTERFACE_MODE_MII },
+	/* Internal GPHY1 with 10/100 firmware for LAN port 3 */
+	{ 5, 0x14, LTQ_ETH_PORT_PHY, PHY_INTERFACE_MODE_MII },
+};
+
+static const struct ltq_eth_board_config eth_board_config = {
+	.ports = eth_port_config,
+	.num_ports = ARRAY_SIZE(eth_port_config),
+};
+
+int board_eth_init(bd_t * bis)
+{
+	const enum ltq_gphy_clk clk = LTQ_GPHY_CLK_25MHZ_PLL0;
+	const ulong fw_addr = 0x80FF0000;
+
+	if (ltq_chip_version_get() == 1)
+		ltq_gphy_phy22f_a1x_load(fw_addr);
+	else
+		ltq_gphy_phy22f_a2x_load(fw_addr);
+
+	ltq_cgu_gphy_clk_src(clk);
+
+	ltq_rcu_gphy_boot(0, fw_addr);
+	ltq_rcu_gphy_boot(1, fw_addr);
+
+	return ltq_eth_initialize(&eth_board_config);
+}
--- /dev/null
+++ b/board/arcadyan/vgv7510kw22/config.mk
@@ -0,0 +1,7 @@
+#
+# Copyright (C) 2011-2013 Daniel Schwierzeck, <EMAIL>
+#
+# SPDX-License-Identifier:	GPL-2.0+
+#
+
+PLATFORM_CPPFLAGS += -I$(TOPDIR)/board/$(BOARDDIR)
--- /dev/null
+++ b/board/arcadyan/vgv7510kw22/ddr_settings.h
@@ -0,0 +1,71 @@
+/*
+ * Copyright (C) 2015 Martin Blumenstingl <<EMAIL>>
+ * Copyright (C) 2016 Mathias Kresin <<EMAIL>>
+ *
+ * The values have been extracted from original brnboot.
+ *
+ * SPDX-License-Identifier:	GPL-2.0+
+ */
+
+#define	MC_CCR00_VALUE	0x101
+#define	MC_CCR01_VALUE	0x1000100
+#define	MC_CCR02_VALUE	0x1010000
+#define	MC_CCR03_VALUE	0x100
+#define	MC_CCR04_VALUE	0x1000000
+#define	MC_CCR05_VALUE	0x1000101
+#define	MC_CCR06_VALUE	0x1000100
+#define	MC_CCR07_VALUE	0x1010000
+#define	MC_CCR08_VALUE	0x1000101
+#define	MC_CCR09_VALUE	0x0
+#define	MC_CCR10_VALUE	0x2000100
+#define	MC_CCR11_VALUE	0x2000401
+#define	MC_CCR12_VALUE	0x30000
+#define	MC_CCR13_VALUE	0x202
+#define	MC_CCR14_VALUE	0x7080A0F
+#define	MC_CCR15_VALUE	0x2040F
+#define	MC_CCR16_VALUE	0x40000
+#define	MC_CCR17_VALUE	0x70102
+#define	MC_CCR18_VALUE	0x4020002
+#define	MC_CCR19_VALUE	0x30302
+#define	MC_CCR20_VALUE	0x8000700
+#define	MC_CCR21_VALUE	0x40F020A
+#define	MC_CCR22_VALUE	0x0
+#define	MC_CCR23_VALUE	0xC020000
+#define	MC_CCR24_VALUE	0x4401B04
+#define	MC_CCR25_VALUE	0x0
+#define	MC_CCR26_VALUE	0x0
+#define	MC_CCR27_VALUE	0x6420000
+#define	MC_CCR28_VALUE	0x0
+#define	MC_CCR29_VALUE	0x0
+#define	MC_CCR30_VALUE	0x798
+#define	MC_CCR31_VALUE	0x2040F
+#define	MC_CCR32_VALUE	0x0
+#define	MC_CCR33_VALUE	0x650000
+#define	MC_CCR34_VALUE	0x200C8
+#define	MC_CCR35_VALUE	0x1D445D
+#define	MC_CCR36_VALUE	0xC8
+#define	MC_CCR37_VALUE	0xC351
+#define	MC_CCR38_VALUE	0x0
+#define	MC_CCR39_VALUE	0x141F04
+#define	MC_CCR40_VALUE	0x142704
+#define	MC_CCR41_VALUE	0x141B42
+#define	MC_CCR42_VALUE	0x141B42
+#define	MC_CCR43_VALUE	0x566504
+#define	MC_CCR44_VALUE	0x566504
+#define	MC_CCR45_VALUE	0x565F17
+#define	MC_CCR46_VALUE	0x565F17
+#define	MC_CCR47_VALUE	0x2040F
+#define	MC_CCR48_VALUE	0x0
+#define	MC_CCR49_VALUE	0x0
+#define	MC_CCR50_VALUE	0x0
+#define	MC_CCR51_VALUE	0x0
+#define	MC_CCR52_VALUE	0x133
+#define	MC_CCR53_VALUE	0xF3014B27
+#define	MC_CCR54_VALUE	0xF3014B27
+#define	MC_CCR55_VALUE	0xF3014B27
+#define	MC_CCR56_VALUE	0xF3014B27
+#define	MC_CCR57_VALUE	0x7800301
+#define	MC_CCR58_VALUE	0x7800301
+#define	MC_CCR59_VALUE	0x7800301
+#define	MC_CCR60_VALUE	0x7800301
+#define	MC_CCR61_VALUE	0x4
--- a/boards.cfg
+++ b/boards.cfg
@@ -531,6 +531,9 @@ Active  mips        mips32         incai
 Active  mips        mips32         incaip      -               incaip              incaip_100MHz                        incaip:CPU_CLOCK_RATE=100000000                                                                                                   Wolfgang Denk <<EMAIL>>
 Active  mips        mips32         incaip      -               incaip              incaip_133MHz                        incaip:CPU_CLOCK_RATE=133000000                                                                                                   Wolfgang Denk <<EMAIL>>
 Active  mips        mips32         incaip      -               incaip              incaip_150MHz                        incaip:CPU_CLOCK_RATE=150000000                                                                                                   Wolfgang Denk <<EMAIL>>
+Active  mips        mips32         vrx200      arcadyan        vgv7510kw22         vgv7510kw22_brn                      vgv7510kw22:SYS_BOOT_BRN                                                                                                           Martin Blumenstingl <<EMAIL>>
+Active  mips        mips32         vrx200      arcadyan        vgv7510kw22         vgv7510kw22_nor                      vgv7510kw22:SYS_BOOT_NOR                                                                                                           Martin Blumenstingl <<EMAIL>>
+Active  mips        mips32         vrx200      arcadyan        vgv7510kw22         vgv7510kw22_ram                      vgv7510kw22:SYS_BOOT_RAM                                                                                                           Martin Blumenstingl <<EMAIL>>
 Active  mips        mips32         vrx200      avm             fb3370              fb3370_eva                           fb3370:SYS_BOOT_EVA                                                                                                               Daniel Schwierzeck <<EMAIL>>
 Active  mips        mips32         vrx200      avm             fb3370              fb3370_ram                           fb3370:SYS_BOOT_RAM                                                                                                               Daniel Schwierzeck <<EMAIL>>
 Active  mips        mips32         vrx200      avm             fb3370              fb3370_sfspl                         fb3370:SYS_BOOT_SFSPL                                                                                                             Daniel Schwierzeck <<EMAIL>>
--- /dev/null
+++ b/include/configs/vgv7510kw22.h
@@ -0,0 +1,59 @@
+/*
+ * Copyright (C) 2015 Martin Blumenstingl <<EMAIL>>
+ *
+ * SPDX-License-Identifier:	GPL-2.0+
+ */
+
+#ifndef __CONFIG_H
+#define __CONFIG_H
+
+#define CONFIG_MACH_TYPE	"VGV7510KW22"
+#define CONFIG_IDENT_STRING	" "CONFIG_MACH_TYPE
+#define CONFIG_BOARD_NAME	"Arcadyan VGV7510KW22"
+
+/* Configure SoC */
+#define CONFIG_LTQ_SUPPORT_UART			/* Enable ASC and UART */
+
+#define CONFIG_LTQ_SUPPORT_ETHERNET		/* Enable ethernet */
+
+#define CONFIG_LTQ_SUPPORT_NOR_FLASH		/* Have a parallel NOR flash */
+
+#define CONFIG_SYS_BOOTM_LEN		0x1000000	/* 16 MB */
+
+#if defined(CONFIG_SYS_BOOT_BRN)
+#define CONFIG_SYS_TEXT_BASE		0x80002000
+#define CONFIG_SKIP_LOWLEVEL_INIT
+#define CONFIG_SYS_DISABLE_CACHE
+#define CONFIG_ENV_IS_NOWHERE
+#define CONFIG_ENV_OVERWRITE		1
+#elif defined(CONFIG_SYS_BOOT_NOR)
+#define CONFIG_ENV_IS_IN_FLASH
+#define CONFIG_ENV_OVERWRITE
+#define CONFIG_ENV_OFFSET		(384 * 1024)
+#define CONFIG_ENV_SECT_SIZE		(128 * 1024)
+#else
+#define CONFIG_ENV_IS_NOWHERE
+#endif
+
+#define CONFIG_ENV_SIZE			(128 * 1024)
+
+#define CONFIG_LOADADDR			CONFIG_SYS_LOAD_ADDR
+
+/* Console */
+#define CONFIG_LTQ_ADVANCED_CONSOLE
+#define CONFIG_BAUDRATE			115200
+#define CONFIG_CONSOLE_ASC		1
+#define CONFIG_CONSOLE_DEV		"ttyLTQ1"
+
+/* Pull in default board configs for Lantiq XWAY VRX200 */
+#include <asm/lantiq/config.h>
+#include <asm/arch/config.h>
+
+/* Pull in default OpenWrt configs for Lantiq SoC */
+#include "openwrt-lantiq-common.h"
+
+#define CONFIG_EXTRA_ENV_SETTINGS	\
+	CONFIG_ENV_LANTIQ_DEFAULTS	\
+	"kernel_addr=0xB0080000\0"
+
+#endif /* __CONFIG_H */
