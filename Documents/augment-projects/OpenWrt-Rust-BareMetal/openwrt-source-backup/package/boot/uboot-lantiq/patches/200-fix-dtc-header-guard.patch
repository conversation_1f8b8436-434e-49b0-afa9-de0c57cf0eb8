--- a/include/libfdt_env.h
+++ b/include/libfdt_env.h
@@ -8,6 +8,7 @@
 
 #ifndef _LIBFDT_ENV_H
 #define _LIBFDT_ENV_H
+#define LIBFDT_ENV_H
 
 #include "compiler.h"
 #include "linux/types.h"
--- a/include/libfdt.h
+++ b/include/libfdt.h
@@ -1,5 +1,6 @@
 #ifndef _LIBFDT_H
 #define _LIBFDT_H
+#define LIBFDT_H
 /*
  * libfdt - Flat Device Tree manipulation
  * Copyright (C) 2006 David <PERSON>, IBM Corporation.
