From 8f584936adad0fca8beece5f55eadcdcd02fad0a Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <<EMAIL>>
Date: Sat, 17 Aug 2013 03:44:46 +0200
Subject: MIPS: lantiq: add default openwrt config

Signed-off-by: <PERSON><PERSON> <<EMAIL>>
Signed-off-by: <PERSON> <daniel.schwier<PERSON><EMAIL>>

--- /dev/null
+++ b/include/configs/openwrt-lantiq-common.h
@@ -0,0 +1,40 @@
+/*
+ * Copyright (C) 2013 <PERSON><PERSON> <<EMAIL>>
+ *
+ * SPDX-License-Identifier:	GPL-2.0+
+ */
+
+#ifndef __OPENWRT_LANTIQ_COMMON_H
+#define __OPENWRT_LANTIQ_COMMON_H
+
+/* Commands */
+#if defined(CONFIG_LTQ_SUPPORT_ETHERNET)
+#define CONFIG_CMD_PING
+#define CONFIG_CMD_TFTPPUT
+#endif
+
+/* Compression */
+#define CONFIG_LZMA
+
+/* Auto boot */
+#define CONFIG_BOOTDELAY	2
+
+/* Environment */
+#if !defined(CONFIG_SYS_BOOT_RAM)
+#define CONFIG_BOOTCOMMAND \
+	"bootm ${kernel_addr}"
+#endif
+
+/* Ethernet */
+#if defined(CONFIG_LTQ_SUPPORT_ETHERNET)
+#define CONFIG_ETHADDR		00:01:02:03:04:05
+#define CONFIG_SERVERIP		***********
+#define CONFIG_IPADDR		***********
+#endif
+
+/* Unnecessary */
+#undef CONFIG_BOOTM_NETBSD
+#undef CONFIG_BOOTM_PLAN9
+#undef CONFIG_BOOTM_RTEMS
+
+#endif /* __OPENWRT_LANTIQ_COMMON_H */
