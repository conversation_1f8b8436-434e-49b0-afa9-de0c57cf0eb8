--- /dev/null
+++ b/board/arcadyan/arv7506pw11/Makefile
@@ -0,0 +1,27 @@
+#
+# Copyright (C) 2000-2011 <PERSON>, DENX Software Engineering, <EMAIL>
+#
+# SPDX-License-Identifier:	GPL-2.0+
+#
+
+include $(TOPDIR)/config.mk
+
+LIB	= $(obj)lib$(BOARD).o
+
+COBJS	= $(BOARD).o
+
+SRCS	:= $(SOBJS:.o=.S) $(COBJS:.o=.c)
+OBJS	:= $(addprefix $(obj),$(COBJS))
+SOBJS	:= $(addprefix $(obj),$(SOBJS))
+
+$(LIB):	$(obj).depend $(OBJS) $(SOBJS)
+	$(call cmd_link_o_target, $(OBJS) $(SOBJS))
+
+#########################################################################
+
+# defines $(obj).depend target
+include $(SRCTREE)/rules.mk
+
+sinclude $(obj).depend
+
+#########################################################################
--- /dev/null
+++ b/board/arcadyan/arv7506pw11/arv7506pw11.c
@@ -0,0 +1,97 @@
+/*
+ * Copyright (C) 2016 Mathias Kresin <<EMAIL>>
+ *
+ * SPDX-License-Identifier:	GPL-2.0+
+ */
+
+#include <common.h>
+#include <switch.h>
+#include <asm/gpio.h>
+#include <asm/lantiq/eth.h>
+#include <asm/lantiq/reset.h>
+#include <asm/lantiq/chipid.h>
+
+#if defined(CONFIG_SYS_BOOT_RAM)
+#define do_gpio_init	1
+#elif defined(CONFIG_SYS_BOOT_NOR)
+#define do_gpio_init	1
+#else
+#define do_gpio_init	0
+#endif
+
+#define GPIO_POWER_GREEN	3
+#define GPIO_POWER_RED	6
+#define GPIO_GPHY_RESET 19
+
+static void gpio_init(void)
+{
+	/* Reset switch to have him in a clean state on reboot */
+	gpio_direction_output(GPIO_GPHY_RESET, 0);
+	udelay(20);
+	gpio_direction_output(GPIO_GPHY_RESET, 1);
+
+	/* Turn on the green power LED */
+	gpio_direction_output(GPIO_POWER_GREEN, 0);
+
+	/* Turn off the red power LED */
+	gpio_direction_output(GPIO_POWER_RED, 1);
+}
+
+int board_early_init_f(void)
+{
+	if (do_gpio_init)
+		gpio_init();
+
+	return 0;
+}
+
+int checkboard(void)
+{
+	puts("Board: " CONFIG_BOARD_NAME "\n");
+	ltq_chip_print_info();
+
+	return 0;
+}
+
+void show_boot_progress(int arg)
+{
+	if (!do_gpio_init)
+		return;
+
+	if (arg >= 0) {
+		/* Success - turn off the red power LED and turn on the green power LED */
+		gpio_set_value(GPIO_POWER_RED, 1);
+		gpio_set_value(GPIO_POWER_GREEN, 0);
+	} else {
+		/* Failure - turn off green power LED and turn on red power LED */
+		gpio_set_value(GPIO_POWER_GREEN, 1);
+		gpio_set_value(GPIO_POWER_RED, 0);
+	}
+
+	return;
+}
+
+static const struct ltq_eth_port_config eth_port_config[] = {
+	/* MAC0: Realtek rtl8306 switch */
+	{ 0, 0x0, LTQ_ETH_PORT_SWITCH, PHY_INTERFACE_MODE_RMII },
+};
+
+static const struct ltq_eth_board_config eth_board_config = {
+	.ports = eth_port_config,
+	.num_ports = ARRAY_SIZE(eth_port_config),
+};
+
+int board_eth_init(bd_t *bis)
+{
+	return ltq_eth_initialize(&eth_board_config);
+}
+static struct switch_device rtl8306_dev = {
+	.name = "rtl8306",
+	.cpu_port = 5,
+	.port_mask = 0xF,
+};
+
+int board_switch_init(void)
+{
+	return switch_device_register(&rtl8306_dev);
+}
--- /dev/null
+++ b/board/arcadyan/arv7506pw11/config.mk
@@ -0,0 +1,7 @@
+#
+# Copyright (C) 2011-2013 Daniel Schwierzeck, <EMAIL>
+#
+# SPDX-License-Identifier:	GPL-2.0+
+#
+
+PLATFORM_CPPFLAGS += -I$(TOPDIR)/board/$(BOARDDIR)
--- /dev/null
+++ b/board/arcadyan/arv7506pw11/ddr_settings.h
@@ -0,0 +1,55 @@
+/*
+ * Copyright (C) 2011-2013 Luka Perkov <<EMAIL>>
+ *
+ * This file has been generated with lantiq_ram_extract_magic.awk script. 
+ *
+ * SPDX-License-Identifier:	GPL-2.0+
+ */
+
+#define MC_DC00_VALUE	0x1B1B
+#define MC_DC01_VALUE	0x0
+#define MC_DC02_VALUE	0x0
+#define MC_DC03_VALUE	0x0
+#define MC_DC04_VALUE	0x0
+#define MC_DC05_VALUE	0x200
+#define MC_DC06_VALUE	0x605
+#define MC_DC07_VALUE	0x303
+#define MC_DC08_VALUE	0x102
+#define MC_DC09_VALUE	0x70A
+#define MC_DC10_VALUE	0x203
+#define MC_DC11_VALUE	0xC02
+#define MC_DC12_VALUE	0x1C8
+#define MC_DC13_VALUE	0x1
+#define MC_DC14_VALUE	0x0
+#define MC_DC15_VALUE	0x142
+#define MC_DC16_VALUE	0xC800
+#define MC_DC17_VALUE	0xD
+#define MC_DC18_VALUE	0x301
+#define MC_DC19_VALUE	0x200
+#define MC_DC20_VALUE	0xA03
+#define MC_DC21_VALUE	0x1300
+#define MC_DC22_VALUE	0x1313
+#define MC_DC23_VALUE	0x0
+#define MC_DC24_VALUE	0x68
+#define MC_DC25_VALUE	0x0
+#define MC_DC26_VALUE	0x0
+#define MC_DC27_VALUE	0x0
+#define MC_DC28_VALUE	0x510
+#define MC_DC29_VALUE	0x4E20
+#define MC_DC30_VALUE	0x8235
+#define MC_DC31_VALUE	0x0
+#define MC_DC32_VALUE	0x0
+#define MC_DC33_VALUE	0x0
+#define MC_DC34_VALUE	0x0
+#define MC_DC35_VALUE	0x0
+#define MC_DC36_VALUE	0x0
+#define MC_DC37_VALUE	0x0
+#define MC_DC38_VALUE	0x0
+#define MC_DC39_VALUE	0x0
+#define MC_DC40_VALUE	0x0
+#define MC_DC41_VALUE	0x0
+#define MC_DC42_VALUE	0x0
+#define MC_DC43_VALUE	0x0
+#define MC_DC44_VALUE	0x0
+#define MC_DC45_VALUE	0x500
+#define MC_DC46_VALUE	0x0
--- a/boards.cfg
+++ b/boards.cfg
@@ -505,6 +505,9 @@ Active  mips        mips32         au1x0
 Active  mips        mips32         danube      arcadyan        arv4519pw           arv4519pw_brn                        arv4519pw:SYS_BOOT_BRN                                                                                                            Luka Perkov <<EMAIL>>
 Active  mips        mips32         danube      arcadyan        arv4519pw           arv4519pw_nor                        arv4519pw:SYS_BOOT_NOR                                                                                                            Luka Perkov <<EMAIL>>
 Active  mips        mips32         danube      arcadyan        arv4519pw           arv4519pw_ram                        arv4519pw:SYS_BOOT_RAM                                                                                                            Luka Perkov <<EMAIL>>
+Active  mips        mips32         danube      arcadyan        arv7506pw11         arv7506pw11_brn                      arv7506pw11:SYS_BOOT_BRN                                                                                                          Mathias Kresin <<EMAIL>>
+Active  mips        mips32         danube      arcadyan        arv7506pw11         arv7506pw11_nor                      arv7506pw11:SYS_BOOT_NOR                                                                                                          Mathias Kresin <<EMAIL>>
+Active  mips        mips32         danube      arcadyan        arv7506pw11         arv7506pw11_ram                      arv7506pw11:SYS_BOOT_RAM                                                                                                          Mathias Kresin <<EMAIL>>
 Active  mips        mips32         danube      arcadyan        arv7510pw           arv7510pw_brn                        arv7510pw:SYS_BOOT_BRN                                                                                                            Luka Perkov <<EMAIL>>
 Active  mips        mips32         danube      arcadyan        arv7510pw           arv7510pw_nor                        arv7510pw:SYS_BOOT_NOR                                                                                                            Luka Perkov <<EMAIL>>
 Active  mips        mips32         danube      arcadyan        arv7510pw           arv7510pw_ram                        arv7510pw:SYS_BOOT_RAM                                                                                                            Luka Perkov <<EMAIL>>
--- /dev/null
+++ b/include/configs/arv7506pw11.h
@@ -0,0 +1,64 @@
+/*
+ * Copyright (C) 2016 Mathias Kresin <<EMAIL>>
+ *
+ * SPDX-License-Identifier:	GPL-2.0+
+ */
+
+#ifndef __CONFIG_H
+#define __CONFIG_H
+
+#define CONFIG_MACH_TYPE	"ARV7506PW11"
+#define CONFIG_IDENT_STRING	" "CONFIG_MACH_TYPE
+#define CONFIG_BOARD_NAME	"Arcadyan ARV7506PW11"
+
+/* Configure SoC */
+#define CONFIG_LTQ_SUPPORT_UART		/* Enable ASC and UART */
+
+#define CONFIG_LTQ_SUPPORT_ETHERNET	/* Enable ethernet */
+
+#define CONFIG_LTQ_SUPPORT_NOR_FLASH	/* Have a parallel NOR flash */
+
+#define CONFIG_SYS_BOOTM_LEN		0x1000000	/* 16 MB */
+
+/* Switch devices */
+#define CONFIG_SWITCH_MULTI
+#define CONFIG_SWITCH_RTL8306
+
+/* Environment */
+#if defined(CONFIG_SYS_BOOT_BRN)
+#define CONFIG_SYS_TEXT_BASE		0x80002000
+#define CONFIG_SKIP_LOWLEVEL_INIT
+#define CONFIG_SYS_DISABLE_CACHE
+#define CONFIG_ENV_IS_NOWHERE
+#define CONFIG_ENV_OVERWRITE 1
+#elif defined(CONFIG_SYS_BOOT_NOR)
+#define CONFIG_ENV_IS_IN_FLASH
+#define CONFIG_ENV_OVERWRITE
+#define CONFIG_ENV_OFFSET		(256 * 1024)
+#define CONFIG_ENV_SECT_SIZE		(64 * 1024)
+#else
+#define CONFIG_ENV_IS_NOWHERE
+#endif
+
+#define CONFIG_ENV_SIZE			(64 * 1024)
+
+#define CONFIG_LOADADDR			CONFIG_SYS_LOAD_ADDR
+
+/* Console */
+#define CONFIG_LTQ_ADVANCED_CONSOLE
+#define CONFIG_BAUDRATE			115200
+#define CONFIG_CONSOLE_ASC		1
+#define CONFIG_CONSOLE_DEV		"ttyLTQ1"
+
+/* Pull in default board configs for Lantiq XWAY Danube */
+#include <asm/lantiq/config.h>
+#include <asm/arch/config.h>
+
+/* Pull in default OpenWrt configs for Lantiq SoC */
+#include "openwrt-lantiq-common.h"
+
+#define CONFIG_EXTRA_ENV_SETTINGS	\
+	CONFIG_ENV_LANTIQ_DEFAULTS	\
+	"kernel_addr=0xB0050000\0"
+
+#endif /* __CONFIG_H */
