From bb7df8c6ff30be3786483767d3afb0e77a69a640 Mon Sep 17 00:00:00 2001
From: <PERSON> <daniel.schwier<PERSON><EMAIL>>
Date: Sat, 12 Oct 2013 21:21:18 +0200
Subject: sf: consistently use debug() for warning/error messages

Signed-off-by: <PERSON> <<EMAIL>>

--- a/drivers/mtd/spi/sf_probe.c
+++ b/drivers/mtd/spi/sf_probe.c
@@ -176,8 +176,8 @@ static struct spi_flash *spi_flash_valid
 	}
 
 	if (i == ARRAY_SIZE(spi_flash_params_table)) {
-		printf("SF: Unsupported flash IDs: ");
-		printf("manuf %02x, jedec %04x, ext_jedec %04x\n",
+		debug("SF: Unsupported flash IDs: ");
+		debug("manuf %02x, jedec %04x, ext_jedec %04x\n",
 		       idcode[0], jedec, ext_jedec);
 		return NULL;
 	}
@@ -296,7 +296,7 @@ struct spi_flash *spi_flash_probe(unsign
 	/* Setup spi_slave */
 	spi = spi_setup_slave(bus, cs, max_hz, spi_mode);
 	if (!spi) {
-		printf("SF: Failed to set up slave\n");
+		debug("SF: Failed to set up slave\n");
 		return NULL;
 	}
 
@@ -310,7 +310,7 @@ struct spi_flash *spi_flash_probe(unsign
 	/* Read the ID codes */
 	ret = spi_flash_cmd(spi, CMD_READ_ID, idcode, sizeof(idcode));
 	if (ret) {
-		printf("SF: Failed to get idcodes\n");
+		debug("SF: Failed to get idcodes\n");
 		goto err_read_id;
 	}
 
@@ -341,8 +341,8 @@ struct spi_flash *spi_flash_probe(unsign
 #endif
 #ifndef CONFIG_SPI_FLASH_BAR
 	if (flash->size > SPI_FLASH_16MB_BOUN) {
-		puts("SF: Warning - Only lower 16MiB accessible,");
-		puts(" Full access #define CONFIG_SPI_FLASH_BAR\n");
+		debug("SF: Warning - Only lower 16MiB accessible,");
+		debug(" Full access #define CONFIG_SPI_FLASH_BAR\n");
 	}
 #endif
 
