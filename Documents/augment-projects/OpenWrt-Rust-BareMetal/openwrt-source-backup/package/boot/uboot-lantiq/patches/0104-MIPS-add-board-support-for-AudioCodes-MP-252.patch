From 4bacfc80eae768be45f9ddf7588ec55281354648 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 8 Mar 2013 13:29:04 +0200
Subject: MIPS: add board support for AudioCodes MP-252

Signed-off-by: <PERSON> <<EMAIL>>

--- /dev/null
+++ b/board/audiocodes/acmp252/Makefile
@@ -0,0 +1,27 @@
+#
+# Copyright (C) 2000-2011 <PERSON>, DENX Software Engineering, <EMAIL>
+#
+# SPDX-License-Identifier:	GPL-2.0+
+#
+
+include $(TOPDIR)/config.mk
+
+LIB	= $(obj)lib$(BOARD).o
+
+COBJS	= $(BOARD).o
+
+SRCS	:= $(SOBJS:.o=.S) $(COBJS:.o=.c)
+OBJS	:= $(addprefix $(obj),$(COBJS))
+SOBJS	:= $(addprefix $(obj),$(SOBJS))
+
+$(LIB):	$(obj).depend $(OBJS) $(SOBJS)
+	$(call cmd_link_o_target, $(OBJS) $(SOBJS))
+
+#########################################################################
+
+# defines $(obj).depend target
+include $(SRCTREE)/rules.mk
+
+sinclude $(obj).depend
+
+#########################################################################
--- /dev/null
+++ b/board/audiocodes/acmp252/acmp252.c
@@ -0,0 +1,66 @@
+/*
+ * Copyright (C) 2013 Daniel Golle <<EMAIL>>
+ * Copyright (C) 2011 Luka Perkov <<EMAIL>>
+ *
+ * SPDX-License-Identifier:	GPL-2.0+
+ */
+
+#include <common.h>
+#include <switch.h>
+#include <asm/gpio.h>
+#include <asm/lantiq/eth.h>
+#include <asm/lantiq/reset.h>
+#include <asm/lantiq/chipid.h>
+
+static void gpio_init(void)
+{
+	/* Activate reset line of ADM6996I switch */
+	gpio_direction_output(19, 0);
+}
+
+int board_early_init_f(void)
+{
+	gpio_init();
+
+	return 0;
+}
+
+int checkboard(void)
+{
+	puts("Board: " CONFIG_BOARD_NAME "\n");
+	ltq_chip_print_info();
+
+	return 0;
+}
+
+static const struct ltq_eth_port_config eth_port_config[] = {
+	/* MAC0: Lantiq ADM6996I switch */
+	{ 0, 0x0, LTQ_ETH_PORT_SWITCH, PHY_INTERFACE_MODE_RMII },
+};
+
+static const struct ltq_eth_board_config eth_board_config = {
+	.ports = eth_port_config,
+	.num_ports = ARRAY_SIZE(eth_port_config),
+};
+
+int board_eth_init(bd_t *bis)
+{
+	return ltq_eth_initialize(&eth_board_config);
+}
+
+static struct switch_device adm6996i_dev = {
+	.name = "adm6996i",
+	.cpu_port = 5,
+	.port_mask = 0xF,
+};
+
+int board_switch_init(void)
+{
+	/* Deactivate reset line of ADM6996I switch */
+	gpio_set_value(19, 1);
+
+	/* ADM6996I needs some time to come out of reset */
+	__udelay(50000);
+
+	return switch_device_register(&adm6996i_dev);
+}
--- /dev/null
+++ b/board/audiocodes/acmp252/config.mk
@@ -0,0 +1,7 @@
+#
+# Copyright (C) 2011-2013 Daniel Schwierzeck, <EMAIL>
+#
+# SPDX-License-Identifier:	GPL-2.0+
+#
+
+PLATFORM_CPPFLAGS += -I$(TOPDIR)/board/$(BOARDDIR)
--- /dev/null
+++ b/board/audiocodes/acmp252/ddr_settings.h
@@ -0,0 +1,55 @@
+/*
+ * Copyright (C) 2011-2013 Luka Perkov <<EMAIL>>
+ *
+ * This file has been generated with lantiq_ram_extract_magic.awk script.
+ *
+ * SPDX-License-Identifier:	GPL-2.0+
+ */
+
+#define MC_DC00_VALUE	0x1B1B
+#define MC_DC01_VALUE	0x0
+#define MC_DC02_VALUE	0x0
+#define MC_DC03_VALUE	0x0
+#define MC_DC04_VALUE	0x0
+#define MC_DC05_VALUE	0x200
+#define MC_DC06_VALUE	0x605
+#define MC_DC07_VALUE	0x403
+#define MC_DC08_VALUE	0x103
+#define MC_DC09_VALUE	0x80B
+#define MC_DC10_VALUE	0x304
+#define MC_DC11_VALUE	0xD03
+#define MC_DC12_VALUE	0x2C8
+#define MC_DC13_VALUE	0x1
+#define MC_DC14_VALUE	0x0
+#define MC_DC15_VALUE	0x13C
+#define MC_DC16_VALUE	0xC800
+#define MC_DC17_VALUE	0xD
+#define MC_DC18_VALUE	0x402
+#define MC_DC19_VALUE	0x200
+#define MC_DC20_VALUE	0xA03
+#define MC_DC21_VALUE	0x1700
+#define MC_DC22_VALUE	0x1717
+#define MC_DC23_VALUE	0x0
+#define MC_DC24_VALUE	0x5C
+#define MC_DC25_VALUE	0x0
+#define MC_DC26_VALUE	0x0
+#define MC_DC27_VALUE	0x0
+#define MC_DC28_VALUE	0x510
+#define MC_DC29_VALUE	0x2D93
+#define MC_DC30_VALUE	0x8300
+#define MC_DC31_VALUE	0x0
+#define MC_DC32_VALUE	0x0
+#define MC_DC33_VALUE	0x0
+#define MC_DC34_VALUE	0x0
+#define MC_DC35_VALUE	0x0
+#define MC_DC36_VALUE	0x0
+#define MC_DC37_VALUE	0x0
+#define MC_DC38_VALUE	0x0
+#define MC_DC39_VALUE	0x0
+#define MC_DC40_VALUE	0x0
+#define MC_DC41_VALUE	0x0
+#define MC_DC42_VALUE	0x0
+#define MC_DC43_VALUE	0x0
+#define MC_DC44_VALUE	0x0
+#define MC_DC45_VALUE	0x500
+#define MC_DC46_VALUE	0x0
--- a/boards.cfg
+++ b/boards.cfg
@@ -508,6 +508,8 @@ Active  mips        mips32         danub
 Active  mips        mips32         danube      arcadyan        arv7518pw           arv7518pw_brn                        arv7518pw:SYS_BOOT_BRN                                                                                                            Luka Perkov <<EMAIL>>
 Active  mips        mips32         danube      arcadyan        arv7518pw           arv7518pw_nor                        arv7518pw:SYS_BOOT_NOR                                                                                                            Luka Perkov <<EMAIL>>
 Active  mips        mips32         danube      arcadyan        arv7518pw           arv7518pw_ram                        arv7518pw:SYS_BOOT_RAM                                                                                                            Luka Perkov <<EMAIL>>
+Active  mips        mips32         danube      audiocodes      acmp252             acmp252_nor                          acmp252:SYS_BOOT_NOR                                                                                                              Daniel Golle <<EMAIL>>
+Active  mips        mips32         danube      audiocodes      acmp252             acmp252_ram                          acmp252:SYS_BOOT_RAM                                                                                                              Daniel Golle <<EMAIL>>
 Active  mips        mips32         danube      lantiq          easy50712           easy50712_nor                        easy50712:SYS_BOOT_NOR                                                                                                            Daniel Schwierzeck <<EMAIL>>
 Active  mips        mips32         danube      lantiq          easy50712           easy50712_norspl                     easy50712:SYS_BOOT_NORSPL                                                                                                         Daniel Schwierzeck <<EMAIL>>
 Active  mips        mips32         danube      lantiq          easy50712           easy50712_ram                        easy50712:SYS_BOOT_RAM                                                                                                            Daniel Schwierzeck <<EMAIL>>
--- /dev/null
+++ b/include/configs/acmp252.h
@@ -0,0 +1,62 @@
+/*
+ * Copyright (C) 2013 Daniel Golle <<EMAIL>>
+ * Copyright (C) 2011 Luka Perkov <<EMAIL>>
+ *
+ * SPDX-License-Identifier:	GPL-2.0+
+ */
+
+#ifndef __CONFIG_H
+#define __CONFIG_H
+
+#define CONFIG_MACH_TYPE	"ACMP252"
+#define CONFIG_IDENT_STRING	" "CONFIG_MACH_TYPE
+#define CONFIG_BOARD_NAME	"AudioCodes MP-252"
+
+/* Configure SoC */
+#define CONFIG_LTQ_SUPPORT_UART		/* Enable ASC and UART */
+
+#define CONFIG_LTQ_SUPPORT_ETHERNET	/* Enable ethernet */
+
+#define CONFIG_LTQ_SUPPORT_NOR_FLASH	/* Have a parallel NOR flash */
+
+#define CONFIG_SYS_BOOTM_LEN		0x1000000	/* 16 MB */
+
+/* Switch devices */
+#define CONFIG_SWITCH_MULTI
+#define CONFIG_SWITCH_ADM6996I
+
+/* Environment */
+#if defined(CONFIG_SYS_BOOT_NOR)
+#define CONFIG_ENV_IS_IN_FLASH
+#define CONFIG_ENV_OVERWRITE
+#define CONFIG_ENV_OFFSET		(256 * 1024)
+#define CONFIG_ENV_SECT_SIZE		(128 * 1024)
+#else
+#define CONFIG_ENV_IS_NOWHERE
+#endif
+
+#define CONFIG_ENV_SIZE			(8 * 1024)
+#define CONFIG_LOADADDR			CONFIG_SYS_LOAD_ADDR
+
+/* Console */
+#define CONFIG_LTQ_ADVANCED_CONSOLE
+#define CONFIG_BAUDRATE			115200
+#define CONFIG_CONSOLE_ASC		1
+#define CONFIG_CONSOLE_DEV		"ttyLTQ1"
+
+/* Pull in default board configs for Lantiq XWAY Danube */
+#include <asm/lantiq/config.h>
+#include <asm/arch/config.h>
+
+/* Pull in default OpenWrt configs for Lantiq SoC */
+#include "openwrt-lantiq-common.h"
+
+#define CONFIG_ENV_UPDATE_UBOOT_NOR		\
+	"update-uboot-nor=run load-uboot-nor write-uboot-nor\0"
+
+#define CONFIG_EXTRA_ENV_SETTINGS	\
+	CONFIG_ENV_LANTIQ_DEFAULTS	\
+	CONFIG_ENV_UPDATE_UBOOT_NOR	\
+	"kernel_addr=0xB0040000\0"
+
+#endif /* __CONFIG_H */
