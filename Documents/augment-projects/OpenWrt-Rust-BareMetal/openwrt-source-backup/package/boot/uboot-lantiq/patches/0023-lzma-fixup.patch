From: <PERSON><PERSON> <<EMAIL>>
Date: Tu<PERSON>, 31 Dec 2013 01:05:42 +0100
Subject: [PATCH] lzma: fix buffer bound check error

Variable uncompressedSize references the space available, while outSizeFull is
the actual expected uncompressed size. Using the wrong value causes LzmaDecode
to return SZ_ERROR_INPUT_EOF. Problem was introduced in commit afca294. While
at it add additional debug message.

Signed-off-by: <PERSON><PERSON> <<EMAIL>>
CC: <PERSON><PERSON> <<EMAIL>>
CC: <PERSON> <<EMAIL>>
CC: <PERSON> <<EMAIL>>
CC: <PERSON><PERSON> <<EMAIL>>
---
 lib/lzma/LzmaTools.c | 5 ++++-
 1 file changed, 4 insertions(+), 1 deletion(-)

--- a/lib/lzma/LzmaTools.c
+++ b/lib/lzma/LzmaTools.c
@@ -102,7 +102,7 @@ int lzmaBuffToBuffDecompress (unsigned c
         return SZ_ERROR_OUTPUT_EOF;
 
     /* Decompress */
-    outProcessed = *uncompressedSize;
+    outProcessed = outSizeFull;
 
     WATCHDOG_RESET();
 
@@ -111,6 +111,9 @@ int lzmaBuffToBuffDecompress (unsigned c
         inStream + LZMA_DATA_OFFSET, &compressedSize,
         inStream, LZMA_PROPS_SIZE, LZMA_FINISH_END, &state, &g_Alloc);
     *uncompressedSize = outProcessed;
+
+    debug("LZMA: Uncompresed ................ 0x%zx\n", outProcessed);
+
     if (res != SZ_OK)  {
         return res;
     }
