Fix header clash with system /usr/include/sha1.h and sha256.h when libmd
is installed.

Backport of u-boot commit "includes: move openssl headers to include/u-boot"
https://github.com/u-boot/u-boot/commit/2b9912e6a7df7b1f60beb7942bd0e6fa5f9d0167

--- a/board/gdsys/p1022/controlcenterd-id.c
+++ b/board/gdsys/p1022/controlcenterd-id.c
@@ -30,7 +30,7 @@
 #include <i2c.h>
 #include <mmc.h>
 #include <tpm.h>
-#include <sha1.h>
+#include <u-boot/sha1.h>
 #include <asm/byteorder.h>
 #include <asm/unaligned.h>
 #include <pca9698.h>
--- a/board/pcs440ep/pcs440ep.c
+++ b/board/pcs440ep/pcs440ep.c
@@ -13,7 +13,7 @@
 #include <asm/processor.h>
 #include <spd_sdram.h>
 #include <status_led.h>
-#include <sha1.h>
+#include <u-boot/sha1.h>
 #include <asm/io.h>
 #include <net.h>
 #include <ata.h>
--- a/common/cmd_sha1sum.c
+++ b/common/cmd_sha1sum.c
@@ -11,7 +11,7 @@
 #include <common.h>
 #include <command.h>
 #include <hash.h>
-#include <sha1.h>
+#include <u-boot/sha1.h>
 
 int do_sha1sum(cmd_tbl_t *cmdtp, int flag, int argc, char * const argv[])
 {
--- a/common/hash.c
+++ b/common/hash.c
@@ -14,8 +14,8 @@
 #include <command.h>
 #include <hw_sha.h>
 #include <hash.h>
-#include <sha1.h>
-#include <sha256.h>
+#include <u-boot/sha1.h>
+#include <u-boot/sha256.h>
 #include <asm/io.h>
 #include <asm/errno.h>
 
--- a/common/image-fit.c
+++ b/common/image-fit.c
@@ -21,7 +21,7 @@ DECLARE_GLOBAL_DATA_PTR;
 #endif /* !USE_HOSTCC*/
 
 #include <bootstage.h>
-#include <sha1.h>
+#include <u-boot/sha1.h>
 #include <u-boot/crc.h>
 #include <u-boot/md5.h>
 
--- a/common/image.c
+++ b/common/image.c
@@ -34,7 +34,7 @@
 #endif
 
 #include <u-boot/md5.h>
-#include <sha1.h>
+#include <u-boot/sha1.h>
 #include <asm/errno.h>
 #include <asm/io.h>
 
--- a/drivers/crypto/ace_sha.c
+++ b/drivers/crypto/ace_sha.c
@@ -5,8 +5,8 @@
  * SPDX-License-Identifier:	GPL-2.0+
  */
 #include <common.h>
-#include <sha256.h>
-#include <sha1.h>
+#include <u-boot/sha256.h>
+#include <u-boot/sha1.h>
 #include <asm/errno.h>
 #include "ace_sha.h"
 
--- /dev/null
+++ b/include/u-boot/sha1.h
@@ -0,0 +1 @@
+#include "../sha1.h"
--- /dev/null
+++ b/include/u-boot/sha256.h
@@ -0,0 +1 @@
+#include "../sha256.h"
--- a/lib/rsa/rsa-verify.c
+++ b/lib/rsa/rsa-verify.c
@@ -7,7 +7,7 @@
 #include <common.h>
 #include <fdtdec.h>
 #include <rsa.h>
-#include <sha1.h>
+#include <u-boot/sha1.h>
 #include <asm/byteorder.h>
 #include <asm/errno.h>
 #include <asm/unaligned.h>
--- a/lib/sha1.c
+++ b/lib/sha1.c
@@ -36,7 +36,7 @@
 #include <string.h>
 #endif /* USE_HOSTCC */
 #include <watchdog.h>
-#include "sha1.h"
+#include <u-boot/sha1.h>
 
 /*
  * 32-bit integer manipulation macros (big endian)
--- a/lib/sha256.c
+++ b/lib/sha256.c
@@ -11,7 +11,7 @@
 #endif /* USE_HOSTCC */
 #include <watchdog.h>
 #include <linux/string.h>
-#include <sha256.h>
+#include <u-boot/sha256.h>
 
 /*
  * 32-bit integer manipulation macros (big endian)
--- a/lib/tpm.c
+++ b/lib/tpm.c
@@ -7,7 +7,7 @@
 
 #include <common.h>
 #include <stdarg.h>
-#include <sha1.h>
+#include <u-boot/sha1.h>
 #include <tpm.h>
 #include <asm/unaligned.h>
 
--- a/tools/imls/imls.c
+++ b/tools/imls/imls.c
@@ -24,7 +24,7 @@
 #include <mtd/mtd-user.h>
 #endif
 
-#include <sha1.h>
+#include <u-boot/sha1.h>
 #include <libfdt.h>
 #include <fdt_support.h>
 #include <image.h>
--- a/tools/mkimage.h
+++ b/tools/mkimage.h
@@ -18,7 +18,7 @@
 #include <sys/stat.h>
 #include <time.h>
 #include <unistd.h>
-#include <sha1.h>
+#include <u-boot/sha1.h>
 #include "fdt_host.h"
 
 #undef MKIMAGE_DEBUG
--- a/tools/ubsha1.c
+++ b/tools/ubsha1.c
@@ -13,7 +13,7 @@
 #include <errno.h>
 #include <string.h>
 #include <sys/stat.h>
-#include "sha1.h"
+#include <u-boot/sha1.h>
 
 int main (int argc, char **argv)
 {
