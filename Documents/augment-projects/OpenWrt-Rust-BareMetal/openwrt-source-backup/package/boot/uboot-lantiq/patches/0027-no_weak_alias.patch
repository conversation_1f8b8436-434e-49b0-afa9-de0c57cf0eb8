From 3422299dc28fa8257677d03cc1253e3c9bf17e9f Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <<EMAIL>>
Date: Thu, 26 Jun 2014 20:18:31 +0200
Subject: [PATCH] common: main.c: make show_boot_progress __weak

This not only looks a bit better it also prevents a
warning with W=1 (no previous prototype).

Signed-off-by: <PERSON><PERSON><PERSON> <<EMAIL>>
Acked-by: <PERSON> <<EMAIL>>
---
 common/main.c | 3 +--
 1 file changed, 1 insertion(+), 2 deletions(-)

--- a/common/main.c
+++ b/common/main.c
@@ -27,8 +27,7 @@ DECLARE_GLOBAL_DATA_PTR;
 /*
  * Board-specific Platform code can reimplement show_boot_progress () if needed
  */
-void inline __show_boot_progress (int val) {}
-void show_boot_progress (int val) __attribute__((weak, alias("__show_boot_progress")));
+__weak void show_boot_progress(int val) {}
 
 #define MAX_DELAY_STOP_STR 32
 
