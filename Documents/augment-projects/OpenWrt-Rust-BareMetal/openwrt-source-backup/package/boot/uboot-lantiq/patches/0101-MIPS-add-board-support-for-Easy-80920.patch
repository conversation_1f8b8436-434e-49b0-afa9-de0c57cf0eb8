--- /dev/null
+++ b/board/lantiq/easy80920/Makefile
@@ -0,0 +1,27 @@
+#
+# Copyright (C) 2000-2011 <PERSON>, DENX Software Engineering, <EMAIL>
+#
+# SPDX-License-Identifier:	GPL-2.0+
+#
+
+include $(TOPDIR)/config.mk
+
+LIB	= $(obj)lib$(BOARD).o
+
+COBJS	= $(BOARD).o
+
+SRCS	:= $(SOBJS:.o=.S) $(COBJS:.o=.c)
+OBJS	:= $(addprefix $(obj),$(COBJS))
+SOBJS	:= $(addprefix $(obj),$(SOBJS))
+
+$(LIB):	$(obj).depend $(OBJS) $(SOBJS)
+	$(call cmd_link_o_target, $(OBJS) $(SOBJS))
+
+#########################################################################
+
+# defines $(obj).depend target
+include $(SRCTREE)/rules.mk
+
+sinclude $(obj).depend
+
+#########################################################################
--- /dev/null
+++ b/board/lantiq/easy80920/config.mk
@@ -0,0 +1,7 @@
+#
+# Copyright (C) 2011-2013 Daniel Schwierzeck, <EMAIL>
+#
+# SPDX-License-Identifier:	GPL-2.0+
+#
+
+PLATFORM_CPPFLAGS += -I$(TOPDIR)/board/$(BOARDDIR)
--- /dev/null
+++ b/board/lantiq/easy80920/ddr_settings.h
@@ -0,0 +1,69 @@
+/*
+ * Copyright (C) 2007-2010 Lantiq Deutschland GmbH
+ * Copyright (C) 2011-2013 Daniel Schwierzeck, <EMAIL>
+ *
+ * SPDX-License-Identifier:	GPL-2.0+
+ */
+
+#define	MC_CCR00_VALUE	0x101
+#define	MC_CCR01_VALUE	0x1000100
+#define	MC_CCR02_VALUE	0x1010000
+#define	MC_CCR03_VALUE	0x101
+#define	MC_CCR04_VALUE	0x1000000
+#define	MC_CCR05_VALUE	0x1000101
+#define	MC_CCR06_VALUE	0x1000100
+#define	MC_CCR07_VALUE	0x1010000
+#define	MC_CCR08_VALUE	0x1000101
+#define	MC_CCR09_VALUE	0x0
+#define	MC_CCR10_VALUE	0x2000100
+#define	MC_CCR11_VALUE	0x2000300
+#define	MC_CCR12_VALUE	0x30000
+#define	MC_CCR13_VALUE	0x202
+#define	MC_CCR14_VALUE	0x7080A0F
+#define	MC_CCR15_VALUE	0x2040F
+#define	MC_CCR16_VALUE	0x40000
+#define	MC_CCR17_VALUE	0x70102
+#define	MC_CCR18_VALUE	0x4020002
+#define	MC_CCR19_VALUE	0x30302
+#define	MC_CCR20_VALUE	0x8000700
+#define	MC_CCR21_VALUE	0x40F020A
+#define	MC_CCR22_VALUE	0x0
+#define	MC_CCR23_VALUE	0xC020000
+#define	MC_CCR24_VALUE	0x4401B04
+#define	MC_CCR25_VALUE	0x0
+#define	MC_CCR26_VALUE	0x0
+#define	MC_CCR27_VALUE	0x6420000
+#define	MC_CCR28_VALUE	0x0
+#define	MC_CCR29_VALUE	0x0
+#define	MC_CCR30_VALUE	0x798
+#define	MC_CCR31_VALUE	0x0
+#define	MC_CCR32_VALUE	0x0
+#define	MC_CCR33_VALUE	0x650000
+#define	MC_CCR34_VALUE	0x200C8
+#define	MC_CCR35_VALUE	0x1D445D
+#define	MC_CCR36_VALUE	0xC8
+#define	MC_CCR37_VALUE	0xC351
+#define	MC_CCR38_VALUE	0x0
+#define	MC_CCR39_VALUE	0x141F04
+#define	MC_CCR40_VALUE	0x142704
+#define	MC_CCR41_VALUE	0x141b42
+#define	MC_CCR42_VALUE	0x141b42
+#define	MC_CCR43_VALUE	0x566504
+#define	MC_CCR44_VALUE	0x566504
+#define	MC_CCR45_VALUE	0x565F17
+#define	MC_CCR46_VALUE	0x565F17
+#define	MC_CCR47_VALUE	0x0
+#define	MC_CCR48_VALUE	0x0
+#define	MC_CCR49_VALUE	0x0
+#define	MC_CCR50_VALUE	0x0
+#define	MC_CCR51_VALUE	0x0
+#define	MC_CCR52_VALUE	0x133
+#define	MC_CCR53_VALUE	0xF3014B27
+#define	MC_CCR54_VALUE	0xF3014B27
+#define	MC_CCR55_VALUE	0xF3014B27
+#define	MC_CCR56_VALUE	0xF3014B27
+#define	MC_CCR57_VALUE	0x7800301
+#define	MC_CCR58_VALUE	0x7800301
+#define	MC_CCR59_VALUE	0x7800301
+#define	MC_CCR60_VALUE	0x7800301
+#define	MC_CCR61_VALUE	0x4
--- /dev/null
+++ b/board/lantiq/easy80920/easy80920.c
@@ -0,0 +1,138 @@
+/*
+ * Copyright (C) 2011-2013 Daniel Schwierzeck, <EMAIL>
+ *
+ * SPDX-License-Identifier:	GPL-2.0+
+ */
+
+#include <common.h>
+#include <spi.h>
+#include <asm/gpio.h>
+#include <asm/lantiq/eth.h>
+#include <asm/lantiq/chipid.h>
+#include <asm/lantiq/cpu.h>
+#include <asm/arch/gphy.h>
+
+#if defined(CONFIG_SPL_BUILD)
+#define do_gpio_init	1
+#define do_pll_init	1
+#define do_dcdc_init	0
+#elif defined(CONFIG_SYS_BOOT_RAM)
+#define do_gpio_init	1
+#define do_pll_init	0
+#define do_dcdc_init	1
+#elif defined(CONFIG_SYS_BOOT_NOR)
+#define do_gpio_init	1
+#define do_pll_init	1
+#define do_dcdc_init	1
+#else
+#define do_gpio_init	0
+#define do_pll_init	0
+#define do_dcdc_init	1
+#endif
+
+static void gpio_init(void)
+{
+	/* SPI CS 0.4 to serial flash */
+	gpio_direction_output(10, 1);
+
+	/* EBU.FL_CS1 as output for NAND CE */
+	gpio_set_altfunc(23, GPIO_ALTSEL_SET, GPIO_ALTSEL_CLR, GPIO_DIR_OUT);
+	/* EBU.FL_A23 as output for NAND CLE */
+	gpio_set_altfunc(24, GPIO_ALTSEL_SET, GPIO_ALTSEL_CLR, GPIO_DIR_OUT);
+	/* EBU.FL_A24 as output for NAND ALE */
+	gpio_set_altfunc(13, GPIO_ALTSEL_SET, GPIO_ALTSEL_CLR, GPIO_DIR_OUT);
+	/* GPIO 3.0 as input for NAND Ready Busy */
+	gpio_set_altfunc(48, GPIO_ALTSEL_SET, GPIO_ALTSEL_CLR, GPIO_DIR_IN);
+	/* GPIO 3.1 as output for NAND Read */
+	gpio_set_altfunc(49, GPIO_ALTSEL_SET, GPIO_ALTSEL_CLR, GPIO_DIR_OUT);
+}
+
+int board_early_init_f(void)
+{
+	if (do_gpio_init)
+		gpio_init();
+
+	if (do_pll_init)
+		ltq_pll_init();
+
+	if (do_dcdc_init)
+		ltq_dcdc_init(0x7F);
+
+	return 0;
+}
+
+int checkboard(void)
+{
+	puts("Board: " CONFIG_BOARD_NAME "\n");
+	ltq_chip_print_info();
+
+	return 0;
+}
+
+static const struct ltq_eth_port_config eth_port_config[] = {
+	/* GMAC0: external Lantiq PEF7071 10/100/1000 PHY for LAN port 0 */
+	{ 0, 0x0, LTQ_ETH_PORT_PHY, PHY_INTERFACE_MODE_RGMII },
+	/* GMAC1: external Lantiq PEF7071 10/100/1000 PHY for LAN port 1 */
+	{ 1, 0x1, LTQ_ETH_PORT_PHY, PHY_INTERFACE_MODE_RGMII },
+	/* GMAC2: internal GPHY0 with 10/100/1000 firmware for LAN port 2 */
+	{ 2, 0x11, LTQ_ETH_PORT_PHY, PHY_INTERFACE_MODE_GMII },
+	/* GMAC3: unused */
+	{ 3, 0x0, LTQ_ETH_PORT_NONE, PHY_INTERFACE_MODE_NONE },
+	/* GMAC4: internal GPHY1 with 10/100/1000 firmware for LAN port 3 */
+	{ 4, 0x13, LTQ_ETH_PORT_PHY, PHY_INTERFACE_MODE_GMII },
+	/* GMAC5: external Lantiq PEF7071 10/100/1000 PHY for WANoE port */
+	{ 5, 0x5, LTQ_ETH_PORT_PHY, PHY_INTERFACE_MODE_RGMII },
+};
+
+static const struct ltq_eth_board_config eth_board_config = {
+	.ports = eth_port_config,
+	.num_ports = ARRAY_SIZE(eth_port_config),
+};
+
+int board_eth_init(bd_t * bis)
+{
+	const enum ltq_gphy_clk clk = LTQ_GPHY_CLK_25MHZ_PLL0;
+	const ulong fw_addr = 0x80FF0000;
+
+	ltq_gphy_phy11g_a1x_load(fw_addr);
+
+	ltq_cgu_gphy_clk_src(clk);
+
+	ltq_rcu_gphy_boot(0, fw_addr);
+	ltq_rcu_gphy_boot(1, fw_addr);
+
+	return ltq_eth_initialize(&eth_board_config);
+}
+
+int spi_cs_is_valid(unsigned int bus, unsigned int cs)
+{
+	if (bus)
+		return 0;
+
+	if (cs == 4)
+		return 1;
+
+	return 0;
+}
+
+void spi_cs_activate(struct spi_slave *slave)
+{
+	switch (slave->cs) {
+	case 4:
+		gpio_set_value(10, 0);
+		break;
+	default:
+		break;
+	}
+}
+
+void spi_cs_deactivate(struct spi_slave *slave)
+{
+	switch (slave->cs) {
+	case 4:
+		gpio_set_value(10, 1);
+		break;
+	default:
+		break;
+	}
+}
--- a/boards.cfg
+++ b/boards.cfg
@@ -509,6 +509,11 @@ Active  mips        mips32         incai
 Active  mips        mips32         incaip      -               incaip              incaip_100MHz                        incaip:CPU_CLOCK_RATE=100000000                                                                                                   Wolfgang Denk <<EMAIL>>
 Active  mips        mips32         incaip      -               incaip              incaip_133MHz                        incaip:CPU_CLOCK_RATE=133000000                                                                                                   Wolfgang Denk <<EMAIL>>
 Active  mips        mips32         incaip      -               incaip              incaip_150MHz                        incaip:CPU_CLOCK_RATE=150000000                                                                                                   Wolfgang Denk <<EMAIL>>
+Active  mips        mips32         vrx200      lantiq          easy80920           easy80920_nandspl                    easy80920:SYS_BOOT_NANDSPL                                                                                                        Daniel Schwierzeck <<EMAIL>>
+Active  mips        mips32         vrx200      lantiq          easy80920           easy80920_nor                        easy80920:SYS_BOOT_NOR                                                                                                            Daniel Schwierzeck <<EMAIL>>
+Active  mips        mips32         vrx200      lantiq          easy80920           easy80920_norspl                     easy80920:SYS_BOOT_NORSPL                                                                                                         Daniel Schwierzeck <<EMAIL>>
+Active  mips        mips32         vrx200      lantiq          easy80920           easy80920_ram                        easy80920:SYS_BOOT_RAM                                                                                                            Daniel Schwierzeck <<EMAIL>>
+Active  mips        mips32         vrx200      lantiq          easy80920           easy80920_sfspl                      easy80920:SYS_BOOT_SFSPL                                                                                                          Daniel Schwierzeck <<EMAIL>>
 Active  mips        mips64         -           -               qemu-mips           qemu_mips64                          qemu-mips64:SYS_BIG_ENDIAN                                                                                                        -
 Active  mips        mips64         -           -               qemu-mips           qemu_mips64el                        qemu-mips64:SYS_LITTLE_ENDIAN                                                                                                     -
 Active  nds32       n1213          ag101       AndesTech       adp-ag101           adp-ag101                            -                                                                                                                                 Andes <<EMAIL>>
--- /dev/null
+++ b/include/configs/easy80920.h
@@ -0,0 +1,109 @@
+/*
+ * Copyright (C) 2011-2013 Daniel Schwierzeck, <EMAIL>
+ *
+ * SPDX-License-Identifier:	GPL-2.0+
+ */
+
+#ifndef __CONFIG_H
+#define __CONFIG_H
+
+#define CONFIG_MACH_TYPE	"EASY80920"
+#define CONFIG_IDENT_STRING	" "CONFIG_MACH_TYPE
+#define CONFIG_BOARD_NAME	"Lantiq EASY80920 VRX200 Family Board"
+
+/* Configure SoC */
+#define CONFIG_LTQ_SUPPORT_UART		/* Enable ASC and UART */
+
+#define CONFIG_LTQ_SUPPORT_ETHERNET	/* Enable ethernet */
+
+#define CONFIG_LTQ_SUPPORT_NOR_FLASH	/* Have a parallel NOR flash */
+
+#define CONFIG_LTQ_SUPPORT_SPI_FLASH
+#define CONFIG_SPI_FLASH_MACRONIX	/* Have a MX29LV620 serial flash */
+
+#define CONFIG_LTQ_SUPPORT_NAND_FLASH
+
+#define CONFIG_LTQ_SUPPORT_SPL_SPI_FLASH	/* Build SPI flash SPL */
+#define CONFIG_SPL_SPI_BUS		0
+#define CONFIG_SPL_SPI_CS		4
+#define CONFIG_SPL_SPI_MAX_HZ		25000000
+#define CONFIG_SPL_SPI_MODE		0
+
+#define CONFIG_LTQ_SUPPORT_SPL_NOR_FLASH	/* Build NOR flash SPL */
+
+#define CONFIG_LTQ_SUPPORT_SPL_NAND_FLASH	/* Build NAND flash SPL */
+#define CONFIG_SYS_NAND_PAGE_COUNT	128
+#define CONFIG_SYS_NAND_PAGE_SIZE	2048
+#define CONFIG_SYS_NAND_OOBSIZE		64
+#define CONFIG_SYS_NAND_BLOCK_SIZE	(256 * 1024)
+#define CONFIG_SYS_NAND_BAD_BLOCK_POS	NAND_LARGE_BADBLOCK_POS
+#define CONFIG_SYS_NAND_U_BOOT_OFFS	0x4000
+
+#define CONFIG_LTQ_SPL_COMP_LZO
+#define CONFIG_LTQ_SPL_CONSOLE
+
+#define CONFIG_SYS_DRAM_PROBE
+
+/* Environment */
+#define CONFIG_ENV_SPI_BUS		CONFIG_SPL_SPI_BUS
+#define CONFIG_ENV_SPI_CS		CONFIG_SPL_SPI_CS
+#define CONFIG_ENV_SPI_MAX_HZ		CONFIG_SPL_SPI_MAX_HZ
+#define CONFIG_ENV_SPI_MODE		CONFIG_SPL_SPI_MODE
+
+#if defined(CONFIG_SYS_BOOT_NOR)
+#define CONFIG_ENV_IS_IN_FLASH
+#define CONFIG_ENV_OVERWRITE
+#define CONFIG_ENV_OFFSET		(384 * 1024)
+#define CONFIG_ENV_SECT_SIZE		(64 * 1024)
+#elif defined(CONFIG_SYS_BOOT_NORSPL)
+#define CONFIG_ENV_IS_IN_FLASH
+#define CONFIG_ENV_OVERWRITE
+#define CONFIG_ENV_OFFSET		(192 * 1024)
+#define CONFIG_ENV_SECT_SIZE		(64 * 1024)
+#elif defined(CONFIG_SYS_BOOT_SFSPL)
+#define CONFIG_ENV_IS_IN_SPI_FLASH
+#define CONFIG_ENV_OVERWRITE
+#define CONFIG_ENV_OFFSET		(192 * 1024)
+#define CONFIG_ENV_SECT_SIZE		(64 * 1024)
+#elif defined(CONFIG_SYS_BOOT_NANDSPL)
+#define CONFIG_ENV_IS_IN_NAND
+#define CONFIG_ENV_OVERWRITE
+#define CONFIG_ENV_OFFSET		(256 * 1024)
+#define CONFIG_ENV_SECT_SIZE		(256 * 1024)
+#else
+#define CONFIG_ENV_IS_NOWHERE
+#endif
+
+#define CONFIG_ENV_SIZE			(8 * 1024)
+
+#define CONFIG_LOADADDR			CONFIG_SYS_LOAD_ADDR
+
+/* Console */
+#define CONFIG_LTQ_ADVANCED_CONSOLE
+#define CONFIG_BAUDRATE			115200
+#define CONFIG_CONSOLE_ASC		1
+#define CONFIG_CONSOLE_DEV		"ttyLTQ1"
+
+/* Pull in default board configs for Lantiq XWAY VRX200 */
+#include <asm/lantiq/config.h>
+#include <asm/arch/config.h>
+
+/* Pull in default OpenWrt configs for Lantiq SoC */
+#include "openwrt-lantiq-common.h"
+
+#define CONFIG_ENV_UPDATE_UBOOT_NOR					\
+	"update-uboot-nor=run load-uboot-norspl-lzo write-uboot-nor\0"
+
+#define CONFIG_ENV_UPDATE_UBOOT_SF					\
+	"update-uboot-sf=run load-uboot-sfspl-lzo write-uboot-sf\0"
+
+#define CONFIG_ENV_UPDATE_UBOOT_NAND					\
+	"update-uboot-nand=run load-uboot-nandspl-lzo write-uboot-nand\0"
+
+#define CONFIG_EXTRA_ENV_SETTINGS	\
+	CONFIG_ENV_LANTIQ_DEFAULTS	\
+	CONFIG_ENV_UPDATE_UBOOT_NOR	\
+	CONFIG_ENV_UPDATE_UBOOT_SF	\
+	CONFIG_ENV_UPDATE_UBOOT_NAND
+
+#endif /* __CONFIG_H */
