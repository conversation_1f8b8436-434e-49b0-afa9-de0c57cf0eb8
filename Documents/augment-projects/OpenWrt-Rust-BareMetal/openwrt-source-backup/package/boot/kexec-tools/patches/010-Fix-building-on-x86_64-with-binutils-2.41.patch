From 328de8e00e298f00d7ba6b25dc3950147e9642e6 Mon Sep 17 00:00:00 2001
From: <PERSON> <salim<PERSON>@fedoraproject.org>
Date: Tue, 30 Jan 2024 04:14:31 -0600
Subject: [PATCH] Fix building on x86_64 with binutils 2.41

Newer versions of the GNU assembler (observed with binutils 2.41) will
complain about the ".arch i386" in files assembled with "as --64",
with the message "Error: 64bit mode not supported on 'i386'".

Fix by moving ".arch i386" below the relevant ".code32" directive, so
that the assembler is no longer expecting 64-bit instructions to be used
by the time that the ".arch i386" directive is encountered.

Based on similar iPXE fix:
https://github.com/ipxe/ipxe/commit/6ca597eee

Signed-off-by: <PERSON> <<EMAIL>>
Signed-off-by: <PERSON> <<EMAIL>>
---
 purgatory/arch/i386/entry32-16-debug.S | 2 +-
 purgatory/arch/i386/entry32-16.S       | 2 +-
 purgatory/arch/i386/entry32.S          | 2 +-
 purgatory/arch/i386/setup-x86.S        | 2 +-
 4 files changed, 4 insertions(+), 4 deletions(-)

--- a/purgatory/arch/i386/entry32-16-debug.S
+++ b/purgatory/arch/i386/entry32-16-debug.S
@@ -25,10 +25,10 @@
 	.globl entry16_debug_pre32
 	.globl entry16_debug_first32
 	.globl entry16_debug_old_first32
-	.arch i386
 	.balign 16
 entry16_debug:
 	.code32
+	.arch i386
 	/* Compute where I am running at (assumes esp valid) */
 	call	1f
 1:	popl	%ebx
--- a/purgatory/arch/i386/entry32-16.S
+++ b/purgatory/arch/i386/entry32-16.S
@@ -20,10 +20,10 @@
 #undef i386	
 	.text
 	.globl entry16, entry16_regs
-	.arch i386
 	.balign 16
 entry16:
 	.code32
+	.arch i386
 	/* Compute where I am running at (assumes esp valid) */
 	call	1f
 1:	popl	%ebx
--- a/purgatory/arch/i386/entry32.S
+++ b/purgatory/arch/i386/entry32.S
@@ -20,10 +20,10 @@
 #undef i386
 
 	.text
-	.arch	i386
 	.globl entry32, entry32_regs
 entry32:
 	.code32
+	.arch	i386
 
 	/* Setup a gdt that should that is generally usefully */
 	lgdt	%cs:gdt
--- a/purgatory/arch/i386/setup-x86.S
+++ b/purgatory/arch/i386/setup-x86.S
@@ -21,10 +21,10 @@
 #undef i386
 
 	.text
-	.arch	i386
 	.globl purgatory_start
 purgatory_start:
 	.code32
+	.arch	i386
 
 	/* Load a gdt so I know what the segment registers are */
 	lgdt	%cs:gdt
