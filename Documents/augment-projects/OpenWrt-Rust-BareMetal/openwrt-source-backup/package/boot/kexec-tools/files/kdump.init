#!/bin/sh /etc/rc.common

START=41
STOP=90

BOOT_IMAGE=/boot/vmlinuz

EXTRA_COMMANDS="status"
EXTRA_HELP="        status  Print crashkernel status"

verify_kdump() {
	local cfg="$1"
	local enabled
	local path
	local save_vmcore
	local save_dmesg

	config_get_bool enabled "$cfg" enabled 1
	config_get_bool save_dmesg "$cfg" save_dmesg 1
	config_get_bool save_vmcore "$cfg" save_vmcore 0

	[ "$enabled" -gt 0 ] || return 2

	[ "$save_dmesg" -gt 0 ] || [ "$save_vmcore" -gt 0 ] || return 2

	config_get path "$cfg" path "/"

	[ -d "$path" ] || mkdir -p "$path" 2>/dev/null || return 1
}

run_kdump() {
	local cfg="$1"
	local enabled
	local path
	local save_vmcore
	local save_dmesg

	config_get_bool enabled "$cfg" enabled 1
	[ "$enabled" -gt 0 ] || return

	config_get_bool save_dmesg "$cfg" save_dmesg 1
	config_get_bool save_vmcore "$cfg" save_vmcore 0
	config_get path "$cfg" path "/"

	timestamp=$(date "+%Y%m%dT%H%M%S")

	if [ "$save_vmcore" -eq 1 ]; then
		echo -n "Saving vmcore (this may take a while)..."
		# would like 'sparse' but busybox doesn't support it
		dd if=/proc/vmcore of="$path/vmcore-$timestamp" conv=fsync bs=1M
		echo " done"
	fi

	if [ "$save_dmesg" -eq 1 ]; then
		vmcore-dmesg /proc/vmcore > "$path/dmesg-$timestamp"
	fi

	sync
	reboot -f
}

find_kernel() {
	. /lib/functions.sh
	local kernel

	kernel="$BOOT_IMAGE"
	if [ -r "$kernel" ]; then
		echo $kernel
		return 0
	fi

	kernel="$(find_mtd_part kernel)"
	if [ -r "$kernel" ]; then
		echo $kernel
		return 0
	fi

	for voldir in /sys/class/ubi/ubi*_*; do
		[ ! -e "$voldir" ] && continue
		if [ "$(cat "${voldir}/name")" = "kernel" ]; then
			kernel="/dev/$(basename "$voldir")"
			echo $kernel
			return 0
		fi
	done

	return 1
}

load_crashkernel() {
	local append_cmdline
	local kernel

	kernel="$(find_kernel)"
	[ $? -gt 0 ] && return 1

	case "$(uname -m)" in
		i?86|x86_64)
			grep -q "crashkernel=" /proc/cmdline || return 1
			append_cmdline="1 irqpoll reset_devices maxcpus=1"
			;;
		arm*)
			append_cmdline="1 maxcpus=1 reset_devices"
			;;
	esac
	kexec -p "$kernel" --reuse-cmdline --append="$append_cmdline"
	return $?
}

start() {
	local retval

	if [ ! -e /sys/kernel/kexec_crash_loaded ]; then
		return 1
	fi

	if [ -e /proc/vmcore ]; then
		config_load kdump
		config_foreach run_kdump kdump
	else
		config_load kdump
		config_foreach verify_kdump kdump
		retval=$?
		[ $retval = 1 ] && return 1
		[ $retval = 0 ] && load_crashkernel
		return $?
	fi
}

stop() {
	[ "$(cat /sys/kernel/kexec_crash_loaded)" = "1" ] || return

	if [ -e "$BOOT_IMAGE" ]; then
		kexec -p -u "$BOOT_IMAGE"
	fi
}

status() {
	local retval kernel

	if [ ! -e /sys/kernel/kexec_crash_loaded ]; then
		echo "crashdump not supported by kernel"
		return
	fi

	if [ $(cat /sys/kernel/kexec_crash_size) -eq 0 ]; then
		echo "memory for crashdump kernel not reserved!"
		echo "check crashkernel= kernel cmdline parameter"
		echo "(a reboot is required after installing kdump)"
		return
	fi

	kernel="$(find_kernel)"
	if [ $? -gt 0 ]; then
		echo "cannot find kernel image"
		return
	else
		echo "using kernel image $kernel"
	fi

	echo -n "kdump configuration is "
	config_load kdump
	retval=$?
	if [ $retval = 0 ]; then
		if [ "$(config_foreach echo kdump)" ]; then
			config_foreach verify_kdump kdump
			retval=$?
		else
			retval=1
		fi
	fi

	if [ $retval = 0 ]; then
		echo "valid"
	elif [ $retval = 2 ]; then
		echo "disabled"
	else
		echo "BROKEN"
	fi

	echo -n "kexec crash kernel "
	if [ "$(cat /sys/kernel/kexec_crash_loaded)" = "0" ]; then
		echo -n "not "
	fi
	echo "loaded"
}
