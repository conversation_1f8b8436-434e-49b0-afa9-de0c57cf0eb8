From 8fde85b609273f8389178d4c0d066390a0e0773d Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sun, 30 Oct 2022 14:56:10 -0500
Subject: [PATCH 21/90] serial: ns16550: Enable clocks during probe

If the UART bus or baud clock has a gate, it must be enabled before the
UART can be used.

Signed-off-by: <PERSON> <<EMAIL>>
---
 drivers/serial/ns16550.c | 5 +++++
 1 file changed, 5 insertions(+)

--- a/drivers/serial/ns16550.c
+++ b/drivers/serial/ns16550.c
@@ -506,6 +506,7 @@ int ns16550_serial_probe(struct udevice
 	struct ns16550_plat *plat = dev_get_plat(dev);
 	struct ns16550 *const com_port = dev_get_priv(dev);
 	struct reset_ctl_bulk reset_bulk;
+	struct clk_bulk clk_bulk;
 	fdt_addr_t addr;
 	int ret;
 
@@ -524,6 +525,10 @@ int ns16550_serial_probe(struct udevice
 	if (!ret)
 		reset_deassert_bulk(&reset_bulk);
 
+	ret = clk_get_bulk(dev, &clk_bulk);
+	if (!ret)
+		clk_enable_bulk(&clk_bulk);
+
 	com_port->plat = dev_get_plat(dev);
 	ns16550_init(com_port, -1);
 
