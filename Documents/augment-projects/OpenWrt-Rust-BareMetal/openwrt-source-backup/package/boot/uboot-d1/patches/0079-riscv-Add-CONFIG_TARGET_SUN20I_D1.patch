From 6b0c83a5c7b9189fb1c5cf56145ec4882d9e5588 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON>PA<PERSON> <<EMAIL>>
Date: Tue, 6 Jun 2023 18:13:34 +0000
Subject: [PATCH 79/90] riscv: Add CONFIG_TARGET_SUN20I_D1

Signed-off-by: <PERSON> <<EMAIL>>
Signed-off-by: <PERSON><PERSON><PERSON> <<EMAIL>>
---
 arch/riscv/Kconfig        |  5 +++++
 board/sunxi/Kconfig       | 30 +++++++++++++++++++++++++++---
 common/spl/Kconfig        |  1 +
 drivers/clk/sunxi/Kconfig |  1 +
 include/configs/sun20i.h  | 11 +++++++++++
 5 files changed, 45 insertions(+), 3 deletions(-)
 create mode 100644 include/configs/sun20i.h

--- a/arch/riscv/Kconfig
+++ b/arch/riscv/Kconfig
@@ -31,6 +31,11 @@ config TARGET_SIPEED_MAIX
 	bool "Support Sipeed Maix Board"
 	select SYS_CACHE_SHIFT_6
 
+config TARGET_SUN20I_D1
+	bool "Support Allwinner D1 Boards"
+	select BOARD_SUNXI
+	select SYS_CACHE_SHIFT_6
+
 endchoice
 
 config SYS_ICACHE_OFF
--- a/board/sunxi/Kconfig
+++ b/board/sunxi/Kconfig
@@ -13,8 +13,18 @@ config BOARD_SUNXI
 	select DM_SERIAL if SERIAL
 	select DM_SPI if SPI
 	select DM_SPI_FLASH if SPI
+	select GENERIC_RISCV if RISCV
 	select OF_BOARD_SETUP
 	select PINCTRL
+	select RAM if SPL_DM
+	select SPL_CLK if SPL_DM
+	select SPL_DM if RISCV && SPL
+	select SPL_DM_SPI if SPL_DM && SPL_SPI
+	select SPL_DM_SPI_FLASH if SPL_DM && SPL_SPI
+	select SPL_OF_CONTROL if SPL_DM
+	select SPL_PINCTRL if SPL_DM
+	select SPL_PINCONF if SPL_DM
+	select SPL_RAM if SPL_DM
 	select SPL_SEPARATE_BSS if SPL
 	select SUPPORT_SPL
 	select SYS_RELOC_GD_ENV_ADDR
@@ -28,12 +38,14 @@ config BOARD_SUNXI
 	imply DISTRO_DEFAULTS
 	imply FAT_WRITE
 	imply FIT
+	imply MMC
 	imply OF_LIBFDT_OVERLAY
 	imply PRE_CONSOLE_BUFFER
 	imply SPL
 	imply SPL_GPIO
 	imply SPL_LIBCOMMON_SUPPORT
 	imply SPL_LIBGENERIC_SUPPORT
+	imply SPL_LOAD_FIT
 	imply SPL_MMC if MMC
 	imply SPL_POWER
 	imply SPL_SERIAL
@@ -41,6 +53,7 @@ config BOARD_SUNXI
 	imply SYS_I2C_MVTWSI
 	imply SYS_NS16550
 	imply SYSRESET
+	imply SYSRESET_SBI
 	imply SYSRESET_WATCHDOG
 	imply SYSRESET_WATCHDOG_AUTO
 	imply USB_EHCI_GENERIC
@@ -67,6 +80,12 @@ config SPL_BSS_START_ADDR
 	default 0x4ff80000 if SUNXI_MINIMUM_DRAM_MB >= 256
 	default 0x43f80000 if SUNXI_MINIMUM_DRAM_MB >= 64
 
+config SPL_OPENSBI_LOAD_ADDR
+	default 0x40000000 if RISCV
+
+config SPL_STACK
+	default 0x48000 if TARGET_SUN20I_D1
+
 config SPL_STACK_R_ADDR
 	default 0x81e00000 if MACH_SUNIV
 	default 0x2fe00000 if MACH_SUN9I
@@ -75,13 +94,13 @@ config SPL_STACK_R_ADDR
 
 config SPL_TEXT_BASE
 	default 0x10060 if MACH_SUN9I || MACH_SUN50I || MACH_SUN50I_H5
-	default 0x20060 if SUN50I_GEN_H6
+	default 0x20060 if SUN50I_GEN_H6 || TARGET_SUN20I_D1
 	default 0x00060
 
 config SUNXI_MINIMUM_DRAM_MB
 	int
 	default 32 if MACH_SUNIV
-	default 64 if MACH_SUN8I_V3S
+	default 64 if MACH_SUN8I_V3S || TARGET_SUN20I_D1
 	default 256
 	help
 	  Minimum DRAM size expected on the board. Traditionally we
@@ -94,7 +113,7 @@ config SUNXI_MINIMUM_DRAM_MB
 config SUNXI_SRAM_ADDRESS
 	hex
 	default 0x10000 if MACH_SUN9I || MACH_SUN50I || MACH_SUN50I_H5
-	default 0x20000 if SUN50I_GEN_H6
+	default 0x20000 if SUN50I_GEN_H6 || TARGET_SUN20I_D1
 	default 0x0
 	help
 	  Older Allwinner SoCs have their boot mask ROM mapped just
@@ -113,6 +132,7 @@ config SYS_CLK_FREQ
 	default  912000000 if MACH_SUN7I
 	default 1008000000 if MACH_SUN8I
 	default 1008000000 if MACH_SUN9I
+	default 1008000000 if TARGET_SUN20I_D1
 	default  816000000 if MACH_SUN50I || MACH_SUN50I_H5
 	default  888000000 if MACH_SUN50I_H6
 	default 1008000000 if MACH_SUN50I_H616
@@ -125,10 +145,14 @@ config SYS_CONFIG_NAME
 	default "sun7i"  if MACH_SUN7I
 	default "sun8i"  if MACH_SUN8I
 	default "sun9i"  if MACH_SUN9I
+	default "sun20i" if TARGET_SUN20I_D1
 	default "sun50i" if MACH_SUN50I
 	default "sun50i" if MACH_SUN50I_H6
 	default "sun50i" if MACH_SUN50I_H616
 
+config SYS_CPU
+	default "generic" if TARGET_SUN20I_D1
+
 config SYS_LOAD_ADDR
 	default 0x81000000 if MACH_SUNIV
 	default 0x22000000 if MACH_SUN9I
--- a/common/spl/Kconfig
+++ b/common/spl/Kconfig
@@ -78,6 +78,7 @@ config SPL_MAX_SIZE
 	hex "Maximum size of the SPL image, excluding BSS"
 	default 0x37fa0 if MACH_SUN50I_H616
 	default 0x30000 if ARCH_MX6 && MX6_OCRAM_256KB
+	default 0x27fa0 if TARGET_SUN20I_D1
 	default 0x25fa0 if MACH_SUN50I_H6
 	default 0x1b000 if AM33XX && !TI_SECURE_DEVICE
 	default 0x10000 if ARCH_MX6 && !MX6_OCRAM_256KB
--- a/drivers/clk/sunxi/Kconfig
+++ b/drivers/clk/sunxi/Kconfig
@@ -98,6 +98,7 @@ config CLK_SUN8I_H3
 
 config CLK_SUN20I_D1
 	bool "Clock driver for Allwinner D1"
+	default TARGET_SUN20I_D1
 	help
 	  This enables common clock driver support for platforms based
 	  on Allwinner D1 SoC.
--- /dev/null
+++ b/include/configs/sun20i.h
@@ -0,0 +1,11 @@
+/* SPDX-License-Identifier: GPL-2.0+ */
+/*
+ * Placeholder wrapper to allow addressing Allwinner D1 (and later) sun20i
+ * CPU based devices separately. Please do not add anything in here.
+ */
+#ifndef __CONFIG_H
+#define __CONFIG_H
+
+#include <configs/sunxi-common.h>
+
+#endif /* __CONFIG_H */
