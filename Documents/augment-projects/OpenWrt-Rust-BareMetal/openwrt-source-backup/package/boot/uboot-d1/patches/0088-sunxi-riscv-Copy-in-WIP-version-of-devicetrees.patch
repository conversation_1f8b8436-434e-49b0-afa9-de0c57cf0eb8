From 197d4096c697bcde8f9833b1d04b17eb2b232b85 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Mon, 31 Oct 2022 22:59:00 -0500
Subject: [PATCH 88/90] sunxi: riscv: Copy in WIP version of devicetrees

While the bindings still are not stable, this should help things work
out of the box.

Signed-off-by: <PERSON> <<EMAIL>>
---
 .../riscv/dts/sun20i-d1-clockworkpi-v3.14.dts | 134 +++++++-
 .../dts/sun20i-d1-common-regulators.dtsi      |  13 +
 arch/riscv/dts/sun20i-d1-devterm-v3.14.dts    |  16 +
 .../dts/sun20i-d1-dongshan-nezha-stu.dts      |  48 ++-
 .../dts/sun20i-d1-lichee-rv-86-panel-480p.dts |  51 +++
 .../dts/sun20i-d1-lichee-rv-86-panel.dtsi     |  64 ++++
 arch/riscv/dts/sun20i-d1-lichee-rv-dock.dts   |  98 ++++++
 arch/riscv/dts/sun20i-d1-lichee-rv.dts        |   6 +
 arch/riscv/dts/sun20i-d1-mangopi-mq-pro.dts   |  41 +++
 arch/riscv/dts/sun20i-d1-nezha.dts            | 117 ++++++-
 arch/riscv/dts/sun20i-d1.dtsi                 | 314 +++++++++++++++++-
 11 files changed, 881 insertions(+), 21 deletions(-)

--- a/arch/riscv/dts/sun20i-d1-clockworkpi-v3.14.dts
+++ b/arch/riscv/dts/sun20i-d1-clockworkpi-v3.14.dts
@@ -22,16 +22,78 @@
 		stdout-path = "serial0:115200n8";
 	};
 
+	audio_amplifier: audio-amplifier {
+		compatible = "simple-audio-amplifier";
+		enable-gpios = <&pio 4 1 GPIO_ACTIVE_HIGH>; /* PE1/GPIO11 */
+		sound-name-prefix = "Amplifier";
+		VCC-supply = <&reg_vcc>;
+	};
+
+	/*
+	 * FIXME: This is not really an amplifier, but the amplifier binding
+	 * has the needed properties and behavior.
+	 */
+	audio_switch: audio-switch {
+		compatible = "simple-audio-amplifier";
+		enable-gpios = <&pio 1 2 GPIO_ACTIVE_HIGH>; /* PB2/AUD_SWITCH */
+		sound-name-prefix = "Switch";
+		VCC-supply = <&reg_aldo1>;
+	};
+
+	backlight: backlight {
+		compatible = "pwm-backlight";
+		power-supply = <&reg_vcc>;
+		pwms = <&pwm 4 50000 0>; /* PD20/GPIO9 */
+	};
+
+	bt_sco_codec: bt-sco-codec {
+		#sound-dai-cells = <0>;
+		compatible = "linux,bt-sco";
+	};
+
+	bt-sound {
+		compatible = "simple-audio-card";
+		simple-audio-card,name = "Bluetooth";
+		#address-cells = <1>;
+		#size-cells = <0>;
+
+		simple-audio-card,dai-link@0 {
+			format = "dsp_a";
+			frame-master = <&bt_sound_cpu>;
+			bitclock-master = <&bt_sound_cpu>;
+
+			bt_sound_cpu: cpu {
+				sound-dai = <&i2s1>;
+			};
+
+			codec {
+				sound-dai = <&bt_sco_codec>;
+			};
+		};
+	};
+
+	hdmi_connector: connector {
+		compatible = "hdmi-connector";
+		type = "d";
+
+		port {
+			hdmi_connector_in: endpoint {
+				remote-endpoint = <&hdmi_out_connector>;
+			};
+		};
+	};
+
 	/*
 	 * This regulator is PWM-controlled, but the PWM controller is not
 	 * yet supported, so fix the regulator to its default voltage.
 	 */
 	reg_vdd_cpu: vdd-cpu {
-		compatible = "regulator-fixed";
+		compatible = "pwm-regulator";
+		pwms = <&pwm 0 50000 0>;
+		pwm-supply = <&reg_vcc>;
 		regulator-name = "vdd-cpu";
-		regulator-min-microvolt = <1100000>;
-		regulator-max-microvolt = <1100000>;
-		vin-supply = <&reg_vcc>;
+		regulator-min-microvolt = <810000>;
+		regulator-max-microvolt = <1160000>;
 	};
 
 	wifi_pwrseq: wifi-pwrseq {
@@ -40,14 +102,51 @@
 	};
 };
 
+&codec {
+	aux-devs = <&audio_amplifier>, <&audio_switch>;
+	hp-det-gpio = <&pio 1 12 GPIO_ACTIVE_HIGH>; /* PB12/GPIO10 */
+	pin-switches = "Internal Speakers";
+	routing = "Internal Speakers", "Amplifier OUTL",
+		  "Internal Speakers", "Amplifier OUTR",
+		  "Amplifier INL", "Switch OUTL",
+		  "Amplifier INR", "Switch OUTR",
+		  "Headphone Jack", "Switch OUTL",
+		  "Headphone Jack", "Switch OUTR",
+		  "Switch INL", "HPOUTL",
+		  "Switch INR", "HPOUTR",
+		  "MICIN3", "Headset Microphone",
+		  "Headset Microphone", "HBIAS";
+	widgets = "Microphone", "Headset Microphone",
+		  "Headphone", "Headphone Jack",
+		  "Speaker", "Internal Speakers";
+};
+
 &cpu0 {
 	cpu-supply = <&reg_vdd_cpu>;
 };
 
+&de {
+	status = "okay";
+};
+
 &ehci1 {
 	status = "okay";
 };
 
+&hdmi {
+	status = "okay";
+};
+
+&hdmi_out {
+	hdmi_out_connector: endpoint {
+		remote-endpoint = <&hdmi_connector_in>;
+	};
+};
+
+&hdmi_phy {
+	status = "okay";
+};
+
 &i2c0 {
 	pinctrl-0 = <&i2c0_pb10_pins>;
 	pinctrl-names = "default";
@@ -169,6 +268,12 @@
 	};
 };
 
+&i2s1 {
+	pinctrl-0 = <&i2s1_clk_pins>, <&i2s1_din_pin>, <&i2s1_dout_pin>;
+	pinctrl-names = "default";
+	status = "okay";
+};
+
 &mmc0 {
 	broken-cd;
 	bus-width = <4>;
@@ -205,6 +310,27 @@
 
 &pio {
 	vcc-pg-supply = <&reg_ldoa>;
+
+	i2s1_clk_pins: i2s1-clk-pins {
+		pins = "PG12", "PG13";
+		function = "i2s1";
+	};
+
+	i2s1_din_pin: i2s1-din-pin {
+		pins = "PG14";
+		function = "i2s1_din";
+	};
+
+	i2s1_dout_pin: i2s1-dout-pin {
+		pins = "PG15";
+		function = "i2s1_dout";
+	};
+};
+
+&pwm {
+	pinctrl-0 = <&pwm0_pd16_pin>, <&pwm4_pd20_pin>;
+	pinctrl-names = "default";
+	status = "okay";
 };
 
 &uart0 {
--- a/arch/riscv/dts/sun20i-d1-common-regulators.dtsi
+++ b/arch/riscv/dts/sun20i-d1-common-regulators.dtsi
@@ -18,6 +18,15 @@
 	};
 };
 
+&codec {
+	avcc-supply = <&reg_aldo>;
+	hpvcc-supply = <&reg_hpldo>;
+};
+
+&hdmi {
+	hvcc-supply = <&reg_ldoa>;
+};
+
 &lradc {
 	vref-supply = <&reg_aldo>;
 };
@@ -49,3 +58,7 @@
 	regulator-max-microvolt = <1800000>;
 	ldo-in-supply = <&reg_vcc_3v3>;
 };
+
+&ths {
+	vref-supply = <&reg_aldo>;
+};
--- a/arch/riscv/dts/sun20i-d1-devterm-v3.14.dts
+++ b/arch/riscv/dts/sun20i-d1-devterm-v3.14.dts
@@ -35,3 +35,19 @@
 		};
 	};
 };
+
+&dsi {
+	pinctrl-0 = <&dsi_4lane_pins>;
+	pinctrl-names = "default";
+	status = "okay";
+
+	panel@0 {
+		compatible = "clockwork,cwd686";
+		reg = <0>;
+		backlight = <&backlight>;
+		reset-gpios = <&pio 3 19 GPIO_ACTIVE_LOW>; /* PD19/GPIO8 */
+		rotation = <90>;
+		iovcc-supply = <&reg_dcdc3>;
+		vci-supply = <&reg_aldo2>;
+	};
+};
--- a/arch/riscv/dts/sun20i-d1-dongshan-nezha-stu.dts
+++ b/arch/riscv/dts/sun20i-d1-dongshan-nezha-stu.dts
@@ -23,6 +23,17 @@
 		stdout-path = "serial0:115200n8";
 	};
 
+	hdmi_connector: connector {
+		compatible = "hdmi-connector";
+		type = "a";
+
+		port {
+			hdmi_connector_in: endpoint {
+				remote-endpoint = <&hdmi_out_connector>;
+			};
+		};
+	};
+
 	leds {
 		compatible = "gpio-leds";
 
@@ -43,16 +54,13 @@
 		vin-supply = <&reg_vcc>;
 	};
 
-	/*
-	 * This regulator is PWM-controlled, but the PWM controller is not
-	 * yet supported, so fix the regulator to its default voltage.
-	 */
 	reg_vdd_cpu: vdd-cpu {
-		compatible = "regulator-fixed";
+		compatible = "pwm-regulator";
+		pwms = <&pwm 0 50000 0>;
+		pwm-supply = <&reg_vcc>;
 		regulator-name = "vdd-cpu";
-		regulator-min-microvolt = <1100000>;
-		regulator-max-microvolt = <1100000>;
-		vin-supply = <&reg_vcc>;
+		regulator-min-microvolt = <810000>;
+		regulator-max-microvolt = <1160000>;
 	};
 };
 
@@ -60,6 +68,10 @@
 	cpu-supply = <&reg_vdd_cpu>;
 };
 
+&de {
+	status = "okay";
+};
+
 &ehci0 {
 	status = "okay";
 };
@@ -73,6 +85,20 @@
 	status = "okay";
 };
 
+&hdmi {
+	status = "okay";
+};
+
+&hdmi_out {
+	hdmi_out_connector: endpoint {
+		remote-endpoint = <&hdmi_connector_in>;
+	};
+};
+
+&hdmi_phy {
+	status = "okay";
+};
+
 &mdio {
 	ext_rgmii_phy: ethernet-phy@1 {
 		compatible = "ethernet-phy-ieee802.3-c22";
@@ -95,6 +121,12 @@
 	status = "okay";
 };
 
+&pwm {
+	pinctrl-0 = <&pwm0_pd16_pin>;
+	pinctrl-names = "default";
+	status = "okay";
+};
+
 &uart0 {
 	pinctrl-0 = <&uart0_pb8_pins>;
 	pinctrl-names = "default";
--- a/arch/riscv/dts/sun20i-d1-lichee-rv-86-panel-480p.dts
+++ b/arch/riscv/dts/sun20i-d1-lichee-rv-86-panel-480p.dts
@@ -7,6 +7,40 @@
 	model = "Sipeed Lichee RV 86 Panel (480p)";
 	compatible = "sipeed,lichee-rv-86-panel-480p", "sipeed,lichee-rv",
 		     "allwinner,sun20i-d1";
+
+	backlight: backlight {
+		compatible = "pwm-backlight";
+		power-supply = <&reg_vcc>;
+		pwms = <&pwm 7 50000 0>;
+	};
+
+	spi {
+		compatible = "spi-gpio";
+		cs-gpios = <&pio 4 14 GPIO_ACTIVE_LOW>; /* PE14 */
+		mosi-gpios = <&pio 4 12 GPIO_ACTIVE_HIGH>; /* PE12 */
+		sck-gpios = <&pio 4 15 GPIO_ACTIVE_HIGH>; /* PE15 */
+		num-chipselects = <1>;
+		#address-cells = <1>;
+		#size-cells = <0>;
+
+		panel@0 {
+			compatible = "sitronix,st7701s";
+			reg = <0>;
+			backlight = <&backlight>;
+			reset-gpios = <&pio 6 13 GPIO_ACTIVE_LOW>; /* PG13 */
+			spi-3wire;
+
+			port {
+				panel_in_tcon_lcd0: endpoint {
+					remote-endpoint = <&tcon_lcd0_out_panel>;
+				};
+			};
+		};
+	};
+};
+
+&de {
+	status = "okay";
 };
 
 &i2c2 {
@@ -27,3 +61,20 @@
 		wakeup-source;
 	};
 };
+
+&pwm {
+	pinctrl-0 = <&pwm7_pd22_pin>;
+	pinctrl-names = "default";
+	status = "okay";
+};
+
+&tcon_lcd0 {
+	pinctrl-0 = <&lcd_rgb666_pins>;
+	pinctrl-names = "default";
+};
+
+&tcon_lcd0_out {
+	tcon_lcd0_out_panel: endpoint {
+		remote-endpoint = <&panel_in_tcon_lcd0>;
+	};
+};
--- a/arch/riscv/dts/sun20i-d1-lichee-rv-86-panel.dtsi
+++ b/arch/riscv/dts/sun20i-d1-lichee-rv-86-panel.dtsi
@@ -9,6 +9,39 @@
 		ethernet1 = &xr829;
 	};
 
+	audio_amplifier: audio-amplifier {
+		compatible = "simple-audio-amplifier";
+		enable-gpios = <&pio 1 10 GPIO_ACTIVE_HIGH>; /* PB10 */
+		sound-name-prefix = "Amplifier";
+	};
+
+	dmic_codec: dmic-codec {
+		compatible = "dmic-codec";
+		num-channels = <2>;
+		#sound-dai-cells = <0>;
+	};
+
+	dmic-sound {
+		compatible = "simple-audio-card";
+		simple-audio-card,name = "DMIC";
+		#address-cells = <1>;
+		#size-cells = <0>;
+
+		simple-audio-card,dai-link@0 {
+			format = "pdm";
+			frame-master = <&link0_cpu>;
+			bitclock-master = <&link0_cpu>;
+
+			link0_cpu: cpu {
+				sound-dai = <&dmic>;
+			};
+
+			link0_codec: codec {
+				sound-dai = <&dmic_codec>;
+			};
+		};
+	};
+
 	/* PC1 is repurposed as BT_WAKE_AP */
 	/delete-node/ leds;
 
@@ -24,6 +57,27 @@
 	};
 };
 
+&codec {
+	aux-devs = <&audio_amplifier>;
+	routing = "Internal Speaker", "Amplifier OUTL",
+		  "Internal Speaker", "Amplifier OUTR",
+		  "Amplifier INL", "HPOUTL",
+		  "Amplifier INR", "HPOUTR",
+		  "LINEINL", "HPOUTL",
+		  "LINEINR", "HPOUTR",
+		  "MICIN3", "Internal Microphone",
+		  "Internal Microphone", "HBIAS";
+	widgets = "Microphone", "Internal Microphone",
+		  "Speaker", "Internal Speaker";
+	status = "okay";
+};
+
+&dmic {
+	pinctrl-0 = <&dmic_pb11_d0_pin>, <&dmic_pe17_clk_pin>;
+	pinctrl-names = "default";
+	status = "okay";
+};
+
 &ehci1 {
 	status = "okay";
 };
@@ -69,6 +123,16 @@
 		pins = "PG11";
 		function = "clk";
 	};
+
+	dmic_pb11_d0_pin: dmic-pb11-d0-pin {
+		pins = "PB11";
+		function = "dmic";
+	};
+
+	dmic_pe17_clk_pin: dmic-pe17-clk-pin {
+		pins = "PE17";
+		function = "dmic";
+	};
 };
 
 &uart1 {
--- a/arch/riscv/dts/sun20i-d1-lichee-rv-dock.dts
+++ b/arch/riscv/dts/sun20i-d1-lichee-rv-dock.dts
@@ -15,16 +15,102 @@
 		ethernet1 = &rtl8723ds;
 	};
 
+	dmic_codec: dmic-codec {
+		compatible = "dmic-codec";
+		num-channels = <2>;
+		#sound-dai-cells = <0>;
+	};
+
+	dmic-sound {
+		compatible = "simple-audio-card";
+		simple-audio-card,name = "DMIC";
+		#address-cells = <1>;
+		#size-cells = <0>;
+
+		simple-audio-card,dai-link@0 {
+			format = "pdm";
+			frame-master = <&link0_cpu>;
+			bitclock-master = <&link0_cpu>;
+
+			link0_cpu: cpu {
+				sound-dai = <&dmic>;
+			};
+
+			link0_codec: codec {
+				sound-dai = <&dmic_codec>;
+			};
+		};
+	};
+
+	hdmi_connector: connector {
+		compatible = "hdmi-connector";
+		type = "a";
+
+		port {
+			hdmi_connector_in: endpoint {
+				remote-endpoint = <&hdmi_out_connector>;
+			};
+		};
+	};
+
 	wifi_pwrseq: wifi-pwrseq {
 		compatible = "mmc-pwrseq-simple";
 		reset-gpios = <&pio 6 12 GPIO_ACTIVE_LOW>; /* PG12 */
 	};
 };
 
+&codec {
+	routing = "Internal Speaker", "HPOUTL",
+		  "Internal Speaker", "HPOUTR",
+		  "LINEINL", "HPOUTL",
+		  "LINEINR", "HPOUTR",
+		  "MICIN3", "Internal Microphone",
+		  "Internal Microphone", "HBIAS";
+	widgets = "Microphone", "Internal Microphone",
+		  "Speaker", "Internal Speaker";
+	status = "okay";
+};
+
+&de {
+	status = "okay";
+};
+
+&dmic {
+	pinctrl-0 = <&dmic_pb11_d0_pin>, <&dmic_pe17_clk_pin>;
+	pinctrl-names = "default";
+	status = "okay";
+};
+
 &ehci1 {
 	status = "okay";
 };
 
+&hdmi {
+	status = "okay";
+};
+
+&hdmi_out {
+	hdmi_out_connector: endpoint {
+		remote-endpoint = <&hdmi_connector_in>;
+	};
+};
+
+&hdmi_phy {
+	status = "okay";
+};
+
+&ledc {
+	pinctrl-0 = <&ledc_pc0_pin>;
+	pinctrl-names = "default";
+	status = "okay";
+
+	multi-led@0 {
+		reg = <0x0>;
+		color = <LED_COLOR_ID_RGB>;
+		function = LED_FUNCTION_STATUS;
+	};
+};
+
 &lradc {
 	status = "okay";
 
@@ -55,6 +141,18 @@
 	status = "okay";
 };
 
+&pio {
+	dmic_pb11_d0_pin: dmic-pb11-d0-pin {
+		pins = "PB11";
+		function = "dmic";
+	};
+
+	dmic_pe17_clk_pin: dmic-pe17-clk-pin {
+		pins = "PE17";
+		function = "dmic";
+	};
+};
+
 &uart1 {
 	uart-has-rtscts;
 	pinctrl-0 = <&uart1_pg6_pins>, <&uart1_pg8_rts_cts_pins>;
--- a/arch/riscv/dts/sun20i-d1-lichee-rv.dts
+++ b/arch/riscv/dts/sun20i-d1-lichee-rv.dts
@@ -65,6 +65,12 @@
 	status = "okay";
 };
 
+&spi0 {
+	pinctrl-0 = <&spi0_pins>;
+	pinctrl-names = "default";
+	status = "okay";
+};
+
 &uart0 {
 	pinctrl-0 = <&uart0_pb8_pins>;
 	pinctrl-names = "default";
--- a/arch/riscv/dts/sun20i-d1-mangopi-mq-pro.dts
+++ b/arch/riscv/dts/sun20i-d1-mangopi-mq-pro.dts
@@ -4,6 +4,7 @@
 /dts-v1/;
 
 #include <dt-bindings/gpio/gpio.h>
+#include <dt-bindings/leds/common.h>
 
 #include "sun20i-d1.dtsi"
 #include "sun20i-d1-common-regulators.dtsi"
@@ -22,6 +23,28 @@
 		stdout-path = "serial0:115200n8";
 	};
 
+	hdmi_connector: connector {
+		compatible = "hdmi-connector";
+		type = "c";
+
+		port {
+			hdmi_connector_in: endpoint {
+				remote-endpoint = <&hdmi_out_connector>;
+			};
+		};
+	};
+
+	leds {
+		compatible = "pwm-leds";
+
+		led {
+			color = <LED_COLOR_ID_BLUE>;
+			function = LED_FUNCTION_STATUS;
+			max-brightness = <255>;
+			pwms = <&pwm 2 50000 0>;
+		};
+	};
+
 	reg_avdd2v8: avdd2v8 {
 		compatible = "regulator-fixed";
 		regulator-name = "avdd2v8";
@@ -56,10 +79,28 @@
 	cpu-supply = <&reg_vdd_cpu>;
 };
 
+&de {
+	status = "okay";
+};
+
 &ehci1 {
 	status = "okay";
 };
 
+&hdmi {
+	status = "okay";
+};
+
+&hdmi_out {
+	hdmi_out_connector: endpoint {
+		remote-endpoint = <&hdmi_connector_in>;
+	};
+};
+
+&hdmi_phy {
+	status = "okay";
+};
+
 &mmc0 {
 	bus-width = <4>;
 	cd-gpios = <&pio 5 6 GPIO_ACTIVE_HIGH>; /* PF6 */
--- a/arch/riscv/dts/sun20i-d1-nezha.dts
+++ b/arch/riscv/dts/sun20i-d1-nezha.dts
@@ -5,6 +5,7 @@
 
 #include <dt-bindings/gpio/gpio.h>
 #include <dt-bindings/input/input.h>
+#include <dt-bindings/leds/common.h>
 
 #include "sun20i-d1.dtsi"
 #include "sun20i-d1-common-regulators.dtsi"
@@ -18,12 +19,24 @@
 		ethernet1 = &xr829;
 		mmc0 = &mmc0;
 		serial0 = &uart0;
+		spi0 = &spi0;
 	};
 
 	chosen {
 		stdout-path = "serial0:115200n8";
 	};
 
+	hdmi_connector: connector {
+		compatible = "hdmi-connector";
+		type = "a";
+
+		port {
+			hdmi_connector_in: endpoint {
+				remote-endpoint = <&hdmi_out_connector>;
+			};
+		};
+	};
+
 	reg_usbvbus: usbvbus {
 		compatible = "regulator-fixed";
 		regulator-name = "usbvbus";
@@ -34,16 +47,13 @@
 		vin-supply = <&reg_vcc>;
 	};
 
-	/*
-	 * This regulator is PWM-controlled, but the PWM controller is not
-	 * yet supported, so fix the regulator to its default voltage.
-	 */
 	reg_vdd_cpu: vdd-cpu {
-		compatible = "regulator-fixed";
+		compatible = "pwm-regulator";
+		pwms = <&pwm 0 50000 0>;
+		pwm-supply = <&reg_vcc>;
 		regulator-name = "vdd-cpu";
-		regulator-min-microvolt = <1100000>;
-		regulator-max-microvolt = <1100000>;
-		vin-supply = <&reg_vcc>;
+		regulator-min-microvolt = <810000>;
+		regulator-max-microvolt = <1160000>;
 	};
 
 	wifi_pwrseq: wifi-pwrseq {
@@ -52,10 +62,26 @@
 	};
 };
 
+&codec {
+	routing = "Headphone Jack", "HPOUTL",
+		  "Headphone Jack", "HPOUTR",
+		  "LINEINL", "HPOUTL",
+		  "LINEINR", "HPOUTR",
+		  "MICIN3", "Headset Microphone",
+		  "Headset Microphone", "HBIAS";
+	widgets = "Microphone", "Headset Microphone",
+		  "Headphone", "Headphone Jack";
+	status = "okay";
+};
+
 &cpu0 {
 	cpu-supply = <&reg_vdd_cpu>;
 };
 
+&de {
+	status = "okay";
+};
+
 &ehci0 {
 	status = "okay";
 };
@@ -73,6 +99,20 @@
 	status = "okay";
 };
 
+&hdmi {
+	status = "okay";
+};
+
+&hdmi_out {
+	hdmi_out_connector: endpoint {
+		remote-endpoint = <&hdmi_connector_in>;
+	};
+};
+
+&hdmi_phy {
+	status = "okay";
+};
+
 &i2c2 {
 	pinctrl-0 = <&i2c2_pb0_pins>;
 	pinctrl-names = "default";
@@ -90,6 +130,18 @@
 	};
 };
 
+&ledc {
+	pinctrl-0 = <&ledc_pc0_pin>;
+	pinctrl-names = "default";
+	status = "okay";
+
+	multi-led@0 {
+		reg = <0x0>;
+		color = <LED_COLOR_ID_RGB>;
+		function = LED_FUNCTION_STATUS;
+	};
+};
+
 &lradc {
 	status = "okay";
 
@@ -142,6 +194,55 @@
 	status = "okay";
 };
 
+&pwm {
+	pinctrl-0 = <&pwm0_pd16_pin>;
+	pinctrl-names = "default";
+	status = "okay";
+};
+
+&spi0 {
+	pinctrl-0 = <&spi0_pins>;
+	pinctrl-names = "default";
+	status = "okay";
+
+	flash@0 {
+		compatible = "spi-nand";
+		reg = <0>;
+
+		partitions {
+			compatible = "fixed-partitions";
+			#address-cells = <1>;
+			#size-cells = <1>;
+
+			partition@0 {
+				label = "boot0";
+				reg = <0x00000000 0x00100000>;
+			};
+
+			partition@100000 {
+				label = "uboot";
+				reg = <0x00100000 0x00300000>;
+			};
+
+			partition@400000 {
+				label = "secure_storage";
+				reg = <0x00400000 0x00100000>;
+			};
+
+			partition@500000 {
+				label = "sys";
+				reg = <0x00500000 0x0fb00000>;
+			};
+		};
+	};
+};
+
+&spi1 {
+	pinctrl-0 = <&spi1_pd_pins>;
+	pinctrl-names = "default";
+	status = "okay";
+};
+
 &uart0 {
 	pinctrl-0 = <&uart0_pb8_pins>;
 	pinctrl-names = "default";
--- a/arch/riscv/dts/sun20i-d1.dtsi
+++ b/arch/riscv/dts/sun20i-d1.dtsi
@@ -59,6 +59,35 @@
 		#clock-cells = <0>;
 	};
 
+	thermal-zones {
+		cpu-thermal {
+			polling-delay = <0>;
+			polling-delay-passive = <0>;
+			thermal-sensors = <&ths>;
+
+			trips {
+				cpu_target: cpu-target {
+					hysteresis = <3000>;
+					temperature = <85000>;
+					type = "passive";
+				};
+
+				cpu-crit {
+					hysteresis = <0>;
+					temperature = <110000>;
+					type = "critical";
+				};
+			};
+
+			cooling-maps {
+				map0 {
+					trip = <&cpu_target>;
+					cooling-device = <&cpu0 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>;
+				};
+			};
+		};
+	};
+
 	soc {
 		compatible = "simple-bus";
 		ranges;
@@ -95,6 +124,14 @@
 			#interrupt-cells = <3>;
 
 			/omit-if-no-ref/
+			dsi_4lane_pins: dsi-4lane-pins {
+				pins = "PD0", "PD1", "PD2", "PD3", "PD4", "PD5",
+				       "PD6", "PD7", "PD8", "PD9";
+				drive-strength = <30>;
+				function = "dsi";
+			};
+
+			/omit-if-no-ref/
 			i2c0_pb10_pins: i2c0-pb10-pins {
 				pins = "PB10", "PB11";
 				function = "i2c0";
@@ -116,6 +153,12 @@
 			};
 
 			/omit-if-no-ref/
+			ledc_pc0_pin: ledc-pc0-pin {
+				pins = "PC0";
+				function = "ledc";
+			};
+
+			/omit-if-no-ref/
 			mmc0_pins: mmc0-pins {
 				pins = "PF0", "PF1", "PF2", "PF3", "PF4", "PF5";
 				function = "mmc0";
@@ -149,6 +192,48 @@
 			};
 
 			/omit-if-no-ref/
+			pwm0_pd16_pin: pwm0-pd16-pin {
+				pins = "PD16";
+				function = "pwm0";
+			};
+
+			/omit-if-no-ref/
+			pwm2_pd18_pin: pwm2-pd18-pin {
+				pins = "PD18";
+				function = "pwm2";
+			};
+
+			/omit-if-no-ref/
+			pwm4_pd20_pin: pwm4-pd20-pin {
+				pins = "PD20";
+				function = "pwm4";
+			};
+
+			/omit-if-no-ref/
+			pwm7_pd22_pin: pwm7-pd22-pin {
+				pins = "PD22";
+				function = "pwm7";
+			};
+
+			/omit-if-no-ref/
+			spi0_pins: spi0-pins {
+				pins = "PC2", "PC3", "PC4", "PC5", "PC6", "PC7";
+				function = "spi0";
+			};
+
+			/omit-if-no-ref/
+			spi1_pb_pins: spi1-pb-pins {
+				pins = "PB0", "PB8", "PB9", "PB10", "PB11", "PB12";
+				function = "spi1";
+			};
+
+			/omit-if-no-ref/
+			spi1_pd_pins: spi1-pd-pins {
+				pins = "PD10", "PD11", "PD12", "PD13", "PD14", "PD15";
+				function = "spi1";
+			};
+
+			/omit-if-no-ref/
 			uart0_pb8_pins: uart0-pb8-pins {
 				pins = "PB8", "PB9";
 				function = "uart0";
@@ -167,6 +252,17 @@
 			};
 		};
 
+		pwm: pwm@2000c00 {
+			compatible = "allwinner,sun20i-d1-pwm";
+			reg = <0x2000c00 0x400>;
+			interrupts = <34 IRQ_TYPE_LEVEL_HIGH>;
+			clocks = <&ccu CLK_BUS_PWM>, <&osc24M>;
+			clock-names = "bus", "mod";
+			resets = <&ccu RST_BUS_PWM>;
+			status = "disabled";
+			#pwm-cells = <3>;
+		};
+
 		ccu: clock-controller@2001000 {
 			compatible = "allwinner,sun20i-d1-ccu";
 			reg = <0x2001000 0x1000>;
@@ -178,6 +274,33 @@
 			#reset-cells = <1>;
 		};
 
+		ledc: led-controller@2008000 {
+			compatible = "allwinner,sun20i-d1-ledc",
+				     "allwinner,sun50i-a100-ledc";
+			reg = <0x2008000 0x400>;
+			interrupts = <36 IRQ_TYPE_LEVEL_HIGH>;
+			clocks = <&ccu CLK_BUS_LEDC>, <&ccu CLK_LEDC>;
+			clock-names = "bus", "mod";
+			resets = <&ccu RST_BUS_LEDC>;
+			dmas = <&dma 42>;
+			dma-names = "tx";
+			status = "disabled";
+			#address-cells = <1>;
+			#size-cells = <0>;
+		};
+
+		ths: temperature-sensor@2009400 {
+			compatible = "allwinner,sun20i-d1-ths";
+			reg = <0x2009400 0x400>;
+			interrupts = <74 IRQ_TYPE_LEVEL_HIGH>;
+			clocks = <&ccu CLK_BUS_THS>, <&osc24M>;
+			clock-names = "bus", "mod";
+			resets = <&ccu RST_BUS_THS>;
+			nvmem-cells = <&ths_calib>;
+			nvmem-cell-names = "calibration";
+			#thermal-sensor-cells = <0>;
+		};
+
 		lradc: keys@2009800 {
 			compatible = "allwinner,sun20i-d1-lradc",
 				     "allwinner,sun50i-r329-lradc";
@@ -188,11 +311,30 @@
 			status = "disabled";
 		};
 
+		iommu: iommu@2010000 {
+			compatible = "allwinner,sun20i-d1-iommu";
+			reg = <0x2010000 0x10000>;
+			interrupts = <80 IRQ_TYPE_LEVEL_HIGH>;
+			clocks = <&ccu CLK_BUS_IOMMU>;
+			#iommu-cells = <1>;
+		};
+
 		codec: audio-codec@2030000 {
-			compatible = "simple-mfd", "syscon";
+			compatible = "allwinner,sun20i-d1-codec", "simple-mfd", "syscon";
 			reg = <0x2030000 0x1000>;
+			interrupts = <41 IRQ_TYPE_LEVEL_HIGH>;
+			clocks = <&ccu CLK_BUS_AUDIO>,
+				 <&ccu CLK_AUDIO_ADC>,
+				 <&ccu CLK_AUDIO_DAC>,
+				 <&osc24M>,
+				 <&rtc CLK_OSC32K>;
+			clock-names = "bus", "adc", "dac", "hosc", "losc";
+			resets = <&ccu RST_BUS_AUDIO>;
+			dmas = <&dma 7>, <&dma 7>;
+			dma-names = "rx", "tx";
 			#address-cells = <1>;
 			#size-cells = <1>;
+			#sound-dai-cells = <0>;
 
 			regulators@2030348 {
 				compatible = "allwinner,sun20i-d1-analog-ldos";
@@ -208,6 +350,21 @@
 			};
 		};
 
+		dmic: dmic@2031000 {
+			compatible = "allwinner,sun20i-d1-dmic",
+				     "allwinner,sun50i-h6-dmic";
+			reg = <0x2031000 0x400>;
+			interrupts = <40 IRQ_TYPE_LEVEL_HIGH>;
+			clocks = <&ccu CLK_BUS_DMIC>,
+				 <&ccu CLK_DMIC>;
+			clock-names = "bus", "mod";
+			resets = <&ccu RST_BUS_DMIC>;
+			dmas = <&dma 8>;
+			dma-names = "rx";
+			status = "disabled";
+			#sound-dai-cells = <0>;
+		};
+
 		i2s0: i2s@2032000 {
 			compatible = "allwinner,sun20i-d1-i2s",
 				     "allwinner,sun50i-r329-i2s";
@@ -238,6 +395,7 @@
 			#sound-dai-cells = <0>;
 		};
 
+		// TODO: how to integrate ASRC? same or separate node?
 		i2s2: i2s@2034000 {
 			compatible = "allwinner,sun20i-d1-i2s",
 				     "allwinner,sun50i-r329-i2s";
@@ -253,6 +411,22 @@
 			#sound-dai-cells = <0>;
 		};
 
+		// TODO: add receive functionality
+		spdif: spdif@2036000 {
+			compatible = "allwinner,sun20i-d1-spdif";
+			reg = <0x2036000 0x400>;
+			interrupts = <39 IRQ_TYPE_LEVEL_HIGH>;
+			clocks = <&ccu CLK_BUS_SPDIF>,
+				 <&ccu CLK_SPDIF_RX>,
+				 <&ccu CLK_SPDIF_TX>;
+			clock-names = "apb", "rx", "tx";
+			resets = <&ccu RST_BUS_SPDIF>;
+			dmas = <&dma 2>, <&dma 2>;
+			dma-names = "rx", "tx";
+			status = "disabled";
+			#sound-dai-cells = <0>;
+		};
+
 		timer: timer@2050000 {
 			compatible = "allwinner,sun20i-d1-timer",
 				     "allwinner,sun8i-a23-timer";
@@ -457,6 +631,18 @@
 			};
 		};
 
+		crypto: crypto@3040000 {
+			compatible = "allwinner,sun20i-d1-crypto";
+			reg = <0x3040000 0x800>;
+			interrupts = <68 IRQ_TYPE_LEVEL_HIGH>;
+			clocks = <&ccu CLK_BUS_CE>,
+				 <&ccu CLK_CE>,
+				 <&ccu CLK_MBUS_CE>,
+				 <&rtc CLK_IOSC>;
+			clock-names = "bus", "mod", "ram", "trng";
+			resets = <&ccu RST_BUS_CE>;
+		};
+
 		mbus: dram-controller@3102000 {
 			compatible = "allwinner,sun20i-d1-mbus";
 			reg = <0x3102000 0x1000>,
@@ -525,6 +711,39 @@
 			#size-cells = <0>;
 		};
 
+		spi0: spi@4025000 {
+			compatible = "allwinner,sun20i-d1-spi",
+				     "allwinner,sun50i-r329-spi";
+			reg = <0x4025000 0x1000>;
+			interrupts = <31 IRQ_TYPE_LEVEL_HIGH>;
+			clocks = <&ccu CLK_BUS_SPI0>, <&ccu CLK_SPI0>;
+			clock-names = "ahb", "mod";
+			resets = <&ccu RST_BUS_SPI0>;
+			dmas = <&dma 22>, <&dma 22>;
+			dma-names = "rx", "tx";
+			num-cs = <1>;
+			status = "disabled";
+			#address-cells = <1>;
+			#size-cells = <0>;
+		};
+
+		spi1: spi@4026000 {
+			compatible = "allwinner,sun20i-d1-spi-dbi",
+				     "allwinner,sun50i-r329-spi-dbi",
+				     "allwinner,sun50i-r329-spi";
+			reg = <0x4026000 0x1000>;
+			interrupts = <32 IRQ_TYPE_LEVEL_HIGH>;
+			clocks = <&ccu CLK_BUS_SPI1>, <&ccu CLK_SPI1>;
+			clock-names = "ahb", "mod";
+			resets = <&ccu RST_BUS_SPI1>;
+			dmas = <&dma 23>, <&dma 23>;
+			dma-names = "rx", "tx";
+			num-cs = <1>;
+			status = "disabled";
+			#address-cells = <1>;
+			#size-cells = <0>;
+		};
+
 		usb_otg: usb@4100000 {
 			compatible = "allwinner,sun20i-d1-musb",
 				     "allwinner,sun8i-a33-musb";
@@ -653,6 +872,7 @@
 				 <&display_clocks CLK_MIXER0>;
 			clock-names = "bus", "mod";
 			resets = <&display_clocks RST_MIXER0>;
+			iommus = <&iommu 2>;
 
 			ports {
 				#address-cells = <1>;
@@ -675,6 +895,7 @@
 				 <&display_clocks CLK_MIXER1>;
 			clock-names = "bus", "mod";
 			resets = <&display_clocks RST_MIXER1>;
+			iommus = <&iommu 2>;
 
 			ports {
 				#address-cells = <1>;
@@ -690,6 +911,40 @@
 			};
 		};
 
+		dsi: dsi@5450000 {
+			compatible = "allwinner,sun20i-d1-mipi-dsi",
+				     "allwinner,sun50i-a100-mipi-dsi";
+			reg = <0x5450000 0x1000>;
+			interrupts = <108 IRQ_TYPE_LEVEL_HIGH>;
+			clocks = <&ccu CLK_BUS_MIPI_DSI>,
+				 <&tcon_top CLK_TCON_TOP_DSI>;
+			clock-names = "bus", "mod";
+			resets = <&ccu RST_BUS_MIPI_DSI>;
+			phys = <&dphy>;
+			phy-names = "dphy";
+			status = "disabled";
+			#address-cells = <1>;
+			#size-cells = <0>;
+
+			port {
+				dsi_in_tcon_lcd0: endpoint {
+					remote-endpoint = <&tcon_lcd0_out_dsi>;
+				};
+			};
+		};
+
+		dphy: phy@5451000 {
+			compatible = "allwinner,sun20i-d1-mipi-dphy",
+				     "allwinner,sun50i-a100-mipi-dphy";
+			reg = <0x5451000 0x1000>;
+			interrupts = <108 IRQ_TYPE_LEVEL_HIGH>;
+			clocks = <&ccu CLK_BUS_MIPI_DSI>,
+				 <&ccu CLK_MIPI_DSI>;
+			clock-names = "bus", "mod";
+			resets = <&ccu RST_BUS_MIPI_DSI>;
+			#phy-cells = <0>;
+		};
+
 		tcon_top: tcon-top@5460000 {
 			compatible = "allwinner,sun20i-d1-tcon-top";
 			reg = <0x5460000 0x1000>;
@@ -770,6 +1025,10 @@
 
 				tcon_top_hdmi_out: port@5 {
 					reg = <5>;
+
+					tcon_top_hdmi_out_hdmi: endpoint {
+						remote-endpoint = <&hdmi_in_tcon_top>;
+					};
 				};
 			};
 		};
@@ -785,6 +1044,8 @@
 			resets = <&ccu RST_BUS_TCON_LCD0>,
 				 <&ccu RST_BUS_LVDS0>;
 			reset-names = "lcd", "lvds";
+			phys = <&dphy>;
+			phy-names = "lvds0";
 			#clock-cells = <0>;
 
 			ports {
@@ -809,6 +1070,13 @@
 
 				tcon_lcd0_out: port@1 {
 					reg = <1>;
+					#address-cells = <1>;
+					#size-cells = <0>;
+
+					tcon_lcd0_out_dsi: endpoint@1 {
+						reg = <1>;
+						remote-endpoint = <&dsi_in_tcon_lcd0>;
+					};
 				};
 			};
 		};
@@ -853,6 +1121,50 @@
 			};
 		};
 
+		hdmi: hdmi@5500000 {
+			compatible = "allwinner,sun20i-d1-dw-hdmi";
+			reg = <0x5500000 0x10000>;
+			reg-io-width = <1>;
+			interrupts = <109 IRQ_TYPE_LEVEL_HIGH>;
+			clocks = <&ccu CLK_BUS_HDMI>,
+				 <&ccu CLK_HDMI_24M>,
+				 <&ccu CLK_HDMI_CEC>;
+			clock-names = "iahb", "isfr", "cec";
+			resets = <&ccu RST_BUS_HDMI_SUB>;
+			reset-names = "ctrl";
+			phys = <&hdmi_phy>;
+			phy-names = "phy";
+			status = "disabled";
+
+			ports {
+				#address-cells = <1>;
+				#size-cells = <0>;
+
+				port@0 {
+					reg = <0>;
+
+					hdmi_in_tcon_top: endpoint {
+						remote-endpoint = <&tcon_top_hdmi_out_hdmi>;
+					};
+				};
+
+				hdmi_out: port@1 {
+					reg = <1>;
+				};
+			};
+		};
+
+		hdmi_phy: phy@5510000 {
+			compatible = "allwinner,sun20i-d1-hdmi-phy";
+			reg = <0x5510000 0x10000>;
+			clocks = <&ccu CLK_BUS_HDMI>, <&ccu CLK_HDMI_24M>;
+			clock-names = "bus", "mod";
+			resets = <&ccu RST_BUS_HDMI_MAIN>;
+			reset-names = "phy";
+			status = "disabled";
+			#phy-cells = <0>;
+		};
+
 		riscv_wdt: watchdog@6011000 {
 			compatible = "allwinner,sun20i-d1-wdt";
 			reg = <0x6011000 0x20>;
