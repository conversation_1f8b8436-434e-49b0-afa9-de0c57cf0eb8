From 41e5a94533e9744b8eac718dd2c359eca57573f8 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 5 Aug 2022 23:25:26 -0500
Subject: [PATCH 65/90] sunxi: Clean up the SPL_STACK_R_ADDR defaults

Update this option to be based on SUNXI_MINIMUM_DRAM_MB. This corrects
the value used on V3s, which previously was the MACH_SUN8I default, and
so relied on addresses wrapping modulo the DRAM size.

Signed-off-by: <PERSON> <<EMAIL>>
---
 board/sunxi/Kconfig | 9 ++-------
 1 file changed, 2 insertions(+), 7 deletions(-)

--- a/board/sunxi/Kconfig
+++ b/board/sunxi/Kconfig
@@ -57,14 +57,9 @@ config IDENT_STRING
 
 config SPL_STACK_R_ADDR
 	default 0x81e00000 if MACH_SUNIV
-	default 0x4fe00000 if MACH_SUN4I
-	default 0x4fe00000 if MACH_SUN5I
-	default 0x4fe00000 if MACH_SUN6I
-	default 0x4fe00000 if MACH_SUN7I
-	default 0x4fe00000 if MACH_SUN8I
 	default 0x2fe00000 if MACH_SUN9I
-	default 0x4fe00000 if MACH_SUN50I
-	default 0x4fe00000 if SUN50I_GEN_H6
+	default 0x4fe00000 if SUNXI_MINIMUM_DRAM_MB >= 256
+	default 0x43e00000 if SUNXI_MINIMUM_DRAM_MB >= 64
 
 config SUNXI_MINIMUM_DRAM_MB
 	int
