From 8e793af8598a8429c9dc0f096c72a92adb360a57 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sun, 15 May 2022 21:51:47 -0500
Subject: [PATCH 29/90] mtd: nand: sunxi: Remove an unnecessary check

Each chip is required to have a unique CS number ("reg" property) in the
range 0-7, so there is no need to separately count the number of chips.

Signed-off-by: <PERSON> <<EMAIL>>
---
 drivers/mtd/nand/raw/sunxi_nand.c | 10 ----------
 1 <USER> <GROUP>, 10 deletions(-)

--- a/drivers/mtd/nand/raw/sunxi_nand.c
+++ b/drivers/mtd/nand/raw/sunxi_nand.c
@@ -1767,16 +1767,6 @@ static int sunxi_nand_chips_init(int nod
 	int ret, i = 0;
 
 	for (nand_node = fdt_first_subnode(blob, node); nand_node >= 0;
-	     nand_node = fdt_next_subnode(blob, nand_node))
-		i++;
-
-	if (i > 8) {
-		dev_err(nfc->dev, "too many NAND chips: %d (max = 8)\n", i);
-		return -EINVAL;
-	}
-
-	i = 0;
-	for (nand_node = fdt_first_subnode(blob, node); nand_node >= 0;
 	     nand_node = fdt_next_subnode(blob, nand_node)) {
 		ret = sunxi_nand_chip_init(nand_node, nfc, i++);
 		if (ret)
