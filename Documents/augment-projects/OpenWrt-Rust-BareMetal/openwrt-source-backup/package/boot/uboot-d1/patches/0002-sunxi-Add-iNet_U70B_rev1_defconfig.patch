From ddb1f06d1c7758c538e286c0c7a9c8545d2af6b1 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sat, 20 Nov 2021 13:26:36 -0600
Subject: [PATCH 02/90] sunxi: Add iNet_U70B_rev1_defconfig

Signed-off-by: <PERSON> <<EMAIL>>
---
 configs/iNet_U70B_rev1_defconfig | 32 ++++++++++++++++++++++++++++++++
 1 file changed, 32 insertions(+)
 create mode 100644 configs/iNet_U70B_rev1_defconfig

--- /dev/null
+++ b/configs/iNet_U70B_rev1_defconfig
@@ -0,0 +1,32 @@
+CONFIG_ARM=y
+CONFIG_ARCH_SUNXI=y
+CONFIG_DEFAULT_DEVICE_TREE="sun8i-a33-inet-u70b-rev1"
+# CONFIG_SPL_SERIAL is not set
+CONFIG_SPL=y
+CONFIG_MACH_SUN8I_A33=y
+CONFIG_DRAM_CLK=480
+CONFIG_DRAM_ZQ=31675
+CONFIG_DRAM_ODT_EN=y
+CONFIG_MMC0_CD_PIN="PB4"
+CONFIG_VIDEO_LCD_MODE="x:1024,y:600,depth:18,pclk_khz:51000,le:158,ri:162,up:25,lo:10,hs:20,vs:3,sync:3,vmode:0"
+CONFIG_VIDEO_LCD_DCLK_PHASE=0
+CONFIG_VIDEO_LCD_POWER="PH7"
+CONFIG_VIDEO_LCD_BL_EN="PH6"
+CONFIG_VIDEO_LCD_BL_PWM="PH0"
+# CONFIG_SYS_MALLOC_CLEAR_ON_INIT is not set
+CONFIG_CMD_BIND=y
+CONFIG_CMD_CLK=y
+CONFIG_CMD_PWM=y
+CONFIG_CMD_I2C=y
+CONFIG_CMD_WDT=y
+CONFIG_CMD_PMIC=y
+CONFIG_CMD_REGULATOR=y
+# CONFIG_NET is not set
+CONFIG_AXP_GPIO=y
+CONFIG_REGULATOR_AXP=y
+CONFIG_REGULATOR_AXP_USB_POWER=y
+CONFIG_AXP_DLDO1_VOLT=3300
+CONFIG_DM_PWM=y
+CONFIG_PWM_SUNXI=y
+# CONFIG_REQUIRE_SERIAL_CONSOLE is not set
+CONFIG_USB_MUSB_HOST=y
