From a588c97f146b67bae47099bc419cf10c02eca169 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Thu, 26 Aug 2021 21:34:33 -0500
Subject: [PATCH 08/90] power: pmic: axp: Probe the drivevbus regulator from
 the DT

Now that some regulator driver exists for this PMIC, add support for
probing regulator drivers from the device tree subnodes.

Signed-off-by: <PERSON> <<EMAIL>>
---
 drivers/power/pmic/axp.c | 10 ++++++++++
 1 file changed, 10 insertions(+)

--- a/drivers/power/pmic/axp.c
+++ b/drivers/power/pmic/axp.c
@@ -45,14 +45,24 @@ static struct dm_pmic_ops axp_pmic_ops =
 	.write		= dm_i2c_write,
 };
 
+static const struct pmic_child_info axp_pmic_child_info[] = {
+	{ "drivevbus",	"axp_drivevbus"	},
+	{ }
+};
+
 static int axp_pmic_bind(struct udevice *dev)
 {
+	ofnode regulators_node;
 	int ret;
 
 	ret = dm_scan_fdt_dev(dev);
 	if (ret)
 		return ret;
 
+	regulators_node = dev_read_subnode(dev, "regulators");
+	if (ofnode_valid(regulators_node))
+		pmic_bind_children(dev, regulators_node, axp_pmic_child_info);
+
 	if (CONFIG_IS_ENABLED(SYSRESET)) {
 		ret = device_bind_driver_to_node(dev, "axp_sysreset", "axp_sysreset",
 						 dev_ofnode(dev), NULL);
