From d45e64aad18e5e324425b9efbe6a0ec9e1a343da Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sat, 20 Nov 2021 13:19:13 -0600
Subject: [PATCH 01/90] ARM: dts: sun8i: A33: Add iNet U70B REV01

Signed-off-by: <PERSON> <<EMAIL>>
---
 arch/arm/dts/Makefile                     |   1 +
 arch/arm/dts/sun8i-a33-inet-u70b-rev1.dts | 172 ++++++++++++++++++++++
 2 files changed, 173 insertions(+)
 create mode 100644 arch/arm/dts/sun8i-a33-inet-u70b-rev1.dts

--- a/arch/arm/dts/Makefile
+++ b/arch/arm/dts/Makefile
@@ -644,6 +644,7 @@ dtb-$(CONFIG_MACH_SUN8I_A33) += \
 	sun8i-a33-et-q8-v1.6.dtb \
 	sun8i-a33-ga10h-v1.1.dtb \
 	sun8i-a33-inet-d978-rev2.dtb \
+	sun8i-a33-inet-u70b-rev1.dtb \
 	sun8i-a33-ippo-q8h-v1.2.dtb \
 	sun8i-a33-olinuxino.dtb \
 	sun8i-a33-q8-tablet.dtb \
--- /dev/null
+++ b/arch/arm/dts/sun8i-a33-inet-u70b-rev1.dts
@@ -0,0 +1,172 @@
+// SPDX-License-Identifier: (GPL-2.0+ or MIT)
+
+/dts-v1/;
+
+#include "sun8i-a33.dtsi"
+#include "sun8i-reference-design-tablet.dtsi"
+
+/ {
+	model = "iNet U70B REV01";
+	compatible = "inet-tek,inet-u70b-rev01", "allwinner,sun8i-a33";
+
+	aliases {
+		ethernet0 = &rtl8723cs;
+	};
+
+	panel: panel {
+		compatible = "panel-dpi";
+		backlight = <&backlight>;
+		enable-gpios = <&pio 7 7 GPIO_ACTIVE_HIGH>; /* PH7 */
+		power-supply = <&reg_dc1sw>;
+
+		panel-timing {
+			clock-frequency = <51000000>;
+			hactive = <1024>;
+			vactive = <600>;
+			hfront-porch = <162>;
+			hback-porch = <158>;
+			hsync-len = <20>;
+			vback-porch = <25>;
+			vfront-porch = <10>;
+			vsync-len = <3>;
+			hsync-active = <1>;
+			vsync-active = <1>;
+		};
+
+		port {
+			panel_in_tcon0: endpoint {
+				remote-endpoint = <&tcon0_out_panel>;
+			};
+		};
+	};
+
+	speaker_amp: audio-amplifier {
+		compatible = "simple-audio-amplifier";
+		enable-gpios = <&pio 7 9 GPIO_ACTIVE_HIGH>; /* PH9 */
+		sound-name-prefix = "Speaker Amp";
+	};
+
+	wifi_pwrseq: wifi-pwrseq {
+		compatible = "mmc-pwrseq-simple";
+		reset-gpios = <&r_pio 0 6 GPIO_ACTIVE_LOW>; /* PL6 */
+		post-power-on-delay-ms = <200>;
+	};
+};
+
+&codec {
+	status = "okay";
+};
+
+&dai {
+	status = "okay";
+};
+
+&de {
+	status = "okay";
+};
+
+&i2c1 {
+	clock-frequency = <400000>;
+
+	accelerometer@18 {
+		compatible = "bosch,bma250";
+		reg = <0x18>;
+		interrupt-parent = <&pio>;
+		interrupts = <7 10 IRQ_TYPE_EDGE_RISING>; /* PH10 / EINT10 */
+	};
+};
+
+&mmc1 {
+	pinctrl-0 = <&mmc1_pg_pins>;
+	pinctrl-names = "default";
+	bus-width = <4>;
+	non-removable;
+	vmmc-supply = <&reg_dldo1>;
+	vqmmc-supply = <&reg_dldo2>;
+	status = "okay";
+
+	rtl8723cs: wifi@1 {
+		reg = <1>;
+		interrupt-parent = <&r_pio>;
+		interrupts = <0 7 IRQ_TYPE_LEVEL_LOW>; /* PL7 */
+	};
+};
+
+&nfc {
+	status = "okay";
+
+	nand@0 {
+		reg = <0>;
+		allwinner,rb = <0>;
+		nand-ecc-maximize;
+	};
+};
+
+&r_uart {
+	status = "disabled";
+};
+
+&reg_dldo2 {
+	regulator-min-microvolt = <3300000>;
+	regulator-max-microvolt = <3300000>;
+	regulator-name = "vcc-wifi-io";
+};
+
+&simplefb_lcd {
+	status = "okay";
+};
+
+&sound {
+	simple-audio-card,aux-devs = <&codec_analog>, <&speaker_amp>;
+	simple-audio-card,widgets = "Headphone", "Headphone Jack",
+				    "Microphone", "Internal Microphone",
+				    "Speaker", "Internal Speaker";
+	simple-audio-card,routing = "Headphone Jack", "HP",
+				    "Internal Speaker", "Speaker Amp OUTL",
+				    "Internal Speaker", "Speaker Amp OUTR",
+				    "Speaker Amp INL", "HP", /* PHONEOUT ??? */
+				    "Speaker Amp INR", "HP", /* PHONEOUT ??? */
+				    "Left DAC", "DACL",
+				    "Right DAC", "DACR",
+				    "ADCL", "Left ADC",
+				    "ADCR", "Right ADC",
+				    "MIC1", "Internal Microphone",
+				    "MIC2", "Headset Microphone",
+				    "Headset Microphone", "HBIAS",
+				    "Internal Microphone", "MBIAS";
+	status = "okay";
+};
+
+&tcon0 {
+	pinctrl-0 = <&lcd_rgb666_pins>;
+	pinctrl-names = "default";
+	status = "okay";
+};
+
+&tcon0_out {
+	tcon0_out_panel: endpoint {
+		remote-endpoint = <&panel_in_tcon0>;
+	};
+};
+
+&touchscreen {
+	reg = <0x40>;
+	compatible = "silead,gsl1680";
+	avdd-supply = <&reg_ldo_io1>;
+	touchscreen-size-x = <1024>;
+	touchscreen-size-y = <600>;
+	status = "okay";
+};
+
+&uart1 {
+	pinctrl-0 = <&uart1_pg_pins>, <&uart1_cts_rts_pg_pins>;
+	pinctrl-names = "default";
+	status = "okay";
+
+	bluetooth {
+		compatible = "realtek,rtl8723cs-bt";
+		device-wake-gpios = <&r_pio 0 10 GPIO_ACTIVE_LOW>; /* PL10 */
+		enable-gpios = <&r_pio 0 8 GPIO_ACTIVE_HIGH>; /* PL8 */
+		host-wake-gpios = <&r_pio 0 9 GPIO_ACTIVE_HIGH>; /* PL9 */
+	};
+};
