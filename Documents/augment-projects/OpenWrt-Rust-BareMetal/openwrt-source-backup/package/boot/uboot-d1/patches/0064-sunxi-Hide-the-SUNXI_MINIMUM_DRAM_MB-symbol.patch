From 8377aa2162d0a7fda76597eae59725a298dda5e6 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Mon, 31 Oct 2022 22:14:36 -0500
Subject: [PATCH 64/90] sunxi: Hide the SUNXI_MINIMUM_DRAM_MB symbol

This option affects the ABI between SPL/U-Boot and U-Boot/scripts, so it
should not normally be changed by the user.

Signed-off-by: <PERSON> <<EMAIL>>
---
 board/sunxi/Kconfig | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

--- a/board/sunxi/Kconfig
+++ b/board/sunxi/Kconfig
@@ -67,7 +67,7 @@ config SPL_STACK_R_ADDR
 	default 0x4fe00000 if SUN50I_GEN_H6
 
 config SUNXI_MINIMUM_DRAM_MB
-	int "minimum DRAM size"
+	int
 	default 32 if MACH_SUNIV
 	default 64 if MACH_SUN8I_V3S
 	default 256
