From 98fb93ceb936b375d7f8f2908f0703a93e27fbc4 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sat, 6 Aug 2022 00:05:52 -0500
Subject: [PATCH 74/90] disk: sunxi: Replace ARCH_SUNXI with BOARD_SUNXI

This provides a unified configuration across all sunxi boards,
regardless of CPU architecture.

Signed-off-by: <PERSON> <<EMAIL>>
---
 disk/Kconfig | 6 +++---
 1 file changed, 3 insertions(+), 3 deletions(-)

--- a/disk/Kconfig
+++ b/disk/Kconfig
@@ -61,7 +61,7 @@ config SPL_DOS_PARTITION
 	bool "Enable MS Dos partition table for SPL"
 	depends on SPL
 	default n if ARCH_MVEBU
-	default n if ARCH_SUNXI
+	default n if BOARD_SUNXI
 	default y if DOS_PARTITION
 	select SPL_PARTITIONS
 
@@ -104,7 +104,7 @@ config EFI_PARTITION
 config EFI_PARTITION_ENTRIES_NUMBERS
 	int "Number of the EFI partition entries"
 	depends on EFI_PARTITION
-	default 56 if ARCH_SUNXI
+	default 56 if BOARD_SUNXI
 	default 128
 	help
 	  Specify the number of partition entries in the GPT. This is
@@ -132,7 +132,7 @@ config SPL_EFI_PARTITION
 	bool "Enable EFI GPT partition table for SPL"
 	depends on  SPL
 	default n if ARCH_MVEBU
-	default n if ARCH_SUNXI
+	default n if BOARD_SUNXI
 	default y if EFI_PARTITION
 	select SPL_PARTITIONS
 
