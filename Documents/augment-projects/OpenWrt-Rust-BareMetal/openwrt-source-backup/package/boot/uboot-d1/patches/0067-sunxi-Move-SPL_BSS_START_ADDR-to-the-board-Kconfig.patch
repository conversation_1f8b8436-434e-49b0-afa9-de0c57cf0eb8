From 71796f9d47a6b7e0dd6bb276436950463039c1b8 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Mon, 31 Oct 2022 00:08:26 -0500
Subject: [PATCH 67/90] sunxi: Move SPL_BSS_START_ADDR to the board Kconfig

This provides a default value for RISC-V when that is added, and it
makes sense to put this option next to the other DRAM layout options.

While at it, provide sensible values for platforms with less DRAM.

Signed-off-by: <PERSON> <<EMAIL>>
---
 board/sunxi/Kconfig | 6 ++++++
 common/spl/Kconfig  | 3 ---
 2 files changed, 6 insertions(+), 3 deletions(-)

--- a/board/sunxi/Kconfig
+++ b/board/sunxi/Kconfig
@@ -61,6 +61,12 @@ config PRE_CON_BUF_ADDR
 	default 0x4f000000 if SUNXI_MINIMUM_DRAM_MB >= 256
 	default 0x43000000 if SUNXI_MINIMUM_DRAM_MB >= 64
 
+config SPL_BSS_START_ADDR
+	default 0x81f80000 if MACH_SUNIV
+	default 0x2ff80000 if MACH_SUN9I
+	default 0x4ff80000 if SUNXI_MINIMUM_DRAM_MB >= 256
+	default 0x43f80000 if SUNXI_MINIMUM_DRAM_MB >= 64
+
 config SPL_STACK_R_ADDR
 	default 0x81e00000 if MACH_SUNIV
 	default 0x2fe00000 if MACH_SUN9I
--- a/common/spl/Kconfig
+++ b/common/spl/Kconfig
@@ -119,9 +119,6 @@ config SPL_BSS_START_ADDR
 	default 0x88200000 if (ARCH_MX6 && (MX6SX || MX6SL || MX6UL || MX6ULL)) || ARCH_MX7
 	default 0x18200000 if ARCH_MX6 && !(MX6SX || MX6SL || MX6UL || MX6ULL)
 	default 0x80a00000 if ARCH_OMAP2PLUS
-	default 0x81f80000 if ARCH_SUNXI && MACH_SUNIV
-	default 0x4ff80000 if ARCH_SUNXI && !(MACH_SUN9I || MACH_SUNIV)
-	default 0x2ff80000 if ARCH_SUNXI && MACH_SUN9I
 	default 0x1000 if ARCH_ZYNQMP
 
 choice
