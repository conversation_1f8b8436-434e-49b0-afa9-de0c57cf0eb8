From cbb281e0ec847b9de41970e470348b3534bb9a9f Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Thu, 26 Aug 2021 18:02:54 -0500
Subject: [PATCH 12/90] gpio: axp: Remove virtual VBUS enable GPIO

Now that this functionality is modeled using the device tree and
regulator uclass, the named GPIO is not referenced anywhere. Remove
it, along with the rest of the support for AXP virtual GPIOs.

Signed-off-by: <PERSON> <<EMAIL>>
---
 arch/arm/include/asm/arch-sunxi/gpio.h |  8 ---
 drivers/gpio/axp_gpio.c                | 75 ++++++++------------------
 drivers/gpio/sunxi_gpio.c              |  8 ---
 include/axp221.h                       |  4 --
 include/axp809.h                       |  4 --
 include/axp818.h                       |  4 --
 6 files changed, 21 insertions(+), 82 deletions(-)

--- a/arch/arm/include/asm/arch-sunxi/gpio.h
+++ b/arch/arm/include/asm/arch-sunxi/gpio.h
@@ -111,7 +111,6 @@ enum sunxi_gpio_number {
 	SUNXI_GPIO_L_START = 352,
 	SUNXI_GPIO_M_START = SUNXI_GPIO_NEXT(SUNXI_GPIO_L),
 	SUNXI_GPIO_N_START = SUNXI_GPIO_NEXT(SUNXI_GPIO_M),
-	SUNXI_GPIO_AXP0_START = 1024,
 };
 
 /* SUNXI GPIO number definitions */
@@ -128,8 +127,6 @@ enum sunxi_gpio_number {
 #define SUNXI_GPM(_nr)	(SUNXI_GPIO_M_START + (_nr))
 #define SUNXI_GPN(_nr)	(SUNXI_GPIO_N_START + (_nr))
 
-#define SUNXI_GPAXP0(_nr)	(SUNXI_GPIO_AXP0_START + (_nr))
-
 /* GPIO pin function config */
 #define SUNXI_GPIO_INPUT	0
 #define SUNXI_GPIO_OUTPUT	1
@@ -207,11 +204,6 @@ enum sunxi_gpio_number {
 #define SUNXI_GPIO_PULL_UP	1
 #define SUNXI_GPIO_PULL_DOWN	2
 
-/* Virtual AXP0 GPIOs */
-#define SUNXI_GPIO_AXP0_PREFIX "AXP0-"
-#define SUNXI_GPIO_AXP0_VBUS_ENABLE	5
-#define SUNXI_GPIO_AXP0_GPIO_COUNT	6
-
 struct sunxi_gpio_plat {
 	struct sunxi_gpio	*regs;
 	char			bank_name[3];
--- a/drivers/gpio/axp_gpio.c
+++ b/drivers/gpio/axp_gpio.c
@@ -15,6 +15,9 @@
 #include <dm/root.h>
 #include <errno.h>
 
+#define AXP_GPIO_PREFIX			"AXP0-"
+#define AXP_GPIO_COUNT			4
+
 static int axp_gpio_set_value(struct udevice *dev, unsigned pin, int val);
 
 static u8 axp_get_gpio_ctrl_reg(unsigned pin)
@@ -46,28 +49,14 @@ static int axp_gpio_direction_input(stru
 static int axp_gpio_direction_output(struct udevice *dev, unsigned pin,
 				     int val)
 {
-	__maybe_unused int ret;
 	u8 reg;
 
-	switch (pin) {
-#ifdef AXP_MISC_CTRL_N_VBUSEN_FUNC
-	/* Only available on later PMICs */
-	case SUNXI_GPIO_AXP0_VBUS_ENABLE:
-		ret = pmic_bus_clrbits(AXP_MISC_CTRL,
-				       AXP_MISC_CTRL_N_VBUSEN_FUNC);
-		if (ret)
-			return ret;
-
-		return axp_gpio_set_value(dev, pin, val);
-#endif
-	default:
-		reg = axp_get_gpio_ctrl_reg(pin);
-		if (reg == 0)
-			return -EINVAL;
+	reg = axp_get_gpio_ctrl_reg(pin);
+	if (reg == 0)
+		return -EINVAL;
 
-		return pmic_bus_write(reg, val ? AXP_GPIO_CTRL_OUTPUT_HIGH :
-						 AXP_GPIO_CTRL_OUTPUT_LOW);
-	}
+	return pmic_bus_write(reg, val ? AXP_GPIO_CTRL_OUTPUT_HIGH :
+					 AXP_GPIO_CTRL_OUTPUT_LOW);
 }
 
 static int axp_gpio_get_value(struct udevice *dev, unsigned pin)
@@ -75,25 +64,16 @@ static int axp_gpio_get_value(struct ude
 	u8 reg, val, mask;
 	int ret;
 
-	switch (pin) {
-#ifdef AXP_MISC_CTRL_N_VBUSEN_FUNC
-	/* Only available on later PMICs */
-	case SUNXI_GPIO_AXP0_VBUS_ENABLE:
-		ret = pmic_bus_read(AXP_VBUS_IPSOUT, &val);
-		mask = AXP_VBUS_IPSOUT_DRIVEBUS;
-		break;
-#endif
-	default:
-		reg = axp_get_gpio_ctrl_reg(pin);
-		if (reg == 0)
-			return -EINVAL;
+	reg = axp_get_gpio_ctrl_reg(pin);
+	if (reg == 0)
+		return -EINVAL;
 
-		ret = pmic_bus_read(AXP_GPIO_STATE, &val);
-		mask = 1 << (pin + AXP_GPIO_STATE_OFFSET);
-	}
+	ret = pmic_bus_read(AXP_GPIO_STATE, &val);
 	if (ret)
 		return ret;
 
+	mask = 1 << (pin + AXP_GPIO_STATE_OFFSET);
+
 	return (val & mask) ? 1 : 0;
 }
 
@@ -101,25 +81,12 @@ static int axp_gpio_set_value(struct ude
 {
 	u8 reg;
 
-	switch (pin) {
-#ifdef AXP_MISC_CTRL_N_VBUSEN_FUNC
-	/* Only available on later PMICs */
-	case SUNXI_GPIO_AXP0_VBUS_ENABLE:
-		if (val)
-			return pmic_bus_setbits(AXP_VBUS_IPSOUT,
-						AXP_VBUS_IPSOUT_DRIVEBUS);
-		else
-			return pmic_bus_clrbits(AXP_VBUS_IPSOUT,
-						AXP_VBUS_IPSOUT_DRIVEBUS);
-#endif
-	default:
-		reg = axp_get_gpio_ctrl_reg(pin);
-		if (reg == 0)
-			return -EINVAL;
+	reg = axp_get_gpio_ctrl_reg(pin);
+	if (reg == 0)
+		return -EINVAL;
 
-		return pmic_bus_write(reg, val ? AXP_GPIO_CTRL_OUTPUT_HIGH :
-						 AXP_GPIO_CTRL_OUTPUT_LOW);
-	}
+	return pmic_bus_write(reg, val ? AXP_GPIO_CTRL_OUTPUT_HIGH :
+					 AXP_GPIO_CTRL_OUTPUT_LOW);
 }
 
 static const struct dm_gpio_ops gpio_axp_ops = {
@@ -134,8 +101,8 @@ static int gpio_axp_probe(struct udevice
 	struct gpio_dev_priv *uc_priv = dev_get_uclass_priv(dev);
 
 	/* Tell the uclass how many GPIOs we have */
-	uc_priv->bank_name = strdup(SUNXI_GPIO_AXP0_PREFIX);
-	uc_priv->gpio_count = SUNXI_GPIO_AXP0_GPIO_COUNT;
+	uc_priv->bank_name = AXP_GPIO_PREFIX;
+	uc_priv->gpio_count = AXP_GPIO_COUNT;
 
 	return 0;
 }
--- a/drivers/gpio/sunxi_gpio.c
+++ b/drivers/gpio/sunxi_gpio.c
@@ -114,15 +114,7 @@ int sunxi_name_to_gpio(const char *name)
 {
 	unsigned int gpio;
 	int ret;
-#if !defined CONFIG_SPL_BUILD && defined CONFIG_AXP_GPIO
-	char lookup[8];
 
-	if (strcasecmp(name, "AXP0-VBUS-ENABLE") == 0) {
-		sprintf(lookup, SUNXI_GPIO_AXP0_PREFIX "%d",
-			SUNXI_GPIO_AXP0_VBUS_ENABLE);
-		name = lookup;
-	}
-#endif
 	ret = gpio_lookup_name(name, NULL, NULL, &gpio);
 
 	return ret ? ret : gpio;
--- a/include/axp221.h
+++ b/include/axp221.h
@@ -53,10 +53,6 @@
 #ifdef CONFIG_AXP221_POWER
 #define AXP_POWER_STATUS		0x00
 #define AXP_POWER_STATUS_ALDO_IN		BIT(0)
-#define AXP_VBUS_IPSOUT			0x30
-#define AXP_VBUS_IPSOUT_DRIVEBUS		(1 << 2)
-#define AXP_MISC_CTRL			0x8f
-#define AXP_MISC_CTRL_N_VBUSEN_FUNC		(1 << 4)
 #define AXP_GPIO0_CTRL			0x90
 #define AXP_GPIO1_CTRL			0x92
 #define AXP_GPIO_CTRL_OUTPUT_LOW		0x00 /* Drive pin low */
--- a/include/axp809.h
+++ b/include/axp809.h
@@ -47,10 +47,6 @@
 #ifdef CONFIG_AXP809_POWER
 #define AXP_POWER_STATUS		0x00
 #define AXP_POWER_STATUS_ALDO_IN		BIT(0)
-#define AXP_VBUS_IPSOUT			0x30
-#define AXP_VBUS_IPSOUT_DRIVEBUS		(1 << 2)
-#define AXP_MISC_CTRL			0x8f
-#define AXP_MISC_CTRL_N_VBUSEN_FUNC		(1 << 4)
 #define AXP_GPIO0_CTRL			0x90
 #define AXP_GPIO1_CTRL			0x92
 #define AXP_GPIO_CTRL_OUTPUT_LOW	0x00 /* Drive pin low */
--- a/include/axp818.h
+++ b/include/axp818.h
@@ -61,10 +61,6 @@
 #ifdef CONFIG_AXP818_POWER
 #define AXP_POWER_STATUS		0x00
 #define AXP_POWER_STATUS_ALDO_IN		BIT(0)
-#define AXP_VBUS_IPSOUT			0x30
-#define AXP_VBUS_IPSOUT_DRIVEBUS		(1 << 2)
-#define AXP_MISC_CTRL			0x8f
-#define AXP_MISC_CTRL_N_VBUSEN_FUNC		(1 << 4)
 #define AXP_GPIO0_CTRL			0x90
 #define AXP_GPIO1_CTRL			0x92
 #define AXP_GPIO_CTRL_OUTPUT_LOW	0x00 /* Drive pin low */
