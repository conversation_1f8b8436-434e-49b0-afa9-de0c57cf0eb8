From 666d3c2bc058268a976397ec3e258f532edcfeb2 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> HERPAI <<EMAIL>>
Date: Tue, 6 Jun 2023 18:12:11 +0000
Subject: [PATCH 73/90] drivers: sunxi: Replace ARCH_SUNXI with BOARD_SUNXI

This provides a unified configuration across all sunxi boards,
regardless of CPU architecture.

Signed-off-by: <PERSON> <<EMAIL>>
Signed-off-by: <PERSON><PERSON><PERSON> HERPAI <<EMAIL>>
---
 drivers/clk/sunxi/Kconfig     |  2 +-
 drivers/fastboot/Kconfig      | 13 ++++++-------
 drivers/gpio/Kconfig          |  2 +-
 drivers/mmc/Kconfig           |  2 +-
 drivers/net/phy/Kconfig       |  4 ++--
 drivers/phy/allwinner/Kconfig |  2 +-
 drivers/pinctrl/sunxi/Kconfig |  2 +-
 drivers/reset/Kconfig         |  2 +-
 drivers/spi/Kconfig           |  2 +-
 drivers/usb/Kconfig           |  2 +-
 drivers/usb/gadget/Kconfig    |  8 ++++----
 drivers/usb/musb-new/Kconfig  |  2 +-
 drivers/video/Kconfig         |  2 +-
 drivers/watchdog/Kconfig      |  4 ++--
 14 files changed, 24 insertions(+), 25 deletions(-)

--- a/drivers/clk/sunxi/Kconfig
+++ b/drivers/clk/sunxi/Kconfig
@@ -1,6 +1,6 @@
 config CLK_SUNXI
 	bool "Clock support for Allwinner SoCs"
-	depends on CLK && ARCH_SUNXI
+	depends on CLK && BOARD_SUNXI
 	select DM_RESET
 	select SPL_DM_RESET if SPL_CLK
 	default y
--- a/drivers/fastboot/Kconfig
+++ b/drivers/fastboot/Kconfig
@@ -8,7 +8,7 @@ config FASTBOOT
 config USB_FUNCTION_FASTBOOT
 	bool "Enable USB fastboot gadget"
 	depends on USB_GADGET
-	default y if ARCH_SUNXI && USB_MUSB_GADGET
+	default y if BOARD_SUNXI && USB_MUSB_GADGET
 	select FASTBOOT
 	select USB_GADGET_DOWNLOAD
 	help
@@ -32,10 +32,9 @@ if FASTBOOT
 
 config FASTBOOT_BUF_ADDR
 	hex "Define FASTBOOT buffer address"
+	default SYS_LOAD_ADDR if BOARD_SUNXI
 	default 0x82000000 if MX6SX || MX6SL || MX6UL || MX6SLL
 	default 0x81000000 if ARCH_OMAP2PLUS
-	default 0x42000000 if ARCH_SUNXI && !MACH_SUN9I
-	default 0x22000000 if ARCH_SUNXI && MACH_SUN9I
 	default 0x60800800 if ROCKCHIP_RK3036 || ROCKCHIP_RK3188 || \
 				ROCKCHIP_RK322X
 	default 0x800800 if ROCKCHIP_RK3288 || ROCKCHIP_RK3329 || \
@@ -52,7 +51,7 @@ config FASTBOOT_BUF_SIZE
 	hex "Define FASTBOOT buffer size"
 	default 0x8000000 if ARCH_ROCKCHIP
 	default 0x6000000 if ARCH_ZYNQMP
-	default 0x2000000 if ARCH_SUNXI
+	default 0x2000000 if BOARD_SUNXI
 	default 0x8192 if SANDBOX
 	default 0x7000000
 	help
@@ -71,7 +70,7 @@ config FASTBOOT_USB_DEV
 
 config FASTBOOT_FLASH
 	bool "Enable FASTBOOT FLASH command"
-	default y if ARCH_SUNXI || ARCH_ROCKCHIP
+	default y if BOARD_SUNXI || ARCH_ROCKCHIP
 	depends on MMC || (MTD_RAW_NAND && CMD_MTDPARTS)
 	select IMAGE_SPARSE
 	help
@@ -105,8 +104,8 @@ config FASTBOOT_FLASH_MMC_DEV
 	int "Define FASTBOOT MMC FLASH default device"
 	depends on FASTBOOT_FLASH_MMC
 	default 0 if ARCH_ROCKCHIP
-	default 0 if ARCH_SUNXI && MMC_SUNXI_SLOT_EXTRA = -1
-	default 1 if ARCH_SUNXI && MMC_SUNXI_SLOT_EXTRA != -1
+	default 0 if BOARD_SUNXI && MMC_SUNXI_SLOT_EXTRA = -1
+	default 1 if BOARD_SUNXI && MMC_SUNXI_SLOT_EXTRA != -1
 	help
 	  The fastboot "flash" command requires additional information
 	  regarding the non-volatile storage device. Define this to
--- a/drivers/gpio/Kconfig
+++ b/drivers/gpio/Kconfig
@@ -370,7 +370,7 @@ config SANDBOX_GPIO_COUNT
 
 config SUNXI_GPIO
 	bool "Allwinner GPIO driver"
-	depends on ARCH_SUNXI
+	depends on BOARD_SUNXI
 	select SPL_STRTO if SPL
 	help
 	  Support the GPIO device in Allwinner SoCs.
--- a/drivers/mmc/Kconfig
+++ b/drivers/mmc/Kconfig
@@ -756,7 +756,7 @@ config ZYNQ_HISPD_BROKEN
 
 config MMC_SUNXI
 	bool "Allwinner sunxi SD/MMC Host Controller support"
-	depends on ARCH_SUNXI
+	depends on BOARD_SUNXI
 	default y
 	help
 	  This selects support for the SD/MMC Host Controller on
--- a/drivers/net/phy/Kconfig
+++ b/drivers/net/phy/Kconfig
@@ -19,14 +19,14 @@ if PHYLIB
 
 config PHY_ADDR_ENABLE
 	bool "Limit phy address"
-	default y if ARCH_SUNXI
+	default y if BOARD_SUNXI
 	help
 	  Select this if you want to control which phy address is used
 
 if PHY_ADDR_ENABLE
 config PHY_ADDR
 	int "PHY address"
-	default 1 if ARCH_SUNXI
+	default 1 if BOARD_SUNXI
 	default 0
 	help
 	  The address of PHY on MII bus. Usually in range of 0 to 31.
--- a/drivers/phy/allwinner/Kconfig
+++ b/drivers/phy/allwinner/Kconfig
@@ -3,7 +3,7 @@
 #
 config PHY_SUN4I_USB
 	bool "Allwinner Sun4I USB PHY driver"
-	depends on ARCH_SUNXI && !MACH_SUN9I
+	depends on depends on BOARD_SUNXI
 	default y
 	select DM_REGULATOR
 	select PHY
--- a/drivers/pinctrl/sunxi/Kconfig
+++ b/drivers/pinctrl/sunxi/Kconfig
@@ -1,6 +1,6 @@
 # SPDX-License-Identifier: GPL-2.0
 
-if ARCH_SUNXI
+if BOARD_SUNXI
 
 config PINCTRL_SUNXI
 	select PINCTRL_FULL
--- a/drivers/reset/Kconfig
+++ b/drivers/reset/Kconfig
@@ -137,7 +137,7 @@ config RESET_MTMIPS
 
 config RESET_SUNXI
 	bool "RESET support for Allwinner SoCs"
-	depends on DM_RESET && ARCH_SUNXI
+	depends on DM_RESET && BOARD_SUNXI
 	default y
 	help
 	  This enables support for common reset driver for
--- a/drivers/spi/Kconfig
+++ b/drivers/spi/Kconfig
@@ -453,7 +453,7 @@ config SOFT_SPI
 
 config SPI_SUNXI
 	bool "Allwinner SoC SPI controllers"
-	default ARCH_SUNXI
+	default BOARD_SUNXI
 	help
 	  Enable the Allwinner SoC SPi controller driver.
 
--- a/drivers/usb/Kconfig
+++ b/drivers/usb/Kconfig
@@ -116,7 +116,7 @@ config USB_KEYBOARD_FN_KEYS
 
 choice
 	prompt "USB keyboard polling"
-	default SYS_USB_EVENT_POLL_VIA_INT_QUEUE if ARCH_SUNXI
+	default SYS_USB_EVENT_POLL_VIA_INT_QUEUE if BOARD_SUNXI
 	default SYS_USB_EVENT_POLL
 	---help---
 	  Enable a polling mechanism for USB keyboard.
--- a/drivers/usb/gadget/Kconfig
+++ b/drivers/usb/gadget/Kconfig
@@ -40,7 +40,7 @@ if USB_GADGET
 
 config USB_GADGET_MANUFACTURER
 	string "Vendor name of the USB device"
-	default "Allwinner Technology" if ARCH_SUNXI
+	default "Allwinner Technology" if BOARD_SUNXI
 	default "Rockchip" if ARCH_ROCKCHIP
 	default "U-Boot"
 	help
@@ -49,7 +49,7 @@ config USB_GADGET_MANUFACTURER
 
 config USB_GADGET_VENDOR_NUM
 	hex "Vendor ID of the USB device"
-	default 0x1f3a if ARCH_SUNXI
+	default 0x1f3a if BOARD_SUNXI
 	default 0x2207 if ARCH_ROCKCHIP
 	default 0x0
 	help
@@ -59,7 +59,7 @@ config USB_GADGET_VENDOR_NUM
 
 config USB_GADGET_PRODUCT_NUM
 	hex "Product ID of the USB device"
-	default 0x1010 if ARCH_SUNXI
+	default 0x1010 if BOARD_SUNXI
 	default 0x310a if ROCKCHIP_RK3036
 	default 0x300a if ROCKCHIP_RK3066
 	default 0x310c if ROCKCHIP_RK3128
@@ -202,7 +202,7 @@ endif # USB_GADGET_DOWNLOAD
 config USB_ETHER
 	bool "USB Ethernet Gadget"
 	depends on NET
-	default y if ARCH_SUNXI && USB_MUSB_GADGET
+	default y if BOARD_SUNXI && USB_MUSB_GADGET
 	help
 	  Creates an Ethernet network device through a USB peripheral
 	  controller. This will create a network interface on both the device
--- a/drivers/usb/musb-new/Kconfig
+++ b/drivers/usb/musb-new/Kconfig
@@ -67,7 +67,7 @@ config USB_MUSB_PIC32
 
 config USB_MUSB_SUNXI
 	bool "Enable sunxi OTG / DRC USB controller"
-	depends on ARCH_SUNXI
+	depends on BOARD_SUNXI
 	select USB_MUSB_PIO_ONLY
 	default y
 	---help---
--- a/drivers/video/Kconfig
+++ b/drivers/video/Kconfig
@@ -183,7 +183,7 @@ config CONSOLE_TRUETYPE_MAX_METRICS
 
 config SYS_WHITE_ON_BLACK
 	bool "Display console as white on a black background"
-	default y if ARCH_AT91 || ARCH_EXYNOS || ARCH_ROCKCHIP || ARCH_TEGRA || X86 || ARCH_SUNXI
+	default y if ARCH_AT91 || ARCH_EXYNOS || ARCH_ROCKCHIP || ARCH_TEGRA || X86 || BOARD_SUNXI
 	help
 	 Normally the display is black on a white background, Enable this
 	 option to invert this, i.e. white on a black background. This can be
--- a/drivers/watchdog/Kconfig
+++ b/drivers/watchdog/Kconfig
@@ -29,7 +29,7 @@ config WATCHDOG_TIMEOUT_MSECS
 	default 128000 if ARCH_MX31 || ARCH_MX5 || ARCH_MX6
 	default 128000 if ARCH_MX7 || ARCH_VF610
 	default 30000 if ARCH_SOCFPGA
-	default 16000 if ARCH_SUNXI
+	default 16000 if BOARD_SUNXI
 	default 60000
 	help
 	  Watchdog timeout in msec
@@ -321,7 +321,7 @@ config WDT_STM32MP
 
 config WDT_SUNXI
 	bool "Allwinner sunxi watchdog timer support"
-	depends on WDT && ARCH_SUNXI
+	depends on WDT && BOARD_SUNXI
 	default y
 	help
 	  Enable support for the watchdog timer in Allwinner sunxi SoCs.
