From c33ca5c6a5be74711460756bf86c45b6c6fd0a3f Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Thu, 4 Nov 2021 17:49:15 -0500
Subject: [PATCH 82/90] pinctrl: sunxi: Hack up the driver for the D1

Signed-off-by: <PERSON> <<EMAIL>>
---
 drivers/pinctrl/sunxi/pinctrl-sunxi.c | 3 ++-
 1 file changed, 2 insertions(+), 1 deletion(-)

--- a/drivers/pinctrl/sunxi/pinctrl-sunxi.c
+++ b/drivers/pinctrl/sunxi/pinctrl-sunxi.c
@@ -9,6 +9,7 @@
 #include <malloc.h>
 
 #include <asm/gpio.h>
+#include "../../../arch/arm/include/asm/arch-sunxi/gpio.h"
 
 extern U_BOOT_DRIVER(gpio_sunxi);
 
@@ -49,7 +50,7 @@ static const char *sunxi_pinctrl_get_pin
 					      uint pin_selector)
 {
 	const struct sunxi_pinctrl_desc *desc = dev_get_priv(dev);
-	static char pin_name[sizeof("PN31")];
+	static char pin_name[sizeof("PN31")] __section(".data");
 
 	snprintf(pin_name, sizeof(pin_name), "P%c%d",
 		 pin_selector / SUNXI_GPIOS_PER_BANK + desc->first_bank + 'A',
