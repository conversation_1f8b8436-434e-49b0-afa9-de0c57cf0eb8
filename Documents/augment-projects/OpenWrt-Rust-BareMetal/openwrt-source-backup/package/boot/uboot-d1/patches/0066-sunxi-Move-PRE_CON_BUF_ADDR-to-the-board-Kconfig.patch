From e947c7377b90897e4c638dad6e64201361dc8a9e Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sat, 6 Aug 2022 00:45:10 -0500
Subject: [PATCH 66/90] sunxi: Move PRE_CON_BUF_ADDR to the board Kconfig

This provides a default value for RISC-V when that is added, and it
makes sense to put this option next to the other DRAM layout options.

While at it, provide sensible values for platforms with less DRAM.

Signed-off-by: <PERSON> <<EMAIL>>
---
 board/sunxi/Kconfig | 6 ++++++
 common/Kconfig      | 2 --
 2 files changed, 6 insertions(+), 2 deletions(-)

--- a/board/sunxi/Kconfig
+++ b/board/sunxi/Kconfig
@@ -55,6 +55,12 @@ if BOARD_SUNXI
 config IDENT_STRING
 	default " Allwinner Technology"
 
+config PRE_CON_BUF_ADDR
+	default 0x81000000 if MACH_SUNIV
+	default 0x2f000000 if MACH_SUN9I
+	default 0x4f000000 if SUNXI_MINIMUM_DRAM_MB >= 256
+	default 0x43000000 if SUNXI_MINIMUM_DRAM_MB >= 64
+
 config SPL_STACK_R_ADDR
 	default 0x81e00000 if MACH_SUNIV
 	default 0x2fe00000 if MACH_SUN9I
--- a/common/Kconfig
+++ b/common/Kconfig
@@ -195,8 +195,6 @@ config PRE_CON_BUF_SZ
 config PRE_CON_BUF_ADDR
 	hex "Address of the pre-console buffer"
 	depends on PRE_CONSOLE_BUFFER
-	default 0x2f000000 if ARCH_SUNXI && MACH_SUN9I
-	default 0x4f000000 if ARCH_SUNXI && !MACH_SUN9I
 	default 0x0f000000 if ROCKCHIP_RK3288
 	default 0x0f200000 if ROCKCHIP_RK3399
 	help
