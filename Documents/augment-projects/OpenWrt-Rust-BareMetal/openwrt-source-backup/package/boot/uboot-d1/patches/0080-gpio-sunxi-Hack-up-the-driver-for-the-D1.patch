From 28682ca027b9fa64f3de4cea99373642f36c4e6c Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sun, 8 Aug 2021 19:32:14 -0500
Subject: [PATCH 80/90] gpio: sunxi: Hack up the driver for the D1

Signed-off-by: <PERSON> <<EMAIL>>
---
 arch/arm/include/asm/arch-sunxi/gpio.h | 12 ++++++++++--
 arch/arm/mach-sunxi/pinmux.c           |  8 +++++++-
 drivers/gpio/sunxi_gpio.c              |  3 +++
 3 files changed, 20 insertions(+), 3 deletions(-)

--- a/arch/arm/include/asm/arch-sunxi/gpio.h
+++ b/arch/arm/include/asm/arch-sunxi/gpio.h
@@ -9,7 +9,9 @@
 #define _SUNXI_GPIO_H
 
 #include <linux/types.h>
+#if 0
 #include <asm/arch/cpu.h>
+#endif
 
 /*
  * sunxi has 9 banks of gpio, they are:
@@ -55,30 +57,36 @@
 struct sunxi_gpio {
 	u32 cfg[4];
 	u32 dat;
-	u32 drv[2];
+	u32 drv[4];
 	u32 pull[2];
+	u32 reserved;
 };
 
 /* gpio interrupt control */
 struct sunxi_gpio_int {
-	u32 cfg[3];
+	u32 cfg[4];
 	u32 ctl;
 	u32 sta;
 	u32 deb;		/* interrupt debounce */
+	u32 reserved;
 };
 
+#if 0
 struct sunxi_gpio_reg {
 	struct sunxi_gpio gpio_bank[SUNXI_GPIO_BANKS];
 	u8 res[0xbc];
 	struct sunxi_gpio_int gpio_int;
 };
+#endif
 
 #define SUN50I_H6_GPIO_POW_MOD_SEL	0x340
 #define SUN50I_H6_GPIO_POW_MOD_VAL	0x348
 
+#if 0
 #define BANK_TO_GPIO(bank)	(((bank) < SUNXI_GPIO_L) ? \
 	&((struct sunxi_gpio_reg *)SUNXI_PIO_BASE)->gpio_bank[bank] : \
 	&((struct sunxi_gpio_reg *)SUNXI_R_PIO_BASE)->gpio_bank[(bank) - SUNXI_GPIO_L])
+#endif
 
 #define GPIO_BANK(pin)		((pin) >> 5)
 #define GPIO_NUM(pin)		((pin) & 0x1f)
--- a/arch/arm/mach-sunxi/pinmux.c
+++ b/arch/arm/mach-sunxi/pinmux.c
@@ -7,7 +7,7 @@
 
 #include <common.h>
 #include <asm/io.h>
-#include <asm/arch/gpio.h>
+//#include <asm/arch/gpio.h>
 
 void sunxi_gpio_set_cfgbank(struct sunxi_gpio *pio, int bank_offset, u32 val)
 {
@@ -17,6 +17,7 @@ void sunxi_gpio_set_cfgbank(struct sunxi
 	clrsetbits_le32(&pio->cfg[index], 0xf << offset, val << offset);
 }
 
+#if !CONFIG_IS_ENABLED(DM_GPIO)
 void sunxi_gpio_set_cfgpin(u32 pin, u32 val)
 {
 	u32 bank = GPIO_BANK(pin);
@@ -24,6 +25,7 @@ void sunxi_gpio_set_cfgpin(u32 pin, u32
 
 	sunxi_gpio_set_cfgbank(pio, pin, val);
 }
+#endif
 
 int sunxi_gpio_get_cfgbank(struct sunxi_gpio *pio, int bank_offset)
 {
@@ -37,6 +39,7 @@ int sunxi_gpio_get_cfgbank(struct sunxi_
 	return cfg & 0xf;
 }
 
+#if !CONFIG_IS_ENABLED(DM_GPIO)
 int sunxi_gpio_get_cfgpin(u32 pin)
 {
 	u32 bank = GPIO_BANK(pin);
@@ -52,6 +55,7 @@ void sunxi_gpio_set_drv(u32 pin, u32 val
 
 	sunxi_gpio_set_drv_bank(pio, pin, val);
 }
+#endif
 
 void sunxi_gpio_set_drv_bank(struct sunxi_gpio *pio, u32 bank_offset, u32 val)
 {
@@ -61,6 +65,7 @@ void sunxi_gpio_set_drv_bank(struct sunx
 	clrsetbits_le32(&pio->drv[index], 0x3 << offset, val << offset);
 }
 
+#if !CONFIG_IS_ENABLED(DM_GPIO)
 void sunxi_gpio_set_pull(u32 pin, u32 val)
 {
 	u32 bank = GPIO_BANK(pin);
@@ -68,6 +73,7 @@ void sunxi_gpio_set_pull(u32 pin, u32 va
 
 	sunxi_gpio_set_pull_bank(pio, pin, val);
 }
+#endif
 
 void sunxi_gpio_set_pull_bank(struct sunxi_gpio *pio, int bank_offset, u32 val)
 {
--- a/drivers/gpio/sunxi_gpio.c
+++ b/drivers/gpio/sunxi_gpio.c
@@ -18,6 +18,9 @@
 #include <asm/gpio.h>
 #include <dt-bindings/gpio/gpio.h>
 
+#include "../../arch/arm/include/asm/arch-sunxi/gpio.h"
+#include "../../arch/arm/mach-sunxi/pinmux.c"
+
 #if !CONFIG_IS_ENABLED(DM_GPIO)
 static int sunxi_gpio_output(u32 pin, u32 val)
 {
