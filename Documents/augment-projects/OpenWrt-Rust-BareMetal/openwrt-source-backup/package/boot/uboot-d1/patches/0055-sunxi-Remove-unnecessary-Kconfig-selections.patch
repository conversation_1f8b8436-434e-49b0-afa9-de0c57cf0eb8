From 891fef47500dbf4aecb16e08c1d8ade3fbc8caec Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Thu, 4 Aug 2022 23:22:05 -0500
Subject: [PATCH 55/90] sunxi: Remove unnecessary Kconfig selections

Two of these selections are redundant and have no effect:
 - DM_KEYBOARD is selected by USB_KEYBOARD
 - DM_MMC is selected by MMC

This selection has no effect by default and is unnecessarily strong:
 - USB_STORAGE is implied by DISTRO_DEFAULTS

Signed-off-by: <PERSON> <<EMAIL>>
---
 arch/arm/Kconfig | 3 ---
 1 file changed, 3 deletions(-)

--- a/arch/arm/Kconfig
+++ b/arch/arm/Kconfig
@@ -1100,8 +1100,6 @@ config ARCH_SUNXI
 	select DM_I2C if I2C
 	select DM_SPI if SPI
 	select DM_SPI_FLASH if SPI
-	select DM_KEYBOARD
-	select DM_MMC if MMC
 	select DM_SCSI if SCSI
 	select DM_SERIAL
 	select GPIO_EXTRA_HEADER
@@ -1119,7 +1117,6 @@ config ARCH_SUNXI
 	select SYS_THUMB_BUILD if !ARM64
 	select USB if DISTRO_DEFAULTS
 	select USB_KEYBOARD if DISTRO_DEFAULTS && USB_HOST
-	select USB_STORAGE if DISTRO_DEFAULTS && USB_HOST
 	select SPL_USE_TINY_PRINTF
 	select USE_PREBOOT
 	select SYS_RELOC_GD_ENV_ADDR
