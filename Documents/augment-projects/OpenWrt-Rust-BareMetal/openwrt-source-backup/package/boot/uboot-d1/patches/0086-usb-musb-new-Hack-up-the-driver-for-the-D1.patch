From eb0cf5922cf0656a7e9f96d6b83397469a26825a Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sat, 11 Sep 2021 10:12:24 -0500
Subject: [PATCH 86/90] usb: musb-new: Hack up the driver for the D1

Signed-off-by: <PERSON> <<EMAIL>>
---
 arch/riscv/include/asm/bitops.h    | 1 +
 arch/riscv/include/asm/io.h        | 2 +-
 arch/riscv/include/asm/processor.h | 2 ++
 drivers/usb/musb-new/musb_io.h     | 2 +-
 drivers/usb/musb-new/sunxi.c       | 6 ++++--
 5 files changed, 9 insertions(+), 4 deletions(-)

--- a/arch/riscv/include/asm/bitops.h
+++ b/arch/riscv/include/asm/bitops.h
@@ -78,6 +78,7 @@ static inline int __test_and_set_bit(int
 	return retval;
 }
 
+#define test_and_clear_bit __test_and_clear_bit
 static inline int __test_and_clear_bit(int nr, void *addr)
 {
 	int mask, retval;
--- a/arch/riscv/include/asm/io.h
+++ b/arch/riscv/include/asm/io.h
@@ -218,6 +218,7 @@ static inline u64 readq(const volatile v
 #define insb(p, d, l)			readsb(__io(p), d, l)
 #define insw(p, d, l)			readsw(__io(p), d, l)
 #define insl(p, d, l)			readsl(__io(p), d, l)
+#endif
 
 static inline void readsb(unsigned int *addr, void *data, int bytelen)
 {
@@ -308,7 +309,6 @@ static inline void writesl(unsigned int
 		longlen--;
 	}
 }
-#endif
 
 #define outb_p(val, port)		outb((val), (port))
 #define outw_p(val, port)		outw((val), (port))
--- a/arch/riscv/include/asm/processor.h
+++ b/arch/riscv/include/asm/processor.h
@@ -23,4 +23,6 @@
  *     no one uses the macros defined in this head file.
  **************************************************************/
 
+#define cpu_relax()			barrier()
+
 #endif /* __ASM_RISCV_PROCESSOR_H */
--- a/drivers/usb/musb-new/musb_io.h
+++ b/drivers/usb/musb-new/musb_io.h
@@ -23,7 +23,7 @@
 #if !defined(CONFIG_ARM) && !defined(CONFIG_SUPERH) \
 	&& !defined(CONFIG_PPC32) \
 	&& !defined(CONFIG_PPC64) && !defined(CONFIG_MIPS) \
-	&& !defined(CONFIG_M68K)
+	&& !defined(CONFIG_M68K) && !defined(CONFIG_RISCV)
 static inline void readsl(const void __iomem *addr, void *buf, int len)
 	{ insl((unsigned long)addr, buf, len); }
 static inline void readsw(const void __iomem *addr, void *buf, int len)
--- a/drivers/usb/musb-new/sunxi.c
+++ b/drivers/usb/musb-new/sunxi.c
@@ -23,8 +23,8 @@
 #include <malloc.h>
 #include <phy-sun4i-usb.h>
 #include <reset.h>
-#include <asm/arch/cpu.h>
-#include <asm/arch/clock.h>
+//#include <asm/arch/cpu.h>
+//#include <asm/arch/clock.h>
 #include <dm/device_compat.h>
 #include <dm/lists.h>
 #include <dm/root.h>
@@ -174,6 +174,7 @@ static void USBC_ForceVbusValidToHigh(__
 
 static void USBC_ConfigFIFO_Base(void)
 {
+#if 0
 	u32 reg_value;
 
 	/* config usb fifo, 8kb mode */
@@ -181,6 +182,7 @@ static void USBC_ConfigFIFO_Base(void)
 	reg_value &= ~(0x03 << 0);
 	reg_value |= BIT(0);
 	writel(reg_value, SUNXI_SRAMC_BASE + 0x04);
+#endif
 }
 
 /******************************************************************************
