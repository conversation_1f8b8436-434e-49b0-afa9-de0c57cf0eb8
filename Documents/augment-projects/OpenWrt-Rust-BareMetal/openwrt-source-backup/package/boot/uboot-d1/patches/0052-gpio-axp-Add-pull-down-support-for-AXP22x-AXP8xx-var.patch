From 4be9f9082b0a2ac2626ae20b7e07006139827442 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sat, 28 Aug 2021 00:34:54 -0500
Subject: [PATCH 52/90] gpio: axp: Add pull-down support for AXP22x/AXP8xx
 variant

The AXP221 and newer PMICs support a pull-down function on their GPIOs.
Add support for it.

Signed-off-by: <PERSON> <<EMAIL>>
---
 drivers/gpio/axp_gpio.c | 15 ++++++++++++++-
 1 file changed, 14 insertions(+), 1 deletion(-)

--- a/drivers/gpio/axp_gpio.c
+++ b/drivers/gpio/axp_gpio.c
@@ -24,6 +24,7 @@ struct axp_gpio_desc {
 	u8		npins;
 	u8		status_reg;
 	u8		status_offset;
+	u8		pull_reg;
 	u8		input_mux;
 };
 
@@ -60,11 +61,22 @@ static int axp_gpio_get_function(struct
 static int axp_gpio_set_flags(struct udevice *dev, unsigned pin, ulong flags)
 {
 	const struct axp_gpio_desc *desc = dev_get_priv(dev);
+	bool pull_down = flags & GPIOD_PULL_DOWN;
+	int ret;
 	u8 mux;
 
-	if (flags & (GPIOD_MASK_DSTYPE | GPIOD_MASK_PULL))
+	if (flags & (GPIOD_MASK_DSTYPE | GPIOD_PULL_UP))
+		return -EINVAL;
+	if (pull_down && !desc->pull_reg)
 		return -EINVAL;
 
+	if (desc->pull_reg) {
+		ret = pmic_clrsetbits(dev->parent, desc->pull_reg,
+				      BIT(pin), pull_down ? BIT(pin) : 0);
+		if (ret)
+			return ret;
+	}
+
 	if (flags & GPIOD_IS_IN)
 		mux = desc->input_mux;
 	else if (flags & GPIOD_IS_OUT_ACTIVE)
@@ -129,6 +141,7 @@ static const struct axp_gpio_desc axp221
 	.pins		= axp221_gpio_pins,
 	.npins		= ARRAY_SIZE(axp221_gpio_pins),
 	.status_reg	= 0x94,
+	.pull_reg	= 0x97,
 	.input_mux	= 2,
 };
 
