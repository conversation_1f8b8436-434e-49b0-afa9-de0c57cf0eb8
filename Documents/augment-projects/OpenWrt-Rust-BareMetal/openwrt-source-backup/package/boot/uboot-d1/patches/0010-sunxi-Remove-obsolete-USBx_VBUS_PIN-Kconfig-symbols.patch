From 649bb7845e30805c66f62fc5725c4dbf350f21cb Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Thu, 26 Aug 2021 22:26:40 -0500
Subject: [PATCH 10/90] sunxi: Remove obsolete USBx_VBUS_PIN Kconfig symbols

Now that the USB PHY driver uses the device tree to get VBUS supply
regulators, these Kconfig symbols are unused. Remove them.

Signed-off-by: <PERSON> <<EMAIL>>
---
 arch/arm/mach-sunxi/Kconfig | 29 -----------------------------
 1 <USER> <GROUP>, 29 deletions(-)

--- a/arch/arm/mach-sunxi/Kconfig
+++ b/arch/arm/mach-sunxi/Kconfig
@@ -693,13 +693,6 @@ config MMC_SUNXI_SLOT_EXTRA
 	slot or emmc on mmc1 - mmc3. Setting this to 1, 2 or 3 will enable
 	support for this.
 
-config USB0_VBUS_PIN
-	string "Vbus enable pin for usb0 (otg)"
-	default ""
-	---help---
-	Set the Vbus enable pin for usb0 (otg). This takes a string in the
-	format understood by sunxi_name_to_gpio, e.g. PH1 for pin 1 of port H.
-
 config USB0_VBUS_DET
 	string "Vbus detect pin for usb0 (otg)"
 	default ""
@@ -714,28 +707,6 @@ config USB0_ID_DET
 	Set the ID detect pin for usb0 (otg). This takes a string in the
 	format understood by sunxi_name_to_gpio, e.g. PH1 for pin 1 of port H.
 
-config USB1_VBUS_PIN
-	string "Vbus enable pin for usb1 (ehci0)"
-	default "PH6" if MACH_SUN4I || MACH_SUN7I
-	default "PH27" if MACH_SUN6I
-	---help---
-	Set the Vbus enable pin for usb1 (ehci0, usb0 is the otg). This takes
-	a string in the format understood by sunxi_name_to_gpio, e.g.
-	PH1 for pin 1 of port H.
-
-config USB2_VBUS_PIN
-	string "Vbus enable pin for usb2 (ehci1)"
-	default "PH3" if MACH_SUN4I || MACH_SUN7I
-	default "PH24" if MACH_SUN6I
-	---help---
-	See USB1_VBUS_PIN help text.
-
-config USB3_VBUS_PIN
-	string "Vbus enable pin for usb3 (ehci2)"
-	default ""
-	---help---
-	See USB1_VBUS_PIN help text.
-
 config I2C0_ENABLE
 	bool "Enable I2C/TWI controller 0"
 	default y if MACH_SUN4I || MACH_SUN5I || MACH_SUN7I || MACH_SUN8I_R40
