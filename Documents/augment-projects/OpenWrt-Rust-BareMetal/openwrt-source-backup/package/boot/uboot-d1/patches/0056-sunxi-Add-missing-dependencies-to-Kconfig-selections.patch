From 9e12a7fd80276092da3a43b7dbaf572bad294419 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Thu, 4 Aug 2022 23:29:13 -0500
Subject: [PATCH 56/90] sunxi: Add missing dependencies to Kconfig selections

Some of the selected symbols have a user-visible dependency. Make the
selections conditional on that dependency to avoid creating invalid
configurations.

Signed-off-by: <PERSON> <<EMAIL>>
---
 arch/arm/Kconfig | 16 ++++++++--------
 1 file changed, 8 insertions(+), 8 deletions(-)

--- a/arch/arm/Kconfig
+++ b/arch/arm/Kconfig
@@ -1090,30 +1090,30 @@ config ARCH_SOCFPGA
 config ARCH_SUNXI
 	bool "Support sunxi (Allwinner) SoCs"
 	select BINMAN
-	select CMD_GPIO
+	select CMD_GPIO if GPIO
 	select CMD_MMC if MMC
 	select CMD_<PERSON> if DISTRO_DEFAULTS && USB_HOST
 	select C<PERSON><PERSON>
 	select DM
-	select DM_ETH
-	select DM_GPIO
+	select DM_ETH if NET
+	select DM_GPIO if GPIO
 	select DM_I2C if I2C
+	select DM_SCSI if BLK && SCSI
+	select DM_SERIAL if SERIAL
 	select DM_SPI if SPI
 	select DM_SPI_FLASH if SPI
-	select DM_SCSI if SCSI
-	select DM_SERIAL
 	select GPIO_EXTRA_HEADER
 	select OF_BOARD_SETUP
 	select OF_CONTROL
 	select OF_SEPARATE
 	select PINCTRL
-	select SPECIFY_CONSOLE_INDEX
+	select SPECIFY_CONSOLE_INDEX if SERIAL
 	select SPL_SEPARATE_BSS if SPL
 	select SPL_STACK_R if SPL
 	select SPL_SYS_MALLOC_SIMPLE if SPL
 	select SPL_SYS_THUMB_BUILD if !ARM64
-	select SUNXI_GPIO
-	select SYS_NS16550
+	select SUNXI_GPIO if GPIO
+	select SYS_NS16550 if SERIAL
 	select SYS_THUMB_BUILD if !ARM64
 	select USB if DISTRO_DEFAULTS
 	select USB_KEYBOARD if DISTRO_DEFAULTS && USB_HOST
