From 79f7d883d980beea9989d06f9fba4fcc0430176a Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Thu, 14 Jul 2022 22:14:38 -0500
Subject: [PATCH 34/90] phy: sun4i-usb: Do not drive VBUS with external VBUS
 present

It is possible to use host-side USB with externally-provided VBUS. For
example, some USB OTG cables have an extra power input which powers
both the board and the USB peripheral.

To support this setup, skip enabling the VBUS switch/regulator if VBUS
voltage is already present. This behavior matches the Linux PHY driver.

Signed-off-by: <PERSON> <<EMAIL>>
---
 drivers/phy/allwinner/phy-sun4i-usb.c | 6 ++++++
 1 file changed, 6 insertions(+)

--- a/drivers/phy/allwinner/phy-sun4i-usb.c
+++ b/drivers/phy/allwinner/phy-sun4i-usb.c
@@ -220,6 +220,12 @@ static int sun4i_usb_phy_power_on(struct
 		initial_usb_scan_delay = 0;
 	}
 
+	/* For phy0 only turn on <PERSON>bus if we don't have an ext. Vbus */
+	if (phy->id == 0 && sun4i_usb_phy_vbus_detect(phy)) {
+		dev_warn(phy->dev, "External vbus detected, not enabling our own vbus\n");
+		return 0;
+	}
+
 	if (usb_phy->vbus) {
 		ret = regulator_set_enable(usb_phy->vbus, true);
 		if (ret && ret != -ENOSYS)
