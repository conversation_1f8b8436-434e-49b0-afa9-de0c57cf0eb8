From 963331be1cc924ad7c928f88b3ee46bc20a41bcd Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON>ERPA<PERSON> <<EMAIL>>
Date: Tue, 6 Jun 2023 18:06:20 +0000
Subject: [PATCH 59/90] sunxi: Move most Kconfig selections to the board
 Kconfig

To maintain consistent behavior across architectures, most of the
options selected by ARCH_SUNXI should be selected for the D1 SoC as
well. To accomplish this, select them from BOARD_SUNXI instead.

No functional change here. Lines are only moved and alphabetized.

Signed-off-by: <PERSON> <<EMAIL>>

Signed-off-by: <PERSON><PERSON><PERSON> HERPAI <<EMAIL>>
---
 arch/arm/Kconfig    | 47 ---------------------------------------------
 board/sunxi/Kconfig | 46 ++++++++++++++++++++++++++++++++++++++++++++
 2 files changed, 46 insertions(+), 47 deletions(-)

--- a/arch/arm/Kconfig
+++ b/arch/arm/Kconfig
@@ -1089,63 +1089,16 @@ config ARCH_SOCFPGA
 
 config ARCH_SUNXI
 	bool "Support sunxi (Allwinner) SoCs"
-	select BINMAN
 	select BOARD_SUNXI
-	select CMD_GPIO if GPIO
-	select CMD_MMC if MMC
-	select CMD_USB if DISTRO_DEFAULTS && USB_HOST
-	select CLK
-	select DM
-	select DM_ETH if NET
-	select DM_GPIO if GPIO
-	select DM_I2C if I2C
-	select DM_SCSI if BLK && SCSI
-	select DM_SERIAL if SERIAL
-	select DM_SPI if SPI
-	select DM_SPI_FLASH if SPI
 	select GPIO_EXTRA_HEADER
-	select OF_BOARD_SETUP
 	select OF_CONTROL
 	select OF_SEPARATE
-	select PINCTRL
 	select SPECIFY_CONSOLE_INDEX if SERIAL
-	select SPL_SEPARATE_BSS if SPL
 	select SPL_STACK_R if SPL
 	select SPL_SYS_MALLOC_SIMPLE if SPL
 	select SPL_SYS_THUMB_BUILD if !ARM64
-	select SUNXI_GPIO if GPIO
-	select SYS_NS16550 if SERIAL
 	select SYS_THUMB_BUILD if !ARM64
-	select USB if DISTRO_DEFAULTS
-	select USB_KEYBOARD if DISTRO_DEFAULTS && USB_HOST
 	select SPL_USE_TINY_PRINTF
-	select USE_PREBOOT
-	select SYS_RELOC_GD_ENV_ADDR
-	imply BOARD_LATE_INIT
-	imply CMD_DM
-	imply CMD_GPT
-	imply CMD_UBI if MTD_RAW_NAND
-	imply DISTRO_DEFAULTS
-	imply FAT_WRITE
-	imply FIT
-	imply OF_LIBFDT_OVERLAY
-	imply PRE_CONSOLE_BUFFER
-	imply SPL_GPIO
-	imply SPL_LIBCOMMON_SUPPORT
-	imply SPL_LIBGENERIC_SUPPORT
-	imply SPL_LOAD_FIT
-	imply SPL_MMC if MMC
-	imply SPL_POWER
-	imply SPL_SERIAL
-	imply SYSRESET
-	imply SYSRESET_WATCHDOG
-	imply SYSRESET_WATCHDOG_AUTO
-	imply USB_EHCI_GENERIC
-	imply USB_EHCI_HCD
-	imply USB_GADGET
-	imply USB_OHCI_GENERIC
-	imply USB_OHCI_HCD
-	imply WDT
 
 config ARCH_U8500
 	bool "ST-Ericsson U8500 Series"
--- a/board/sunxi/Kconfig
+++ b/board/sunxi/Kconfig
@@ -1,5 +1,51 @@
 config BOARD_SUNXI
 	bool
+	select BINMAN
+	select CLK
+	select CMD_GPIO if GPIO
+	select CMD_MMC if MMC
+	select CMD_USB if DISTRO_DEFAULTS && USB_HOST
+	select DM
+	select DM_ETH if NET
+	select DM_GPIO if GPIO
+	select DM_I2C if I2C
+	select DM_SCSI if BLK && SCSI
+	select DM_SERIAL if SERIAL
+	select DM_SPI if SPI
+	select DM_SPI_FLASH if SPI
+	select OF_BOARD_SETUP
+	select PINCTRL
+	select SPL_SEPARATE_BSS if SPL
+	select SUNXI_GPIO if GPIO
+	select SYS_NS16550 if SERIAL
+	select SYS_RELOC_GD_ENV_ADDR
+	select USB if DISTRO_DEFAULTS
+	select USB_KEYBOARD if DISTRO_DEFAULTS && USB_HOST
+	select USE_PREBOOT
+	imply BOARD_LATE_INIT
+	imply CMD_DM
+	imply CMD_GPT
+	imply CMD_UBI if MTD_RAW_NAND
+	imply DISTRO_DEFAULTS
+	imply FAT_WRITE
+	imply FIT
+	imply OF_LIBFDT_OVERLAY
+	imply PRE_CONSOLE_BUFFER
+	imply SPL_GPIO
+	imply SPL_LIBCOMMON_SUPPORT
+	imply SPL_LIBGENERIC_SUPPORT
+	imply SPL_MMC if MMC
+	imply SPL_POWER
+	imply SPL_SERIAL
+	imply SYSRESET
+	imply SYSRESET_WATCHDOG
+	imply SYSRESET_WATCHDOG_AUTO
+	imply USB_EHCI_GENERIC
+	imply USB_EHCI_HCD
+	imply USB_GADGET
+	imply USB_OHCI_GENERIC
+	imply USB_OHCI_HCD
+	imply WDT
 
 if BOARD_SUNXI
 
