From 27834df51087a005b0330f094492b984cc225f6a Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 5 Aug 2022 23:28:54 -0500
Subject: [PATCH 72/90] env: sunxi: Replace ARCH_SUNXI with BOARD_SUNXI

This ensures the same environment layout will be used across all sunxi
boards, regardless of CPU architecture.

Signed-off-by: <PERSON> <<EMAIL>>
---
 env/Kconfig | 12 ++++++------
 1 file changed, 6 insertions(+), 6 deletions(-)

--- a/env/Kconfig
+++ b/env/Kconfig
@@ -92,7 +92,7 @@ config ENV_IS_IN_FAT
 	bool "Environment is in a FAT filesystem"
 	depends on !CHAIN_OF_TRUST
 	default y if ARCH_BCM283X
-	default y if ARCH_SUNXI && MMC
+	default y if BOARD_SUNXI && MMC
 	default y if MMC_OMAP_HS && TI_COMMON_CMD_OPTIONS
 	select FS_FAT
 	select FAT_WRITE
@@ -338,7 +338,7 @@ config ENV_IS_IN_SPI_FLASH
 	default y if NORTHBRIDGE_INTEL_IVYBRIDGE
 	default y if INTEL_QUARK
 	default y if INTEL_QUEENSBAY
-	default y if ARCH_SUNXI
+	default y if BOARD_SUNXI
 	help
 	  Define this if you have a SPI Flash memory device which you
 	  want to use for the environment.
@@ -461,7 +461,7 @@ config ENV_FAT_DEVICE_AND_PART
 	depends on ENV_IS_IN_FAT
 	default "0:1" if TI_COMMON_CMD_OPTIONS
 	default "0:auto" if ARCH_ZYNQ || ARCH_ZYNQMP || ARCH_VERSAL
-	default ":auto" if ARCH_SUNXI
+	default ":auto" if BOARD_SUNXI
 	default "0" if ARCH_AT91
 	help
 	  Define this to a string to specify the partition of the device. It can
@@ -555,7 +555,7 @@ config ENV_OFFSET
 		    ENV_IS_IN_SPI_FLASH
 	default 0x3f8000 if ARCH_ROCKCHIP && ENV_IS_IN_MMC
 	default 0x140000 if ARCH_ROCKCHIP && ENV_IS_IN_SPI_FLASH
-	default 0xF0000 if ARCH_SUNXI
+	default 0xF0000 if BOARD_SUNXI
 	default 0xE0000 if ARCH_ZYNQ
 	default 0x1E00000 if ARCH_ZYNQMP
 	default 0x7F40000 if ARCH_VERSAL || ARCH_VERSAL_NET
@@ -580,7 +580,7 @@ config ENV_SIZE
 	hex "Environment Size"
 	default 0x40000 if ENV_IS_IN_SPI_FLASH && ARCH_ZYNQMP
 	default 0x20000 if ARCH_ZYNQ || ARCH_OMAP2PLUS || ARCH_AT91
-	default 0x10000 if ARCH_SUNXI
+	default 0x10000 if BOARD_SUNXI
 	default 0x8000 if ARCH_ROCKCHIP && ENV_IS_IN_MMC
 	default 0x2000 if ARCH_ROCKCHIP && ENV_IS_IN_SPI_FLASH
 	default 0x8000 if ARCH_ZYNQMP || ARCH_VERSAL || ARCH_VERSAL_NET
@@ -596,7 +596,7 @@ config ENV_SECT_SIZE
 	default 0x40000 if ARCH_ZYNQMP || ARCH_VERSAL || ARCH_VERSAL_NET
 	default 0x20000 if ARCH_ZYNQ || ARCH_OMAP2PLUS || ARCH_AT91
 	default 0x20000 if MICROBLAZE && ENV_IS_IN_SPI_FLASH
-	default 0x10000 if ARCH_SUNXI && ENV_IS_IN_SPI_FLASH
+	default 0x10000 if BOARD_SUNXI && ENV_IS_IN_SPI_FLASH
 	help
 	  Size of the sector containing the environment.
 
