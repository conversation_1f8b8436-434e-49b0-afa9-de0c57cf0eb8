From 5838fd3a53e613312d46ab4cb6015a502c4c45d0 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON>PA<PERSON> <<EMAIL>>
Date: Tue, 6 Jun 2023 18:07:24 +0000
Subject: [PATCH 60/90] sunxi: Globally enable SUPPORT_SPL

This was already supported by every machine type. It is unlikely that
any new SoC support will be added without SPL support.

Signed-off-by: <PERSON> <<EMAIL>>
Signed-off-by: <PERSON><PERSON><PERSON>ERPA<PERSON> <<EMAIL>>
---
 arch/arm/mach-sunxi/Kconfig | 14 --------------
 board/sunxi/Kconfig         |  2 ++
 2 files changed, 2 insertions(+), 14 deletions(-)

--- a/arch/arm/mach-sunxi/Kconfig
+++ b/arch/arm/mach-sunxi/Kconfig
@@ -133,7 +133,6 @@ config SUN50I_GEN_H6
 	select FIT
 	select SPL_LOAD_FIT
 	select MMC_SUNXI_HAS_NEW_MODE
-	select SUPPORT_SPL
 	---help---
 	Select this for sunxi SoCs which have H6 like peripherals, clocks
 	and memory map.
@@ -166,7 +165,6 @@ config MACH_SUNXI_H3_H5
 	select SUNXI_DRAM_DW
 	select SUNXI_DRAM_DW_32BIT
 	select SUNXI_GEN_SUN6I
-	select SUPPORT_SPL
 
 # TODO: try out A80's 8GiB DRAM space
 config SUNXI_DRAM_MAX_SIZE
@@ -183,7 +181,6 @@ config MACH_SUNIV
 	bool "suniv (Allwinner F1C100s/F1C200s/F1C600/R6)"
 	select CPU_ARM926EJS
 	select SUNXI_GEN_SUN6I
-	select SUPPORT_SPL
 	select SKIP_LOWLEVEL_INIT_ONLY
 	select SPL_SKIP_LOWLEVEL_INIT_ONLY
 
@@ -192,7 +189,6 @@ config MACH_SUN4I
 	select CPU_V7A
 	select DRAM_SUN4I
 	select SUNXI_GEN_SUN4I
-	select SUPPORT_SPL
 	imply SPL_SYS_I2C_LEGACY
 	imply SYS_I2C_LEGACY
 
@@ -201,7 +197,6 @@ config MACH_SUN5I
 	select CPU_V7A
 	select DRAM_SUN4I
 	select SUNXI_GEN_SUN4I
-	select SUPPORT_SPL
 	imply SPL_SYS_I2C_LEGACY
 	imply SYS_I2C_LEGACY
 
@@ -216,7 +211,6 @@ config MACH_SUN6I
 	select SPL_I2C
 	select SUN6I_PRCM
 	select SUNXI_GEN_SUN6I
-	select SUPPORT_SPL
 	select SYS_I2C_SUN6I_P2WI
 	select ARMV7_BOOT_SEC_DEFAULT if OLD_SUNXI_KERNEL_COMPAT
 
@@ -229,7 +223,6 @@ config MACH_SUN7I
 	select SPL_ARMV7_SET_CORTEX_SMPEN
 	select DRAM_SUN4I
 	select SUNXI_GEN_SUN4I
-	select SUPPORT_SPL
 	select ARMV7_BOOT_SEC_DEFAULT if OLD_SUNXI_KERNEL_COMPAT
 	imply SPL_SYS_I2C_LEGACY
 	imply SYS_I2C_LEGACY
@@ -243,7 +236,6 @@ config MACH_SUN8I_A23
 	select DRAM_SUN8I_A23
 	select SPL_I2C
 	select SUNXI_GEN_SUN6I
-	select SUPPORT_SPL
 	select SYS_I2C_SUN8I_RSB
 	select ARMV7_BOOT_SEC_DEFAULT if OLD_SUNXI_KERNEL_COMPAT
 
@@ -256,7 +248,6 @@ config MACH_SUN8I_A33
 	select DRAM_SUN8I_A33
 	select SPL_I2C
 	select SUNXI_GEN_SUN6I
-	select SUPPORT_SPL
 	select SYS_I2C_SUN8I_RSB
 	select ARMV7_BOOT_SEC_DEFAULT if OLD_SUNXI_KERNEL_COMPAT
 
@@ -268,7 +259,6 @@ config MACH_SUN8I_A83T
 	select SUNXI_GEN_SUN6I
 	select MMC_SUNXI_HAS_NEW_MODE
 	select MMC_SUNXI_HAS_MODE_SWITCH
-	select SUPPORT_SPL
 	select SYS_I2C_SUN8I_RSB
 
 config MACH_SUN8I_H3
@@ -288,7 +278,6 @@ config MACH_SUN8I_R40
 	select ARCH_SUPPORT_PSCI
 	select SUNXI_GEN_SUN6I
 	select MMC_SUNXI_HAS_NEW_MODE
-	select SUPPORT_SPL
 	select SUNXI_DRAM_DW
 	select SUNXI_DRAM_DW_32BIT
 	imply SPL_SYS_I2C_LEGACY
@@ -302,7 +291,6 @@ config MACH_SUN8I_V3S
 	select SUNXI_GEN_SUN6I
 	select SUNXI_DRAM_DW
 	select SUNXI_DRAM_DW_16BIT
-	select SUPPORT_SPL
 	select ARMV7_BOOT_SEC_DEFAULT if OLD_SUNXI_KERNEL_COMPAT
 
 config MACH_SUN9I
@@ -313,7 +301,6 @@ config MACH_SUN9I
 	select SPL_I2C
 	select SUN6I_PRCM
 	select SUNXI_GEN_SUN6I
-	select SUPPORT_SPL
 
 config MACH_SUN50I
 	bool "sun50i (Allwinner A64)"
@@ -322,7 +309,6 @@ config MACH_SUN50I
 	select SUNXI_DE2
 	select SUNXI_GEN_SUN6I
 	select MMC_SUNXI_HAS_NEW_MODE
-	select SUPPORT_SPL
 	select SUNXI_DRAM_DW
 	select SUNXI_DRAM_DW_32BIT
 	select FIT
--- a/board/sunxi/Kconfig
+++ b/board/sunxi/Kconfig
@@ -18,6 +18,7 @@ config BOARD_SUNXI
 	select SPL_SEPARATE_BSS if SPL
 	select SUNXI_GPIO if GPIO
 	select SYS_NS16550 if SERIAL
+	select SUPPORT_SPL
 	select SYS_RELOC_GD_ENV_ADDR
 	select USB if DISTRO_DEFAULTS
 	select USB_KEYBOARD if DISTRO_DEFAULTS && USB_HOST
@@ -31,6 +32,7 @@ config BOARD_SUNXI
 	imply FIT
 	imply OF_LIBFDT_OVERLAY
 	imply PRE_CONSOLE_BUFFER
+	imply SPL
 	imply SPL_GPIO
 	imply SPL_LIBCOMMON_SUPPORT
 	imply SPL_LIBGENERIC_SUPPORT
