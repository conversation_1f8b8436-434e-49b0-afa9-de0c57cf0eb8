From fdf871a6089ee2f56439880b69d33a7d0d707d15 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sat, 28 Aug 2021 22:24:28 -0500
Subject: [PATCH 20/90] pinctrl: sunxi: Add support for the D1

Signed-off-by: <PERSON> <<EMAIL>>
---
 drivers/pinctrl/sunxi/Kconfig         |  5 +++++
 drivers/pinctrl/sunxi/pinctrl-sunxi.c | 31 +++++++++++++++++++++++++++
 2 files changed, 36 insertions(+)

--- a/drivers/pinctrl/sunxi/Kconfig
+++ b/drivers/pinctrl/sunxi/Kconfig
@@ -89,6 +89,11 @@ config PINCTRL_SUN9I_A80_R
 	default MACH_SUN9I
 	select PINCTRL_SUNXI
 
+config PINCTRL_SUN20I_D1
+	bool "Support for the Allwinner D1 PIO"
+	default TARGET_SUN20I_D1
+	select PINCTRL_SUNXI
+
 config PINCTRL_SUN50I_A64
 	bool "Support for the Allwinner A64 PIO"
 	default MACH_SUN50I
--- a/drivers/pinctrl/sunxi/pinctrl-sunxi.c
+++ b/drivers/pinctrl/sunxi/pinctrl-sunxi.c
@@ -588,6 +588,31 @@ static const struct sunxi_pinctrl_desc _
 	.num_banks	= 3,
 };
 
+static const struct sunxi_pinctrl_function sun20i_d1_pinctrl_functions[] = {
+	{ "emac",	8 },	/* PE0-PE15 */
+	{ "gpio_in",	0 },
+	{ "gpio_out",	1 },
+	{ "i2c0",	4 },	/* PB10-PB11 */
+	{ "mmc0",	2 },	/* PF0-PF5 */
+	{ "mmc1",	2 },	/* PG0-PG5 */
+	{ "mmc2",	3 },	/* PC2-PC7 */
+	{ "spi0",	2 },	/* PC2-PC7 */
+#if IS_ENABLED(CONFIG_UART0_PORT_F)
+	{ "uart0",	3 },	/* PF2-PF4 */
+#else
+	{ "uart0",	6 },	/* PB8-PB9 */
+#endif
+	{ "uart1",	2 },	/* PG6-PG7 */
+	{ "uart2",	7 },	/* PB0-PB1 */
+};
+
+static const struct sunxi_pinctrl_desc __maybe_unused sun20i_d1_pinctrl_desc = {
+	.functions	= sun20i_d1_pinctrl_functions,
+	.num_functions	= ARRAY_SIZE(sun20i_d1_pinctrl_functions),
+	.first_bank	= SUNXI_GPIO_A,
+	.num_banks	= 7,
+};
+
 static const struct sunxi_pinctrl_function sun50i_a64_pinctrl_functions[] = {
 	{ "emac",	4 },	/* PD8-PD23 */
 	{ "gpio_in",	0 },
@@ -849,6 +874,12 @@ static const struct udevice_id sunxi_pin
 		.data = (ulong)&sun9i_a80_r_pinctrl_desc,
 	},
 #endif
+#ifdef CONFIG_PINCTRL_SUN20I_D1
+	{
+		.compatible = "allwinner,sun20i-d1-pinctrl",
+		.data = (ulong)&sun20i_d1_pinctrl_desc,
+	},
+#endif
 #ifdef CONFIG_PINCTRL_SUN50I_A64
 	{
 		.compatible = "allwinner,sun50i-a64-pinctrl",
