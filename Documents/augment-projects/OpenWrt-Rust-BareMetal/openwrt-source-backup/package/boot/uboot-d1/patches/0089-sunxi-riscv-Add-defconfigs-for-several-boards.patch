From 0a4e7a1a19dd505bc5b6ff887b2ce39983aaa92d Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Mon, 31 Oct 2022 23:27:08 -0500
Subject: [PATCH 89/90] sunxi: riscv: Add defconfigs for several boards

Signed-off-by: <PERSON> <<EMAIL>>
---
 configs/devterm_r_01_defconfig       | 10 ++++++++++
 configs/dongshan_nezha_stu_defconfig | 13 +++++++++++++
 configs/lichee_rv_86_panel_defconfig | 13 +++++++++++++
 configs/lichee_rv_dock_defconfig     | 10 ++++++++++
 configs/mangopi_mq_pro_defconfig     | 10 ++++++++++
 configs/nezha_defconfig              | 13 +++++++++++++
 6 files changed, 69 insertions(+)
 create mode 100644 configs/devterm_r_01_defconfig
 create mode 100644 configs/dongshan_nezha_stu_defconfig
 create mode 100644 configs/lichee_rv_86_panel_defconfig
 create mode 100644 configs/lichee_rv_dock_defconfig
 create mode 100644 configs/mangopi_mq_pro_defconfig
 create mode 100644 configs/nezha_defconfig

--- /dev/null
+++ b/configs/devterm_r_01_defconfig
@@ -0,0 +1,10 @@
+CONFIG_RISCV=y
+CONFIG_DEFAULT_DEVICE_TREE="sun20i-d1-devterm-v3.14"
+CONFIG_TARGET_SUN20I_D1=y
+CONFIG_ARCH_RV64I=y
+CONFIG_RISCV_SMODE=y
+# CONFIG_SPL_SMP is not set
+CONFIG_SYS_SPL_MALLOC=y
+CONFIG_SPL_OPENSBI_SCRATCH_OPTIONS=0x0
+# CONFIG_SYS_I2C_MVTWSI is not set
+CONFIG_DM_REGULATOR_FIXED=y
--- /dev/null
+++ b/configs/dongshan_nezha_stu_defconfig
@@ -0,0 +1,13 @@
+CONFIG_RISCV=y
+CONFIG_DEFAULT_DEVICE_TREE="sun20i-d1-dongshan-nezha-stu"
+CONFIG_TARGET_SUN20I_D1=y
+CONFIG_ARCH_RV64I=y
+CONFIG_RISCV_SMODE=y
+# CONFIG_SPL_SMP is not set
+CONFIG_SYS_SPL_MALLOC=y
+CONFIG_SPL_OPENSBI_SCRATCH_OPTIONS=0x0
+CONFIG_NET_RANDOM_ETHADDR=y
+# CONFIG_SYS_I2C_MVTWSI is not set
+CONFIG_PHY_REALTEK=y
+CONFIG_SUN8I_EMAC=y
+CONFIG_DM_REGULATOR_FIXED=y
--- /dev/null
+++ b/configs/lichee_rv_86_panel_defconfig
@@ -0,0 +1,13 @@
+CONFIG_RISCV=y
+CONFIG_DEFAULT_DEVICE_TREE="sun20i-d1-lichee-rv-86-panel-480p"
+CONFIG_TARGET_SUN20I_D1=y
+CONFIG_ARCH_RV64I=y
+CONFIG_RISCV_SMODE=y
+# CONFIG_SPL_SMP is not set
+CONFIG_SYS_SPL_MALLOC=y
+CONFIG_SPL_OPENSBI_SCRATCH_OPTIONS=0x0
+CONFIG_NET_RANDOM_ETHADDR=y
+# CONFIG_SYS_I2C_MVTWSI is not set
+CONFIG_PHY_REALTEK=y
+CONFIG_SUN8I_EMAC=y
+CONFIG_DM_REGULATOR_FIXED=y
--- /dev/null
+++ b/configs/lichee_rv_dock_defconfig
@@ -0,0 +1,10 @@
+CONFIG_RISCV=y
+CONFIG_DEFAULT_DEVICE_TREE="sun20i-d1-lichee-rv-dock"
+CONFIG_TARGET_SUN20I_D1=y
+CONFIG_ARCH_RV64I=y
+CONFIG_RISCV_SMODE=y
+# CONFIG_SPL_SMP is not set
+CONFIG_SYS_SPL_MALLOC=y
+CONFIG_SPL_OPENSBI_SCRATCH_OPTIONS=0x0
+# CONFIG_SYS_I2C_MVTWSI is not set
+CONFIG_DM_REGULATOR_FIXED=y
--- /dev/null
+++ b/configs/mangopi_mq_pro_defconfig
@@ -0,0 +1,10 @@
+CONFIG_RISCV=y
+CONFIG_DEFAULT_DEVICE_TREE="sun20i-d1-mangopi-mq-pro"
+CONFIG_TARGET_SUN20I_D1=y
+CONFIG_ARCH_RV64I=y
+CONFIG_RISCV_SMODE=y
+# CONFIG_SPL_SMP is not set
+CONFIG_SYS_SPL_MALLOC=y
+CONFIG_SPL_OPENSBI_SCRATCH_OPTIONS=0x0
+# CONFIG_SYS_I2C_MVTWSI is not set
+CONFIG_DM_REGULATOR_FIXED=y
--- /dev/null
+++ b/configs/nezha_defconfig
@@ -0,0 +1,13 @@
+CONFIG_RISCV=y
+CONFIG_DEFAULT_DEVICE_TREE="sun20i-d1-nezha"
+CONFIG_TARGET_SUN20I_D1=y
+CONFIG_ARCH_RV64I=y
+CONFIG_RISCV_SMODE=y
+# CONFIG_SPL_SMP is not set
+CONFIG_SYS_SPL_MALLOC=y
+CONFIG_SPL_OPENSBI_SCRATCH_OPTIONS=0x0
+CONFIG_NET_RANDOM_ETHADDR=y
+# CONFIG_SYS_I2C_MVTWSI is not set
+CONFIG_PHY_REALTEK=y
+CONFIG_SUN8I_EMAC=y
+CONFIG_DM_REGULATOR_FIXED=y
