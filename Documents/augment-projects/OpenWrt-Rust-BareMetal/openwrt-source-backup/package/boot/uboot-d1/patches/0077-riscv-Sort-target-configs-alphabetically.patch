From 35da34adec7b5b06ad81455a21c67a9c1152e2c9 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sat, 7 Aug 2021 12:09:35 -0500
Subject: [PATCH 77/90] riscv: Sort target configs alphabetically

Signed-off-by: <PERSON> <<EMAIL>>
---
 arch/riscv/Kconfig | 8 ++++----
 1 file changed, 4 insertions(+), 4 deletions(-)

--- a/arch/riscv/Kconfig
+++ b/arch/riscv/Kconfig
@@ -14,6 +14,9 @@ config TARGET_AX25_AE350
 config TARGET_MICROCHIP_ICICLE
 	bool "Support Microchip PolarFire-SoC Icicle Board"
 
+config TARGET_OPENPITON_RISCV64
+	bool "Support RISC-V cores on OpenPiton SoC"
+
 config TARGET_QEMU_VIRT
 	bool "Support QEMU Virt Board"
 
@@ -28,9 +31,6 @@ config TARGET_SIPEED_MAIX
 	bool "Support Sipeed Maix Board"
 	select SYS_CACHE_SHIFT_6
 
-config TARGET_OPENPITON_RISCV64
-	bool "Support RISC-V cores on OpenPiton SoC"
-
 endchoice
 
 config SYS_ICACHE_OFF
@@ -61,9 +61,9 @@ config SPL_SYS_DCACHE_OFF
 source "board/AndesTech/ax25-ae350/Kconfig"
 source "board/emulation/qemu-riscv/Kconfig"
 source "board/microchip/mpfs_icicle/Kconfig"
+source "board/openpiton/riscv64/Kconfig"
 source "board/sifive/unleashed/Kconfig"
 source "board/sifive/unmatched/Kconfig"
-source "board/openpiton/riscv64/Kconfig"
 source "board/sipeed/maix/Kconfig"
 
 # platform-specific options below
