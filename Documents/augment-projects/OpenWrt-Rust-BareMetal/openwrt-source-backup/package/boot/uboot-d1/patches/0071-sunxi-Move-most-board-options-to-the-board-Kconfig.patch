From 6a1b660a83b262237b6bebed26e44db923a86a6b Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON>ERPA<PERSON> <<EMAIL>>
Date: Tue, 6 Jun 2023 18:09:19 +0000
Subject: [PATCH 71/90] sunxi: Move most board options to the board Kconfig

This excludes options that are inherently ARM-specific or are specific
to legacy non-DM drivers.

Some help text is cleaned up along the way.

Signed-off-by: <PERSON> <<EMAIL>>
Signed-off-by: <PERSON><PERSON><PERSON> HERPAI <<EMAIL>>
---
 arch/arm/mach-sunxi/Kconfig | 71 ------------------------------------
 board/sunxi/Kconfig         | 72 +++++++++++++++++++++++++++++++++++++
 2 files changed, 72 insertions(+), 71 deletions(-)

--- a/arch/arm/mach-sunxi/Kconfig
+++ b/arch/arm/mach-sunxi/Kconfig
@@ -548,16 +548,6 @@ config DRAM_ODT_CORRECTION
 	then the correction is negative. Usually the value for this is 0.
 endif
 
-config UART0_PORT_F
-	bool "UART0 on MicroSD breakout board"
-	---help---
-	Repurpose the SD card slot for getting access to the UART0 serial
-	console. Primarily useful only for low level u-boot debugging on
-	tablets, where normal UART0 is difficult to access and requires
-	device disassembly and/or soldering. As the SD card can't be used
-	at the same time, the system can be only booted in the FEL mode.
-	Only enable this if you really know what you are doing.
-
 config OLD_SUNXI_KERNEL_COMPAT
 	bool "Enable workarounds for booting old kernels"
 	---help---
@@ -571,20 +561,6 @@ config MACPWR
 	  Set the pin used to power the MAC. This takes a string in the format
 	  understood by sunxi_name_to_gpio, e.g. PH1 for pin 1 of port H.
 
-config MMC1_PINS_PH
-	bool "Pins for mmc1 are on Port H"
-	depends on MACH_SUN4I || MACH_SUN7I || MACH_SUN8I_R40
-	---help---
-	Select this option for boards where mmc1 uses the Port H pinmux.
-
-config MMC_SUNXI_SLOT_EXTRA
-	int "mmc extra slot number"
-	default -1
-	---help---
-	sunxi builds always enable mmc0, some boards also have a second sdcard
-	slot or emmc on mmc1 - mmc3. Setting this to 1, 2 or 3 will enable
-	support for this.
-
 config I2C0_ENABLE
 	bool "Enable I2C/TWI controller 0"
 	default y if MACH_SUN4I || MACH_SUN5I || MACH_SUN7I || MACH_SUN8I_R40
@@ -612,16 +588,6 @@ config R_I2C_ENABLE
 	Set this to y to enable the I2C controller which is part of the PRCM.
 endif
 
-config AXP_DISABLE_BOOT_ON_POWERON
-	bool "Disable device boot on power plug-in"
-	depends on AXP209_POWER || AXP221_POWER || AXP809_POWER || AXP818_POWER
-	default n
-	---help---
-	  Say Y here to prevent the device from booting up because of a plug-in
-	  event. When set, the device will boot into the SPL briefly to
-	  determine why it was powered on, and if it was determined because of
-	  a plug-in event instead of a button press event it will shut back off.
-
 config VIDEO_SUNXI
 	bool "Enable graphical uboot console on HDMI, LCD or VGA"
 	depends on !MACH_SUN8I_A83T
@@ -850,41 +816,4 @@ config SPL_SPI_SUNXI
 	  sunxi SPI Flash. It uses the same method as the boot ROM, so does
 	  not need any extra configuration.
 
-config PINE64_DT_SELECTION
-	bool "Enable Pine64 device tree selection code"
-	depends on MACH_SUN50I
-	help
-	  The original Pine A64 and Pine A64+ are similar but different
-	  boards and can be differed by the DRAM size. Pine A64 has
-	  512MiB DRAM, and Pine A64+ has 1GiB or 2GiB. By selecting this
-	  option, the device tree selection code specific to Pine64 which
-	  utilizes the DRAM size will be enabled.
-
-config PINEPHONE_DT_SELECTION
-	bool "Enable PinePhone device tree selection code"
-	depends on MACH_SUN50I
-	help
-	  Enable this option to automatically select the device tree for the
-	  correct PinePhone hardware revision during boot.
-
-config BLUETOOTH_DT_DEVICE_FIXUP
-	string "Fixup the Bluetooth controller address"
-	default ""
-	help
-	  This option specifies the DT compatible name of the Bluetooth
-	  controller for which to set the "local-bd-address" property.
-	  Set this option if your device ships with the Bluetooth controller
-	  default address.
-	  The used address is "bdaddr" if set, and "ethaddr" with the LSB
-	  flipped elsewise.
-
 endif
-
-config CHIP_DIP_SCAN
-	bool "Enable DIPs detection for CHIP board"
-	select SUPPORT_EXTENSION_SCAN
-	select W1
-	select W1_GPIO
-	select W1_EEPROM
-	select W1_EEPROM_DS24XXX
-	select CMD_EXTENSION
--- a/board/sunxi/Kconfig
+++ b/board/sunxi/Kconfig
@@ -171,6 +171,78 @@ config SPL_IMAGE_TYPE
 	default "sunxi_egon" if SPL_IMAGE_TYPE_SUNXI_EGON
 	default "sunxi_toc0" if SPL_IMAGE_TYPE_SUNXI_TOC0
 
+config MMC_SUNXI_SLOT_EXTRA
+	int "MMC extra slot number"
+	default -1
+	help
+	  sunxi builds always enable mmc0. Some boards also have a
+	  second SD card slot or eMMC on mmc1 - mmc3. Setting this to 1,
+	  2 or 3 will enable support for this.
+
+config MMC1_PINS_PH
+	bool "MMC1 pins are on Port H"
+	depends on MACH_SUN4I || MACH_SUN7I || MACH_SUN8I_R40
+	help
+	  Select this option on boards where mmc1 uses the Port H pinmux.
+
+config UART0_PORT_F
+	bool "UART0 pins are on Port F (MicroSD breakout board)"
+	help
+	  Repurpose the SD card slot for getting access to the UART0
+	  serial console. Primarily useful only for low level u-boot
+	  debugging on tablets, where normal UART0 is difficult to
+	  access and requires device disassembly and/or soldering. As
+	  the SD card can't be used at the same time, the system can be
+	  only booted in FEL mode. Only enable this if you really know
+	  what you are doing.
+
+config AXP_DISABLE_BOOT_ON_POWERON
+	bool "Disable device boot on power plug-in"
+	depends on AXP209_POWER || AXP221_POWER || AXP809_POWER || AXP818_POWER
+	help
+	  Say Y here to prevent the device from booting up because of a
+	  plug-in event. When set, the device will boot into the SPL
+	  briefly to determine why it was powered on, and if the board
+	  was powered on because of a plug-in event instead of a button
+	  press event, it will shut back off.
+
+config CHIP_DIP_SCAN
+	bool "Enable DIPs detection for CHIP board"
+	select SUPPORT_EXTENSION_SCAN
+	select W1
+	select W1_GPIO
+	select W1_EEPROM
+	select W1_EEPROM_DS24XXX
+	select CMD_EXTENSION
+
+config PINE64_DT_SELECTION
+	bool "Enable Pine64 device tree selection code"
+	depends on MACH_SUN50I
+	help
+	  The original Pine A64 and Pine A64+ are similar but different
+	  boards and can be differed by the DRAM size. Pine A64 has
+	  512MiB DRAM, and Pine A64+ has 1GiB or 2GiB. By selecting this
+	  option, the device tree selection code specific to Pine64 which
+	  utilizes the DRAM size will be enabled.
+
+config PINEPHONE_DT_SELECTION
+	bool "Enable PinePhone device tree selection code"
+	depends on MACH_SUN50I
+	help
+	  Enable this option to automatically select the device tree for the
+	  correct PinePhone hardware revision during boot.
+
+config BLUETOOTH_DT_DEVICE_FIXUP
+	string "Fixup the Bluetooth controller address"
+	default ""
+	help
+	  This option specifies the DT compatible name of the Bluetooth
+	  controller for which to set the "local-bd-address" property.
+	  Set this option if your device ships with the Bluetooth controller
+	  default address.
+	  The used address is "bdaddr" if set, and "ethaddr" with the LSB
+	  flipped elsewise.
+
 endmenu
 
 endif
