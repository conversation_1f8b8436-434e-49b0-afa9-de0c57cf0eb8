From 4993c31d314d5c7ebad1d08e3e3f79694dca8738 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sat, 11 Sep 2021 23:12:06 -0500
Subject: [PATCH 85/90] spi: sunxi: Add support for the D1

Signed-off-by: <PERSON> <<EMAIL>>
---
 drivers/spi/spi-sunxi.c | 4 ++++
 1 file changed, 4 insertions(+)

--- a/drivers/spi/spi-sunxi.c
+++ b/drivers/spi/spi-sunxi.c
@@ -558,6 +558,10 @@ static const struct udevice_id sun4i_spi
 	  .compatible = "allwinner,sun8i-h3-spi",
 	  .data = (ulong)&sun8i_h3_spi_variant,
 	},
+	{
+	  .compatible = "allwinner,sun50i-r329-spi",
+	  .data = (ulong)&sun8i_h3_spi_variant,
+	},
 	{ /* sentinel */ }
 };
 
