From 2fdd94449c2668b4ff69326ff8d5daabdf2c9f00 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sat, 9 Oct 2021 15:04:16 -0500
Subject: [PATCH 39/90] arm: dts: sunxi: h3: Add nodes for AR100 remoteproc

Signed-off-by: <PERSON> <<EMAIL>>
---
 arch/arm/dts/sun8i-h3.dtsi | 14 ++++++++++++++
 1 file changed, 14 insertions(+)

--- a/arch/arm/dts/sun8i-h3.dtsi
+++ b/arch/arm/dts/sun8i-h3.dtsi
@@ -170,6 +170,14 @@
 			#size-cells = <1>;
 			ranges;
 
+			sram_a2: sram@40000 {
+				compatible = "mmio-sram";
+				reg = <0x00040000 0xc000>;
+				#address-cells = <1>;
+				#size-cells = <1>;
+				ranges = <0 0x00040000 0xc000>;
+			};
+
 			sram_c: sram@1d00000 {
 				compatible = "mmio-sram";
 				reg = <0x01d00000 0x80000>;
@@ -239,6 +247,12 @@
 			nvmem-cell-names = "calibration";
 			#thermal-sensor-cells = <0>;
 		};
+
+		remoteproc@1f01c00 {
+			compatible = "allwinner,sun6i-a31-ar100";
+			reg = <0x01f01c00 0x400>;
+			sram = <&sram_a2>;
+		};
 	};
 
 	thermal-zones {
