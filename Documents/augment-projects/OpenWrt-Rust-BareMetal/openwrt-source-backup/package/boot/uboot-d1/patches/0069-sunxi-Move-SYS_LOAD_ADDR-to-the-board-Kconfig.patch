From 03f3ba82e9dfb67f1ae0812f72aea6559aa61bb4 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 5 Aug 2022 23:23:34 -0500
Subject: [PATCH 69/90] sunxi: Move SYS_LOAD_ADDR to the board Kconfig

This will provide a default value for RISC-V when that is added, and it
makes sense to put this option next to the other DRAM layout options.

Signed-off-by: <PERSON> <<EMAIL>>
---
 Kconfig             | 3 ---
 board/sunxi/Kconfig | 5 +++++
 2 files changed, 5 insertions(+), 3 deletions(-)

--- a/Kconfig
+++ b/Kconfig
@@ -508,9 +508,6 @@ config SYS_LOAD_ADDR
 	hex "Address in memory to use by default"
 	default 0x01000000 if ARCH_SOCFPGA
 	default 0x02000000 if PPC || X86
-	default 0x81000000 if MACH_SUNIV
-	default 0x22000000 if MACH_SUN9I
-	default 0x42000000 if ARCH_SUNXI
 	default 0x82000000 if ARCH_KEYSTONE || ARCH_OMAP2PLUS || ARCH_K3
 	default 0x82000000 if ARCH_MX6 && (MX6SL || MX6SLL  || MX6SX || MX6UL || MX6ULL)
 	default 0x12000000 if ARCH_MX6 && !(MX6SL || MX6SLL  || MX6SX || MX6UL || MX6ULL)
--- a/board/sunxi/Kconfig
+++ b/board/sunxi/Kconfig
@@ -129,6 +129,11 @@ config SYS_CONFIG_NAME
 	default "sun50i" if MACH_SUN50I_H6
 	default "sun50i" if MACH_SUN50I_H616
 
+config SYS_LOAD_ADDR
+	default 0x81000000 if MACH_SUNIV
+	default 0x22000000 if MACH_SUN9I
+	default 0x42000000
+
 config SYS_SOC
 	default "sunxi"
 
