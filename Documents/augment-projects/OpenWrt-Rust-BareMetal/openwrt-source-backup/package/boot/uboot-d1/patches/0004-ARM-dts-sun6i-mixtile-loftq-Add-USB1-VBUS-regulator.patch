From 40a0ec0fdb6a110d69151de5480148772877f267 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Thu, 26 Aug 2021 20:39:33 -0500
Subject: [PATCH 04/90] ARM: dts: sun6i: mixtile-loftq: Add USB1 VBUS regulator

This board is configured with CONFIG_USB1_VBUS_PIN="PH24", but no
regulator exists in its device tree. Add the regulator, so USB will
continue to work when the PHY driver switches to using the regulator
uclass instead of a GPIO.

Update the device tree here because it does not exist in Linux.

Signed-off-by: <PERSON> <<EMAIL>>
---
 arch/arm/dts/sun6i-a31-mixtile-loftq.dts | 17 +++++++++++++++++
 1 file changed, 17 insertions(+)

--- a/arch/arm/dts/sun6i-a31-mixtile-loftq.dts
+++ b/arch/arm/dts/sun6i-a31-mixtile-loftq.dts
@@ -6,6 +6,9 @@
  */
 
 /dts-v1/;
+
+#include <dt-bindings/gpio/gpio.h>
+
 #include "sun6i-a31.dtsi"
 
 / {
@@ -19,6 +22,15 @@
 	chosen {
 		stdout-path = "serial0:115200n8";
 	};
+
+	reg_usb1_vbus: usb1-vbus {
+		compatible = "regulator-fixed";
+		regulator-name = "usb1-vbus";
+		regulator-min-microvolt = <5000000>;
+		regulator-max-microvolt = <5000000>;
+		enable-active-high;
+		gpio = <&pio 7 24 GPIO_ACTIVE_HIGH>; /* PH24 */
+	};
 };
 
 &ehci0 {
@@ -56,3 +68,8 @@
 	pinctrl-0 = <&uart0_ph_pins>;
 	status = "okay";
 };
+
+&usbphy {
+	usb1_vbus-supply = <&reg_usb1_vbus>;
+	status = "okay";
+};
