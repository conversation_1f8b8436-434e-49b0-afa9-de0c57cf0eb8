From 7be2405244565973cff0a40196bbed08df90f6a3 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Mon, 16 May 2022 00:31:36 -0500
Subject: [PATCH 28/90] pinctrl: sunxi: Add NAND pinmuxes

NAND is always at function 2 on port C.

Pin lists and mux values were taken from the Linux drivers.

Signed-off-by: <PERSON> <<EMAIL>>
---
 drivers/pinctrl/sunxi/pinctrl-sunxi.c | 13 +++++++++++++
 1 file changed, 13 insertions(+)

--- a/drivers/pinctrl/sunxi/pinctrl-sunxi.c
+++ b/drivers/pinctrl/sunxi/pinctrl-sunxi.c
@@ -269,6 +269,7 @@ static const struct sunxi_pinctrl_functi
 #endif
 	{ "mmc2",	3 },	/* PC6-PC15 */
 	{ "mmc3",	2 },	/* PI4-PI9 */
+	{ "nand0",	2 },	/* PC0-PC24 */
 	{ "spi0",	3 },	/* PC0-PC2, PC23 */
 #if IS_ENABLED(CONFIG_UART0_PORT_F)
 	{ "uart0",	4 },	/* PF2-PF4 */
@@ -293,6 +294,7 @@ static const struct sunxi_pinctrl_functi
 	{ "mmc0",	2 },	/* PF0-PF5 */
 	{ "mmc1",	2 },	/* PG3-PG8 */
 	{ "mmc2",	3 },	/* PC6-PC15 */
+	{ "nand0",	2 },	/* PC0-PC19 */
 	{ "spi0",	3 },	/* PC0-PC3 */
 #if IS_ENABLED(CONFIG_UART0_PORT_F)
 	{ "uart0",	4 },	/* PF2-PF4 */
@@ -319,6 +321,7 @@ static const struct sunxi_pinctrl_functi
 	{ "mmc1",	2 },	/* PG0-PG5 */
 	{ "mmc2",	3 },	/* PC6-PC15, PC24 */
 	{ "mmc3",	4 },	/* PC6-PC15, PC24 */
+	{ "nand0",	2 },	/* PC0-PC26 */
 	{ "spi0",	3 },	/* PC0-PC2, PC27 */
 #if IS_ENABLED(CONFIG_UART0_PORT_F)
 	{ "uart0",	3 },	/* PF2-PF4 */
@@ -363,6 +366,7 @@ static const struct sunxi_pinctrl_functi
 	{ "mmc1",	4 },	/* PG0-PG5 */
 #endif
 	{ "mmc2",	3 },	/* PC5-PC15, PC24 */
+	{ "nand0",	2 },	/* PC0-PC24 */
 	{ "spi0",	3 },	/* PC0-PC2, PC23 */
 #if IS_ENABLED(CONFIG_UART0_PORT_F)
 	{ "uart0",	4 },	/* PF2-PF4 */
@@ -386,6 +390,7 @@ static const struct sunxi_pinctrl_functi
 	{ "mmc0",	2 },	/* PF0-PF5 */
 	{ "mmc1",	2 },	/* PG0-PG5 */
 	{ "mmc2",	3 },	/* PC5-PC16 */
+	{ "nand0",	2 },	/* PC0-PC16 */
 	{ "spi0",	3 },	/* PC0-PC3 */
 #if IS_ENABLED(CONFIG_UART0_PORT_F)
 	{ "uart0",	3 },	/* PF2-PF4 */
@@ -424,6 +429,7 @@ static const struct sunxi_pinctrl_functi
 	{ "mmc0",	2 },	/* PF0-PF5 */
 	{ "mmc1",	2 },	/* PG0-PG5 */
 	{ "mmc2",	3 },	/* PC5-PC16 */
+	{ "nand0",	2 },	/* PC0-PC16 */
 	{ "spi0",	3 },	/* PC0-PC3 */
 #if IS_ENABLED(CONFIG_UART0_PORT_F)
 	{ "uart0",	3 },	/* PF2-PF4 */
@@ -450,6 +456,7 @@ static const struct sunxi_pinctrl_functi
 	{ "mmc0",	2 },	/* PF0-PF5 */
 	{ "mmc1",	2 },	/* PG0-PG5 */
 	{ "mmc2",	3 },	/* PC5-PC16 */
+	{ "nand0",	2 },	/* PC0-PC18 */
 	{ "spi0",	3 },	/* PC0-PC3 */
 #if IS_ENABLED(CONFIG_UART0_PORT_F)
 	{ "uart0",	3 },	/* PF2-PF4 */
@@ -491,6 +498,7 @@ static const struct sunxi_pinctrl_functi
 	{ "mmc0",	2 },	/* PF0-PF5 */
 	{ "mmc1",	2 },	/* PG0-PG5 */
 	{ "mmc2",	3 },	/* PC5-PC16 */
+	{ "nand0",	2 },	/* PC0-PC16 */
 	{ "spi0",	3 },	/* PC0-PC3 */
 #if IS_ENABLED(CONFIG_UART0_PORT_F)
 	{ "uart0",	3 },	/* PF2-PF4 */
@@ -557,6 +565,7 @@ static const struct sunxi_pinctrl_functi
 	{ "mmc0",	2 },	/* PF0-PF5 */
 	{ "mmc1",	2 },	/* PG0-PG5 */
 	{ "mmc2",	3 },	/* PC6-PC16 */
+	{ "nand0",	2 },	/* PC0-PC18 */
 	{ "spi0",	3 },	/* PC0-PC2, PC19 */
 #if IS_ENABLED(CONFIG_UART0_PORT_F)
 	{ "uart0",	4 },	/* PF2-PF4 */
@@ -622,6 +631,7 @@ static const struct sunxi_pinctrl_functi
 	{ "mmc0",	2 },	/* PF0-PF5 */
 	{ "mmc1",	2 },	/* PG0-PG5 */
 	{ "mmc2",	3 },	/* PC1-PC16 */
+	{ "nand0",	2 },	/* PC0-PC16 */
 	{ "pwm",	2 },	/* PD22 */
 	{ "spi0",	4 },	/* PC0-PC3 */
 #if IS_ENABLED(CONFIG_UART0_PORT_F)
@@ -664,6 +674,7 @@ static const struct sunxi_pinctrl_functi
 	{ "mmc0",	2 },	/* PF0-PF5 */
 	{ "mmc1",	2 },	/* PG0-PG5 */
 	{ "mmc2",	3 },	/* PC1-PC16 */
+	{ "nand0",	2 },	/* PC0-PC16 */
 	{ "spi0",	3 },	/* PC0-PC3 */
 #if IS_ENABLED(CONFIG_UART0_PORT_F)
 	{ "uart0",	3 },	/* PF2-PF4 */
@@ -690,6 +701,7 @@ static const struct sunxi_pinctrl_functi
 	{ "mmc0",	2 },	/* PF0-PF5 */
 	{ "mmc1",	2 },	/* PG0-PG5 */
 	{ "mmc2",	3 },	/* PC1-PC14 */
+	{ "nand0",	2 },	/* PC0-PC16 */
 	{ "spi0",	4 },	/* PC0-PC7 */
 #if IS_ENABLED(CONFIG_UART0_PORT_F)
 	{ "uart0",	3 },	/* PF2-PF4 */
@@ -728,6 +740,7 @@ static const struct sunxi_pinctrl_functi
 	{ "mmc0",	2 },	/* PF0-PF5 */
 	{ "mmc1",	2 },	/* PG0-PG5 */
 	{ "mmc2",	3 },	/* PC0-PC16 */
+	{ "nand0",	2 },	/* PC0-PC16 */
 	{ "spi0",	4 },	/* PC0-PC7, PC15-PC16 */
 #if IS_ENABLED(CONFIG_UART0_PORT_F)
 	{ "uart0",	3 },	/* PF2-PF4 */
