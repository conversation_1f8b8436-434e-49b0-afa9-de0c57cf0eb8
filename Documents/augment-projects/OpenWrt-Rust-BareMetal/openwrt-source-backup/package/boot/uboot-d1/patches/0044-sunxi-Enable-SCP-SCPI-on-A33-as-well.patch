From 5a7a91bf9a78a3a73e26d6de975261e62f9fb127 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Wed, 8 Jun 2022 07:55:54 -0500
Subject: [PATCH 44/90] sunxi: Enable SCP/SCPI on A33 as well

Signed-off-by: <PERSON> <<EMAIL>>
---
 arch/arm/cpu/armv7/Kconfig           |  2 +-
 arch/arm/cpu/armv7/sunxi/Makefile    |  2 +-
 arch/arm/cpu/armv7/sunxi/psci-scpi.c |  4 ++++
 arch/arm/dts/sun8i-a23-a33.dtsi      | 14 ++++++++++++++
 arch/arm/dts/sunxi-u-boot.dtsi       |  4 ++--
 5 files changed, 22 insertions(+), 4 deletions(-)

--- a/arch/arm/cpu/armv7/Kconfig
+++ b/arch/arm/cpu/armv7/Kconfig
@@ -75,7 +75,7 @@ config ARMV7_PSCI
 choice
 	prompt "Supported PSCI version"
 	depends on ARMV7_PSCI
-	default ARMV7_PSCI_1_1 if MACH_SUN8I_H3
+	default ARMV7_PSCI_1_1 if MACH_SUN8I_A33 || MACH_SUN8I_H3
 	default ARMV7_PSCI_0_1 if ARCH_SUNXI
 	default ARMV7_PSCI_1_0
 	help
--- a/arch/arm/cpu/armv7/sunxi/Makefile
+++ b/arch/arm/cpu/armv7/sunxi/Makefile
@@ -13,7 +13,7 @@ obj-$(CONFIG_MACH_SUN6I)	+= sram.o
 obj-$(CONFIG_MACH_SUN8I)	+= sram.o
 
 ifndef CONFIG_SPL_BUILD
-ifdef CONFIG_MACH_SUN8I_H3
+ifneq ($(CONFIG_MACH_SUN8I_A33)$(CONFIG_MACH_SUN8I_H3),)
 obj-$(CONFIG_ARMV7_PSCI)	+= psci-scpi.o
 else
 obj-$(CONFIG_ARMV7_PSCI)	+= psci.o
--- a/arch/arm/cpu/armv7/sunxi/psci-scpi.c
+++ b/arch/arm/cpu/armv7/sunxi/psci-scpi.c
@@ -24,7 +24,11 @@
 #define MPIDR_AFFLVL0(mpidr)	(mpidr & 0xf)
 #define MPIDR_AFFLVL1(mpidr)	(mpidr >> 8 & 0xf)
 
+#if defined(CONFIG_MACH_SUN8I_H3)
 #define SCPI_SHMEM_BASE		0x0004be00
+#else
+#define SCPI_SHMEM_BASE		0x00053e00
+#endif
 #define SCPI_SHMEM		((struct scpi_shmem *)SCPI_SHMEM_BASE)
 
 #define SCPI_RX_CHANNEL		1
--- a/arch/arm/dts/sun8i-a23-a33.dtsi
+++ b/arch/arm/dts/sun8i-a23-a33.dtsi
@@ -138,6 +138,14 @@
 			#size-cells = <1>;
 			ranges;
 
+			sram_a2: sram@40000 {
+				compatible = "mmio-sram";
+				reg = <0x00040000 0x14000>;
+				#address-cells = <1>;
+				#size-cells = <1>;
+				ranges = <0 0x00040000 0x14000>;
+			};
+
 			sram_c: sram@1d00000 {
 				compatible = "mmio-sram";
 				reg = <0x01d00000 0x80000>;
@@ -847,5 +855,11 @@
 			#address-cells = <1>;
 			#size-cells = <0>;
 		};
+
+		remoteproc@1f01c00 {
+			compatible = "allwinner,sun6i-a31-ar100";
+			reg = <0x01f01c00 0x400>;
+			sram = <&sram_a2>;
+		};
 	};
 };
--- a/arch/arm/dts/sunxi-u-boot.dtsi
+++ b/arch/arm/dts/sunxi-u-boot.dtsi
@@ -6,11 +6,11 @@
 #define ARCH "arm"
 #endif
 
+#if defined(CONFIG_ARMV7_PSCI) && (defined(CONFIG_MACH_SUN8I_A33) || defined(CONFIG_MACH_SUN8I_H3))
 #if defined(CONFIG_MACH_SUN8I_H3)
-#ifdef CONFIG_ARMV7_PSCI
 #define RESUME_ADDR	SUNXI_RESUME_BASE
-#define SCP_ADDR	SUNXI_SCP_BASE
 #endif
+#define SCP_ADDR	SUNXI_SCP_BASE
 #elif defined(CONFIG_MACH_SUN50I) || defined(CONFIG_MACH_SUN50I_H5)
 #define BL31_ADDR	0x00044000
 #define SCP_ADDR	0x00050000
