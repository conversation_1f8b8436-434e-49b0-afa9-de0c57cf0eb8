From 2f48dfc23d612f6f1798ff761854fd3141d0671f Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sun, 15 May 2022 21:29:22 -0500
Subject: [PATCH 27/90] clk: sunxi: Add NAND clocks and resets

Currently NAND clock setup is done in board code, both in SPL and in
U-Boot proper. Add the NAND clocks/resets here so they can be used by
the "full" NAND driver once it is converted to the driver model.

The bit locations are copied from the Linux CCU drivers.

Signed-off-by: <PERSON> <<EMAIL>>
---
 drivers/clk/sunxi/clk_a10.c  | 2 ++
 drivers/clk/sunxi/clk_a10s.c | 2 ++
 drivers/clk/sunxi/clk_a23.c  | 3 +++
 drivers/clk/sunxi/clk_a31.c  | 6 ++++++
 drivers/clk/sunxi/clk_a64.c  | 3 +++
 drivers/clk/sunxi/clk_a80.c  | 8 ++++++++
 drivers/clk/sunxi/clk_a83t.c | 3 +++
 drivers/clk/sunxi/clk_h3.c   | 3 +++
 drivers/clk/sunxi/clk_h6.c   | 6 ++++++
 drivers/clk/sunxi/clk_h616.c | 6 ++++++
 drivers/clk/sunxi/clk_r40.c  | 3 +++
 11 files changed, 45 insertions(+)

--- a/drivers/clk/sunxi/clk_a10.c
+++ b/drivers/clk/sunxi/clk_a10.c
@@ -23,6 +23,7 @@ static struct ccu_clk_gate a10_gates[] =
 	[CLK_AHB_MMC1]		= GATE(0x060, BIT(9)),
 	[CLK_AHB_MMC2]		= GATE(0x060, BIT(10)),
 	[CLK_AHB_MMC3]		= GATE(0x060, BIT(11)),
+	[CLK_AHB_NAND]		= GATE(0x060, BIT(13)),
 	[CLK_AHB_EMAC]		= GATE(0x060, BIT(17)),
 	[CLK_AHB_SPI0]		= GATE(0x060, BIT(20)),
 	[CLK_AHB_SPI1]		= GATE(0x060, BIT(21)),
@@ -47,6 +48,7 @@ static struct ccu_clk_gate a10_gates[] =
 	[CLK_APB1_UART6]	= GATE(0x06c, BIT(22)),
 	[CLK_APB1_UART7]	= GATE(0x06c, BIT(23)),
 
+	[CLK_NAND]		= GATE(0x080, BIT(31)),
 	[CLK_SPI0]		= GATE(0x0a0, BIT(31)),
 	[CLK_SPI1]		= GATE(0x0a4, BIT(31)),
 	[CLK_SPI2]		= GATE(0x0a8, BIT(31)),
--- a/drivers/clk/sunxi/clk_a10s.c
+++ b/drivers/clk/sunxi/clk_a10s.c
@@ -20,6 +20,7 @@ static struct ccu_clk_gate a10s_gates[]
 	[CLK_AHB_MMC0]		= GATE(0x060, BIT(8)),
 	[CLK_AHB_MMC1]		= GATE(0x060, BIT(9)),
 	[CLK_AHB_MMC2]		= GATE(0x060, BIT(10)),
+	[CLK_AHB_NAND]		= GATE(0x060, BIT(13)),
 	[CLK_AHB_EMAC]		= GATE(0x060, BIT(17)),
 	[CLK_AHB_SPI0]		= GATE(0x060, BIT(20)),
 	[CLK_AHB_SPI1]		= GATE(0x060, BIT(21)),
@@ -35,6 +36,7 @@ static struct ccu_clk_gate a10s_gates[]
 	[CLK_APB1_UART2]	= GATE(0x06c, BIT(18)),
 	[CLK_APB1_UART3]	= GATE(0x06c, BIT(19)),
 
+	[CLK_NAND]		= GATE(0x080, BIT(31)),
 	[CLK_SPI0]		= GATE(0x0a0, BIT(31)),
 	[CLK_SPI1]		= GATE(0x0a4, BIT(31)),
 	[CLK_SPI2]		= GATE(0x0a8, BIT(31)),
--- a/drivers/clk/sunxi/clk_a23.c
+++ b/drivers/clk/sunxi/clk_a23.c
@@ -17,6 +17,7 @@ static struct ccu_clk_gate a23_gates[] =
 	[CLK_BUS_MMC0]		= GATE(0x060, BIT(8)),
 	[CLK_BUS_MMC1]		= GATE(0x060, BIT(9)),
 	[CLK_BUS_MMC2]		= GATE(0x060, BIT(10)),
+	[CLK_BUS_NAND]		= GATE(0x060, BIT(13)),
 	[CLK_BUS_SPI0]		= GATE(0x060, BIT(20)),
 	[CLK_BUS_SPI1]		= GATE(0x060, BIT(21)),
 	[CLK_BUS_OTG]		= GATE(0x060, BIT(24)),
@@ -34,6 +35,7 @@ static struct ccu_clk_gate a23_gates[] =
 	[CLK_BUS_UART3]		= GATE(0x06c, BIT(19)),
 	[CLK_BUS_UART4]		= GATE(0x06c, BIT(20)),
 
+	[CLK_NAND]		= GATE(0x080, BIT(31)),
 	[CLK_SPI0]		= GATE(0x0a0, BIT(31)),
 	[CLK_SPI1]		= GATE(0x0a4, BIT(31)),
 
@@ -52,6 +54,7 @@ static struct ccu_reset a23_resets[] = {
 	[RST_BUS_MMC0]		= RESET(0x2c0, BIT(8)),
 	[RST_BUS_MMC1]		= RESET(0x2c0, BIT(9)),
 	[RST_BUS_MMC2]		= RESET(0x2c0, BIT(10)),
+	[RST_BUS_NAND]		= RESET(0x2c0, BIT(13)),
 	[RST_BUS_SPI0]		= RESET(0x2c0, BIT(20)),
 	[RST_BUS_SPI1]		= RESET(0x2c0, BIT(21)),
 	[RST_BUS_OTG]		= RESET(0x2c0, BIT(24)),
--- a/drivers/clk/sunxi/clk_a31.c
+++ b/drivers/clk/sunxi/clk_a31.c
@@ -18,6 +18,8 @@ static struct ccu_clk_gate a31_gates[] =
 	[CLK_AHB1_MMC1]		= GATE(0x060, BIT(9)),
 	[CLK_AHB1_MMC2]		= GATE(0x060, BIT(10)),
 	[CLK_AHB1_MMC3]		= GATE(0x060, BIT(11)),
+	[CLK_AHB1_NAND1]	= GATE(0x060, BIT(12)),
+	[CLK_AHB1_NAND0]	= GATE(0x060, BIT(13)),
 	[CLK_AHB1_EMAC]		= GATE(0x060, BIT(17)),
 	[CLK_AHB1_SPI0]		= GATE(0x060, BIT(20)),
 	[CLK_AHB1_SPI1]		= GATE(0x060, BIT(21)),
@@ -43,6 +45,8 @@ static struct ccu_clk_gate a31_gates[] =
 	[CLK_APB2_UART4]	= GATE(0x06c, BIT(20)),
 	[CLK_APB2_UART5]	= GATE(0x06c, BIT(21)),
 
+	[CLK_NAND0]		= GATE(0x080, BIT(31)),
+	[CLK_NAND1]		= GATE(0x084, BIT(31)),
 	[CLK_SPI0]		= GATE(0x0a0, BIT(31)),
 	[CLK_SPI1]		= GATE(0x0a4, BIT(31)),
 	[CLK_SPI2]		= GATE(0x0a8, BIT(31)),
@@ -65,6 +69,8 @@ static struct ccu_reset a31_resets[] = {
 	[RST_AHB1_MMC1]		= RESET(0x2c0, BIT(9)),
 	[RST_AHB1_MMC2]		= RESET(0x2c0, BIT(10)),
 	[RST_AHB1_MMC3]		= RESET(0x2c0, BIT(11)),
+	[RST_AHB1_NAND1]	= RESET(0x2c0, BIT(12)),
+	[RST_AHB1_NAND0]	= RESET(0x2c0, BIT(13)),
 	[RST_AHB1_EMAC]		= RESET(0x2c0, BIT(17)),
 	[RST_AHB1_SPI0]		= RESET(0x2c0, BIT(20)),
 	[RST_AHB1_SPI1]		= RESET(0x2c0, BIT(21)),
--- a/drivers/clk/sunxi/clk_a64.c
+++ b/drivers/clk/sunxi/clk_a64.c
@@ -19,6 +19,7 @@ static const struct ccu_clk_gate a64_gat
 	[CLK_BUS_MMC0]		= GATE(0x060, BIT(8)),
 	[CLK_BUS_MMC1]		= GATE(0x060, BIT(9)),
 	[CLK_BUS_MMC2]		= GATE(0x060, BIT(10)),
+	[CLK_BUS_NAND]		= GATE(0x060, BIT(13)),
 	[CLK_BUS_EMAC]		= GATE(0x060, BIT(17)),
 	[CLK_BUS_SPI0]		= GATE(0x060, BIT(20)),
 	[CLK_BUS_SPI1]		= GATE(0x060, BIT(21)),
@@ -39,6 +40,7 @@ static const struct ccu_clk_gate a64_gat
 	[CLK_BUS_UART3]		= GATE(0x06c, BIT(19)),
 	[CLK_BUS_UART4]		= GATE(0x06c, BIT(20)),
 
+	[CLK_NAND]		= GATE(0x080, BIT(31)),
 	[CLK_SPI0]		= GATE(0x0a0, BIT(31)),
 	[CLK_SPI1]		= GATE(0x0a4, BIT(31)),
 
@@ -58,6 +60,7 @@ static const struct ccu_reset a64_resets
 	[RST_BUS_MMC0]		= RESET(0x2c0, BIT(8)),
 	[RST_BUS_MMC1]		= RESET(0x2c0, BIT(9)),
 	[RST_BUS_MMC2]		= RESET(0x2c0, BIT(10)),
+	[RST_BUS_NAND]		= RESET(0x2c0, BIT(13)),
 	[RST_BUS_EMAC]		= RESET(0x2c0, BIT(17)),
 	[RST_BUS_SPI0]		= RESET(0x2c0, BIT(20)),
 	[RST_BUS_SPI1]		= RESET(0x2c0, BIT(21)),
--- a/drivers/clk/sunxi/clk_a80.c
+++ b/drivers/clk/sunxi/clk_a80.c
@@ -14,12 +14,18 @@
 #include <linux/bitops.h>
 
 static const struct ccu_clk_gate a80_gates[] = {
+	[CLK_NAND0_0]		= GATE(0x400, BIT(31)),
+	[CLK_NAND0_1]		= GATE(0x404, BIT(31)),
+	[CLK_NAND1_0]		= GATE(0x408, BIT(31)),
+	[CLK_NAND1_1]		= GATE(0x40c, BIT(31)),
 	[CLK_SPI0]		= GATE(0x430, BIT(31)),
 	[CLK_SPI1]		= GATE(0x434, BIT(31)),
 	[CLK_SPI2]		= GATE(0x438, BIT(31)),
 	[CLK_SPI3]		= GATE(0x43c, BIT(31)),
 
 	[CLK_BUS_MMC]		= GATE(0x580, BIT(8)),
+	[CLK_BUS_NAND0]		= GATE(0x580, BIT(12)),
+	[CLK_BUS_NAND1]		= GATE(0x580, BIT(13)),
 	[CLK_BUS_SPI0]		= GATE(0x580, BIT(20)),
 	[CLK_BUS_SPI1]		= GATE(0x580, BIT(21)),
 	[CLK_BUS_SPI2]		= GATE(0x580, BIT(22)),
@@ -42,6 +48,8 @@ static const struct ccu_clk_gate a80_gat
 
 static const struct ccu_reset a80_resets[] = {
 	[RST_BUS_MMC]		= RESET(0x5a0, BIT(8)),
+	[RST_BUS_NAND0]		= RESET(0x5a0, BIT(12)),
+	[RST_BUS_NAND1]		= RESET(0x5a0, BIT(13)),
 	[RST_BUS_SPI0]		= RESET(0x5a0, BIT(20)),
 	[RST_BUS_SPI1]		= RESET(0x5a0, BIT(21)),
 	[RST_BUS_SPI2]		= RESET(0x5a0, BIT(22)),
--- a/drivers/clk/sunxi/clk_a83t.c
+++ b/drivers/clk/sunxi/clk_a83t.c
@@ -17,6 +17,7 @@ static struct ccu_clk_gate a83t_gates[]
 	[CLK_BUS_MMC0]		= GATE(0x060, BIT(8)),
 	[CLK_BUS_MMC1]		= GATE(0x060, BIT(9)),
 	[CLK_BUS_MMC2]		= GATE(0x060, BIT(10)),
+	[CLK_BUS_NAND]		= GATE(0x060, BIT(13)),
 	[CLK_BUS_EMAC]		= GATE(0x060, BIT(17)),
 	[CLK_BUS_SPI0]		= GATE(0x060, BIT(20)),
 	[CLK_BUS_SPI1]		= GATE(0x060, BIT(21)),
@@ -36,6 +37,7 @@ static struct ccu_clk_gate a83t_gates[]
 	[CLK_BUS_UART3]		= GATE(0x06c, BIT(19)),
 	[CLK_BUS_UART4]		= GATE(0x06c, BIT(20)),
 
+	[CLK_NAND]		= GATE(0x080, BIT(31)),
 	[CLK_SPI0]		= GATE(0x0a0, BIT(31)),
 	[CLK_SPI1]		= GATE(0x0a4, BIT(31)),
 
@@ -54,6 +56,7 @@ static struct ccu_reset a83t_resets[] =
 	[RST_BUS_MMC0]		= RESET(0x2c0, BIT(8)),
 	[RST_BUS_MMC1]		= RESET(0x2c0, BIT(9)),
 	[RST_BUS_MMC2]		= RESET(0x2c0, BIT(10)),
+	[RST_BUS_NAND]		= RESET(0x2c0, BIT(13)),
 	[RST_BUS_EMAC]		= RESET(0x2c0, BIT(17)),
 	[RST_BUS_SPI0]		= RESET(0x2c0, BIT(20)),
 	[RST_BUS_SPI1]		= RESET(0x2c0, BIT(21)),
--- a/drivers/clk/sunxi/clk_h3.c
+++ b/drivers/clk/sunxi/clk_h3.c
@@ -19,6 +19,7 @@ static struct ccu_clk_gate h3_gates[] =
 	[CLK_BUS_MMC0]		= GATE(0x060, BIT(8)),
 	[CLK_BUS_MMC1]		= GATE(0x060, BIT(9)),
 	[CLK_BUS_MMC2]		= GATE(0x060, BIT(10)),
+	[CLK_BUS_NAND]		= GATE(0x060, BIT(13)),
 	[CLK_BUS_EMAC]		= GATE(0x060, BIT(17)),
 	[CLK_BUS_SPI0]		= GATE(0x060, BIT(20)),
 	[CLK_BUS_SPI1]		= GATE(0x060, BIT(21)),
@@ -44,6 +45,7 @@ static struct ccu_clk_gate h3_gates[] =
 
 	[CLK_BUS_EPHY]		= GATE(0x070, BIT(0)),
 
+	[CLK_NAND]		= GATE(0x080, BIT(31)),
 	[CLK_SPI0]		= GATE(0x0a0, BIT(31)),
 	[CLK_SPI1]		= GATE(0x0a4, BIT(31)),
 
@@ -66,6 +68,7 @@ static struct ccu_reset h3_resets[] = {
 	[RST_BUS_MMC0]		= RESET(0x2c0, BIT(8)),
 	[RST_BUS_MMC1]		= RESET(0x2c0, BIT(9)),
 	[RST_BUS_MMC2]		= RESET(0x2c0, BIT(10)),
+	[RST_BUS_NAND]		= RESET(0x2c0, BIT(13)),
 	[RST_BUS_EMAC]		= RESET(0x2c0, BIT(17)),
 	[RST_BUS_SPI0]		= RESET(0x2c0, BIT(20)),
 	[RST_BUS_SPI1]		= RESET(0x2c0, BIT(21)),
--- a/drivers/clk/sunxi/clk_h6.c
+++ b/drivers/clk/sunxi/clk_h6.c
@@ -18,6 +18,10 @@ static struct ccu_clk_gate h6_gates[] =
 
 	[CLK_APB1]		= GATE_DUMMY,
 
+	[CLK_NAND0]		= GATE(0x810, BIT(31)),
+	[CLK_NAND1]		= GATE(0x814, BIT(31)),
+	[CLK_BUS_NAND]		= GATE(0x82c, BIT(0)),
+
 	[CLK_BUS_MMC0]		= GATE(0x84c, BIT(0)),
 	[CLK_BUS_MMC1]		= GATE(0x84c, BIT(1)),
 	[CLK_BUS_MMC2]		= GATE(0x84c, BIT(2)),
@@ -58,6 +62,8 @@ static struct ccu_clk_gate h6_gates[] =
 };
 
 static struct ccu_reset h6_resets[] = {
+	[RST_BUS_NAND]		= RESET(0x82c, BIT(16)),
+
 	[RST_BUS_MMC0]		= RESET(0x84c, BIT(16)),
 	[RST_BUS_MMC1]		= RESET(0x84c, BIT(17)),
 	[RST_BUS_MMC2]		= RESET(0x84c, BIT(18)),
--- a/drivers/clk/sunxi/clk_h616.c
+++ b/drivers/clk/sunxi/clk_h616.c
@@ -17,6 +17,10 @@ static struct ccu_clk_gate h616_gates[]
 
 	[CLK_APB1]		= GATE_DUMMY,
 
+	[CLK_NAND0]		= GATE(0x810, BIT(31)),
+	[CLK_NAND1]		= GATE(0x814, BIT(31)),
+	[CLK_BUS_NAND]		= GATE(0x82c, BIT(0)),
+
 	[CLK_BUS_MMC0]		= GATE(0x84c, BIT(0)),
 	[CLK_BUS_MMC1]		= GATE(0x84c, BIT(1)),
 	[CLK_BUS_MMC2]		= GATE(0x84c, BIT(2)),
@@ -67,6 +71,8 @@ static struct ccu_clk_gate h616_gates[]
 };
 
 static struct ccu_reset h616_resets[] = {
+	[RST_BUS_NAND]		= RESET(0x82c, BIT(16)),
+
 	[RST_BUS_MMC0]		= RESET(0x84c, BIT(16)),
 	[RST_BUS_MMC1]		= RESET(0x84c, BIT(17)),
 	[RST_BUS_MMC2]		= RESET(0x84c, BIT(18)),
--- a/drivers/clk/sunxi/clk_r40.c
+++ b/drivers/clk/sunxi/clk_r40.c
@@ -18,6 +18,7 @@ static struct ccu_clk_gate r40_gates[] =
 	[CLK_BUS_MMC1]		= GATE(0x060, BIT(9)),
 	[CLK_BUS_MMC2]		= GATE(0x060, BIT(10)),
 	[CLK_BUS_MMC3]		= GATE(0x060, BIT(11)),
+	[CLK_BUS_NAND]		= GATE(0x060, BIT(13)),
 	[CLK_BUS_SPI0]		= GATE(0x060, BIT(20)),
 	[CLK_BUS_SPI1]		= GATE(0x060, BIT(21)),
 	[CLK_BUS_SPI2]		= GATE(0x060, BIT(22)),
@@ -48,6 +49,7 @@ static struct ccu_clk_gate r40_gates[] =
 	[CLK_BUS_UART6]		= GATE(0x06c, BIT(22)),
 	[CLK_BUS_UART7]		= GATE(0x06c, BIT(23)),
 
+	[CLK_NAND]		= GATE(0x080, BIT(31)),
 	[CLK_SPI0]		= GATE(0x0a0, BIT(31)),
 	[CLK_SPI1]		= GATE(0x0a4, BIT(31)),
 	[CLK_SPI2]		= GATE(0x0a8, BIT(31)),
@@ -70,6 +72,7 @@ static struct ccu_reset r40_resets[] = {
 	[RST_BUS_MMC1]		= RESET(0x2c0, BIT(9)),
 	[RST_BUS_MMC2]		= RESET(0x2c0, BIT(10)),
 	[RST_BUS_MMC3]		= RESET(0x2c0, BIT(11)),
+	[RST_BUS_NAND]		= RESET(0x2c0, BIT(13)),
 	[RST_BUS_SPI0]		= RESET(0x2c0, BIT(20)),
 	[RST_BUS_SPI1]		= RESET(0x2c0, BIT(21)),
 	[RST_BUS_SPI2]		= RESET(0x2c0, BIT(22)),
