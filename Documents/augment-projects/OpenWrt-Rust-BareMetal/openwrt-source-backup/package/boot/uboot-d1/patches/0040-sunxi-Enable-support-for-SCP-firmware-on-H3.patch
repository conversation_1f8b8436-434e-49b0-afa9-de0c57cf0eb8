From aefe751d6f23c9d526bca447c6c28da97e45e528 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sat, 17 Apr 2021 13:33:54 -0500
Subject: [PATCH 40/90] sunxi: Enable support for SCP firmware on H3

Now that issues with the BROM have been sorted out, we can implement
PSCI system suspend on H3 by delegating to SCP firmware. Let's start by
including the firmware in the FIT image and starting the coprocessor if
valid firmware is loaded.

Signed-off-by: <PERSON> <<EMAIL>>
---
 arch/arm/dts/sunxi-u-boot.dtsi | 1 +
 board/sunxi/board.c            | 8 ++++++++
 include/configs/sun8i.h        | 3 +++
 3 files changed, 12 insertions(+)

--- a/arch/arm/dts/sunxi-u-boot.dtsi
+++ b/arch/arm/dts/sunxi-u-boot.dtsi
@@ -9,6 +9,7 @@
 #if defined(CONFIG_MACH_SUN8I_H3)
 #ifdef CONFIG_ARMV7_PSCI
 #define RESUME_ADDR	SUNXI_RESUME_BASE
+#define SCP_ADDR	SUNXI_SCP_BASE
 #endif
 #elif defined(CONFIG_MACH_SUN50I) || defined(CONFIG_MACH_SUN50I_H5)
 #define BL31_ADDR	0x00044000
--- a/board/sunxi/board.c
+++ b/board/sunxi/board.c
@@ -19,6 +19,7 @@
 #include <init.h>
 #include <log.h>
 #include <mmc.h>
+#include <remoteproc.h>
 #include <axp_pmic.h>
 #include <generic-phy.h>
 #include <phy-sun4i-usb.h>
@@ -867,6 +868,13 @@ int board_late_init(void)
 	usb_ether_init();
 #endif
 
+#ifdef CONFIG_REMOTEPROC_SUN6I_AR100
+	if (!rproc_load(0, SUNXI_SCP_BASE, SUNXI_SCP_MAX_SIZE)) {
+		puts("Starting SCP...\n");
+		rproc_start(0);
+	}
+#endif
+
 	return 0;
 }
 
--- a/include/configs/sun8i.h
+++ b/include/configs/sun8i.h
@@ -12,6 +12,9 @@
 					 CONFIG_ARMV7_SECURE_MAX_SIZE)
 #define SUNXI_RESUME_SIZE		1024
 
+#define SUNXI_SCP_BASE			(SUNXI_RESUME_BASE + SUNXI_RESUME_SIZE)
+#define SUNXI_SCP_MAX_SIZE		(16 * 1024)
+
 #include <configs/sunxi-common.h>
 
 #endif /* __CONFIG_H */
