From bcc2e01668041c146d964ed5f77b819dcc35b3e2 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <<EMAIL>>
Date: Tue, 6 Jun 2023 15:07:47 +0000
Subject: [PATCH 18/90] sunxi: remove CONFIG_MMC?_CD_PIN

For legacy reasons we were defining the card detect GPIO for all sunxi
boards in each board's defconfig.
There is actually no need for a card-detect check in the SPL code (which
consequently has been removed already), and also in U-Boot proper we
have DM code to query the CD GPIO name from the device tree.

That means we don't have any user of that information left, so can
remove the definitions from the defconfigs.

Signed-off-by: <PERSON> <<EMAIL>>
Signed-off-by: <PERSON><PERSON><PERSON> <<EMAIL>>
---
 arch/arm/mach-sunxi/Kconfig                  | 27 --------------------
 configs/A10-OLinuXino-Lime_defconfig         |  1 -
 configs/A10s-OLinuXino-M_defconfig           |  2 --
 configs/A13-OLinuXino_defconfig              |  1 -
 configs/A20-OLinuXino-Lime2-eMMC_defconfig   |  1 -
 configs/A20-OLinuXino-Lime_defconfig         |  1 -
 configs/A20-OLinuXino_MICRO-eMMC_defconfig   |  1 -
 configs/A20-OLinuXino_MICRO_defconfig        |  2 --
 configs/A20-Olimex-SOM-EVB_defconfig         |  2 --
 configs/A20-Olimex-SOM204-EVB-eMMC_defconfig |  1 -
 configs/Bananapi_M2_Ultra_defconfig          |  1 -
 configs/Bananapi_m2m_defconfig               |  1 -
 configs/Cubieboard2_defconfig                |  1 -
 configs/Cubieboard4_defconfig                |  1 -
 configs/Cubieboard_defconfig                 |  1 -
 configs/Itead_Ibox_A20_defconfig             |  1 -
 configs/Lamobo_R1_defconfig                  |  1 -
 configs/Mele_M3_defconfig                    |  1 -
 configs/Mele_M5_defconfig                    |  1 -
 configs/Merrii_A80_Optimus_defconfig         |  1 -
 configs/Orangepi_mini_defconfig              |  2 --
 configs/Sinlinx_SinA31s_defconfig            |  1 -
 configs/Sinlinx_SinA33_defconfig             |  1 -
 configs/Sunchip_CX-A99_defconfig             |  1 -
 configs/UTOO_P66_defconfig                   |  1 -
 configs/Yones_Toptech_BD1078_defconfig       |  2 --
 configs/bananapi_m2_zero_defconfig           |  1 -
 configs/bananapi_m64_defconfig               |  1 -
 configs/beelink_gs1_defconfig                |  1 -
 configs/nanopi_m1_plus_defconfig             |  1 -
 configs/oceanic_5205_5inmfd_defconfig        |  1 -
 configs/orangepi_3_defconfig                 |  1 -
 configs/orangepi_lite2_defconfig             |  1 -
 configs/orangepi_one_plus_defconfig          |  1 -
 configs/orangepi_zero2_defconfig             |  1 -
 configs/orangepi_zero_plus2_defconfig        |  1 -
 configs/orangepi_zero_plus2_h3_defconfig     |  1 -
 configs/parrot_r16_defconfig                 |  1 -
 configs/pine64-lts_defconfig                 |  1 -
 configs/pine_h64_defconfig                   |  1 -
 configs/sopine_baseboard_defconfig           |  1 -
 configs/tanix_tx6_defconfig                  |  1 -
 42 files changed, 73 deletions(-)

--- a/arch/arm/mach-sunxi/Kconfig
+++ b/arch/arm/mach-sunxi/Kconfig
@@ -652,33 +652,6 @@ config MACPWR
 	  Set the pin used to power the MAC. This takes a string in the format
 	  understood by sunxi_name_to_gpio, e.g. PH1 for pin 1 of port H.
 
-config MMC0_CD_PIN
-	string "Card detect pin for mmc0"
-	default "PF6" if MACH_SUN8I_A83T || MACH_SUNXI_H3_H5 || MACH_SUN50I
-	default ""
-	---help---
-	Set the card detect pin for mmc0, leave empty to not use cd. This
-	takes a string in the format understood by sunxi_name_to_gpio, e.g.
-	PH1 for pin 1 of port H.
-
-config MMC1_CD_PIN
-	string "Card detect pin for mmc1"
-	default ""
-	---help---
-	See MMC0_CD_PIN help text.
-
-config MMC2_CD_PIN
-	string "Card detect pin for mmc2"
-	default ""
-	---help---
-	See MMC0_CD_PIN help text.
-
-config MMC3_CD_PIN
-	string "Card detect pin for mmc3"
-	default ""
-	---help---
-	See MMC0_CD_PIN help text.
-
 config MMC1_PINS_PH
 	bool "Pins for mmc1 are on Port H"
 	depends on MACH_SUN4I || MACH_SUN7I || MACH_SUN8I_R40
--- a/configs/A10-OLinuXino-Lime_defconfig
+++ b/configs/A10-OLinuXino-Lime_defconfig
@@ -6,7 +6,6 @@ CONFIG_MACH_SUN4I=y
 CONFIG_DRAM_CLK=480
 CONFIG_DRAM_EMR1=4
 CONFIG_SYS_CLK_FREQ=912000000
-CONFIG_MMC0_CD_PIN="PH1"
 CONFIG_I2C1_ENABLE=y
 CONFIG_SATAPWR="PC3"
 CONFIG_AHCI=y
--- a/configs/A10s-OLinuXino-M_defconfig
+++ b/configs/A10s-OLinuXino-M_defconfig
@@ -4,8 +4,6 @@ CONFIG_DEFAULT_DEVICE_TREE="sun5i-a10s-o
 CONFIG_SPL=y
 CONFIG_MACH_SUN5I=y
 CONFIG_DRAM_CLK=432
-CONFIG_MMC0_CD_PIN="PG1"
-CONFIG_MMC1_CD_PIN="PG13"
 CONFIG_MMC_SUNXI_SLOT_EXTRA=1
 CONFIG_USB1_VBUS_PIN="PB10"
 # CONFIG_SYS_MALLOC_CLEAR_ON_INIT is not set
--- a/configs/A13-OLinuXino_defconfig
+++ b/configs/A13-OLinuXino_defconfig
@@ -5,7 +5,6 @@ CONFIG_SPL=y
 CONFIG_MACH_SUN5I=y
 CONFIG_DRAM_CLK=408
 CONFIG_DRAM_EMR1=0
-CONFIG_MMC0_CD_PIN="PG0"
 CONFIG_USB0_VBUS_DET="PG1"
 CONFIG_USB1_VBUS_PIN="PG11"
 CONFIG_AXP_GPIO=y
--- a/configs/A20-OLinuXino-Lime2-eMMC_defconfig
+++ b/configs/A20-OLinuXino-Lime2-eMMC_defconfig
@@ -4,7 +4,6 @@ CONFIG_DEFAULT_DEVICE_TREE="sun7i-a20-ol
 CONFIG_SPL=y
 CONFIG_MACH_SUN7I=y
 CONFIG_DRAM_CLK=384
-CONFIG_MMC0_CD_PIN="PH1"
 CONFIG_MMC_SUNXI_SLOT_EXTRA=2
 CONFIG_USB0_VBUS_PIN="PC17"
 CONFIG_USB0_VBUS_DET="PH5"
--- a/configs/A20-OLinuXino-Lime_defconfig
+++ b/configs/A20-OLinuXino-Lime_defconfig
@@ -4,7 +4,6 @@ CONFIG_DEFAULT_DEVICE_TREE="sun7i-a20-ol
 CONFIG_SPL=y
 CONFIG_MACH_SUN7I=y
 CONFIG_DRAM_CLK=384
-CONFIG_MMC0_CD_PIN="PH1"
 CONFIG_I2C1_ENABLE=y
 CONFIG_SATAPWR="PC3"
 CONFIG_AHCI=y
--- a/configs/A20-OLinuXino_MICRO-eMMC_defconfig
+++ b/configs/A20-OLinuXino_MICRO-eMMC_defconfig
@@ -4,7 +4,6 @@ CONFIG_DEFAULT_DEVICE_TREE="sun7i-a20-ol
 CONFIG_SPL=y
 CONFIG_MACH_SUN7I=y
 CONFIG_DRAM_CLK=384
-CONFIG_MMC0_CD_PIN="PH1"
 CONFIG_MMC_SUNXI_SLOT_EXTRA=2
 CONFIG_I2C1_ENABLE=y
 CONFIG_VIDEO_VGA=y
--- a/configs/A20-OLinuXino_MICRO_defconfig
+++ b/configs/A20-OLinuXino_MICRO_defconfig
@@ -4,8 +4,6 @@ CONFIG_DEFAULT_DEVICE_TREE="sun7i-a20-ol
 CONFIG_SPL=y
 CONFIG_MACH_SUN7I=y
 CONFIG_DRAM_CLK=384
-CONFIG_MMC0_CD_PIN="PH1"
-CONFIG_MMC3_CD_PIN="PH11"
 CONFIG_MMC_SUNXI_SLOT_EXTRA=3
 CONFIG_I2C1_ENABLE=y
 CONFIG_VIDEO_VGA=y
--- a/configs/A20-Olimex-SOM-EVB_defconfig
+++ b/configs/A20-Olimex-SOM-EVB_defconfig
@@ -4,8 +4,6 @@ CONFIG_DEFAULT_DEVICE_TREE="sun7i-a20-ol
 CONFIG_SPL=y
 CONFIG_MACH_SUN7I=y
 CONFIG_DRAM_CLK=384
-CONFIG_MMC0_CD_PIN="PH1"
-CONFIG_MMC3_CD_PIN="PH0"
 CONFIG_MMC_SUNXI_SLOT_EXTRA=3
 CONFIG_USB0_VBUS_PIN="PB9"
 CONFIG_USB0_VBUS_DET="PH5"
--- a/configs/A20-Olimex-SOM204-EVB-eMMC_defconfig
+++ b/configs/A20-Olimex-SOM204-EVB-eMMC_defconfig
@@ -4,7 +4,6 @@ CONFIG_DEFAULT_DEVICE_TREE="sun7i-a20-ol
 CONFIG_SPL=y
 CONFIG_MACH_SUN7I=y
 CONFIG_DRAM_CLK=384
-CONFIG_MMC0_CD_PIN="PH1"
 CONFIG_MMC_SUNXI_SLOT_EXTRA=2
 CONFIG_USB0_VBUS_PIN="PC17"
 CONFIG_USB0_VBUS_DET="PH5"
--- a/configs/Bananapi_M2_Ultra_defconfig
+++ b/configs/Bananapi_M2_Ultra_defconfig
@@ -5,7 +5,6 @@ CONFIG_SPL=y
 CONFIG_MACH_SUN8I_R40=y
 CONFIG_DRAM_CLK=576
 CONFIG_MACPWR="PA17"
-CONFIG_MMC0_CD_PIN="PH13"
 CONFIG_MMC_SUNXI_SLOT_EXTRA=2
 CONFIG_USB1_VBUS_PIN="PH23"
 CONFIG_USB2_VBUS_PIN="PH23"
--- a/configs/Bananapi_m2m_defconfig
+++ b/configs/Bananapi_m2m_defconfig
@@ -6,7 +6,6 @@ CONFIG_MACH_SUN8I_A33=y
 CONFIG_DRAM_CLK=600
 CONFIG_DRAM_ZQ=15291
 CONFIG_DRAM_ODT_EN=y
-CONFIG_MMC0_CD_PIN="PB4"
 CONFIG_MMC_SUNXI_SLOT_EXTRA=2
 CONFIG_USB0_ID_DET="PH8"
 # CONFIG_SYS_MALLOC_CLEAR_ON_INIT is not set
--- a/configs/Cubieboard2_defconfig
+++ b/configs/Cubieboard2_defconfig
@@ -4,7 +4,6 @@ CONFIG_DEFAULT_DEVICE_TREE="sun7i-a20-cu
 CONFIG_SPL=y
 CONFIG_MACH_SUN7I=y
 CONFIG_DRAM_CLK=480
-CONFIG_MMC0_CD_PIN="PH1"
 CONFIG_SATAPWR="PB8"
 CONFIG_AHCI=y
 # CONFIG_SYS_MALLOC_CLEAR_ON_INIT is not set
--- a/configs/Cubieboard4_defconfig
+++ b/configs/Cubieboard4_defconfig
@@ -4,7 +4,6 @@ CONFIG_DEFAULT_DEVICE_TREE="sun9i-a80-cu
 CONFIG_SPL=y
 CONFIG_MACH_SUN9I=y
 CONFIG_DRAM_CLK=672
-CONFIG_MMC0_CD_PIN="PH18"
 CONFIG_MMC_SUNXI_SLOT_EXTRA=2
 CONFIG_USB0_VBUS_PIN="AXP0-VBUS-ENABLE"
 CONFIG_USB0_VBUS_DET="AXP0-VBUS-DETECT"
--- a/configs/Cubieboard_defconfig
+++ b/configs/Cubieboard_defconfig
@@ -4,7 +4,6 @@ CONFIG_DEFAULT_DEVICE_TREE="sun4i-a10-cu
 CONFIG_SPL=y
 CONFIG_MACH_SUN4I=y
 CONFIG_DRAM_CLK=480
-CONFIG_MMC0_CD_PIN="PH1"
 CONFIG_SATAPWR="PB8"
 CONFIG_AHCI=y
 # CONFIG_SYS_MALLOC_CLEAR_ON_INIT is not set
--- a/configs/Itead_Ibox_A20_defconfig
+++ b/configs/Itead_Ibox_A20_defconfig
@@ -4,7 +4,6 @@ CONFIG_DEFAULT_DEVICE_TREE="sun7i-a20-it
 CONFIG_SPL=y
 CONFIG_MACH_SUN7I=y
 CONFIG_DRAM_CLK=480
-CONFIG_MMC0_CD_PIN="PH1"
 CONFIG_SATAPWR="PB8"
 CONFIG_AHCI=y
 # CONFIG_SYS_MALLOC_CLEAR_ON_INIT is not set
--- a/configs/Lamobo_R1_defconfig
+++ b/configs/Lamobo_R1_defconfig
@@ -5,7 +5,6 @@ CONFIG_SPL=y
 CONFIG_MACH_SUN7I=y
 CONFIG_DRAM_CLK=432
 CONFIG_MACPWR="PH23"
-CONFIG_MMC0_CD_PIN="PH10"
 CONFIG_SATAPWR="PB3"
 CONFIG_GMAC_TX_DELAY=4
 CONFIG_AHCI=y
--- a/configs/Mele_M3_defconfig
+++ b/configs/Mele_M3_defconfig
@@ -4,7 +4,6 @@ CONFIG_DEFAULT_DEVICE_TREE="sun7i-a20-m3
 CONFIG_SPL=y
 CONFIG_MACH_SUN7I=y
 CONFIG_DRAM_CLK=384
-CONFIG_MMC0_CD_PIN="PH1"
 CONFIG_MMC_SUNXI_SLOT_EXTRA=2
 CONFIG_VIDEO_VGA=y
 CONFIG_VIDEO_COMPOSITE=y
--- a/configs/Mele_M5_defconfig
+++ b/configs/Mele_M5_defconfig
@@ -5,7 +5,6 @@ CONFIG_SPL=y
 CONFIG_MACH_SUN7I=y
 CONFIG_DRAM_CLK=432
 CONFIG_DRAM_ZQ=122
-CONFIG_MMC0_CD_PIN="PH1"
 CONFIG_VIDEO_COMPOSITE=y
 CONFIG_AHCI=y
 # CONFIG_SYS_MALLOC_CLEAR_ON_INIT is not set
--- a/configs/Merrii_A80_Optimus_defconfig
+++ b/configs/Merrii_A80_Optimus_defconfig
@@ -4,7 +4,6 @@ CONFIG_DEFAULT_DEVICE_TREE="sun9i-a80-op
 CONFIG_SPL=y
 CONFIG_MACH_SUN9I=y
 CONFIG_DRAM_CLK=672
-CONFIG_MMC0_CD_PIN="PH18"
 CONFIG_MMC_SUNXI_SLOT_EXTRA=2
 CONFIG_USB0_VBUS_PIN="AXP0-VBUS-ENABLE"
 CONFIG_USB0_VBUS_DET="AXP0-VBUS-DETECT"
--- a/configs/Orangepi_mini_defconfig
+++ b/configs/Orangepi_mini_defconfig
@@ -5,8 +5,6 @@ CONFIG_SPL=y
 CONFIG_MACH_SUN7I=y
 CONFIG_DRAM_CLK=432
 CONFIG_MACPWR="PH23"
-CONFIG_MMC0_CD_PIN="PH10"
-CONFIG_MMC3_CD_PIN="PH11"
 CONFIG_MMC_SUNXI_SLOT_EXTRA=3
 CONFIG_USB1_VBUS_PIN="PH26"
 CONFIG_USB2_VBUS_PIN="PH22"
--- a/configs/Sinlinx_SinA31s_defconfig
+++ b/configs/Sinlinx_SinA31s_defconfig
@@ -5,7 +5,6 @@ CONFIG_SPL=y
 CONFIG_MACH_SUN6I=y
 CONFIG_DRAM_CLK=432
 CONFIG_DRAM_ZQ=251
-CONFIG_MMC0_CD_PIN="PA4"
 CONFIG_MMC_SUNXI_SLOT_EXTRA=3
 CONFIG_USB1_VBUS_PIN=""
 CONFIG_USB2_VBUS_PIN=""
--- a/configs/Sinlinx_SinA33_defconfig
+++ b/configs/Sinlinx_SinA33_defconfig
@@ -5,7 +5,6 @@ CONFIG_SPL=y
 CONFIG_MACH_SUN8I_A33=y
 CONFIG_DRAM_CLK=552
 CONFIG_DRAM_ZQ=15291
-CONFIG_MMC0_CD_PIN="PB4"
 CONFIG_MMC_SUNXI_SLOT_EXTRA=2
 CONFIG_USB0_ID_DET="PH8"
 CONFIG_VIDEO_LCD_MODE="x:1024,y:600,depth:18,pclk_khz:66000,le:90,ri:160,up:3,lo:127,hs:70,vs:20,sync:3,vmode:0"
--- a/configs/Sunchip_CX-A99_defconfig
+++ b/configs/Sunchip_CX-A99_defconfig
@@ -6,7 +6,6 @@ CONFIG_MACH_SUN9I=y
 CONFIG_DRAM_CLK=600
 CONFIG_DRAM_ZQ=3881915
 CONFIG_DRAM_ODT_EN=y
-CONFIG_MMC0_CD_PIN="PH17"
 CONFIG_MMC_SUNXI_SLOT_EXTRA=2
 CONFIG_USB0_VBUS_PIN="PH15"
 CONFIG_USB1_VBUS_PIN="PL7"
--- a/configs/UTOO_P66_defconfig
+++ b/configs/UTOO_P66_defconfig
@@ -5,7 +5,6 @@ CONFIG_SPL=y
 CONFIG_MACH_SUN5I=y
 CONFIG_DRAM_CLK=432
 CONFIG_DRAM_EMR1=0
-CONFIG_MMC0_CD_PIN="PG0"
 CONFIG_MMC_SUNXI_SLOT_EXTRA=2
 CONFIG_USB0_VBUS_PIN="PB04"
 CONFIG_USB0_VBUS_DET="PG01"
--- a/configs/Yones_Toptech_BD1078_defconfig
+++ b/configs/Yones_Toptech_BD1078_defconfig
@@ -4,8 +4,6 @@ CONFIG_DEFAULT_DEVICE_TREE="sun7i-a20-yo
 CONFIG_SPL=y
 CONFIG_MACH_SUN7I=y
 CONFIG_DRAM_CLK=408
-CONFIG_MMC0_CD_PIN="PH1"
-CONFIG_MMC1_CD_PIN="PH2"
 CONFIG_MMC1_PINS_PH=y
 CONFIG_MMC_SUNXI_SLOT_EXTRA=1
 CONFIG_USB0_VBUS_PIN="PB9"
--- a/configs/bananapi_m2_zero_defconfig
+++ b/configs/bananapi_m2_zero_defconfig
@@ -4,5 +4,4 @@ CONFIG_DEFAULT_DEVICE_TREE="sun8i-h2-plu
 CONFIG_SPL=y
 CONFIG_MACH_SUN8I_H3=y
 CONFIG_DRAM_CLK=408
-CONFIG_MMC0_CD_PIN=""
 # CONFIG_SYS_MALLOC_CLEAR_ON_INIT is not set
--- a/configs/bananapi_m64_defconfig
+++ b/configs/bananapi_m64_defconfig
@@ -4,7 +4,6 @@ CONFIG_DEFAULT_DEVICE_TREE="sun50i-a64-b
 CONFIG_SPL=y
 CONFIG_MACH_SUN50I=y
 CONFIG_RESERVE_ALLWINNER_BOOT0_HEADER=y
-CONFIG_MMC0_CD_PIN="PH13"
 CONFIG_MMC_SUNXI_SLOT_EXTRA=2
 # CONFIG_SYS_MALLOC_CLEAR_ON_INIT is not set
 CONFIG_SUPPORT_EMMC_BOOT=y
--- a/configs/beelink_gs1_defconfig
+++ b/configs/beelink_gs1_defconfig
@@ -4,7 +4,6 @@ CONFIG_DEFAULT_DEVICE_TREE="sun50i-h6-be
 CONFIG_SPL=y
 CONFIG_MACH_SUN50I_H6=y
 CONFIG_SUNXI_DRAM_H6_LPDDR3=y
-CONFIG_MMC0_CD_PIN="PF6"
 CONFIG_MMC_SUNXI_SLOT_EXTRA=2
 # CONFIG_PSCI_RESET is not set
 # CONFIG_SYS_MALLOC_CLEAR_ON_INIT is not set
--- a/configs/nanopi_m1_plus_defconfig
+++ b/configs/nanopi_m1_plus_defconfig
@@ -5,7 +5,6 @@ CONFIG_SPL=y
 CONFIG_MACH_SUN8I_H3=y
 CONFIG_DRAM_CLK=408
 CONFIG_MACPWR="PD6"
-CONFIG_MMC0_CD_PIN="PH13"
 CONFIG_MMC_SUNXI_SLOT_EXTRA=2
 # CONFIG_SYS_MALLOC_CLEAR_ON_INIT is not set
 CONFIG_SUN8I_EMAC=y
--- a/configs/oceanic_5205_5inmfd_defconfig
+++ b/configs/oceanic_5205_5inmfd_defconfig
@@ -7,7 +7,6 @@ CONFIG_RESERVE_ALLWINNER_BOOT0_HEADER=y
 CONFIG_SUNXI_DRAM_LPDDR3_STOCK=y
 CONFIG_DRAM_CLK=552
 CONFIG_DRAM_ZQ=3881949
-CONFIG_MMC0_CD_PIN=""
 CONFIG_SPL_SPI_SUNXI=y
 # CONFIG_SYS_MALLOC_CLEAR_ON_INIT is not set
 CONFIG_SUN8I_EMAC=y
--- a/configs/orangepi_3_defconfig
+++ b/configs/orangepi_3_defconfig
@@ -4,7 +4,6 @@ CONFIG_DEFAULT_DEVICE_TREE="sun50i-h6-or
 CONFIG_SPL=y
 CONFIG_MACH_SUN50I_H6=y
 CONFIG_SUNXI_DRAM_H6_LPDDR3=y
-CONFIG_MMC0_CD_PIN="PF6"
 CONFIG_MMC_SUNXI_SLOT_EXTRA=2
 CONFIG_BLUETOOTH_DT_DEVICE_FIXUP="brcm,bcm4345c5"
 # CONFIG_SYS_MALLOC_CLEAR_ON_INIT is not set
--- a/configs/orangepi_lite2_defconfig
+++ b/configs/orangepi_lite2_defconfig
@@ -4,7 +4,6 @@ CONFIG_DEFAULT_DEVICE_TREE="sun50i-h6-or
 CONFIG_SPL=y
 CONFIG_MACH_SUN50I_H6=y
 CONFIG_SUNXI_DRAM_H6_LPDDR3=y
-CONFIG_MMC0_CD_PIN="PF6"
 # CONFIG_PSCI_RESET is not set
 # CONFIG_SYS_MALLOC_CLEAR_ON_INIT is not set
 CONFIG_USB_EHCI_HCD=y
--- a/configs/orangepi_one_plus_defconfig
+++ b/configs/orangepi_one_plus_defconfig
@@ -4,7 +4,6 @@ CONFIG_DEFAULT_DEVICE_TREE="sun50i-h6-or
 CONFIG_SPL=y
 CONFIG_MACH_SUN50I_H6=y
 CONFIG_SUNXI_DRAM_H6_LPDDR3=y
-CONFIG_MMC0_CD_PIN="PF6"
 # CONFIG_PSCI_RESET is not set
 # CONFIG_SYS_MALLOC_CLEAR_ON_INIT is not set
 CONFIG_USB_EHCI_HCD=y
--- a/configs/orangepi_zero2_defconfig
+++ b/configs/orangepi_zero2_defconfig
@@ -7,7 +7,6 @@ CONFIG_DRAM_SUN50I_H616_READ_CALIBRATION
 CONFIG_DRAM_SUN50I_H616_READ_TRAINING=y
 CONFIG_DRAM_SUN50I_H616_WRITE_TRAINING=y
 CONFIG_MACH_SUN50I_H616=y
-CONFIG_MMC0_CD_PIN="PF6"
 CONFIG_R_I2C_ENABLE=y
 CONFIG_SPL_SPI_SUNXI=y
 # CONFIG_SYS_MALLOC_CLEAR_ON_INIT is not set
--- a/configs/orangepi_zero_plus2_defconfig
+++ b/configs/orangepi_zero_plus2_defconfig
@@ -6,7 +6,6 @@ CONFIG_MACH_SUN50I_H5=y
 CONFIG_DRAM_CLK=672
 CONFIG_DRAM_ZQ=3881977
 # CONFIG_DRAM_ODT_EN is not set
-CONFIG_MMC0_CD_PIN="PH13"
 CONFIG_MMC_SUNXI_SLOT_EXTRA=2
 # CONFIG_SYS_MALLOC_CLEAR_ON_INIT is not set
 CONFIG_SUN8I_EMAC=y
--- a/configs/orangepi_zero_plus2_h3_defconfig
+++ b/configs/orangepi_zero_plus2_h3_defconfig
@@ -5,7 +5,6 @@ CONFIG_SPL=y
 CONFIG_MACH_SUN8I_H3=y
 CONFIG_DRAM_CLK=672
 # CONFIG_DRAM_ODT_EN is not set
-CONFIG_MMC0_CD_PIN="PH13"
 CONFIG_MMC_SUNXI_SLOT_EXTRA=2
 # CONFIG_SYS_MALLOC_CLEAR_ON_INIT is not set
 CONFIG_SUN8I_EMAC=y
--- a/configs/parrot_r16_defconfig
+++ b/configs/parrot_r16_defconfig
@@ -5,7 +5,6 @@ CONFIG_SPL=y
 CONFIG_MACH_SUN8I_A33=y
 CONFIG_DRAM_CLK=600
 CONFIG_DRAM_ZQ=15291
-CONFIG_MMC0_CD_PIN="PD14"
 CONFIG_MMC_SUNXI_SLOT_EXTRA=2
 CONFIG_USB0_ID_DET="PD10"
 CONFIG_USB1_VBUS_PIN="PD12"
--- a/configs/pine64-lts_defconfig
+++ b/configs/pine64-lts_defconfig
@@ -6,7 +6,6 @@ CONFIG_MACH_SUN50I=y
 CONFIG_SUNXI_DRAM_LPDDR3_STOCK=y
 CONFIG_DRAM_CLK=552
 CONFIG_DRAM_ZQ=3881949
-CONFIG_MMC0_CD_PIN=""
 CONFIG_MMC_SUNXI_SLOT_EXTRA=2
 CONFIG_SPL_SPI_SUNXI=y
 # CONFIG_SYS_MALLOC_CLEAR_ON_INIT is not set
--- a/configs/pine_h64_defconfig
+++ b/configs/pine_h64_defconfig
@@ -5,7 +5,6 @@ CONFIG_SPL=y
 CONFIG_MACH_SUN50I_H6=y
 CONFIG_SUNXI_DRAM_H6_LPDDR3=y
 CONFIG_MACPWR="PC16"
-CONFIG_MMC0_CD_PIN="PF6"
 CONFIG_MMC_SUNXI_SLOT_EXTRA=2
 CONFIG_USB3_VBUS_PIN="PL5"
 CONFIG_SPL_SPI_SUNXI=y
--- a/configs/sopine_baseboard_defconfig
+++ b/configs/sopine_baseboard_defconfig
@@ -7,7 +7,6 @@ CONFIG_RESERVE_ALLWINNER_BOOT0_HEADER=y
 CONFIG_SUNXI_DRAM_LPDDR3_STOCK=y
 CONFIG_DRAM_CLK=552
 CONFIG_DRAM_ZQ=3881949
-CONFIG_MMC0_CD_PIN=""
 CONFIG_MMC_SUNXI_SLOT_EXTRA=2
 CONFIG_SPL_SPI_SUNXI=y
 # CONFIG_SYS_MALLOC_CLEAR_ON_INIT is not set
--- a/configs/tanix_tx6_defconfig
+++ b/configs/tanix_tx6_defconfig
@@ -5,6 +5,5 @@ CONFIG_SPL=y
 CONFIG_MACH_SUN50I_H6=y
 CONFIG_SUNXI_DRAM_H6_DDR3_1333=y
 CONFIG_DRAM_CLK=648
-CONFIG_MMC0_CD_PIN="PF6"
 CONFIG_MMC_SUNXI_SLOT_EXTRA=2
 # CONFIG_SYS_MALLOC_CLEAR_ON_INIT is not set
