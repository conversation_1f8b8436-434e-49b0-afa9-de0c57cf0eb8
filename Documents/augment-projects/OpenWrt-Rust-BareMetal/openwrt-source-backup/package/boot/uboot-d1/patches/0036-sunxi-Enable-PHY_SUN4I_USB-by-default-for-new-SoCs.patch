From b13140a914199dcdd80331fef6f33d47f008f1b4 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 5 Aug 2022 23:40:22 -0500
Subject: [PATCH 36/90] sunxi: Enable PHY_SUN4I_USB by default for new SoCs

With one exception (sun9i), all sunxi SoCs released to date use variants
of the same USB PHY. Instead of requiring each new SoC to duplicate the
PHY driver selection, enable it by default.

Series-to: <PERSON> <<EMAIL>>
Series-to: <PERSON><PERSON> <<EMAIL>>

Signed-off-by: <PERSON> <<EMAIL>>
---
 arch/arm/mach-sunxi/Kconfig   | 11 -----------
 drivers/phy/allwinner/Kconfig |  3 ++-
 2 files changed, 2 insertions(+), 12 deletions(-)

--- a/arch/arm/mach-sunxi/Kconfig
+++ b/arch/arm/mach-sunxi/Kconfig
@@ -162,7 +162,6 @@ endif
 
 config MACH_SUNXI_H3_H5
 	bool
-	select PHY_SUN4I_USB
 	select SUNXI_DE2
 	select SUNXI_DRAM_DW
 	select SUNXI_DRAM_DW_32BIT
@@ -191,7 +190,6 @@ config MACH_SUNIV
 config MACH_SUN4I
 	bool "sun4i (Allwinner A10)"
 	select CPU_V7A
-	select PHY_SUN4I_USB
 	select DRAM_SUN4I
 	select SUNXI_GEN_SUN4I
 	select SUPPORT_SPL
@@ -202,7 +200,6 @@ config MACH_SUN5I
 	bool "sun5i (Allwinner A13)"
 	select CPU_V7A
 	select DRAM_SUN4I
-	select PHY_SUN4I_USB
 	select SUNXI_GEN_SUN4I
 	select SUPPORT_SPL
 	imply SPL_SYS_I2C_LEGACY
@@ -216,7 +213,6 @@ config MACH_SUN6I
 	select ARCH_SUPPORT_PSCI
 	select SPL_ARMV7_SET_CORTEX_SMPEN
 	select DRAM_SUN6I
-	select PHY_SUN4I_USB
 	select SPL_I2C
 	select SUN6I_PRCM
 	select SUNXI_GEN_SUN6I
@@ -232,7 +228,6 @@ config MACH_SUN7I
 	select ARCH_SUPPORT_PSCI
 	select SPL_ARMV7_SET_CORTEX_SMPEN
 	select DRAM_SUN4I
-	select PHY_SUN4I_USB
 	select SUNXI_GEN_SUN4I
 	select SUPPORT_SPL
 	select ARMV7_BOOT_SEC_DEFAULT if OLD_SUNXI_KERNEL_COMPAT
@@ -246,7 +241,6 @@ config MACH_SUN8I_A23
 	select CPU_V7_HAS_VIRT
 	select ARCH_SUPPORT_PSCI
 	select DRAM_SUN8I_A23
-	select PHY_SUN4I_USB
 	select SPL_I2C
 	select SUNXI_GEN_SUN6I
 	select SUPPORT_SPL
@@ -260,7 +254,6 @@ config MACH_SUN8I_A33
 	select CPU_V7_HAS_VIRT
 	select ARCH_SUPPORT_PSCI
 	select DRAM_SUN8I_A33
-	select PHY_SUN4I_USB
 	select SPL_I2C
 	select SUNXI_GEN_SUN6I
 	select SUPPORT_SPL
@@ -271,7 +264,6 @@ config MACH_SUN8I_A83T
 	bool "sun8i (Allwinner A83T)"
 	select CPU_V7A
 	select DRAM_SUN8I_A83T
-	select PHY_SUN4I_USB
 	select SPL_I2C
 	select SUNXI_GEN_SUN6I
 	select MMC_SUNXI_HAS_NEW_MODE
@@ -299,7 +291,6 @@ config MACH_SUN8I_R40
 	select SUPPORT_SPL
 	select SUNXI_DRAM_DW
 	select SUNXI_DRAM_DW_32BIT
-	select PHY_SUN4I_USB
 	imply SPL_SYS_I2C_LEGACY
 
 config MACH_SUN8I_V3S
@@ -327,7 +318,6 @@ config MACH_SUN9I
 config MACH_SUN50I
 	bool "sun50i (Allwinner A64)"
 	select ARM64
-	select PHY_SUN4I_USB
 	select SUN6I_PRCM
 	select SUNXI_DE2
 	select SUNXI_GEN_SUN6I
@@ -350,7 +340,6 @@ config MACH_SUN50I_H5
 config MACH_SUN50I_H6
 	bool "sun50i (Allwinner H6)"
 	select ARM64
-	select PHY_SUN4I_USB
 	select DRAM_SUN50I_H6
 	select SUN50I_GEN_H6
 
--- a/drivers/phy/allwinner/Kconfig
+++ b/drivers/phy/allwinner/Kconfig
@@ -3,7 +3,8 @@
 #
 config PHY_SUN4I_USB
 	bool "Allwinner Sun4I USB PHY driver"
-	depends on ARCH_SUNXI
+	depends on ARCH_SUNXI && !MACH_SUN9I
+	default y
 	select DM_REGULATOR
 	select PHY
 	help
