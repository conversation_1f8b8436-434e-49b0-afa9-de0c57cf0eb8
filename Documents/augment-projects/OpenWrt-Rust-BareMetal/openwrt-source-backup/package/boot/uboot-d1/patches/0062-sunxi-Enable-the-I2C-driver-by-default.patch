From ad619478827b825d7b88dce22eb9b5e1c6ea7eb0 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sat, 6 Aug 2022 00:11:54 -0500
Subject: [PATCH 62/90] sunxi: Enable the I2C driver by default

This is used by quite a large number of boards, for PMIC/regulator or
LCD panel control.

Signed-off-by: <PERSON> <<EMAIL>>
---
 board/sunxi/Kconfig | 1 +
 1 file changed, 1 insertion(+)

--- a/board/sunxi/Kconfig
+++ b/board/sunxi/Kconfig
@@ -38,6 +38,7 @@ config BOARD_SUNXI
 	imply SPL_POWER
 	imply SPL_SERIAL
 	imply SUNXI_GPIO
+	imply SYS_I2C_MVTWSI
 	imply SYS_NS16550
 	imply SYSRESET
 	imply SYSRESET_WATCHDOG
