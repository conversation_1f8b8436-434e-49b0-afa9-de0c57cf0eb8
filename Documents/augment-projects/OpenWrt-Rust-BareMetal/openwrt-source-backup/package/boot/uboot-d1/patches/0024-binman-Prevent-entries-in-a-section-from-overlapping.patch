From b7150f7dd885012868c94b29ac4fe6152c065a95 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sat, 9 Oct 2021 10:43:56 -0500
Subject: [PATCH 24/90] binman: Prevent entries in a section from overlapping

Currently, if the "offset" property is given for an entry, the section's
running offset is completely ignored. This causes entries to overlap if
the provided offset is less than the size of the entries earlier in the
section. Avoid the overlap by only using the provided offset when it is
greater than the running offset.

The motivation for this change is the rule used by SPL to find U-Boot on
sunxi boards: U-Boot starts 32 KiB after the start of SPL, unless SPL is
larger than 32 KiB, in which case U-Boot immediately follows SPL.

Signed-off-by: <PERSON> <<EMAIL>>
---
 tools/binman/entry.py | 4 +++-
 1 file changed, 3 insertions(+), 1 deletion(-)

--- a/tools/binman/entry.py
+++ b/tools/binman/entry.py
@@ -483,7 +483,9 @@ class Entry(object):
             if self.offset_unset:
                 self.Raise('No offset set with offset-unset: should another '
                            'entry provide this correct offset?')
-            self.offset = tools.align(offset, self.align)
+        elif self.offset > offset:
+            offset = self.offset
+        self.offset = tools.align(offset, self.align)
         needed = self.pad_before + self.contents_size + self.pad_after
         needed = tools.align(needed, self.align_size)
         size = self.size
