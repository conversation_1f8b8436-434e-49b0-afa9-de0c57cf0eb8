From 37e19d9b8a23c88413dd845dbb3dd58dd3636a6d Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sat, 28 Aug 2021 00:36:33 -0500
Subject: [PATCH 51/90] gpio: axp: Add support for getting the pin function

Implement the .get_function operation, so the gpio command can report
the current function. Since the GPIOF_FUNC (versus GPIOF_UNUSED) mux
values vary among the PMICs, report all non-GPIO mux values as UNKNOWN.

Signed-off-by: <PERSON> <<EMAIL>>
---
 drivers/gpio/axp_gpio.c | 19 +++++++++++++++++++
 1 file changed, 19 insertions(+)

--- a/drivers/gpio/axp_gpio.c
+++ b/drivers/gpio/axp_gpio.c
@@ -39,6 +39,24 @@ static int axp_gpio_get_value(struct ude
 	return !!(ret & BIT(desc->status_offset + pin));
 }
 
+static int axp_gpio_get_function(struct udevice *dev, unsigned pin)
+{
+	const struct axp_gpio_desc *desc = dev_get_priv(dev);
+	int ret;
+
+	ret = pmic_reg_read(dev->parent, desc->pins[pin]);
+	if (ret < 0)
+		return ret;
+
+	ret &= AXP_GPIO_CTRL_MASK;
+	if (ret == desc->input_mux)
+		return GPIOF_INPUT;
+	if (ret == AXP_GPIO_CTRL_OUTPUT_HIGH || ret == AXP_GPIO_CTRL_OUTPUT_LOW)
+		return GPIOF_OUTPUT;
+
+	return GPIOF_UNKNOWN;
+}
+
 static int axp_gpio_set_flags(struct udevice *dev, unsigned pin, ulong flags)
 {
 	const struct axp_gpio_desc *desc = dev_get_priv(dev);
@@ -60,6 +78,7 @@ static int axp_gpio_set_flags(struct ude
 
 static const struct dm_gpio_ops axp_gpio_ops = {
 	.get_value		= axp_gpio_get_value,
+	.get_function		= axp_gpio_get_function,
 	.xlate			= gpio_xlate_offs_flags,
 	.set_flags		= axp_gpio_set_flags,
 };
