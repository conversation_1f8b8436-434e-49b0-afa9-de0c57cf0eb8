From 52c172782d659750b447572281cd11835d1edf58 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sun, 31 Jul 2022 18:19:39 -0500
Subject: [PATCH 53/90] gpio: axp: Report the correct value for outputs

Signed-off-by: <PERSON> <<EMAIL>>
---
 drivers/gpio/axp_gpio.c | 9 +++++++++
 1 file changed, 9 insertions(+)

--- a/drivers/gpio/axp_gpio.c
+++ b/drivers/gpio/axp_gpio.c
@@ -33,6 +33,15 @@ static int axp_gpio_get_value(struct ude
 	const struct axp_gpio_desc *desc = dev_get_priv(dev);
 	int ret;
 
+	ret = pmic_reg_read(dev->parent, desc->pins[pin]);
+	if (ret < 0)
+		return ret;
+
+	if (ret == AXP_GPIO_CTRL_OUTPUT_LOW)
+		return 0;
+	if (ret == AXP_GPIO_CTRL_OUTPUT_HIGH)
+		return 1;
+
 	ret = pmic_reg_read(dev->parent, desc->status_reg);
 	if (ret < 0)
 		return ret;
