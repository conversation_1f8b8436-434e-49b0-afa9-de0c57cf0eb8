From 46f73ce33478734dabd5a93d425f7148b60198e1 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Wed, 8 Sep 2021 21:31:06 -0500
Subject: [PATCH 84/90] spi: sunxi: Hack up the driver for the D1

Signed-off-by: <PERSON> <<EMAIL>>
---
 drivers/spi/spi-sunxi.c | 3 ++-
 1 file changed, 2 insertions(+), 1 deletion(-)

--- a/drivers/spi/spi-sunxi.c
+++ b/drivers/spi/spi-sunxi.c
@@ -30,6 +30,7 @@
 #include <asm/global_data.h>
 #include <dm/device_compat.h>
 #include <linux/bitops.h>
+#include <linux/log2.h>
 
 #include <asm/bitops.h>
 #include <asm/io.h>
@@ -85,7 +86,7 @@ DECLARE_GLOBAL_DATA_PTR;
 #define SUN4I_SPI_DEFAULT_RATE		1000000
 #define SUN4I_SPI_TIMEOUT_MS		1000
 
-#define SPI_REG(priv, reg)		((priv)->base + \
+#define SPI_REG(priv, reg)		(void *)((priv)->base + \
 					(priv)->variant->regs[reg])
 #define SPI_BIT(priv, bit)		((priv)->variant->bits[bit])
 #define SPI_CS(priv, cs)		(((cs) << SPI_BIT(priv, SPI_TCR_CS_SEL)) & \
