From 08b45a89c7b25eb7828589360cf4ca2d9910cc59 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 5 Aug 2022 21:48:53 -0500
Subject: [PATCH 70/90] sunxi: Move TEXT_BASE to the board Kconfig

This is how the vast majority of platforms provided TEXT_BASE.
sunxi was the exception here.

Signed-off-by: <PERSON> <<EMAIL>>
---
 board/sunxi/Kconfig | 6 ++++++
 boot/Kconfig        | 4 ----
 2 files changed, 6 insertions(+), 4 deletions(-)

--- a/board/sunxi/Kconfig
+++ b/board/sunxi/Kconfig
@@ -137,6 +137,12 @@ config SYS_LOAD_ADDR
 config SYS_SOC
 	default "sunxi"
 
+config TEXT_BASE
+	default 0x81700000 if MACH_SUNIV
+	default 0x2a000000 if MACH_SUN9I
+	default 0x4a000000 if SUNXI_MINIMUM_DRAM_MB >= 256
+	default 0x42e00000 if SUNXI_MINIMUM_DRAM_MB >= 64
+
 menu "sunxi board options"
 
 choice
--- a/boot/Kconfig
+++ b/boot/Kconfig
@@ -633,10 +633,6 @@ config TEXT_BASE
 	depends on HAVE_TEXT_BASE
 	default 0x0 if POSITION_INDEPENDENT
 	default 0x80800000 if ARCH_OMAP2PLUS || ARCH_K3
-	default 0x81700000 if MACH_SUNIV
-	default 0x2a000000 if MACH_SUN9I
-	default 0x4a000000 if SUNXI_MINIMUM_DRAM_MB >= 256
-	default 0x42e00000 if SUNXI_MINIMUM_DRAM_MB >= 64
 	hex "Text Base"
 	help
 	  The address in memory that U-Boot will be running from, initially.
