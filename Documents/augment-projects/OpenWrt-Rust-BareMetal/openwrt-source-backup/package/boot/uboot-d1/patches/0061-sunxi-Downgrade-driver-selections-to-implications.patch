From 6c8707fcd3372015829a1e8b8d5e8030c5806382 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sat, 6 Aug 2022 00:10:46 -0500
Subject: [PATCH 61/90] sunxi: Downgrade driver selections to implications

While not especially likely, it is plausible that someone wants to build
U-Boot without GPIO or UART support. Don't force building these drivers.

Signed-off-by: <PERSON> <<EMAIL>>
---
 board/sunxi/Kconfig | 4 ++--
 1 file changed, 2 insertions(+), 2 deletions(-)

--- a/board/sunxi/Kconfig
+++ b/board/sunxi/Kconfig
@@ -16,8 +16,6 @@ config BOARD_SUNXI
 	select OF_BOARD_SETUP
 	select PIN<PERSON><PERSON>
 	select SPL_SEPARATE_BSS if SPL
-	select SUNXI_GPIO if GPIO
-	select SYS_NS16550 if SERIAL
 	select SUPPORT_SPL
 	select SYS_RELOC_GD_ENV_ADDR
 	select USB if DISTRO_DEFAULTS
@@ -39,6 +37,8 @@ config BOARD_SUNXI
 	imply SPL_MMC if M<PERSON>
 	imply SPL_POWER
 	imply SPL_SERIAL
+	imply SUNXI_GPIO
+	imply SYS_NS16550
 	imply SYSRESET
 	imply SYSRESET_WATCHDOG
 	imply SYSRESET_WATCHDOG_AUTO
