From 5d60490d0f0ca0a5d414ba9a4e41ceea8a98b4d2 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sat, 6 Aug 2022 00:07:47 -0500
Subject: [PATCH 68/90] sunxi: Move SPL_TEXT_BASE to the board Kconfig

It makes sense to put this near the definition of SUNXI_SRAM_ADDRESS.

Signed-off-by: <PERSON> <<EMAIL>>
---
 board/sunxi/Kconfig | 5 +++++
 common/spl/Kconfig  | 3 ---
 2 files changed, 5 insertions(+), 3 deletions(-)

--- a/board/sunxi/Kconfig
+++ b/board/sunxi/Kconfig
@@ -73,6 +73,11 @@ config SPL_STACK_R_ADDR
 	default 0x4fe00000 if SUNXI_MINIMUM_DRAM_MB >= 256
 	default 0x43e00000 if SUNXI_MINIMUM_DRAM_MB >= 64
 
+config SPL_TEXT_BASE
+	default 0x10060 if MACH_SUN9I || MACH_SUN50I || MACH_SUN50I_H5
+	default 0x20060 if SUN50I_GEN_H6
+	default 0x00060
+
 config SUNXI_MINIMUM_DRAM_MB
 	int
 	default 32 if MACH_SUNIV
--- a/common/spl/Kconfig
+++ b/common/spl/Kconfig
@@ -261,9 +261,6 @@ config SPL_TEXT_BASE
 	default 0x402F4000 if AM43XX
 	default 0x402F0400 if AM33XX
 	default 0x40301350 if OMAP54XX
-	default 0x10060 if MACH_SUN50I || MACH_SUN50I_H5 || MACH_SUN9I
-	default 0x20060 if SUN50I_GEN_H6
-	default 0x00060 if ARCH_SUNXI
 	default 0xfffc0000 if ARCH_ZYNQMP
 	default 0x0
 	help
