From 64de4fd71d35c6154a0f7b4c7c02cb24e978a4ce Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 5 Aug 2022 23:45:19 -0500
Subject: [PATCH 54/90] sunxi: Fix default-enablement of USB host drivers

We tried to enable USB_EHCI_GENERIC and USB_OHCI_GENERIC by default.
This did not work because those symbols depend on USB_EHCI_HCD and
USB_OHCI_HCD, which were not enabled. Fix this by implying all four.

Signed-off-by: <PERSON> <<EMAIL>>
---
 arch/arm/Kconfig         | 4 ++++
 drivers/usb/host/Kconfig | 2 --
 2 files changed, 4 insertions(+), 2 deletions(-)

--- a/arch/arm/Kconfig
+++ b/arch/arm/Kconfig
@@ -1142,7 +1142,11 @@ config ARCH_SUNXI
 	imply SYSRESET
 	imply SYSRESET_WATCHDOG
 	imply SYSRESET_WATCHDOG_AUTO
+	imply USB_EHCI_GENERIC
+	imply USB_EHCI_HCD
 	imply USB_GADGET
+	imply USB_OHCI_GENERIC
+	imply USB_OHCI_HCD
 	imply WDT
 
 config ARCH_U8500
--- a/drivers/usb/host/Kconfig
+++ b/drivers/usb/host/Kconfig
@@ -281,7 +281,6 @@ config USB_EHCI_ZYNQ
 config USB_EHCI_GENERIC
 	bool "Support for generic EHCI USB controller"
 	depends on DM_USB
-	default ARCH_SUNXI
 	---help---
 	  Enables support for generic EHCI controller.
 
@@ -343,7 +342,6 @@ config USB_OHCI_PCI
 
 config USB_OHCI_GENERIC
 	bool "Support for generic OHCI USB controller"
-	default ARCH_SUNXI
 	---help---
 	  Enables support for generic OHCI controller.
 
