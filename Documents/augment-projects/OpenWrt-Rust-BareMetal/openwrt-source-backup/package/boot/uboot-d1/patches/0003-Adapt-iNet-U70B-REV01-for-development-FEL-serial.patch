From ef808412055d1ef6fe77ff130d3f5a9432fef2d7 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Tue, 3 May 2022 22:35:12 -0500
Subject: [PATCH 03/90] Adapt iNet U70B REV01 for development (FEL + serial)

Signed-off-by: <PERSON> <<EMAIL>>
---
 arch/arm/dts/sun8i-a33-inet-u70b-rev1.dts | 11 +++++++++++
 configs/iNet_U70B_rev1_defconfig          | 14 +++++---------
 2 files changed, 16 insertions(+), 9 deletions(-)

--- a/arch/arm/dts/sun8i-a33-inet-u70b-rev1.dts
+++ b/arch/arm/dts/sun8i-a33-inet-u70b-rev1.dts
@@ -11,6 +11,7 @@
 
 	aliases {
 		ethernet0 = &rtl8723cs;
+		serial0 = &uart0;
 	};
 
 	panel: panel {
@@ -76,6 +77,10 @@
 	};
 };
 
+&mmc0 {
+	status = "disabled";
+};
+
 &mmc1 {
 	pinctrl-0 = <&mmc1_pg_pins>;
 	pinctrl-names = "default";
@@ -158,6 +163,12 @@
 	status = "okay";
 };
 
+&uart0 {
+	pinctrl-0 = <&uart0_pf_pins>;
+	pinctrl-names = "default";
+	status = "okay";
+};
+
 &uart1 {
 	pinctrl-0 = <&uart1_pg_pins>, <&uart1_cts_rts_pg_pins>;
 	pinctrl-names = "default";
--- a/configs/iNet_U70B_rev1_defconfig
+++ b/configs/iNet_U70B_rev1_defconfig
@@ -1,12 +1,12 @@
 CONFIG_ARM=y
 CONFIG_ARCH_SUNXI=y
 CONFIG_DEFAULT_DEVICE_TREE="sun8i-a33-inet-u70b-rev1"
-# CONFIG_SPL_SERIAL is not set
 CONFIG_SPL=y
 CONFIG_MACH_SUN8I_A33=y
 CONFIG_DRAM_CLK=480
 CONFIG_DRAM_ZQ=31675
 CONFIG_DRAM_ODT_EN=y
+CONFIG_UART0_PORT_F=y
 CONFIG_MMC0_CD_PIN="PB4"
 CONFIG_VIDEO_LCD_MODE="x:1024,y:600,depth:18,pclk_khz:51000,le:158,ri:162,up:25,lo:10,hs:20,vs:3,sync:3,vmode:0"
 CONFIG_VIDEO_LCD_DCLK_PHASE=0
@@ -14,19 +14,15 @@ CONFIG_VIDEO_LCD_POWER="PH7"
 CONFIG_VIDEO_LCD_BL_EN="PH6"
 CONFIG_VIDEO_LCD_BL_PWM="PH0"
 # CONFIG_SYS_MALLOC_CLEAR_ON_INIT is not set
-CONFIG_CMD_BIND=y
-CONFIG_CMD_CLK=y
-CONFIG_CMD_PWM=y
-CONFIG_CMD_I2C=y
-CONFIG_CMD_WDT=y
+CONFIG_PREBOOT="fastboot usb 0"
 CONFIG_CMD_PMIC=y
 CONFIG_CMD_REGULATOR=y
-# CONFIG_NET is not set
 CONFIG_AXP_GPIO=y
 CONFIG_REGULATOR_AXP=y
 CONFIG_REGULATOR_AXP_USB_POWER=y
 CONFIG_AXP_DLDO1_VOLT=3300
 CONFIG_DM_PWM=y
 CONFIG_PWM_SUNXI=y
-# CONFIG_REQUIRE_SERIAL_CONSOLE is not set
-CONFIG_USB_MUSB_HOST=y
+CONFIG_REMOTEPROC_SUN6I_AR100=y
+CONFIG_USB_MUSB_GADGET=y
+CONFIG_WATCHDOG_AUTOSTART=y
