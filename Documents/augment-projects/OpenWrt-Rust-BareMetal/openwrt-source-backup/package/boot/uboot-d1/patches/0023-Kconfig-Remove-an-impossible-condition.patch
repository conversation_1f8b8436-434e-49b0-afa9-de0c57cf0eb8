From 2d85df851c590b454749ac989a778bb226637bfc Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON>ERPA<PERSON> <<EMAIL>>
Date: Tue, 6 Jun 2023 15:08:39 +0000
Subject: [PATCH 23/90] Kconfig: Remove an impossible condition

ARCH_SUNXI selects BINMAN, so the condition "!BINMAN && ARCH_SUNXI"
is impossible to satisfy.

Signed-off-by: <PERSON> <<EMAIL>>
Signed-off-by: <PERSON><PERSON><PERSON> HERPA<PERSON> <<EMAIL>>
---
 Kconfig | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

--- a/Kconfig
+++ b/Kconfig
@@ -459,7 +459,7 @@ config BUILD_TARGET
 	default "u-boot-with-spl.kwb" if ARCH_MVEBU && SPL
 	default "u-boot-elf.srec" if RCAR_GEN3
 	default "u-boot.itb" if !BINMAN && SPL_LOAD_FIT && (ARCH_ROCKCHIP || \
-				ARCH_SUNXI || RISCV || ARCH_ZYNQMP)
+				RISCV || ARCH_ZYNQMP)
 	default "u-boot.kwb" if ARCH_KIRKWOOD
 	default "u-boot-with-spl.bin" if ARCH_AT91 && SPL_NAND_SUPPORT
 	default "u-boot-with-spl.imx" if ARCH_MX6 && SPL
