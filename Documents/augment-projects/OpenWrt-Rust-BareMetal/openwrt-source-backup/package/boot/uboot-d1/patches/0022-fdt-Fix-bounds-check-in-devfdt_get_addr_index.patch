From 0e4edc3a01f179337bb0bd0d31855dbce338a23e Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sun, 30 Oct 2022 14:53:45 -0500
Subject: [PATCH 22/90] fdt: Fix bounds check in devfdt_get_addr_index

reg must contain enough cells for the entire next address/size pair
after skipping `index` pairs. The previous code allows an out-of-bounds
read when na + ns > 1.

Series-to: <PERSON> <<EMAIL>>

Fixes: 69b41388ba45 ("dm: core: Add a new api to get indexed device address")
Signed-off-by: <PERSON> <<EMAIL>>
---
 drivers/core/fdtaddr.c | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

--- a/drivers/core/fdtaddr.c
+++ b/drivers/core/fdtaddr.c
@@ -43,7 +43,7 @@ fdt_addr_t devfdt_get_addr_index(const s
 		}
 
 		reg = fdt_getprop(gd->fdt_blob, offset, "reg", &len);
-		if (!reg || (len <= (index * sizeof(fdt32_t) * (na + ns)))) {
+		if (!reg || (len < ((index + 1) * sizeof(fdt32_t) * (na + ns)))) {
 			debug("Req index out of range\n");
 			return FDT_ADDR_T_NONE;
 		}
