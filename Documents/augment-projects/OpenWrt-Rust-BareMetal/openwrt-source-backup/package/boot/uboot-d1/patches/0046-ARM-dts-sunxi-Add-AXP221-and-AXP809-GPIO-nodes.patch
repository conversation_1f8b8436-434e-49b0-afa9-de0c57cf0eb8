From 9e34036e651528df9575c2ffb38d78ae1f613481 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sat, 21 Aug 2021 17:11:54 -0500
Subject: [PATCH 46/90] ARM: dts: sunxi: Add AXP221 and AXP809 GPIO nodes

These PMICs each have two GPIO pins, and are supported by the axp_gpio
driver. In order to convert the axp_gpio driver to probe using the
device tree, the corresponding device tree nodes must be present. Add
them, following the same binding as the AXP209 and AXP813.

Signed-off-by: <PERSON> <<EMAIL>>
---
 arch/arm/dts/axp22x.dtsi | 6 ++++++
 arch/arm/dts/axp809.dtsi | 7 +++++++
 2 files changed, 13 insertions(+)

--- a/arch/arm/dts/axp22x.dtsi
+++ b/arch/arm/dts/axp22x.dtsi
@@ -67,6 +67,12 @@
 		status = "disabled";
 	};
 
+	axp_gpio: gpio {
+		compatible = "x-powers,axp221-gpio";
+		gpio-controller;
+		#gpio-cells = <2>;
+	};
+
 	regulators {
 		/* Default work frequency for buck regulators */
 		x-powers,dcdc-freq = <3000>;
--- a/arch/arm/dts/axp809.dtsi
+++ b/arch/arm/dts/axp809.dtsi
@@ -50,4 +50,11 @@
 	compatible = "x-powers,axp809";
 	interrupt-controller;
 	#interrupt-cells = <1>;
+
+	axp_gpio: gpio {
+		compatible = "x-powers,axp809-gpio",
+			     "x-powers,axp221-gpio";
+		gpio-controller;
+		#gpio-cells = <2>;
+	};
 };
