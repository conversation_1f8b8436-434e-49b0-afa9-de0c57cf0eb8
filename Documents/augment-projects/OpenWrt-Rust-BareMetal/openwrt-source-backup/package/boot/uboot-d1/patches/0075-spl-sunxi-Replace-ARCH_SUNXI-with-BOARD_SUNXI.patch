From 7f06dca4df9302a22a8d27af887da50a67b7dd1d Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sat, 6 Aug 2022 00:09:38 -0500
Subject: [PATCH 75/90] spl: sunxi: Replace ARCH_SUNXI with BOARD_SUNXI

This provides a unified configuration across all sunxi boards,
regardless of CPU architecture.

Signed-off-by: <PERSON> <<EMAIL>>
---
 common/spl/Kconfig   | 12 ++++++------
 scripts/Makefile.spl |  2 +-
 2 files changed, 7 insertions(+), 7 deletions(-)

--- a/common/spl/Kconfig
+++ b/common/spl/Kconfig
@@ -111,7 +111,7 @@ config SPL_PAD_TO
 config SPL_HAS_BSS_LINKER_SECTION
 	depends on SPL_FRAMEWORK
 	bool "Use a specific address for the BSS via the linker script"
-	default y if ARCH_SUNXI || ARCH_MX6 || ARCH_OMAP2PLUS || MIPS || RISCV || ARCH_ZYNQMP
+	default y if ARCH_MX6 || ARCH_OMAP2PLUS || ARCH_ZYNQMP || BOARD_SUNXI || MIPS || RISCV
 
 config SPL_BSS_START_ADDR
 	hex "Link address for the BSS within the SPL binary"
@@ -335,7 +335,7 @@ config SPL_SYS_MALLOC_SIMPLE
 config SPL_SHARES_INIT_SP_ADDR
 	bool "SPL and U-Boot use the same initial stack pointer location"
 	depends on (ARM || ARCH_JZ47XX || MICROBLAZE || RISCV) && SPL_FRAMEWORK
-	default n if ARCH_SUNXI || ARCH_MX6 || ARCH_MX7
+	default n if BOARD_SUNXI || ARCH_MX6 || ARCH_MX7
 	default y
 	help
 	  In many cases, we can use the same initial stack pointer address for
@@ -453,7 +453,7 @@ config SPL_DISPLAY_PRINT
 
 config SYS_MMCSD_RAW_MODE_U_BOOT_USE_SECTOR
 	bool "MMC raw mode: by sector"
-	default y if ARCH_SUNXI || ARCH_DAVINCI || ARCH_UNIPHIER || \
+	default y if BOARD_SUNXI || ARCH_DAVINCI || ARCH_UNIPHIER || \
 		     ARCH_MX6 || ARCH_MX7 || \
 		     ARCH_ROCKCHIP || ARCH_MVEBU ||  ARCH_SOCFPGA || \
 		     ARCH_AT91 || ARCH_ZYNQ || ARCH_KEYSTONE || OMAP34XX || \
@@ -466,7 +466,7 @@ config SYS_MMCSD_RAW_MODE_U_BOOT_USE_SEC
 config SYS_MMCSD_RAW_MODE_U_BOOT_SECTOR
 	hex "Address on the MMC to load U-Boot from"
 	depends on SYS_MMCSD_RAW_MODE_U_BOOT_USE_SECTOR
-	default 0x40 if ARCH_SUNXI
+	default 0x40 if BOARD_SUNXI
 	default 0x75 if ARCH_DAVINCI
 	default 0x8a if ARCH_MX6 || ARCH_MX7
 	default 0x100 if ARCH_UNIPHIER
@@ -483,7 +483,7 @@ config SYS_MMCSD_RAW_MODE_U_BOOT_SECTOR
 config SYS_MMCSD_RAW_MODE_U_BOOT_DATA_PART_OFFSET
 	hex "U-Boot main hardware partition image offset"
 	depends on SYS_MMCSD_RAW_MODE_U_BOOT_USE_SECTOR
-	default 0x10 if ARCH_SUNXI
+	default 0x10 if BOARD_SUNXI
 	default 0x0
 	help
 	  On some platforms SPL location depends on hardware partition. The ROM
@@ -1308,7 +1308,7 @@ endif # SPL_SPI_FLASH_SUPPORT
 
 config SYS_SPI_U_BOOT_OFFS
 	hex "address of u-boot payload in SPI flash"
-	default 0x8000 if ARCH_SUNXI
+	default 0x8000 if BOARD_SUNXI
 	default 0x0
 	depends on SPL_SPI_LOAD || SPL_SPI_SUNXI
 	help
--- a/scripts/Makefile.spl
+++ b/scripts/Makefile.spl
@@ -264,7 +264,7 @@ endif
 
 INPUTS-$(CONFIG_TARGET_SOCFPGA_SOC64) += $(obj)/u-boot-spl-dtb.hex
 
-ifdef CONFIG_ARCH_SUNXI
+ifdef CONFIG_BOARD_SUNXI
 INPUTS-y	+= $(obj)/sunxi-spl.bin
 
 ifdef CONFIG_NAND_SUNXI
