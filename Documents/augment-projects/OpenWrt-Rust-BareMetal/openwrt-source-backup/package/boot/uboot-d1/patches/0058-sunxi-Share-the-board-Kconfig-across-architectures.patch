From 5d197433cd54085306e369ac260e09fe6077bfbb Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Thu, 4 Aug 2022 21:30:57 -0500
Subject: [PATCH 58/90] sunxi: Share the board Kconfig across architectures

With the introduction of the Allwinner D1, the sunxi board family now
spans multiple architectures (ARM and RISC-V). Since ARCH_SUNXI depends
on ARM, it cannot be used to gate architecture-independent options.
Specifically, this means the board Kconfig file cannot be sourced from
inside the "if ARCH_SUNXI" block.

Introduce a new BOARD_SUNXI symbol that can be selected by both
ARCH_SUNXI now and the new RISC-V SoC symbols when they are added, and
use it to gate the architecture-independent board options.

Signed-off-by: <PERSON> <<EMAIL>>
---
 arch/Kconfig                |  1 +
 arch/arm/Kconfig            |  1 +
 arch/arm/mach-sunxi/Kconfig |  2 --
 board/sunxi/Kconfig         | 11 +++++++++++
 4 files changed, 13 insertions(+), 2 deletions(-)

--- a/arch/Kconfig
+++ b/arch/Kconfig
@@ -482,6 +482,7 @@ source "arch/Kconfig.nxp"
 endif
 
 source "board/keymile/Kconfig"
+source "board/sunxi/Kconfig"
 
 if MIPS || MICROBLAZE
 
--- a/arch/arm/Kconfig
+++ b/arch/arm/Kconfig
@@ -1090,6 +1090,7 @@ config ARCH_SOCFPGA
 config ARCH_SUNXI
 	bool "Support sunxi (Allwinner) SoCs"
 	select BINMAN
+	select BOARD_SUNXI
 	select CMD_GPIO if GPIO
 	select CMD_MMC if MMC
 	select CMD_USB if DISTRO_DEFAULTS && USB_HOST
--- a/arch/arm/mach-sunxi/Kconfig
+++ b/arch/arm/mach-sunxi/Kconfig
@@ -959,8 +959,6 @@ config BLUETOOTH_DT_DEVICE_FIXUP
 	  The used address is "bdaddr" if set, and "ethaddr" with the LSB
 	  flipped elsewise.
 
-source "board/sunxi/Kconfig"
-
 endif
 
 config CHIP_DIP_SCAN
--- a/board/sunxi/Kconfig
+++ b/board/sunxi/Kconfig
@@ -1,3 +1,10 @@
+config BOARD_SUNXI
+	bool
+
+if BOARD_SUNXI
+
+menu "sunxi board options"
+
 choice
 	prompt "SPL Image Type"
 	depends on SPL
@@ -23,3 +30,7 @@ config SPL_IMAGE_TYPE
 	string
 	default "sunxi_egon" if SPL_IMAGE_TYPE_SUNXI_EGON
 	default "sunxi_toc0" if SPL_IMAGE_TYPE_SUNXI_TOC0
+
+endmenu
+
+endif
