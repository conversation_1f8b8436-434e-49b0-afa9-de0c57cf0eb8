From c750151e1107a8d46ca0f9bd30c1da276b142ec1 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Thu, 26 Aug 2021 18:02:54 -0500
Subject: [PATCH 06/90] gpio: axp/sunxi: Remove virtual VBUS detection GPIO

Now that this functionality is modeled using the device tree and
regulator uclass, the named GPIO is not referenced anywhere. Remove it.

Signed-off-by: <PERSON> <<EMAIL>>
---
 arch/arm/include/asm/arch-sunxi/gpio.h |  1 -
 drivers/gpio/axp_gpio.c                | 21 ++++-----------------
 drivers/gpio/sunxi_gpio.c              |  6 +-----
 include/axp209.h                       |  1 -
 include/axp221.h                       |  1 -
 include/axp809.h                       |  1 -
 include/axp818.h                       |  1 -
 7 files changed, 5 insertions(+), 27 deletions(-)

--- a/arch/arm/include/asm/arch-sunxi/gpio.h
+++ b/arch/arm/include/asm/arch-sunxi/gpio.h
@@ -209,7 +209,6 @@ enum sunxi_gpio_number {
 
 /* Virtual AXP0 GPIOs */
 #define SUNXI_GPIO_AXP0_PREFIX "AXP0-"
-#define SUNXI_GPIO_AXP0_VBUS_DETECT	4
 #define SUNXI_GPIO_AXP0_VBUS_ENABLE	5
 #define SUNXI_GPIO_AXP0_GPIO_COUNT	6
 
--- a/drivers/gpio/axp_gpio.c
+++ b/drivers/gpio/axp_gpio.c
@@ -36,18 +36,11 @@ static int axp_gpio_direction_input(stru
 {
 	u8 reg;
 
-	switch (pin) {
-#ifndef CONFIG_AXP152_POWER /* NA on axp152 */
-	case SUNXI_GPIO_AXP0_VBUS_DETECT:
-		return 0;
-#endif
-	default:
-		reg = axp_get_gpio_ctrl_reg(pin);
-		if (reg == 0)
-			return -EINVAL;
+	reg = axp_get_gpio_ctrl_reg(pin);
+	if (reg == 0)
+		return -EINVAL;
 
-		return pmic_bus_write(reg, AXP_GPIO_CTRL_INPUT);
-	}
+	return pmic_bus_write(reg, AXP_GPIO_CTRL_INPUT);
 }
 
 static int axp_gpio_direction_output(struct udevice *dev, unsigned pin,
@@ -83,12 +76,6 @@ static int axp_gpio_get_value(struct ude
 	int ret;
 
 	switch (pin) {
-#ifndef CONFIG_AXP152_POWER /* NA on axp152 */
-	case SUNXI_GPIO_AXP0_VBUS_DETECT:
-		ret = pmic_bus_read(AXP_POWER_STATUS, &val);
-		mask = AXP_POWER_STATUS_VBUS_PRESENT;
-		break;
-#endif
 #ifdef AXP_MISC_CTRL_N_VBUSEN_FUNC
 	/* Only available on later PMICs */
 	case SUNXI_GPIO_AXP0_VBUS_ENABLE:
--- a/drivers/gpio/sunxi_gpio.c
+++ b/drivers/gpio/sunxi_gpio.c
@@ -117,11 +117,7 @@ int sunxi_name_to_gpio(const char *name)
 #if !defined CONFIG_SPL_BUILD && defined CONFIG_AXP_GPIO
 	char lookup[8];
 
-	if (strcasecmp(name, "AXP0-VBUS-DETECT") == 0) {
-		sprintf(lookup, SUNXI_GPIO_AXP0_PREFIX "%d",
-			SUNXI_GPIO_AXP0_VBUS_DETECT);
-		name = lookup;
-	} else if (strcasecmp(name, "AXP0-VBUS-ENABLE") == 0) {
+	if (strcasecmp(name, "AXP0-VBUS-ENABLE") == 0) {
 		sprintf(lookup, SUNXI_GPIO_AXP0_PREFIX "%d",
 			SUNXI_GPIO_AXP0_VBUS_ENABLE);
 		name = lookup;
--- a/include/axp209.h
+++ b/include/axp209.h
@@ -77,7 +77,6 @@ enum axp209_reg {
 #ifdef CONFIG_AXP209_POWER
 #define AXP_POWER_STATUS		0x00
 #define AXP_POWER_STATUS_ALDO_IN		BIT(0)
-#define AXP_POWER_STATUS_VBUS_PRESENT		BIT(5)
 #define AXP_GPIO0_CTRL			0x90
 #define AXP_GPIO1_CTRL			0x92
 #define AXP_GPIO2_CTRL			0x93
--- a/include/axp221.h
+++ b/include/axp221.h
@@ -53,7 +53,6 @@
 #ifdef CONFIG_AXP221_POWER
 #define AXP_POWER_STATUS		0x00
 #define AXP_POWER_STATUS_ALDO_IN		BIT(0)
-#define AXP_POWER_STATUS_VBUS_PRESENT		BIT(5)
 #define AXP_VBUS_IPSOUT			0x30
 #define AXP_VBUS_IPSOUT_DRIVEBUS		(1 << 2)
 #define AXP_MISC_CTRL			0x8f
--- a/include/axp809.h
+++ b/include/axp809.h
@@ -47,7 +47,6 @@
 #ifdef CONFIG_AXP809_POWER
 #define AXP_POWER_STATUS		0x00
 #define AXP_POWER_STATUS_ALDO_IN		BIT(0)
-#define AXP_POWER_STATUS_VBUS_PRESENT		BIT(5)
 #define AXP_VBUS_IPSOUT			0x30
 #define AXP_VBUS_IPSOUT_DRIVEBUS		(1 << 2)
 #define AXP_MISC_CTRL			0x8f
--- a/include/axp818.h
+++ b/include/axp818.h
@@ -61,7 +61,6 @@
 #ifdef CONFIG_AXP818_POWER
 #define AXP_POWER_STATUS		0x00
 #define AXP_POWER_STATUS_ALDO_IN		BIT(0)
-#define AXP_POWER_STATUS_VBUS_PRESENT		BIT(5)
 #define AXP_VBUS_IPSOUT			0x30
 #define AXP_VBUS_IPSOUT_DRIVEBUS		(1 << 2)
 #define AXP_MISC_CTRL			0x8f
