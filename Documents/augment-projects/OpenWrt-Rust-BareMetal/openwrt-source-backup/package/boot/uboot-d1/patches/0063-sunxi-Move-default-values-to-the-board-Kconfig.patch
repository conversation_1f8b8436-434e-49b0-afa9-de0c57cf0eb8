From fde804c2ece090eb7802a218781e38c7c6d6f00d Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 5 Aug 2022 23:10:11 -0500
Subject: [PATCH 63/90] sunxi: Move default values to the board Kconfig

This keeps all of the defaults for sunxi platforms in one place. Most of
these only depend on architecture-independent features of the SoC (clock
tree or SRAM layout) anyway.

No functional change; just some minor help text cleanup.

Signed-off-by: <PERSON> <<EMAIL>>
---
 arch/arm/mach-sunxi/Kconfig | 67 ------------------------------------
 board/sunxi/Kconfig         | 68 +++++++++++++++++++++++++++++++++++++
 2 files changed, 68 insertions(+), 67 deletions(-)

--- a/arch/arm/mach-sunxi/Kconfig
+++ b/arch/arm/mach-sunxi/Kconfig
@@ -1,8 +1,5 @@
 if ARCH_SUNXI
 
-config IDENT_STRING
-	default " Allwinner Technology"
-
 config DRAM_SUN4I
 	bool
 	help
@@ -99,17 +96,6 @@ config AXP_PMIC_BUS
 	  Select this PMIC bus access helpers for Sunxi platform PRCM or other
 	  AXP family PMIC devices.
 
-config SUNXI_SRAM_ADDRESS
-	hex
-	default 0x10000 if MACH_SUN9I || MACH_SUN50I || MACH_SUN50I_H5
-	default 0x20000 if SUN50I_GEN_H6
-	default 0x0
-	---help---
-	Older Allwinner SoCs have their mask boot ROM mapped just below 4GB,
-	with the first SRAM region being located at address 0.
-	Some newer SoCs map the boot ROM at address 0 instead and move the
-	SRAM to a different address.
-
 config SUNXI_A64_TIMER_ERRATUM
 	bool
 
@@ -562,48 +548,6 @@ config DRAM_ODT_CORRECTION
 	then the correction is negative. Usually the value for this is 0.
 endif
 
-config SYS_CLK_FREQ
-	default 408000000 if MACH_SUNIV
-	default 1008000000 if MACH_SUN4I
-	default 1008000000 if MACH_SUN5I
-	default 1008000000 if MACH_SUN6I
-	default 912000000 if MACH_SUN7I
-	default 816000000 if MACH_SUN50I || MACH_SUN50I_H5
-	default 1008000000 if MACH_SUN8I
-	default 1008000000 if MACH_SUN9I
-	default 888000000 if MACH_SUN50I_H6
-	default 1008000000 if MACH_SUN50I_H616
-
-config SYS_CONFIG_NAME
-	default "suniv" if MACH_SUNIV
-	default "sun4i" if MACH_SUN4I
-	default "sun5i" if MACH_SUN5I
-	default "sun6i" if MACH_SUN6I
-	default "sun7i" if MACH_SUN7I
-	default "sun8i" if MACH_SUN8I
-	default "sun9i" if MACH_SUN9I
-	default "sun50i" if MACH_SUN50I
-	default "sun50i" if MACH_SUN50I_H6
-	default "sun50i" if MACH_SUN50I_H616
-
-config SYS_BOARD
-	default "sunxi"
-
-config SYS_SOC
-	default "sunxi"
-
-config SUNXI_MINIMUM_DRAM_MB
-	int "minimum DRAM size"
-	default 32 if MACH_SUNIV
-	default 64 if MACH_SUN8I_V3S
-	default 256
-	---help---
-	Minimum DRAM size expected on the board. Traditionally we assumed
-	256 MB, so that U-Boot would load at 160MB. With co-packaged DRAM
-	we have smaller sizes, though, so that U-Boot's own load address and
-	the default payload addresses must be shifted down.
-	This is expected to be fixed by the SoC selection.
-
 config UART0_PORT_F
 	bool "UART0 on MicroSD breakout board"
 	---help---
@@ -898,17 +842,6 @@ config GMAC_TX_DELAY
 	---help---
 	Set the GMAC Transmit Clock Delay Chain value.
 
-config SPL_STACK_R_ADDR
-	default 0x81e00000 if MACH_SUNIV
-	default 0x4fe00000 if MACH_SUN4I
-	default 0x4fe00000 if MACH_SUN5I
-	default 0x4fe00000 if MACH_SUN6I
-	default 0x4fe00000 if MACH_SUN7I
-	default 0x4fe00000 if MACH_SUN8I
-	default 0x2fe00000 if MACH_SUN9I
-	default 0x4fe00000 if MACH_SUN50I
-	default 0x4fe00000 if SUN50I_GEN_H6
-
 config SPL_SPI_SUNXI
 	bool "Support for SPI Flash on Allwinner SoCs in SPL"
 	depends on MACH_SUN4I || MACH_SUN5I || MACH_SUN7I || MACH_SUNXI_H3_H5 || MACH_SUN50I || MACH_SUN8I_R40 || SUN50I_GEN_H6 || MACH_SUNIV
--- a/board/sunxi/Kconfig
+++ b/board/sunxi/Kconfig
@@ -52,6 +52,74 @@ config BOARD_SUNXI
 
 if BOARD_SUNXI
 
+config IDENT_STRING
+	default " Allwinner Technology"
+
+config SPL_STACK_R_ADDR
+	default 0x81e00000 if MACH_SUNIV
+	default 0x4fe00000 if MACH_SUN4I
+	default 0x4fe00000 if MACH_SUN5I
+	default 0x4fe00000 if MACH_SUN6I
+	default 0x4fe00000 if MACH_SUN7I
+	default 0x4fe00000 if MACH_SUN8I
+	default 0x2fe00000 if MACH_SUN9I
+	default 0x4fe00000 if MACH_SUN50I
+	default 0x4fe00000 if SUN50I_GEN_H6
+
+config SUNXI_MINIMUM_DRAM_MB
+	int "minimum DRAM size"
+	default 32 if MACH_SUNIV
+	default 64 if MACH_SUN8I_V3S
+	default 256
+	help
+	  Minimum DRAM size expected on the board. Traditionally we
+	  assumed 256 MB, so that U-Boot would load at 160MB. With
+	  co-packaged DRAM we have smaller sizes, though, so U-Boot's
+	  own load address and the default payload addresses must be
+	  shifted down. This is expected to be fixed by the SoC
+	  selection.
+
+config SUNXI_SRAM_ADDRESS
+	hex
+	default 0x10000 if MACH_SUN9I || MACH_SUN50I || MACH_SUN50I_H5
+	default 0x20000 if SUN50I_GEN_H6
+	default 0x0
+	help
+	  Older Allwinner SoCs have their boot mask ROM mapped just
+	  below 4GB, with the first SRAM region located at address 0.
+	  Newer SoCs map the boot ROM at address 0 instead and move the
+	  SRAM to a different address.
+
+config SYS_BOARD
+	default "sunxi"
+
+config SYS_CLK_FREQ
+	default  408000000 if MACH_SUNIV
+	default 1008000000 if MACH_SUN4I
+	default 1008000000 if MACH_SUN5I
+	default 1008000000 if MACH_SUN6I
+	default  912000000 if MACH_SUN7I
+	default 1008000000 if MACH_SUN8I
+	default 1008000000 if MACH_SUN9I
+	default  816000000 if MACH_SUN50I || MACH_SUN50I_H5
+	default  888000000 if MACH_SUN50I_H6
+	default 1008000000 if MACH_SUN50I_H616
+
+config SYS_CONFIG_NAME
+	default "suniv"  if MACH_SUNIV
+	default "sun4i"  if MACH_SUN4I
+	default "sun5i"  if MACH_SUN5I
+	default "sun6i"  if MACH_SUN6I
+	default "sun7i"  if MACH_SUN7I
+	default "sun8i"  if MACH_SUN8I
+	default "sun9i"  if MACH_SUN9I
+	default "sun50i" if MACH_SUN50I
+	default "sun50i" if MACH_SUN50I_H6
+	default "sun50i" if MACH_SUN50I_H616
+
+config SYS_SOC
+	default "sunxi"
+
 menu "sunxi board options"
 
 choice
