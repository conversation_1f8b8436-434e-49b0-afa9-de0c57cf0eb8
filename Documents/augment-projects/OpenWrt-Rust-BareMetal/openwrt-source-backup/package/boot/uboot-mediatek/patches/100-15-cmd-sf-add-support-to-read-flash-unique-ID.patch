From e60939acbebd07161f3978d1c6f13123fdd2ebf2 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <<EMAIL>>
Date: Mon, 25 Jul 2022 11:27:02 +0800
Subject: [PATCH 50/71] cmd: sf: add support to read flash unique ID

This patch adds support to display unique ID from spi-nor flashes

Signed-off-by: <PERSON><PERSON><PERSON> <<EMAIL>>
---
 cmd/sf.c | 13 ++++++++++++-
 1 file changed, 12 insertions(+), 1 deletion(-)

--- a/cmd/sf.c
+++ b/cmd/sf.c
@@ -421,6 +421,14 @@ static int do_spi_protect(int argc, char
 	return ret == 0 ? 0 : 1;
 }
 
+static int do_spi_flash_read_uuid(void)
+{
+	int ret = 0;
+	ret = flash->read_uuid(flash);
+
+	return ret == 0 ? 0 : 1;
+}
+
 enum {
 	STAGE_ERASE,
 	STAGE_CHECK,
@@ -615,6 +623,8 @@ static int do_spi_flash(struct cmd_tbl *
 		ret = do_spi_flash_erase(argc, argv);
 	else if (IS_ENABLED(CONFIG_SPI_FLASH_LOCK) && strcmp(cmd, "protect") == 0)
 		ret = do_spi_protect(argc, argv);
+	else if (strcmp(cmd, "uuid") == 0)
+		ret = do_spi_flash_read_uuid();
 	else if (IS_ENABLED(CONFIG_CMD_SF_TEST) && !strcmp(cmd, "test"))
 		ret = do_spi_flash_test(argc, argv);
 	else
@@ -643,8 +653,9 @@ U_BOOT_LONGHELP(sf,
 	"					  at address 'sector'"
 #endif
 #ifdef CONFIG_CMD_SF_TEST
-	"\nsf test offset len		- run a very basic destructive test"
+	"\nsf test offset len			- run a very basic destructive test"
 #endif
+	"\nsf uuid					- read uuid from flash"
 	);
 
 U_BOOT_CMD(
