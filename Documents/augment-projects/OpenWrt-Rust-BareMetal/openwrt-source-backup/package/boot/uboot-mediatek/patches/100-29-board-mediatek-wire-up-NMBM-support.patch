From 6792b57b3ba61ca6d69ea4a13a58bed65fc5da87 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sun, 7 Aug 2022 04:04:46 +0200
Subject: [PATCH] board: mediatek: wire-up NMBM support

---
 board/mediatek/mt7622/mt7622_rfb.c | 38 +++++++++++++++++++++
 board/mediatek/mt7629/mt7629_rfb.c | 38 +++++++++++++++++++++
 board/mediatek/mt7981/mt7981_rfb.c | 52 ++++++++++++++++++++++++++++
 board/mediatek/mt7986/mt7986_rfb.c | 54 ++++++++++++++++++++++++++++++
 4 files changed, 182 insertions(+)

--- a/board/mediatek/mt7622/mt7622_rfb.c
+++ b/board/mediatek/mt7622/mt7622_rfb.c
@@ -9,9 +9,47 @@
 #include <init.h>
 #include <asm/global_data.h>
 
+#include <mtd.h>
+#include <linux/mtd/mtd.h>
+#include <nmbm/nmbm.h>
+#include <nmbm/nmbm-mtd.h>
+
 DECLARE_GLOBAL_DATA_PTR;
 
 int board_init(void)
 {
 	return 0;
 }
+
+int board_nmbm_init(void)
+{
+#ifdef CONFIG_ENABLE_NAND_NMBM
+	struct mtd_info *lower, *upper;
+	int ret;
+
+	printf("\n");
+	printf("Initializing NMBM ...\n");
+
+	mtd_probe_devices();
+
+	lower = get_mtd_device_nm("spi-nand0");
+	if (IS_ERR(lower) || !lower) {
+		printf("Lower MTD device 'spi-nand0' not found\n");
+		return 0;
+	}
+
+	ret = nmbm_attach_mtd(lower,
+			      NMBM_F_CREATE | NMBM_F_EMPTY_PAGE_ECC_OK,
+			      CONFIG_NMBM_MAX_RATIO,
+			      CONFIG_NMBM_MAX_BLOCKS, &upper);
+
+	printf("\n");
+
+	if (ret)
+		return 0;
+
+	add_mtd_device(upper);
+#endif
+
+	return 0;
+}
--- a/board/mediatek/mt7629/mt7629_rfb.c
+++ b/board/mediatek/mt7629/mt7629_rfb.c
@@ -6,6 +6,11 @@
 #include <config.h>
 #include <asm/global_data.h>
 
+#include <mtd.h>
+#include <linux/mtd/mtd.h>
+#include <nmbm/nmbm.h>
+#include <nmbm/nmbm-mtd.h>
+
 DECLARE_GLOBAL_DATA_PTR;
 
 int board_init(void)
@@ -15,3 +20,36 @@ int board_init(void)
 
 	return 0;
 }
+
+int board_nmbm_init(void)
+{
+#ifdef CONFIG_ENABLE_NAND_NMBM
+	struct mtd_info *lower, *upper;
+	int ret;
+
+	printf("\n");
+	printf("Initializing NMBM ...\n");
+
+	mtd_probe_devices();
+
+	lower = get_mtd_device_nm("spi-nand0");
+	if (IS_ERR(lower) || !lower) {
+		printf("Lower MTD device 'spi-nand0' not found\n");
+		return 0;
+	}
+
+	ret = nmbm_attach_mtd(lower,
+			      NMBM_F_CREATE | NMBM_F_EMPTY_PAGE_ECC_OK,
+			      CONFIG_NMBM_MAX_RATIO,
+			      CONFIG_NMBM_MAX_BLOCKS, &upper);
+
+	printf("\n");
+
+	if (ret)
+		return 0;
+
+	add_mtd_device(upper);
+#endif
+
+	return 0;
+}
--- a/board/mediatek/mt7981/mt7981_rfb.c
+++ b/board/mediatek/mt7981/mt7981_rfb.c
@@ -4,7 +4,57 @@
  * Author: Sam Shih <<EMAIL>>
  */
 
+#include <config.h>
+#include <env.h>
+#include <init.h>
+#include <asm/global_data.h>
+
+#include <mtd.h>
+#include <linux/mtd/mtd.h>
+#include <nmbm/nmbm.h>
+#include <nmbm/nmbm-mtd.h>
+
+DECLARE_GLOBAL_DATA_PTR;
+
 int board_init(void)
 {
 	return 0;
 }
+
+int board_late_init(void)
+{
+	gd->env_valid = 1; //to load environment variable from persistent store
+	env_relocate();
+	return 0;
+}
+
+int board_nmbm_init(void)
+{
+#ifdef CONFIG_ENABLE_NAND_NMBM
+	struct mtd_info *lower, *upper;
+	int ret;
+
+	printf("\n");
+	printf("Initializing NMBM ...\n");
+
+	mtd_probe_devices();
+
+	lower = get_mtd_device_nm("spi-nand0");
+	if (IS_ERR(lower) || !lower) {
+		printf("Lower MTD device 'spi-nand0' not found\n");
+		return 0;
+	}
+
+	ret = nmbm_attach_mtd(lower, NMBM_F_CREATE, CONFIG_NMBM_MAX_RATIO,
+		CONFIG_NMBM_MAX_BLOCKS, &upper);
+
+	printf("\n");
+
+	if (ret)
+		return 0;
+
+	add_mtd_device(upper);
+#endif
+
+	return 0;
+}
--- a/board/mediatek/mt7986/mt7986_rfb.c
+++ b/board/mediatek/mt7986/mt7986_rfb.c
@@ -4,7 +4,59 @@
  * Author: Sam Shih <<EMAIL>>
  */
 
+#include <config.h>
+#include <env.h>
+#include <init.h>
+#include <asm/global_data.h>
+
+#include <mtd.h>
+#include <linux/mtd/mtd.h>
+#include <nmbm/nmbm.h>
+#include <nmbm/nmbm-mtd.h>
+
+DECLARE_GLOBAL_DATA_PTR;
+
 int board_init(void)
 {
 	return 0;
 }
+
+int board_late_init(void)
+{
+	gd->env_valid = 1; //to load environment variable from persistent store
+	env_relocate();
+	return 0;
+}
+
+int board_nmbm_init(void)
+{
+#ifdef CONFIG_ENABLE_NAND_NMBM
+	struct mtd_info *lower, *upper;
+	int ret;
+
+	printf("\n");
+	printf("Initializing NMBM ...\n");
+
+	mtd_probe_devices();
+
+	lower = get_mtd_device_nm("spi-nand0");
+	if (IS_ERR(lower) || !lower) {
+		printf("Lower MTD device 'spi-nand0' not found\n");
+		return 0;
+	}
+
+	ret = nmbm_attach_mtd(lower,
+			      NMBM_F_CREATE | NMBM_F_EMPTY_PAGE_ECC_OK,
+			      CONFIG_NMBM_MAX_RATIO,
+			      CONFIG_NMBM_MAX_BLOCKS, &upper);
+
+	printf("\n");
+
+	if (ret)
+		return 0;
+
+	add_mtd_device(upper);
+#endif
+
+	return 0;
+}
