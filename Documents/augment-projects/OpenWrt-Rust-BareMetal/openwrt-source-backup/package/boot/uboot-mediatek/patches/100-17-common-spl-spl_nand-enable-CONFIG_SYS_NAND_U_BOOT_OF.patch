From 7ab891faaaf2b6126694352d4503dc40605a6aec Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <<EMAIL>>
Date: Mon, 25 Jul 2022 15:10:02 +0800
Subject: [PATCH 52/71] common: spl: spl_nand: enable
 CONFIG_SYS_NAND_U_BOOT_OFFS undefined

Enable using spl_nand with CONFIG_SYS_NAND_U_BOOT_OFFS undefined since
mtk-snand does not require raw nand framework.

Signed-off-by: <PERSON><PERSON><PERSON> <<EMAIL>>
---
 common/spl/spl_nand.c | 4 ++++
 1 file changed, 4 insertions(+)

--- a/common/spl/spl_nand.c
+++ b/common/spl/spl_nand.c
@@ -18,7 +18,11 @@
 
 uint32_t __weak spl_nand_get_uboot_raw_page(void)
 {
+#ifdef CONFIG_SYS_NAND_U_BOOT_OFFS
 	return CONFIG_SYS_NAND_U_BOOT_OFFS;
+#else
+	return 0;
+#endif
 }
 
 #if defined(CONFIG_SPL_NAND_RAW_ONLY)
