From fc0c70a7c6a088072d0c77e5a59d5e9b7754c6db Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <<EMAIL>>
Date: Mon, 25 Jul 2022 17:01:20 +0800
Subject: [PATCH 61/71] env: ubi: add support to create environment volume if
 it does not exist

Add an option to allow environment volume being auto created if not exist.

Signed-off-by: <PERSON><PERSON><PERSON> <<EMAIL>>
---
 env/Kconfig |  6 ++++++
 env/ubi.c   | 20 ++++++++++++++++++++
 2 files changed, 26 insertions(+)

--- a/env/Kconfig
+++ b/env/Kconfig
@@ -701,6 +701,12 @@ config ENV_UBI_VOLUME_REDUND
 	help
 	  Name of the redundant volume that you want to store the environment in.
 
+config ENV_UBI_VOLUME_CREATE
+	bool "Create UBI volume if not exist"
+	depends on ENV_IS_IN_UBI
+	help
+	  Create the UBI volume if it does not exist.
+
 config ENV_UBI_VID_OFFSET
 	int "ubi environment VID offset"
 	depends on ENV_IS_IN_UBI
--- a/env/ubi.c
+++ b/env/ubi.c
@@ -105,6 +105,18 @@ static int env_ubi_save(void)
 #endif /* CONFIG_SYS_REDUNDAND_ENVIRONMENT */
 #endif /* CONFIG_CMD_SAVEENV */
 
+int __weak env_ubi_volume_create(const char *volume)
+{
+	struct ubi_volume *vol;
+
+	vol = ubi_find_volume((char *)volume);
+	if (vol)
+		return 0;
+
+	return ubi_create_vol((char *)volume, CONFIG_ENV_SIZE, true,
+			      UBI_VOL_NUM_AUTO, false);
+}
+
 #ifdef CONFIG_SYS_REDUNDAND_ENVIRONMENT
 static int env_ubi_load(void)
 {
@@ -134,6 +146,10 @@ static int env_ubi_load(void)
 		return -EIO;
 	}
 
+	if (IS_ENABLED(CONFIG_ENV_UBI_VOLUME_CREATE)) {
+		env_ubi_volume_create(CONFIG_ENV_UBI_VOLUME);
+		env_ubi_volume_create(CONFIG_ENV_UBI_VOLUME_REDUND);
+	}
 	read1_fail = ubi_volume_read(CONFIG_ENV_UBI_VOLUME, (void *)tmp_env1, 0,
 				     CONFIG_ENV_SIZE);
 	if (read1_fail)
@@ -171,6 +187,9 @@ static int env_ubi_load(void)
 		return -EIO;
 	}
 
+	if (IS_ENABLED(CONFIG_ENV_UBI_VOLUME_CREATE))
+		env_ubi_volume_create(CONFIG_ENV_UBI_VOLUME);
+
 	if (ubi_volume_read(CONFIG_ENV_UBI_VOLUME, buf, 0, CONFIG_ENV_SIZE)) {
 		printf("\n** Unable to read env from %s:%s **\n",
 		       CONFIG_ENV_UBI_PART, CONFIG_ENV_UBI_VOLUME);
