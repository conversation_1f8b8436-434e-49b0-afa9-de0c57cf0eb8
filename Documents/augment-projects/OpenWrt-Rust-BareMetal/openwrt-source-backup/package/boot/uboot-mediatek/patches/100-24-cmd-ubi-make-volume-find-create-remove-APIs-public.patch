From dd66fc817f7ab7a4fcab9836a9251a8f64f329df Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <<EMAIL>>
Date: Mon, 25 Jul 2022 16:58:36 +0800
Subject: [PATCH 59/71] cmd: ubi: make volume find/create/remove APIs public

Export ubi_create_vol/ubi_find_volume/ubi_remove_vol to public so that they
can be used by other programs.

Signed-off-by: <PERSON><PERSON><PERSON> <<EMAIL>>
---
 cmd/ubi.c           | 8 ++++----
 include/ubi_uboot.h | 4 ++++
 2 files changed, 8 insertions(+), 4 deletions(-)

--- a/cmd/ubi.c
+++ b/cmd/ubi.c
@@ -213,8 +213,8 @@ bad:
 	return err;
 }
 
-static int ubi_create_vol(char *volume, int64_t size, int dynamic, int vol_id,
-			  bool skipcheck)
+int ubi_create_vol(char *volume, int64_t size, int dynamic, int vol_id,
+		   bool skipcheck)
 {
 	struct ubi_mkvol_req req;
 	int err;
@@ -247,7 +247,7 @@ static int ubi_create_vol(char *volume,
 	return ubi_create_volume(ubi, &req);
 }
 
-static struct ubi_volume *ubi_find_volume(char *volume)
+struct ubi_volume *ubi_find_volume(char *volume)
 {
 	struct ubi_volume *vol;
 	int i;
@@ -262,7 +262,7 @@ static struct ubi_volume *ubi_find_volum
 	return NULL;
 }
 
-static int ubi_remove_vol(char *volume)
+int ubi_remove_vol(char *volume)
 {
 	int err, reserved_pebs, i;
 	struct ubi_volume *vol;
--- a/include/ubi_uboot.h
+++ b/include/ubi_uboot.h
@@ -50,6 +50,10 @@ extern void ubi_exit(void);
 extern int ubi_part(char *part_name, const char *vid_header_offset);
 extern int ubi_volume_write(char *volume, void *buf, loff_t offset, size_t size);
 extern int ubi_volume_read(char *volume, char *buf, loff_t offset, size_t size);
+extern int ubi_create_vol(char *volume, int64_t size, int dynamic, int vol_id,
+			  bool skipcheck);
+extern struct ubi_volume *ubi_find_volume(char *volume);
+extern int ubi_remove_vol(char *volume);
 
 extern struct ubi_device *ubi_devices[];
 int cmd_ubifs_mount(char *vol_name);
