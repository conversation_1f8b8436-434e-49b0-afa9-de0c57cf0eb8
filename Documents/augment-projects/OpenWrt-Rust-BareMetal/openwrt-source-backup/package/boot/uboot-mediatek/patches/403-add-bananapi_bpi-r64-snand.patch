--- a/arch/arm/dts/mt7622-bananapi-bpi-r64.dts
+++ b/arch/arm/dts/mt7622-bananapi-bpi-r64.dts
@@ -20,7 +20,7 @@
 	};
 
 	aliases {
-		spi0 = &snfi;
+		spi0 = &snand;
 		ethernet0 = &eth;
 	};
 
@@ -208,16 +208,27 @@
 	};
 };
 
-&snfi {
-	pinctrl-names = "default", "snfi";
-	pinctrl-0 = <&snor_pins>;
-	pinctrl-1 = <&snfi_pins>;
+&snand {
+	pinctrl-names = "default";
+	pinctrl-0 = <&snfi_pins>;
+	quad-spi;
 	status = "okay";
 
-	spi-flash@0{
-		compatible = "jedec,spi-nor";
-		reg = <0>;
-		bootph-all;
+	partitions {
+		compatible = "fixed-partitions";
+		#address-cells = <1>;
+		#size-cells = <1>;
+
+		partition@0 {
+			label = "bl2";
+			reg = <0x0 0x80000>;
+		};
+
+		partition@80000 {
+			label = "ubi";
+			reg = <0x80000 0x7f80000>;
+			compatible = "linux,ubi";
+		};
 	};
 };
 
