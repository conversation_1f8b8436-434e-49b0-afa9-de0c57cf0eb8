--- a/arch/arm/dts/mt7981-rfb.dts
+++ b/arch/arm/dts/mt7981-rfb.dts
@@ -153,6 +153,37 @@
 		spi-max-frequency = <52000000>;
 		spi-rx-bus-width = <4>;
 		spi-tx-bus-width = <4>;
+
+		partitions {
+			compatible = "fixed-partitions";
+			#address-cells = <1>;
+			#size-cells = <1>;
+
+			partition@0 {
+				label = "BL2";
+				reg = <0x00000 0x0100000>;
+			};
+
+			partition@100000 {
+				label = "u-boot-env";
+				reg = <0x0100000 0x0080000>;
+			};
+
+			factory: partition@180000 {
+				label = "Factory";
+				reg = <0x180000 0x0200000>;
+			};
+
+			partition@380000 {
+				label = "FIP";
+				reg = <0x380000 0x0200000>;
+			};
+
+			partition@580000 {
+				label = "ubi";
+				reg = <0x580000 0x4000000>;
+			};
+		};
 	};
 };
 
@@ -176,6 +207,37 @@
 		spi-max-frequency = <52000000>;
 		spi-rx-bus-width = <4>;
 		spi-tx-bus-width = <4>;
+
+		partitions {
+			compatible = "fixed-partitions";
+			#address-cells = <1>;
+			#size-cells = <1>;
+
+			partition@00000 {
+				label = "BL2";
+				reg = <0x00000 0x0040000>;
+			};
+
+			partition@40000 {
+				label = "u-boot-env";
+				reg = <0x40000 0x0010000>;
+			};
+
+			partition@50000 {
+				label = "Factory";
+				reg = <0x50000 0x00B0000>;
+			};
+
+			partition@100000 {
+				label = "FIP";
+				reg = <0x100000 0x0080000>;
+			};
+
+			partition@180000 {
+				label = "firmware";
+				reg = <0x180000 0xE00000>;
+			};
+		};
 	};
 };
 
--- a/arch/arm/dts/mt7981-snfi-nand-rfb.dts
+++ b/arch/arm/dts/mt7981-snfi-nand-rfb.dts
@@ -107,11 +107,11 @@
 	};
 
 	mmc0_pins_default: mmc0default {
-                mux {
-                       function = "flash";
-                       groups =  "emmc_45";
-                 };
-         };
+		mux {
+			function = "flash";
+			groups =  "emmc_45";
+		};
+	};
 };
 
 &snand {
@@ -119,6 +119,42 @@
 	pinctrl-0 = <&snfi_pins>;
 	status = "okay";
 	quad-spi;
+
+	spi_nand@0 {
+		compatible = "spi-nand";
+		reg = <0>;
+
+		partitions {
+			compatible = "fixed-partitions";
+			#address-cells = <1>;
+			#size-cells = <1>;
+
+			partition@0 {
+				label = "BL2";
+				reg = <0x00000 0x0100000>;
+			};
+
+			partition@100000 {
+				label = "u-boot-env";
+				reg = <0x0100000 0x0080000>;
+			};
+
+			factory: partition@180000 {
+				label = "Factory";
+				reg = <0x180000 0x0200000>;
+			};
+
+			partition@380000 {
+				label = "FIP";
+				reg = <0x380000 0x0200000>;
+			};
+
+			partition@580000 {
+				label = "ubi";
+				reg = <0x580000 0x4000000>;
+			};
+		};
+	};
 };
 
 &pwm {
