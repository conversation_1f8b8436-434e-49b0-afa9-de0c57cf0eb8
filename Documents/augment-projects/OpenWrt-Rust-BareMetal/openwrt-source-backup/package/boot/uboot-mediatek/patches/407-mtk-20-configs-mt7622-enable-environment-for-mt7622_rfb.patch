From 93d7086edb0db4b05149dfea21a2a82d8f160944 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <<EMAIL>>
Date: Sat, 6 Mar 2021 16:29:33 +0800
Subject: [PATCH 10/12] configs: mt7622: enable environment for mt7622_rfb

Enable environment vairables for mt7622_rfb

Signed-off-by: <PERSON><PERSON><PERSON> <<EMAIL>>
---
 configs/mt7622_rfb_defconfig | 5 +++++
 1 file changed, 5 insertions(+)

--- a/configs/mt7622_rfb_defconfig
+++ b/configs/mt7622_rfb_defconfig
@@ -5,6 +5,8 @@ CONFIG_ARCH_MEDIATEK=y
 CONFIG_TEXT_BASE=0x41e00000
 CONFIG_SYS_MALLOC_F_LEN=0x4000
 CONFIG_NR_DRAM_BANKS=1
+CONFIG_ENV_SIZE=0x20000
+CONFIG_ENV_OFFSET=0x280000
 CONFIG_DEFAULT_DEVICE_TREE="mt7622-rfb"
 CONFIG_SYS_LOAD_ADDR=0x4007ff28
 CONFIG_DEBUG_UART_BASE=0x11002000
@@ -26,6 +28,9 @@ CONFIG_CMD_SF_TEST=y
 CONFIG_CMD_PING=y
 CONFIG_CMD_SMC=y
 CONFIG_ENV_OVERWRITE=y
+CONFIG_ENV_IS_IN_MTD=y
+CONFIG_ENV_MTD_NAME="spi-nand0"
+CONFIG_ENV_SIZE_REDUND=0x40000
 CONFIG_ENV_VARS_UBOOT_RUNTIME_CONFIG=y
 CONFIG_USE_IPADDR=y
 CONFIG_IPADDR="***********"
