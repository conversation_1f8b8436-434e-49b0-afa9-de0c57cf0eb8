--- /dev/null
+++ b/configs/mt7621_zbtlink_zbt-wg3526-16m_defconfig
@@ -0,0 +1,98 @@
+CONFIG_MIPS=y
+CONFIG_SYS_HAS_NONCACHED_MEMORY=y
+CONFIG_SYS_MALLOC_LEN=0x100000
+CONFIG_SPL_LIBCOMMON_SUPPORT=y
+CONFIG_SPL_LIBGENERIC_SUPPORT=y
+CONFIG_NR_DRAM_BANKS=1
+CONFIG_SF_DEFAULT_SPEED=********
+CONFIG_ENV_SIZE=0x10000
+CONFIG_ENV_OFFSET=0x30000
+CONFIG_DEFAULT_DEVICE_TREE="zbtlink,zbt-wg3526"
+CONFIG_SPL_SERIAL=y
+CONFIG_TPL_SYS_MALLOC_F_LEN=0x1000
+CONFIG_SPL_SYS_MALLOC_F_LEN=0x40000
+CONFIG_SPL_BSS_START_ADDR=0x80140000
+CONFIG_SPL_BSS_MAX_SIZE=0x80000
+CONFIG_SYS_LOAD_ADDR=0x83000000
+CONFIG_SPL=y
+CONFIG_DEBUG_UART_BASE=0xbe000c00
+CONFIG_DEBUG_UART_CLOCK=********
+CONFIG_ARCH_MTMIPS=y
+CONFIG_SOC_MT7621=y
+CONFIG_SYS_MIPS_TIMER_FREQ=*********
+# CONFIG_MIPS_CACHE_SETUP is not set
+# CONFIG_MIPS_CACHE_DISABLE is not set
+CONFIG_RESTORE_EXCEPTION_VECTOR_BASE=y
+CONFIG_MIPS_BOOT_FDT=y
+CONFIG_DEBUG_UART=y
+CONFIG_FIT=y
+# CONFIG_BOOTSTD is not set
+CONFIG_BOOTDELAY=30
+CONFIG_AUTOBOOT_KEYED=y
+CONFIG_AUTOBOOT_MENU_SHOW=y
+# CONFIG_ARCH_FIXUP_FDT_MEMORY is not set
+CONFIG_LOGLEVEL=6
+CONFIG_SYS_CONSOLE_INFO_QUIET=y
+CONFIG_SPL_SYS_MALLOC_SIMPLE=y
+CONFIG_SPL_NOR_SUPPORT=y
+CONFIG_TPL=y
+# CONFIG_TPL_FRAMEWORK is not set
+CONFIG_HUSH_PARSER=y
+# CONFIG_SYS_LONGHELP is not set
+# CONFIG_SYS_XTRACE is not set
+# CONFIG_CMD_BOOTD is not set
+# CONFIG_BOOTM_NETBSD is not set
+# CONFIG_BOOTM_PLAN9 is not set
+# CONFIG_BOOTM_RTEMS is not set
+# CONFIG_BOOTM_VXWORKS is not set
+CONFIG_CMD_BOOTMENU=y
+# CONFIG_CMD_ELF is not set
+CONFIG_CMD_ASKENV=y
+CONFIG_CMD_ERASEENV=y
+CONFIG_CMD_ENV_FLAGS=y
+CONFIG_CMD_GPIO=y
+CONFIG_CMD_MMC=y
+CONFIG_CMD_MTD=y
+CONFIG_CMD_SF_TEST=y
+# CONFIG_CMD_BOOTP is not set
+CONFIG_CMD_PING=y
+CONFIG_CMD_HASH=y
+CONFIG_DOS_PARTITION=y
+# CONFIG_SPL_DOS_PARTITION is not set
+# CONFIG_ISO_PARTITION is not set
+CONFIG_ENV_IS_IN_MTD=y
+CONFIG_ENV_MTD_NAME="nor0"
+CONFIG_ENV_SIZE_REDUND=0x10000
+CONFIG_SYS_RELOC_GD_ENV_ADDR=y
+CONFIG_USE_DEFAULT_ENV_FILE=y
+CONFIG_DEFAULT_ENV_FILE="defenvs/zbtlink_zbt-wg3526-16m_env"
+CONFIG_ENV_VARS_UBOOT_RUNTIME_CONFIG=y
+CONFIG_VERSION_VARIABLE=y
+CONFIG_BOOTP_SEND_HOSTNAME=y
+CONFIG_BUTTON=y
+CONFIG_BUTTON_GPIO=y
+CONFIG_GPIO_HOG=y
+# CONFIG_I2C is not set
+# CONFIG_INPUT is not set
+CONFIG_MMC=y
+# CONFIG_MMC_QUIRKS is not set
+# CONFIG_MMC_HW_PARTITIONING is not set
+CONFIG_MMC_MTK=y
+CONFIG_DM_MTD=y
+CONFIG_SPI_FLASH_WINBOND=y
+CONFIG_SPI_FLASH_MTD=y
+CONFIG_MEDIATEK_ETH=y
+CONFIG_PHY=y
+CONFIG_PHY_MTK_TPHY=y
+CONFIG_SERIAL_RX_BUFFER=y
+CONFIG_DEBUG_UART_SHIFT=2
+CONFIG_SPI=y
+CONFIG_MT7621_SPI=y
+CONFIG_SYSRESET=y
+CONFIG_SYSRESET_RESETCTL=y
+CONFIG_WDT=y
+CONFIG_WDT_MT7621=y
+# CONFIG_BINMAN_FDT is not set
+CONFIG_LZMA=y
+# CONFIG_GZIP is not set
+CONFIG_SPL_LZMA=y
--- /dev/null
+++ b/defenvs/zbtlink_zbt-wg3526-16m_env
@@ -0,0 +1,36 @@
+ethaddr_factory=mtd read factory $loadaddr 0x0 0x10000 ; setexpr macoffs $loadaddr + 0xe000 ; env readmem -b ethaddr $macoffs 0x6 ; setenv ethaddr_factory
+ipaddr=***********
+serverip=*************
+loadaddr=0x83000000
+bootcmd=run boot_nor
+bootdelay=0
+bootfile=openwrt-ramips-mt7621-zbtlink_zbt-wg3526-16m-initramfs-kernel.bin
+bootfile_uboot=u-boot-mt7621.bin
+bootfile_upg=openwrt-ramips-mt7621-zbtlink_zbt-wg3526-16m-squashfs-sysupgrade.bin
+bootmenu_confirm_return=askenv - Press ENTER to return to menu ; bootmenu 60
+bootmenu_default=0
+bootmenu_delay=0
+bootmenu_title=      [0;34m( ( ( [1;39mOpenWrt[0;34m ) ) )[0m
+bootmenu_0=Initialize environment.=run _firstboot
+bootmenu_0d=Run default boot command.=run boot_default
+bootmenu_1=Boot system via TFTP.=run boot_tftp ; run bootmenu_confirm_return
+bootmenu_2=Boot system from flash.=run boot_nor ; run bootmenu_confirm_return
+bootmenu_3=Load system via TFTP then write to flash.=run boot_tftp_sysupgrade ; run bootmenu_confirm_return
+bootmenu_4=[31mLoad U-Boot via TFTP then write to flash.[0m=run boot_tftp_write_uboot ; run bootmenu_confirm_return
+bootmenu_5=Reset all settings to factory defaults.=run reset_factory ; reset
+bootmenu_6=Reboot.=reset
+boot_first=if button reset ; then run boot_tftp ; setenv flag_recover 1 ; run boot_default ; fi ; bootmenu
+boot_default=if env exists flag_recover ; then else run bootcmd ; fi ; run boot_tftp_forever
+boot_nor=bootm 0x1fc50000
+boot_tftp=tftpboot $loadaddr $bootfile && bootm $loadaddr
+boot_tftp_forever=while true ; do run boot_tftp ; sleep 1 ; done
+boot_tftp_sysupgrade=tftpboot $loadaddr $bootfile_upg && iminfo $loadaddr && run nor_write_production
+boot_tftp_write_uboot=tftpboot $loadaddr $bootfile_uboot && run nor_write_uboot
+reset_factory=mtd erase u-boot-env 0x0 0x10000 && reset
+nor_pad_size=setexpr image_eb $filesize / 0x1000 ; setexpr tmp1 image_size % 0x1000 ; test 0x$tmp1 -gt 0 && setexpr image_eb $image_eb + 1 ; setexpr image_eb $image_eb * 0x1000
+nor_write_production=run nor_pad_size ; test 0x$image_eb -le 0xfb0000 && mtd erase firmware 0x0 0x$image_eb && mtd write firmware $loadaddr 0x0 $filesize
+nor_write_uboot=mtd erase u-boot 0x0 0x30000 && mtd write u-boot $loadaddr 0x0 0x30000
+_init_env=setenv _init_env ; saveenv
+_firstboot=setenv _firstboot ; run _switch_to_menu ; run ethaddr_factory ; run _init_env ; run boot_first
+_switch_to_menu=setenv _switch_to_menu ; setenv bootdelay 3 ; setenv bootmenu_delay 3 ; setenv bootmenu_0 $bootmenu_0d ; setenv bootmenu_0d ; run _bootmenu_update_title
+_bootmenu_update_title=setenv _bootmenu_update_title ; setenv bootmenu_title "$bootmenu_title       [33m$ver[0m"
--- /dev/null
+++ b/arch/mips/dts/zbtlink,zbt-wg3526.dts
@@ -0,0 +1,131 @@
+// SPDX-License-Identifier: GPL-2.0
+/*
+ * Copyright (C) 2022 MediaTek Inc. All rights reserved.
+ *
+ * Author: Weijie Gao <<EMAIL>>
+ */
+
+/dts-v1/;
+
+#include "mt7621.dtsi"
+#include <dt-bindings/gpio/gpio.h>
+
+/ {
+	compatible = "zbtlink,zbt-wg3526", "mediatek,mt7621-rfb", "mediatek,mt7621-soc";
+	model = "Zbtlink WG3526";
+
+	aliases {
+		ethernet0 = &eth;
+		serial0 = &uart0;
+		spi0 = &spi;
+	};
+
+	chosen {
+		stdout-path = &uart0;
+	};
+
+	keys {
+		compatible = "gpio-keys";
+
+		reset {
+			label = "reset";
+			gpios = <&gpio0 18 GPIO_ACTIVE_LOW>;
+		};
+	};
+
+	leds {
+		compatible = "gpio-leds";
+
+		led_status: status {
+			label = "green:status";
+			gpios = <&gpio0 24 GPIO_ACTIVE_LOW>;
+		};
+	};
+};
+
+&pinctrl {
+	state_default: pin_state {
+		gpios {
+			groups = "i2c", "uart3", "pcie reset";
+			function = "gpio";
+		};
+
+		wdt {
+			groups = "wdt";
+			function = "wdt rst";
+		};
+
+		jtag {
+			groups = "jtag";
+			function = "jtag";
+		};
+	};
+};
+
+&uart0 {
+	status = "okay";
+};
+
+&gpio {
+	status = "okay";
+};
+
+&spi {
+	status = "okay";
+	num-cs = <2>;
+
+	spi-flash@0 {
+		#address-cells = <1>;
+		#size-cells = <1>;
+		compatible = "jedec,spi-nor";
+		spi-max-frequency = <25000000>;
+		reg = <0>;
+
+
+		partitions {
+			compatible = "fixed-partitions";
+			#address-cells = <1>;
+			#size-cells = <1>;
+
+			partition@0 {
+				label = "u-boot";
+				reg = <0x0 0x30000>;
+			};
+
+			partition@30000 {
+				label = "u-boot-env";
+				reg = <0x30000 0x10000>;
+			};
+
+			factory: partition@40000 {
+				label = "factory";
+				reg = <0x40000 0x10000>;
+				read-only;
+			};
+
+			firmware: partition@50000 {
+				compatible = "denx,uimage";
+				label = "firmware";
+				reg = <0x50000 0xfb0000>;
+			};
+		};
+	};
+};
+
+&eth {
+	status = "okay";
+};
+
+&mmc {
+	cap-sd-highspeed;
+
+	status = "okay";
+};
+
+&ssusb {
+	status = "okay";
+};
+
+&u3phy {
+	status = "okay";
+};
