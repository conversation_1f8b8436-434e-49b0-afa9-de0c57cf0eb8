From ae461cde5c559675fc4c0ba351c7c31ace705f56 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <<EMAIL>>
Date: Sun, 10 Nov 2024 22:50:47 +0200
Subject: [PATCH] mtd: spinand: add support for FORESEE F35SQA001G

Add support for FORESEE F35SQA001G SPI NAND.

Similar to F35SQA002G, but differs in capacity.
Datasheet:
  -  https://cdn.ozdisan.com/ETicaret_Dosya/704795_871495.pdf

Tested on Xiaomi AX3000T flashed with OpenWRT.

Signed-off-by: <PERSON><PERSON><PERSON> <<EMAIL>>
Signed-off-by: <PERSON><PERSON> <<EMAIL>>
---
 drivers/mtd/nand/spi/foresee.c | 10 ++++++++++
 1 file changed, 10 insertions(+)

--- a/drivers/mtd/nand/spi/foresee.c
+++ b/drivers/mtd/nand/spi/foresee.c
@@ -83,6 +83,16 @@ static const struct spinand_info foresee
 		     SPINAND_HAS_QE_BIT,
 		     SPINAND_ECCINFO(&f35sqa002g_ooblayout,
 				     f35sqa002g_ecc_get_status)),
+	SPINAND_INFO("F35SQA001G",
+		     SPINAND_ID(SPINAND_READID_METHOD_OPCODE_DUMMY, 0x71, 0x71),
+		     NAND_MEMORG(1, 2048, 64, 64, 1024, 20, 1, 1, 1),
+		     NAND_ECCREQ(1, 512),
+		     SPINAND_INFO_OP_VARIANTS(&read_cache_variants,
+					      &write_cache_variants,
+					      &update_cache_variants),
+		     SPINAND_HAS_QE_BIT,
+		     SPINAND_ECCINFO(&f35sqa002g_ooblayout,
+				     f35sqa002g_ecc_get_status)),
 };
 
 static const struct spinand_manufacturer_ops foresee_spinand_manuf_ops = {
