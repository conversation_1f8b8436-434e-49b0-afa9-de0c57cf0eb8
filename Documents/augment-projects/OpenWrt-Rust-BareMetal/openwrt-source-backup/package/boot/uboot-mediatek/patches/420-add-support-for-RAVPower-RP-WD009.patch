From 593db38363297247df731566c2aa307a5d795005 Mon Sep 17 00:00:00 2001
From: <PERSON> <mail@david-b<PERSON>.net>
Date: Thu, 18 Jun 2020 00:13:11 +0200
Subject: [PATCH] add support for RAVPower RP-WD009

---
 arch/mips/dts/Makefile                  |  3 +-
 arch/mips/dts/ravpower-rp-wd009.dts     | 50 +++++++++++++++++++++
 arch/mips/mach-mtmips/Kconfig           |  9 ++++
 board/ravpower/rp-wd009/Kconfig         | 12 +++++
 board/ravpower/rp-wd009/Makefile        |  3 ++
 board/ravpower/rp-wd009/board.c         | 16 +++++++
 configs/ravpower-rp-wd009-ram_defconfig | 59 +++++++++++++++++++++++++
 include/configs/ravpower-rp-wd009.h     | 48 ++++++++++++++++++++
 8 files changed, 199 insertions(+), 1 deletion(-)
 create mode 100644 arch/mips/dts/ravpower-rp-wd009.dts
 create mode 100644 board/ravpower/rp-wd009/Kconfig
 create mode 100644 board/ravpower/rp-wd009/Makefile
 create mode 100644 board/ravpower/rp-wd009/board.c
 create mode 100644 configs/ravpower-rp-wd009-ram_defconfig
 create mode 100644 include/configs/ravpower-rp-wd009.h

--- a/arch/mips/dts/Makefile
+++ b/arch/mips/dts/Makefile
@@ -26,6 +26,7 @@ dtb-$(CONFIG_TARGET_OCTEON_EBB7304) += m
 dtb-$(CONFIG_TARGET_OCTEON_NIC23) += mrvl,octeon-nic23.dtb
 dtb-$(CONFIG_BOARD_NETGEAR_CG3100D) += netgear,cg3100d.dtb
 dtb-$(CONFIG_BOARD_NETGEAR_DGND3700V2) += netgear,dgnd3700v2.dtb
+dtb-$(CONFIG_BOARD_RAVPOWER_RP_WD009) += ravpower-rp-wd009.dtb
 dtb-$(CONFIG_BOARD_SAGEM_FAST1704) += sagem,<EMAIL>
 dtb-$(CONFIG_BOARD_SFR_NB4_SER) += sfr,nb4-ser.dtb
 dtb-$(CONFIG_BOARD_TPLINK_WDR4300) += tplink_wdr4300.dtb
--- /dev/null
+++ b/arch/mips/dts/ravpower-rp-wd009.dts
@@ -0,0 +1,50 @@
+// SPDX-License-Identifier: GPL-2.0
+/*
+ * Copyright (C) 2018 Stefan Roese <<EMAIL>>
+ */
+
+/dts-v1/;
+
+#include "mt7628a.dtsi"
+#include <dt-bindings/gpio/gpio.h>
+
+/ {
+	compatible = "ravpower,rp-wd009", "ralink,mt7628a-soc";
+	model = "RAVPower RP-WD009";
+
+	aliases {
+		serial0 = &uart0;
+		spi0 = &spi0;
+	};
+
+	memory@0 {
+		device_type = "memory";
+		reg = <0x0 0x4000000>;
+	};
+
+	chosen {
+		stdout-path = "serial0:115200n8";
+	};
+};
+
+&uart0 {
+	status = "okay";
+};
+
+&spi0 {
+	status = "okay";
+	num-cs = <2>;
+
+	spi-flash@0 {
+		#address-cells = <1>;
+		#size-cells = <1>;
+		compatible = "jedec,spi-nor";
+		spi-max-frequency = <40000000>;
+		reg = <0>;
+	};
+};
+
+&eth {
+	pinctrl-names = "default";
+	pinctrl-0 = <&ephy_router_mode>;
+};
--- /dev/null
+++ b/board/ravpower/rp-wd009/Kconfig
@@ -0,0 +1,12 @@
+if BOARD_RAVPOWER_RP_WD009
+
+config SYS_BOARD
+	default "rp-wd009"
+
+config SYS_VENDOR
+	default "ravpower"
+
+config SYS_CONFIG_NAME
+	default "ravpower-rp-wd009"
+
+endif
--- /dev/null
+++ b/board/ravpower/rp-wd009/Makefile
@@ -0,0 +1,3 @@
+# SPDX-License-Identifier: GPL-2.0+
+
+obj-y += board.o
--- /dev/null
+++ b/board/ravpower/rp-wd009/board.c
@@ -0,0 +1,16 @@
+// SPDX-License-Identifier: GPL-2.0+
+/*
+ * Copyright (C) 2018 Stefan Roese <<EMAIL>>
+ */
+
+
+int board_early_init_f(void)
+{
+	return 0;
+}
+
+
+int board_late_init(void)
+{
+	return 0;
+}
--- /dev/null
+++ b/configs/ravpower-rp-wd009-ram_defconfig
@@ -0,0 +1,62 @@
+CONFIG_MIPS=y
+CONFIG_NR_DRAM_BANKS=1
+CONFIG_DEFAULT_DEVICE_TREE="ravpower-rp-wd009"
+CONFIG_SYS_LOAD_ADDR=0x80010000
+CONFIG_ARCH_MTMIPS=y
+CONFIG_SOC_MT7628=y
+CONFIG_BOARD_RAVPOWER_RP_WD009=y
+CONFIG_SYS_MIPS_TIMER_FREQ=*********
+CONFIG_MIPS_BOOT_FDT=y
+CONFIG_FIT=y
+CONFIG_OF_STDOUT_VIA_ALIAS=y
+CONFIG_USE_BOOTCOMMAND=y
+CONFIG_BOOTCOMMAND="sf probe && mtd read firmware ******** && bootm ********"
+CONFIG_USE_PREBOOT=y
+CONFIG_SYS_CBSIZE=512
+CONFIG_SYS_CONSOLE_INFO_QUIET=y
+CONFIG_HUSH_PARSER=y
+CONFIG_CMD_LICENSE=y
+# CONFIG_CMD_ELF is not set
+# CONFIG_CMD_XIMG is not set
+CONFIG_CMD_MEMINFO=y
+CONFIG_CMD_GPIO=y
+# CONFIG_CMD_LOADS is not set
+CONFIG_CMD_MTD=y
+CONFIG_CMD_SPI=y
+CONFIG_CMD_WDT=y
+CONFIG_CMD_DHCP=y
+CONFIG_CMD_MII=y
+CONFIG_CMD_PING=y
+CONFIG_CMD_TIME=y
+CONFIG_CMD_UUID=y
+CONFIG_CMD_MTDPARTS=y
+CONFIG_MTDIDS_DEFAULT="nor0=spi0.0"
+CONFIG_MTDPARTS_DEFAULT="spi0.0:192k(factory-uboot),64k(config),64k(factory),1536k(loader),64k(params),64k(user_backup),64k(user),14272k(firmware),64k(mode)"
+CONFIG_VERSION_VARIABLE=y
+CONFIG_USE_IPADDR=y
+CONFIG_IPADDR="***********"
+CONFIG_USE_SERVERIP=y
+CONFIG_SERVERIP="*************"
+CONFIG_NET_RANDOM_ETHADDR=y
+# CONFIG_DM_DEVICE_REMOVE is not set
+CONFIG_LED=y
+CONFIG_LED_BLINK=y
+CONFIG_LED_GPIO=y
+CONFIG_DM_MTD=y
+CONFIG_SPI_FLASH_GIGADEVICE=y
+CONFIG_SPI_FLASH_MACRONIX=y
+CONFIG_SPI_FLASH_SPANSION=y
+CONFIG_SPI_FLASH_STMICRO=y
+CONFIG_SPI_FLASH_WINBOND=y
+CONFIG_SPI_FLASH_XMC=y
+CONFIG_SPI_FLASH_MTD=y
+CONFIG_MT7628_ETH=y
+CONFIG_PHY=y
+CONFIG_BAUDRATE=57600
+CONFIG_SERIAL_RX_BUFFER=y
+CONFIG_SPI=y
+CONFIG_MT7621_SPI=y
+CONFIG_SYSRESET_SYSCON=y
+CONFIG_WDT=y
+CONFIG_WDT_MT7621=y
+CONFIG_LZMA=y
--- /dev/null
+++ b/include/configs/ravpower-rp-wd009.h
@@ -0,0 +1,17 @@
+/* SPDX-License-Identifier: GPL-2.0+ */
+/*
+ * Copyright (C) 2018 Stefan Roese <<EMAIL>>
+ */
+
+#ifndef __CONFIG_RAVPOWER_RP_WD009_H
+#define __CONFIG_RAVPOWER_RP_WD009_H
+
+/* RAM */
+#define CFG_SYS_SDRAM_BASE		0x80000000
+
+#define CFG_SYS_INIT_SP_OFFSET		0x400000
+
+/* UART */
+#define CFG_SYS_BAUDRATE_TABLE		{ 9600, 19200, 38400, 57600, 115200, \
+					  230400, 460800, 921600 }
+#endif /* __CONFIG_RAVPOWER_RP_WD009_H */
--- a/arch/mips/mach-mtmips/mt7628/Kconfig
+++ b/arch/mips/mach-mtmips/mt7628/Kconfig
@@ -27,6 +27,14 @@ config BOARD_MT7628_RFB
 	  SPI-NOR flash, 1 built-in switch with 5 ports, 1 UART, 1 USB host,
 	  1 SDXC, 1 PCIe socket and JTAG pins.
 
+config BOARD_RAVPOWER_RP_WD009
+	bool "RAVPower RP-WD009"
+	depends on SOC_MT7628
+	select BOARD_LATE_INIT
+	select SUPPORTS_BOOT_RAM
+	help
+	  RAVPower RP-WD009
+
 config BOARD_VOCORE2
 	bool "VoCore2"
 	select SPL_SERIAL
@@ -52,6 +60,7 @@ config SYS_CONFIG_NAME
 	default "mt7628" if BOARD_MT7628_RFB
 
 source "board/gardena/smart-gateway-mt7688/Kconfig"
+source "board/ravpower/rp-wd009/Kconfig"
 source "board/seeed/linkit-smart-7688/Kconfig"
 source "board/vocore/vocore2/Kconfig"
 
