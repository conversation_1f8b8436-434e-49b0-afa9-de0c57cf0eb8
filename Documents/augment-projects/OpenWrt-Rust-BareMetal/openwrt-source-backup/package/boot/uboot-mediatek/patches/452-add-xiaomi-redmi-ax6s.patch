From 57dc777bddf0baf3c27177576c40b5113309ce54 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <<EMAIL>>
Date: Sat, 2 Mar 2024 20:30:16 +0800
Subject: [PATCH] add xiaomi redmi ax6s

---
 arch/arm/dts/Makefile                         |   1 +
 .../dts/mt7622-xiaomi-redmi-router-ax6s.dts   | 166 ++++++++++++++++++
 ...omi_redmi-router-ax6s-ubi-loader_defconfig |  98 +++++++++++
 xiaomi-redmi-router-ax6s-ubi-loader_env       |  22 +++
 4 files changed, 287 insertions(+)
 create mode 100644 arch/arm/dts/mt7622-xiaomi-redmi-router-ax6s.dts
 create mode 100644 configs/mt7622_xiaomi_redmi-router-ax6s-ubi-loader_defconfig
 create mode 100644 xiaomi-redmi-router-ax6s-ubi-loader_env

--- a/arch/arm/dts/Makefile
+++ b/arch/arm/dts/Makefile
@@ -1137,6 +1137,7 @@ dtb-$(CONFIG_ARCH_MEDIATEK) += \
 	mt7622-linksys-e8450-ubi.dtb \
 	mt7622-ubnt-unifi-6-lr.dtb \
 	mt7622-ubnt-unifi-6-lr-v3.dtb \
+	mt7622-xiaomi-redmi-router-ax6s.dtb \
 	mt7623n-bananapi-bpi-r2.dtb \
 	mt7981-rfb.dtb \
 	mt7981-snfi-nand-rfb.dtb \
--- /dev/null
+++ b/arch/arm/dts/mt7622-xiaomi-redmi-router-ax6s.dts
@@ -0,0 +1,166 @@
+// SPDX-License-Identifier: GPL-2.0-or-later OR MIT
+
+/dts-v1/;
+#include <dt-bindings/input/linux-event-codes.h>
+#include <dt-bindings/leds/common.h>
+#include "mt7622.dtsi"
+#include "mt7622-u-boot.dtsi"
+
+/ {
+	#address-cells = <1>;
+	#size-cells = <1>;
+	model = "Xiaomi Redmi Router AX6S";
+	compatible = "xiaomi,redmi-router-ax6s", "mediatek,mt7622";
+
+	chosen {
+		stdout-path = &uart0;
+		tick-timer = &timer0;
+	};
+
+	aliases {
+		spi0 = &snand;
+		ethernet0 = &eth;
+	};
+
+	memory@40000000 {
+		device_type = "memory";
+		reg = <0x40000000 0x8000000>;
+	};
+
+	leds {
+		compatible = "gpio-leds";
+
+		led_power_blue: power_blue {
+			function = LED_FUNCTION_POWER;
+			color = <LED_COLOR_ID_BLUE>;
+			gpios = <&gpio 18 GPIO_ACTIVE_LOW>;
+		};
+
+		led_power_amber: power_amber {
+			function = LED_FUNCTION_POWER;
+			color = <LED_COLOR_ID_AMBER>;
+			gpios = <&gpio 17 GPIO_ACTIVE_LOW>;
+		};
+
+		led_net_blue: net_blue {
+			label = "blue:net";
+			gpios = <&gpio 1 GPIO_ACTIVE_LOW>;
+		};
+
+		led_net_amber: net_amber {
+			label = "amber:net";
+			gpios = <&gpio 16 GPIO_ACTIVE_LOW>;
+		};
+
+	};
+
+	keys {
+		compatible = "gpio-keys";
+
+		reset {
+			label = "reset";
+			gpios = <&gpio 0 GPIO_ACTIVE_LOW>;
+			linux,code = <KEY_RESTART>;
+		};
+
+		mesh {
+			label = "mesh";
+			gpios = <&gpio 102 GPIO_ACTIVE_LOW>;
+			linux,code = <BTN_9>;
+			linux,input-type = <EV_SW>;
+		};
+	};
+
+	reg_1p8v: regulator-1p8v {
+		compatible = "regulator-fixed";
+		regulator-name = "fixed-1.8V";
+		regulator-min-microvolt = <1800000>;
+		regulator-max-microvolt = <1800000>;
+		regulator-boot-on;
+		regulator-always-on;
+	};
+
+	reg_3p3v: regulator-3p3v {
+		compatible = "regulator-fixed";
+		regulator-name = "fixed-3.3V";
+		regulator-min-microvolt = <3300000>;
+		regulator-max-microvolt = <3300000>;
+		regulator-boot-on;
+		regulator-always-on;
+	};
+};
+
+&pcie {
+	pinctrl-names = "default";
+	pinctrl-0 = <&pcie0_pins>;
+	status = "okay";
+
+	pcie@0,0 {
+		status = "okay";
+	};
+};
+
+&pinctrl {
+	pcie0_pins: pcie0-pins {
+		mux {
+			function = "pcie";
+			groups = "pcie0_pad_perst",
+				 "pcie0_1_waken",
+				 "pcie0_1_clkreq";
+		};
+	};
+
+	snfi_pins: snfi-pins {
+		mux {
+			function = "flash";
+			groups = "snfi";
+		};
+	};
+
+	uart0_pins: uart0 {
+		mux {
+			function = "uart";
+			groups = "uart0_0_tx_rx" ;
+		};
+	};
+
+	watchdog_pins: watchdog-default {
+		mux {
+			function = "watchdog";
+			groups = "watchdog";
+		};
+	};
+};
+
+&snand {
+	pinctrl-names = "default";
+	pinctrl-0 = <&snfi_pins>;
+	quad-spi;
+	status = "okay";
+};
+
+&uart0 {
+	pinctrl-names = "default";
+	pinctrl-0 = <&uart0_pins>;
+	mediatek,force-highspeed;
+	status = "okay";
+};
+
+&watchdog {
+	pinctrl-names = "default";
+	pinctrl-0 = <&watchdog_pins>;
+	status = "okay";
+};
+
+&eth {
+	status = "okay";
+	mediatek,gmac-id = <0>;
+	phy-mode = "2500base-x";
+	mediatek,switch = "mt7531";
+	reset-gpios = <&gpio 54 GPIO_ACTIVE_HIGH>;
+
+	fixed-link {
+		speed = <2500>;
+		full-duplex;
+	};
+};
--- /dev/null
+++ b/configs/mt7622_xiaomi_redmi-router-ax6s-ubi-loader_defconfig
@@ -0,0 +1,98 @@
+CONFIG_ARM=y
+CONFIG_SYS_HAS_NONCACHED_MEMORY=y
+CONFIG_POSITION_INDEPENDENT=y
+CONFIG_ARCH_MEDIATEK=y
+CONFIG_TEXT_BASE=0x41e00000
+CONFIG_SYS_MALLOC_F_LEN=0x4000
+CONFIG_NR_DRAM_BANKS=1
+CONFIG_DEFAULT_DEVICE_TREE="mt7622-xiaomi-redmi-router-ax6s"
+CONFIG_SYS_LOAD_ADDR=0x40080000
+CONFIG_PRE_CON_BUF_ADDR=0x4007EF00
+CONFIG_DEBUG_UART_BASE=0x11002000
+CONFIG_DEBUG_UART_CLOCK=********
+CONFIG_PCI=y
+CONFIG_DEBUG_UART=y
+CONFIG_FIT=y
+CONFIG_BOOTDELAY=30
+CONFIG_AUTOBOOT_KEYED=y
+CONFIG_DEFAULT_FDT_FILE="mediatek/mt7622-xiaomi-redmi-router-ax6s"
+CONFIG_LOGLEVEL=7
+CONFIG_PRE_CONSOLE_BUFFER=y
+CONFIG_LOG=y
+CONFIG_BOARD_LATE_INIT=y
+CONFIG_HUSH_PARSER=y
+# CONFIG_AUTO_COMPLETE is not set
+CONFIG_SYS_PROMPT="MT7622> "
+CONFIG_CMD_LICENSE=y
+CONFIG_CMD_ASKENV=y
+CONFIG_CMD_ERASEENV=y
+CONFIG_CMD_ENV_FLAGS=y
+# CONFIG_CMD_UNLZ4 is not set
+# CONFIG_CMD_UNZIP is not set
+CONFIG_CMD_GPIO=y
+CONFIG_CMD_MTD=y
+# CONFIG_CMD_BOOTP is not set
+CONFIG_CMD_TFTPSRV=y
+CONFIG_CMD_PING=y
+CONFIG_CMD_HASH=y
+CONFIG_CMD_SMC=y
+CONFIG_CMD_FS_GENERIC=y
+CONFIG_CMD_FS_UUID=y
+CONFIG_CMD_MTDPARTS=y
+CONFIG_MTDPARTS_DEFAULT="mtdparts=spi-nand0:512k(preloader),2816k(reserved),117248k(ubi)"
+CONFIG_CMD_UBI=y
+CONFIG_CMD_UBI_RENAME=y
+CONFIG_DOS_PARTITION=y
+CONFIG_EFI_PARTITION=y
+CONFIG_SYS_REDUNDAND_ENVIRONMENT=y
+CONFIG_SYS_RELOC_GD_ENV_ADDR=y
+CONFIG_USE_DEFAULT_ENV_FILE=y
+CONFIG_DEFAULT_ENV_FILE="defenvs/xiaomi-redmi-router-ax6s-ubi-loader_env"
+CONFIG_ENV_VARS_UBOOT_RUNTIME_CONFIG=y
+CONFIG_VERSION_VARIABLE=y
+CONFIG_PROT_UDP=y
+CONFIG_BOOTP_SEND_HOSTNAME=y
+CONFIG_NETCONSOLE=y
+CONFIG_USE_IPADDR=y
+CONFIG_IPADDR="***********"
+CONFIG_USE_SERVERIP=y
+CONFIG_SERVERIP="*************"
+CONFIG_NET_RANDOM_ETHADDR=y
+CONFIG_REGMAP=y
+CONFIG_SYSCON=y
+CONFIG_BUTTON=y
+CONFIG_BUTTON_GPIO=y
+CONFIG_CLK=y
+CONFIG_GPIO_HOG=y
+CONFIG_LED=y
+CONFIG_LED_BLINK=y
+CONFIG_LED_GPIO=y
+# CONFIG_MMC is not set
+CONFIG_MTD=y
+CONFIG_DM_MTD=y
+CONFIG_MTK_SPI_NAND=y
+CONFIG_MTK_SPI_NAND_MTD=y
+CONFIG_UBI_SILENCE_MSG=y
+CONFIG_MTD_UBI_FASTMAP=y
+CONFIG_PHY_FIXED=y
+CONFIG_MEDIATEK_ETH=y
+CONFIG_PCIE_MEDIATEK=y
+CONFIG_PHY=y
+CONFIG_PINCTRL=y
+CONFIG_PINCONF=y
+CONFIG_PINCTRL_MT7622=y
+CONFIG_POWER_DOMAIN=y
+CONFIG_MTK_POWER_DOMAIN=y
+CONFIG_DM_REGULATOR=y
+CONFIG_DM_REGULATOR_FIXED=y
+CONFIG_DM_REGULATOR_GPIO=y
+CONFIG_RAM=y
+CONFIG_DM_SERIAL=y
+CONFIG_SERIAL_RX_BUFFER=y
+CONFIG_MTK_SERIAL=y
+CONFIG_SPI=y
+CONFIG_DM_SPI=y
+CONFIG_UBIFS_SILENCE_MSG=y
+CONFIG_LZ4=y
+CONFIG_ZSTD=y
+CONFIG_HEXDUMP=y
--- /dev/null
+++ b/defenvs/xiaomi-redmi-router-ax6s-ubi-loader_env
@@ -0,0 +1,22 @@
+ipaddr=***********
+serverip=*************
+loadaddr=0x46000000
+console=earlycon=uart8250,mmio32,0x11002000 console=ttyS0
+bootled_pwr=power_blue
+bootled_rec=power_amber
+bootcmd=run boot_or_recovery
+bootconf=config-1
+bootdelay=0
+bootfile=openwrt-mediatek-mt7622-xiaomi_redmi-router-ax6s-initramfs-recovery.itb
+bootfile_upg=openwrt-mediatek-mt7622-xiaomi_redmi-router-ax6s-squashfs-sysupgrade.itb
+boot_or_recovery=run boot_production ; led $bootled_pwr off ; led $bootled_rec on ; if ubi check fit ; then run boot_tftp_forever ; else run tftp_production ; fi
+boot_tftp=tftpboot $loadaddr $bootfile && bootm $loadaddr#$bootconf
+boot_tftp_forever=while true ; do run boot_tftp ; sleep 1 ; done
+boot_production=run ubi_read_production && bootm $loadaddr#$bootconf
+ubi_format=ubi detach ; mtd erase ubi && ubi part ubi
+ubi_init=ubi part ubi || run ubi_format
+ubi_prepare_rootfs=if ubi check rootfs_data ; then else if env exists rootfs_data_max ; then ubi create rootfs_data $rootfs_data_max dynamic || ubi create rootfs_data - dynamic ; else ubi create rootfs_data - dynamic ; fi ; fi
+ubi_remove_rootfs=ubi check rootfs_data && ubi remove rootfs_data
+ubi_write_production=ubi check fit && ubi remove fit ; run ubi_remove_rootfs ; ubi create fit $filesize dynamic && ubi write $loadaddr fit $filesize
+ubi_read_production=run ubi_init && ubi read $loadaddr fit && iminfo $loadaddr && run ubi_prepare_rootfs
+tftp_production=tftpboot $loadaddr $bootfile_upg && iminfo $loadaddr && run ubi_write_production && reset
