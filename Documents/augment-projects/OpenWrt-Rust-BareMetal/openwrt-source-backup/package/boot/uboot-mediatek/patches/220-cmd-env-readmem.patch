--- a/cmd/Kconfig
+++ b/cmd/Kconfig
@@ -707,6 +707,12 @@ config CMD_ENV_EXISTS
 	  Check if a variable is defined in the environment for use in
 	  shell scripting.
 
+config CMD_ENV_READMEM
+	bool "env readmem"
+	default y
+	help
+	  Store memory content into environment variable.
+
 config CMD_ENV_CALLBACK
 	bool "env callbacks - print callbacks and their associated variables"
 	help
--- a/cmd/nvedit.c
+++ b/cmd/nvedit.c
@@ -273,6 +273,60 @@ static int do_env_ask(struct cmd_tbl *cm
 }
 #endif
 
+#if defined(CONFIG_CMD_ENV_READMEM)
+int do_env_readmem(struct cmd_tbl *cmdtp, int flag, int argc, char *const argv[])
+{
+	char varstr[CONFIG_SYS_CBSIZE];
+	const void *buf;
+	char *local_args[4];
+	ulong addr, bytes = 6;
+	int hexdump = 0;
+
+	/*
+	 * Check the syntax:
+	 *
+	 * readmem [-b] name address [size]
+	 */
+	if (argc < 3)
+		return CMD_RET_USAGE;
+
+	local_args[0] = argv[0];
+
+	if (!strncmp(argv[1], "-b", 3))
+		hexdump = 1;
+
+	local_args[1] = argv[hexdump + 1];
+	local_args[2] = varstr;
+	local_args[3] = NULL;
+
+	addr = simple_strtoul(argv[hexdump + 2], NULL, 16);
+
+	if (!hexdump)
+		bytes = simple_strtoul(argv[hexdump + 3], NULL, 16);
+
+	if (bytes < 1)
+		return 1;
+
+	if ((hexdump * 3) * bytes >= CONFIG_SYS_CBSIZE)
+		return 1;
+
+	buf = map_sysmem(addr, bytes);
+	if (!buf)
+		return 1;
+
+	if (hexdump) {
+		sprintf(varstr, "%pM", buf);
+	} else {
+		memcpy(varstr, buf, bytes);
+		varstr[bytes] = '\0';
+	}
+	unmap_sysmem(buf);
+
+	/* Continue calling setenv code */
+	return env_do_env_set(flag, 3, local_args, H_INTERACTIVE);
+}
+#endif
+
 #if defined(CONFIG_CMD_ENV_CALLBACK)
 static int print_static_binding(const char *var_name, const char *callback_name,
 				void *priv)
@@ -1092,6 +1146,9 @@ static struct cmd_tbl cmd_env_sub[] = {
 	U_BOOT_CMD_MKENT(load, 1, 0, do_env_load, "", ""),
 #endif
 	U_BOOT_CMD_MKENT(print, CONFIG_SYS_MAXARGS, 1, do_env_print, "", ""),
+#if defined(CONFIG_CMD_ENV_READMEM)
+	U_BOOT_CMD_MKENT(readmem, CONFIG_SYS_MAXARGS, 3, do_env_readmem, "", ""),
+#endif
 #if defined(CONFIG_CMD_RUN)
 	U_BOOT_CMD_MKENT(run, CONFIG_SYS_MAXARGS, 1, do_run, "", ""),
 #endif
@@ -1176,6 +1233,9 @@ U_BOOT_LONGHELP(env,
 #if defined(CONFIG_CMD_NVEDIT_EFI)
 	"env print -e [-guid guid] [-n] [name ...] - print UEFI environment\n"
 #endif
+#if defined(CONFIG_CMD_ENV_READMEM)
+	"env readmem [-b] name address size - read variable from memory\n"
+#endif
 #if defined(CONFIG_CMD_RUN)
 	"env run var [...] - run commands in an environment variable\n"
 #endif
@@ -1284,6 +1344,17 @@ U_BOOT_CMD(
 );
 #endif
 
+#if defined(CONFIG_CMD_ENV_READMEM)
+U_BOOT_CMD_COMPLETE(
+	readmem,	CONFIG_SYS_MAXARGS,	3,	do_env_readmem,
+	"get environment variable from memory address",
+	"name [-b] address size\n"
+	"    - store memory address to env variable\n"
+	"      \"-b\": read binary ethaddr",
+	var_complete
+);
+#endif
+
 #if defined(CONFIG_CMD_RUN)
 U_BOOT_CMD_COMPLETE(
 	run,	CONFIG_SYS_MAXARGS,	1,	do_run,
