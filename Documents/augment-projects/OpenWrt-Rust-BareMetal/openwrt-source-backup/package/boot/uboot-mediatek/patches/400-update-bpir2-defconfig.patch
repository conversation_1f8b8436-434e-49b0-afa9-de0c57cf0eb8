--- a/configs/mt7623n_bpir2_defconfig
+++ b/configs/mt7623n_bpir2_defconfig
@@ -7,34 +7,67 @@ CONFIG_SYS_MALLOC_F_LEN=0x4000
 CONFIG_NR_DRAM_BANKS=1
 CONFIG_HAS_CUSTOM_SYS_INIT_SP_ADDR=y
 CONFIG_CUSTOM_SYS_INIT_SP_ADDR=0x81ffff10
-CONFIG_ENV_SIZE=0x1000
+CONFIG_ENV_SIZE=0x10000
 CONFIG_ENV_OFFSET=0x100000
 CONFIG_DEFAULT_DEVICE_TREE="mt7623n-bananapi-bpi-r2"
+CONFIG_OF_LIBFDT_OVERLAY=y
 CONFIG_TARGET_MT7623=y
+CONFIG_RESET_BUTTON_LABEL="factory"
 CONFIG_SYS_BOOTM_LEN=0x4000000
 CONFIG_SYS_LOAD_ADDR=0x84000000
+CONFIG_PCI=y
+CONFIG_AHCI=y
 # CONFIG_EFI_GRUB_ARM32_WORKAROUND is not set
 CONFIG_FIT=y
 CONFIG_FIT_VERBOSE=y
+# CONFIG_BOOTSTD is not set
 CONFIG_DISTRO_DEFAULTS=y
 CONFIG_BOOTDELAY=3
+CONFIG_AUTOBOOT_KEYED=y
+CONFIG_AUTOBOOT_MENU_SHOW=y
+CONFIG_OF_SYSTEM_SETUP=y
 CONFIG_DEFAULT_FDT_FILE="mt7623n-bananapi-bpi-r2.dtb"
 CONFIG_SYS_PBSIZE=1049
+CONFIG_LOGLEVEL=7
 CONFIG_SYS_CONSOLE_IS_IN_ENV=y
+CONFIG_LOG=y
 # CONFIG_DISPLAY_BOARDINFO is not set
-CONFIG_SYS_PROMPT="U-Boot> "
+CONFIG_BOARD_LATE_INIT=y
+CONFIG_SYS_PROMPT="MT7623> "
 CONFIG_SYS_MAXARGS=8
+CONFIG_CMD_LICENSE=y
+# CONFIG_CMD_BOOTEFI_BOOTMGR is not set
 CONFIG_CMD_BOOTMENU=y
 # CONFIG_CMD_ELF is not set
 # CONFIG_CMD_XIMG is not set
+CONFIG_CMD_ASKENV=y
+CONFIG_CMD_ERASEENV=y
+CONFIG_CMD_ENV_FLAGS=y
+CONFIG_CMD_STRINGS=y
+CONFIG_CMD_DM=y
 CONFIG_CMD_GPIO=y
-CONFIG_CMD_GPT=y
+CONFIG_CMD_MBR=y
 CONFIG_CMD_MMC=y
 CONFIG_CMD_READ=y
-# CONFIG_CMD_SETEXPR is not set
+CONFIG_CMD_SATA=y
+CONFIG_CMD_TFTPSRV=y
+CONFIG_CMD_RARP=y
+CONFIG_CMD_CDP=y
+CONFIG_CMD_SNTP=y
+CONFIG_CMD_LINK_LOCAL=y
+CONFIG_CMD_DNS=y
+CONFIG_CMD_CACHE=y
+CONFIG_CMD_UUID=y
+CONFIG_CMD_HASH=y
+CONFIG_CMD_FS_UUID=y
 CONFIG_ENV_OVERWRITE=y
 CONFIG_ENV_IS_IN_MMC=y
 CONFIG_SYS_RELOC_GD_ENV_ADDR=y
+CONFIG_USE_DEFAULT_ENV_FILE=y
+CONFIG_DEFAULT_ENV_FILE="defenvs/bananapi_bpi-r2_env"
+CONFIG_VERSION_VARIABLE=y
+CONFIG_BOOTP_SEND_HOSTNAME=y
+CONFIG_NETCONSOLE=y
 CONFIG_USE_IPADDR=y
 CONFIG_IPADDR="***********"
 CONFIG_USE_SERVERIP=y
@@ -42,23 +75,38 @@ CONFIG_SERVERIP="***********"
 CONFIG_NET_RANDOM_ETHADDR=y
 CONFIG_REGMAP=y
 CONFIG_SYSCON=y
+CONFIG_SCSI_AHCI=y
+CONFIG_AHCI_PCI=y
+CONFIG_BUTTON=y
+CONFIG_BUTTON_GPIO=y
 CONFIG_CLK=y
+CONFIG_LED=y
+CONFIG_LED_BLINK=y
+CONFIG_LED_GPIO=y
 # CONFIG_MMC_QUIRKS is not set
 CONFIG_SUPPORT_EMMC_BOOT=y
 CONFIG_MMC_HS400_SUPPORT=y
 CONFIG_MMC_MTK=y
 CONFIG_PHY_FIXED=y
 CONFIG_MEDIATEK_ETH=y
+CONFIG_PCIE_MEDIATEK=y
+CONFIG_PHY=y
 CONFIG_PINCTRL=y
 CONFIG_PINCONF=y
 CONFIG_PINCTRL_MT7623=y
 CONFIG_POWER_DOMAIN=y
 CONFIG_MTK_POWER_DOMAIN=y
+CONFIG_DM_REGULATOR=y
+CONFIG_DM_REGULATOR_FIXED=y
+CONFIG_DM_REGULATOR_GPIO=y
+CONFIG_SCSI=y
 CONFIG_DM_SERIAL=y
+CONFIG_SERIAL_RX_BUFFER=y
 CONFIG_MTK_SERIAL=y
 CONFIG_SYSRESET=y
 CONFIG_SYSRESET_WATCHDOG=y
 CONFIG_TIMER=y
 CONFIG_MTK_TIMER=y
 CONFIG_WDT_MTK=y
+CONFIG_RANDOM_UUID=y
 CONFIG_LZMA=y
--- /dev/null
+++ b/defenvs/bananapi_bpi-r2_env
@@ -0,0 +1,69 @@
+ipaddr=***********
+serverip=*************
+loadaddr=0x88000000
+dtaddr=0x83f00000
+console=earlycon=uart8250,mmio32,0x11004000 console=ttyS2,115200 console=tty1
+initrd_high=0xafffffff
+part_default=3
+part_recovery=2
+bootcmd=run boot_mmc
+bootdelay=0
+bootfile=openwrt-mediatek-mt7623-bananapi_bpi-r2-initramfs-recovery.itb
+bootfile_upg=openwrt-mediatek-mt7623-bananapi_bpi-r2-squashfs-sysupgrade.itb
+bootled_pwr=bpi-r2:pio:green
+bootled_rec=bpi-r2:pio:blue
+bootmenu_confirm_return=askenv - Press ENTER to return to menu ; bootmenu 60
+bootmenu_default=0
+bootmenu_delay=0
+bootmenu_title=      [0;34m( ( ( [1;39mOpenWrt[0;34m ) ) )
+bootmenu_0=Initialize environment.=run _firstboot
+bootmenu_0d=Run default boot command.=run boot_default
+bootmenu_1=Boot system via TFTP.=run boot_tftp ; run bootmenu_confirm_return
+boot_default=if env exists flag_recover ; then else run bootcmd ; fi ; run boot_recovery ; setenv replacevol 1 ; run boot_tftp_forever
+boot_first=if button factory ; then led $bootled_rec on ; run boot_tftp_recovery ; setenv flag_recover 1 ; run boot_default ; fi ; bootmenu
+boot_tftp_forever=led bpi-r2:pio:blue on ; while true ; do run boot_tftp_recovery ; sleep 1 ; done
+boot_tftp_production=tftpboot $loadaddr $bootfile_upg && env exists replacevol && iminfo $loadaddr && run mmc_write_production ; if env exists noboot ; then else bootm $loadaddr ; fi
+boot_tftp_recovery=tftpboot $loadaddr $bootfile && env exists replacevol && iminfo $loadaddr && run mmc_write_recovery ; if env exists noboot ; then else bootm $loadaddr ; fi
+boot_tftp=tftpboot $loadaddr $bootfile && bootm $loadaddr
+boot_mmc=run boot_production ; run boot_recovery
+emmc_init=run emmc_init_bl && run emmc_init_openwrt ; env default bootcmd ; saveenv
+emmc_init_bl=run sdmmc_read_emmc_hdr && run emmc_write_hdr && run sdmmc_read_preloader && run emmc_write_preloader && run sdmmc_read_uboot && run emmc_write_uboot
+emmc_init_openwrt=run sdmmc_read_recovery && run emmc_write_recovery ; run sdmmc_read_production && run emmc_write_production
+emmc_write_hdr=mmc dev 0 0 ; mmc erase 0x0 0x2000 ; mmc write $loadaddr 0x0 0x4 ; mmc dev 0 1 ; mmc partconf 0 1 1 1 ; mmc erase 0x0 0x400 ; mmc write $loadaddr 0x0 0x4 ; mmc partconf 0 1 1 0
+emmc_write_preloader=mmc dev 0 1 ; mmc partconf 0 1 1 1 ; mmc write $loadaddr 0x4 0x100 ; mmc partconf 0 1 1 0
+emmc_write_uboot=mmc dev 0 0 ; part size mmc 0 1 part_size && part start mmc 0 1 part_addr && mmc write $loadaddr $part_addr 0x400
+emmc_write_production=mmc dev 0 0 ; iminfo $loadaddr && part start mmc 0 $part_default part_addr && part size mmc 0 $part_default part_size && run mmc_write_vol
+emmc_write_recovery=mmc dev 0 0 ; iminfo $loadaddr && part start mmc 0 $part_recovery part_addr && part size mmc 0 $part_recovery part_size && run mmc_write_vol
+emmc_read_production=mmc dev 0 0 ; part start mmc 0 $part_default part_addr && part size mmc 0 $part_default part_size && run mmc_read_vol
+emmc_read_recovery=mmc dev 0 0 ; part start mmc 0 $part_recovery part_addr && part size mmc 0 $part_recovery part_size && run mmc_read_vol
+mmc_write_production=if test "$bootedfrom" = "SD" ; then run sdmmc_write_production ; else run emmc_write_production ; fi
+mmc_write_recovery=if test "$bootedfrom" = "SD" ; run sdmmc_write_recovery ; else run emmc_write_recovery ; fi
+mmc_write_vol=imszb $loadaddr image_size && test 0x$image_size -le 0x$part_size && mmc erase 0x$part_addr 0x$image_size && mmc write $loadaddr 0x$part_addr 0x$image_size
+mmc_read_vol=mmc read $loadaddr $part_addr 0x8 && imszb $loadaddr image_size && test 0x$image_size -le 0x$part_size && mmc read $loadaddr 0x$part_addr 0x$image_size && setexpr filesize $image_size * 0x200
+reset_factory=eraseenv && reset
+sdmmc_read_emmc_hdr=mmc dev 1 && mmc read $loadaddr 0x1ff8 0x8
+sdmmc_read_preloader=mmc dev 1 && mmc read $loadaddr 0x4 0x100
+sdmmc_read_uboot=mmc dev 1 ; part start mmc 1 1 part_addr && part size mmc 1 1 part_size && mmc read $loadaddr $part_addr $part_size
+sdmmc_read_production=mmc dev 1 ; part start mmc 1 $part_default part_addr && part size mmc 1 $part_default part_size && run mmc_read_vol
+sdmmc_read_recovery=mmc dev 1 ; part start mmc 1 $part_recovery part_addr && part size mmc 1 $part_recovery part_size && run mmc_read_vol
+sdmmc_write_production=iminfo $fileaddr && mmc dev 1 && part start mmc 1 $part_default part_addr && part size mmc 1 $part_default part_size && run mmc_write_vol
+sdmmc_write_recovery=iminfo $fileaddr && mmc dev 1 && part start mmc 1 $part_recovery part_addr && part size mmc 1 $part_recovery part_size && run mmc_write_vol
+_checkbootedfrom=setenv _checkbootedfrom ; if itest.l *81dffff0 == 434d4d65 ; then setenv bootedfrom eMMC ; else setenv bootedfrom SD ; fi
+_init_env=setenv _init_env ; setenv _create_env ; saveenv ; saveenv
+_firstboot=setenv _firstboot ; led $bootled_pwr off ;led $bootled_rec on ; run _checkbootedfrom _switch_to_menu _update_bootcmd _update_bootcmd2 _init_env boot_first
+_set_bootcmd_sdmmc=setenv boot_production "led $bootled_rec off ; led $bootled_pwr on ; run sdmmc_read_production && bootm $loadaddr ; led $bootled_pwr off"
+_set_bootcmd_emmc=setenv boot_production "led $bootled_rec off ; led $bootled_pwr on ; run emmc_read_production && bootm $loadaddr ; led $bootled_pwr off"
+_update_bootcmd=setenv _update_bootcmd ; if test "$bootedfrom" = "SD" ; then run _set_bootcmd_sdmmc ; else run _set_bootcmd_emmc ; fi ; setenv _set_bootcmd_sdmmc ; setenv _set_bootcmd_emmc
+_set_bootcmd2_sdmmc=setenv boot_recovery "led $bootled_pwr off ; led $bootled_rec on ; run sdmmc_read_recovery && bootm $loadaddr ; led $bootled_rec off"
+_set_bootcmd2_emmc=setenv boot_recovery "led $bootled_pwr off ; led $bootled_rec on ; run emmc_read_recovery && bootm $loadaddr ; led $bootled_rec off"
+_update_bootcmd2=setenv _update_bootcmd2 ; if test "$bootedfrom" = "SD" ; then run _set_bootcmd2_sdmmc ; else run _set_bootcmd2_emmc ; fi ; setenv _set_bootcmd2_sdmmc ; setenv _set_bootcmd2_emmc
+_switch_to_menu=setenv _switch_to_menu ; setenv bootdelay 3 ; setenv bootmenu_delay 3 ; setenv bootmenu_0 $bootmenu_0d ; setenv bootmenu_0d ; run _bootmenu_update_title
+_bootmenu_update_title=setenv _bootmenu_update_title ; setenv bootmenu_title "$bootmenu_title  [0;36m[$bootedfrom][0m    [33m$ver[0m" ; run _set_bm2
+_set_bm2=setenv _set_bm2 ; setenv bootmenu_2 "Boot production system from $bootedfrom.=run boot_production ; run bootmenu_confirm_return" ; run _set_bm3
+_set_bm3=setenv _set_bm3 ; setenv bootmenu_3 "Boot recovery system from $bootedfrom.=run boot_recovery ; run bootmenu_confirm_return" ; run _set_bm4
+_set_bm4=setenv _set_bm4 ; setenv bootmenu_4 "Load production system via TFTP then write to $bootedfrom.=setenv noboot 1 ; setenv replacevol 1 ; run boot_tftp_production ; setenv noboot ; setenv replacevol ; run bootmenu_confirm_return" ; run _set_bm5
+_set_bm5=setenv _set_bm5 ; setenv bootmenu_5 "Load recovery system via TFTP then write to $bootedfrom.=setenv noboot 1 ; setenv replacevol 1 ; run boot_tftp_recovery ; setenv noboot ; setenv replacevol ; run bootmenu_confirm_return" ; run _set_bm5a
+_set_bm5a=setenv _set_bm5a ; if test "$bootedfrom" = "SD" ; then run _set_bm6 ; else setenv _set_bm6 ; setenv _menu_next 6 ; fi ; run _set_bmr
+_set_bm6=setenv _set_bm6 ; setenv bootmenu_6 "[31mInstall bootloader, recovery and production to eMMC.[0m=run emmc_init ; run bootmenu_confirm_return" ; setenv _menu_next 7
+_set_bmr=setenv _set_bmr ; setenv bootmenu_${_menu_next} "Reboot.=reset" ; setexpr _menu_next ${_menu_next} + 1 ; run _set_bmf
+_set_bmf=setenv _set_bmf ; setenv bootmenu_${_menu_next} "Reset all settings to factory defaults.=run reset_factory ; reset" ; setenv _menu_next
--- a/arch/arm/dts/mt7623n-bananapi-bpi-r2.dts
+++ b/arch/arm/dts/mt7623n-bananapi-bpi-r2.dts
@@ -6,6 +6,7 @@
  */
 
 /dts-v1/;
+#include <dt-bindings/input/linux-event-codes.h>
 #include "mt7623.dtsi"
 #include "mt7623-u-boot.dtsi"
 
@@ -66,6 +67,16 @@
 			default-state = "off";
 		};
 	};
+
+	gpio-keys {
+		compatible = "gpio-keys";
+
+		factory {
+			label = "factory";
+			gpios = <&gpio 256 GPIO_ACTIVE_LOW>;
+			linux,code = <KEY_RESTART>;
+		};
+	};
 };
 
 &eth {
