From 5f2d5915f8ea4785bc2b8a26955e176a7898c15b Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Tue, 12 Apr 2022 21:00:43 +0100
Subject: [PATCH] image-fdt: save name of FIT configuration in '/chosen' node

It can be useful for the OS (Linux) to know which configuration has
been chosen by <PERSON>-Bo<PERSON> when launching a FIT image.
Store the name of the FIT configuration node used in a new string
property called 'u-boot,bootconf' in the '/chosen' node in device tree.

Signed-off-by: <PERSON> <<EMAIL>>
Reviewed-by: <PERSON> <<EMAIL>>
---
 boot/image-fdt.c | 6 ++++++
 1 file changed, 6 insertions(+)

--- a/boot/image-fdt.c
+++ b/boot/image-fdt.c
@@ -613,6 +613,12 @@ int image_setup_libfdt(struct bootm_head
 					images->fit_uname_cfg,
 					strlen(images->fit_uname_cfg) + 1, 1);
 
+	/* Store name of configuration node as u-boot,bootconf in /chosen node */
+	if (images->fit_uname_cfg)
+		fdt_find_and_setprop(blob, "/chosen", "u-boot,bootconf",
+					images->fit_uname_cfg,
+					strlen(images->fit_uname_cfg) + 1, 1);
+
 	/* Update ethernet nodes */
 	fdt_fixup_ethernet(blob);
 #if IS_ENABLED(CONFIG_CMD_PSTORE)
