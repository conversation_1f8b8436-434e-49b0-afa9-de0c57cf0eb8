--- a/cmd/bootmenu.c
+++ b/cmd/bootmenu.c
@@ -482,7 +482,11 @@ static void menu_display_statusline(stru
 	printf(ANSI_CURSOR_POSITION, 1, 1);
 	puts(ANSI_CLEAR_LINE);
 	printf(ANSI_CURSOR_POSITION, 2, 3);
-	puts("*** U-Boot Boot Menu ***");
+	if (menu->mtitle)
+		puts(menu->mtitle);
+	else
+		puts("  *** U-Boot Boot Menu ***");
+
 	puts(ANSI_CLEAR_LINE_TO_END);
 	printf(ANSI_CURSOR_POSITION, 3, 1);
 	puts(ANSI_CLEAR_LINE);
@@ -573,6 +577,7 @@ static enum bootmenu_ret bootmenu_show(i
 		return BOOTMENU_RET_FAIL;
 	}
 
+	bootmenu->mtitle = env_get("bootmenu_title");
 	for (iter = bootmenu->first; iter; iter = iter->next) {
 		if (menu_item_add(menu, iter->key, iter) != 1)
 			goto cleanup;
--- a/include/menu.h
+++ b/include/menu.h
@@ -43,6 +43,7 @@ struct bootmenu_data {
 	int last_active;		/* last active menu entry */
 	int count;			/* total count of menu entries */
 	struct bootmenu_entry *first;	/* first menu entry */
+	char *mtitle;			/* custom menu title */
 };
 
 /** enum bootmenu_key - keys that can be returned by the bootmenu */
