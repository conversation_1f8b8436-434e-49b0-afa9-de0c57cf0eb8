From 189a2fe96931ef3ea0e187c8e9bfa589c2a0ae10 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <<EMAIL>>
Date: Mon, 25 Jul 2022 17:24:56 +0800
Subject: [PATCH 62/71] mtd: ubi: add support for UBI end-of-filesystem marker
 used by OpenWrt

Add support for UBI end-of-filesystem marker used by OpenWrt to allow
attaching a new UBI mtd partition just upgraded.

Signed-off-by: <PERSON><PERSON><PERSON> <<EMAIL>>
---
 drivers/mtd/ubi/attach.c | 25 ++++++++++++++++++++++---
 drivers/mtd/ubi/ubi.h    |  1 +
 2 files changed, 23 insertions(+), 3 deletions(-)

--- a/drivers/mtd/ubi/attach.c
+++ b/drivers/mtd/ubi/attach.c
@@ -803,6 +803,13 @@ out_unlock:
 	return err;
 }
 
+static bool ec_hdr_has_eof(struct ubi_ec_hdr *ech)
+{
+	return ech->padding1[0] == 'E' &&
+	       ech->padding1[1] == 'O' &&
+	       ech->padding1[2] == 'F';
+}
+
 /**
  * scan_peb - scan and process UBI headers of a PEB.
  * @ubi: UBI device description object
@@ -833,9 +840,21 @@ static int scan_peb(struct ubi_device *u
 		return 0;
 	}
 
-	err = ubi_io_read_ec_hdr(ubi, pnum, ech, 0);
-	if (err < 0)
-		return err;
+	if (!ai->eof_found) {
+		err = ubi_io_read_ec_hdr(ubi, pnum, ech, 0);
+		if (err < 0)
+			return err;
+
+		if (ec_hdr_has_eof(ech)) {
+			pr_notice("UBI: EOF marker found, PEBs from %d will be erased\n",
+				pnum);
+			ai->eof_found = true;
+		}
+	}
+
+	if (ai->eof_found)
+		err = UBI_IO_FF_BITFLIPS;
+
 	switch (err) {
 	case 0:
 		break;
--- a/drivers/mtd/ubi/ubi.h
+++ b/drivers/mtd/ubi/ubi.h
@@ -746,6 +746,7 @@ struct ubi_attach_info {
 	int mean_ec;
 	uint64_t ec_sum;
 	int ec_count;
+	bool eof_found;
 	struct kmem_cache *aeb_slab_cache;
 };
 
