From f6a4130959af1e6d13d616203e42ed3c894666ad Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <<EMAIL>>
Date: Mon, 25 Jul 2022 17:00:00 +0800
Subject: [PATCH 60/71] cmd: ubi: allow creating volume with all free spaces

Allow creating volume with all free spaces by giving a negative size value.

Signed-off-by: <PERSON><PERSON><PERSON> <<EMAIL>>
---
 cmd/ubi.c | 6 +++++-
 1 file changed, 5 insertions(+), 1 deletion(-)

--- a/cmd/ubi.c
+++ b/cmd/ubi.c
@@ -226,7 +226,11 @@ int ubi_create_vol(char *volume, int64_t
 
 	req.vol_id = vol_id;
 	req.alignment = 1;
-	req.bytes = size;
+
+	if (size < 0)
+		req.bytes = ubi->avail_pebs * ubi->leb_size;
+	else
+		req.bytes = size;
 
 	strcpy(req.name, volume);
 	req.name_len = strlen(volume);
