From 4c1803cc08b1618d935c1386f43f43a4e9c97697 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <<EMAIL>>
Date: Wed, 3 Mar 2021 10:51:43 +0800
Subject: [PATCH 54/71] board: mt7622: use new spi-nand driver

Enable new spi-nand driver support for mt7622_rfb_defconfig

Signed-off-by: <PERSON><PERSON><PERSON> <<EMAIL>>
---
 arch/arm/dts/mt7622-rfb.dts  |  7 +++++++
 arch/arm/dts/mt7622.dtsi     | 16 ++++++++++++++++
 configs/mt7622_rfb_defconfig |  5 +++++
 3 files changed, 28 insertions(+)

--- a/arch/arm/dts/mt7622-rfb.dts
+++ b/arch/arm/dts/mt7622-rfb.dts
@@ -196,6 +196,13 @@
 	};
 };
 
+&snand {
+	pinctrl-names = "default";
+	pinctrl-0 = <&snfi_pins>;
+	status = "okay";
+	quad-spi;
+};
+
 &uart0 {
 	status = "okay";
 };
--- a/arch/arm/dts/mt7622.dtsi
+++ b/arch/arm/dts/mt7622.dtsi
@@ -53,6 +53,22 @@
 		#size-cells = <0>;
 	};
 
+	snand: snand@1100d000 {
+		compatible = "mediatek,mt7622-snand";
+		reg = <0x1100d000 0x1000>,
+		      <0x1100e000 0x1000>;
+		reg-names = "nfi", "ecc";
+		clocks = <&pericfg CLK_PERI_NFI_PD>,
+			 <&pericfg CLK_PERI_SNFI_PD>,
+			 <&pericfg CLK_PERI_NFIECC_PD>;
+		clock-names = "nfi_clk", "pad_clk", "ecc_clk";
+		assigned-clocks = <&topckgen CLK_TOP_AXI_SEL>,
+				  <&topckgen CLK_TOP_NFI_INFRA_SEL>;
+		assigned-clock-parents = <&topckgen CLK_TOP_SYSPLL1_D2>,
+					 <&topckgen CLK_TOP_UNIVPLL2_D8>;
+		status = "disabled";
+	};
+
 	snor: snor@11014000 {
 		compatible = "mediatek,mtk-snor";
 		reg = <0x11014000 0x1000>;
--- a/configs/mt7622_rfb_defconfig
+++ b/configs/mt7622_rfb_defconfig
@@ -20,6 +20,7 @@ CONFIG_SYS_PROMPT="MT7622> "
 CONFIG_SYS_MAXARGS=8
 CONFIG_CMD_BOOTMENU=y
 CONFIG_CMD_MMC=y
+CONFIG_CMD_MTD=y
 CONFIG_CMD_PCI=y
 CONFIG_CMD_SF_TEST=y
 CONFIG_CMD_PING=y
@@ -37,6 +38,9 @@ CONFIG_CLK=y
 CONFIG_MMC_HS200_SUPPORT=y
 CONFIG_MMC_MTK=y
 CONFIG_MTD=y
+CONFIG_DM_MTD=y
+CONFIG_MTK_SPI_NAND=y
+CONFIG_MTK_SPI_NAND_MTD=y
 CONFIG_DM_SPI_FLASH=y
 CONFIG_SPI_FLASH_EON=y
 CONFIG_SPI_FLASH_GIGADEVICE=y
