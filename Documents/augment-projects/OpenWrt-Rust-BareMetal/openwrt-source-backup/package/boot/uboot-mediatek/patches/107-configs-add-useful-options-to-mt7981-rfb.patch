--- a/configs/mt7981_emmc_rfb_defconfig
+++ b/configs/mt7981_emmc_rfb_defconfig
@@ -8,37 +8,56 @@ CONFIG_NR_DRAM_BANKS=1
 CONFIG_ENV_SIZE=0x80000
 CONFIG_ENV_OFFSET=0x300000
 CONFIG_DEFAULT_DEVICE_TREE="mt7981-emmc-rfb"
+CONFIG_OF_LIBFDT_OVERLAY=y
 CONFIG_TARGET_MT7981=y
 CONFIG_SYS_LOAD_ADDR=0x46000000
 CONFIG_DEBUG_UART_BASE=0x11002000
 CONFIG_DEBUG_UART_CLOCK=********
 CONFIG_DEBUG_UART=y
 # CONFIG_EFI_LOADER is not set
-# CONFIG_AUTOBOOT is not set
+CONFIG_FIT=y
+CONFIG_BOOTSTD_FULL=y
+CONFIG_OF_SYSTEM_SETUP=y
 CONFIG_DEFAULT_FDT_FILE="mt7981-emmc-rfb"
 CONFIG_SYS_CBSIZE=512
 CONFIG_SYS_PBSIZE=1049
 CONFIG_LOGLEVEL=7
 CONFIG_LOG=y
+CONFIG_BOARD_LATE_INIT=y
 CONFIG_SYS_PROMPT="MT7981> "
+CONFIG_CMD_CPU=y
+CONFIG_CMD_LICENSE=y
 # CONFIG_BOOTM_NETBSD is not set
 # CONFIG_BOOTM_PLAN9 is not set
 # CONFIG_BOOTM_RTEMS is not set
 # CONFIG_BOOTM_VXWORKS is not set
-# CONFIG_CMD_ELF is not set
+CONFIG_CMD_BOOTMENU=y
+CONFIG_CMD_ASKENV=y
+CONFIG_CMD_ERASEENV=y
+CONFIG_CMD_ENV_FLAGS=y
+CONFIG_CMD_STRINGS=y
 # CONFIG_CMD_UNLZ4 is not set
 # CONFIG_CMD_UNZIP is not set
+CONFIG_CMD_DM=y
 CONFIG_CMD_GPIO=y
 CONFIG_CMD_GPT=y
 CONFIG_CMD_GPT_RENAME=y
 CONFIG_CMD_LSBLK=y
 CONFIG_CMD_MMC=y
-CONFIG_CMD_PART=y
 CONFIG_CMD_READ=y
-CONFIG_CMD_PING=y
+CONFIG_CMD_TFTPSRV=y
+CONFIG_CMD_RARP=y
+CONFIG_CMD_CDP=y
+CONFIG_CMD_SNTP=y
+CONFIG_CMD_LINK_LOCAL=y
+CONFIG_CMD_DNS=y
+CONFIG_CMD_CACHE=y
+CONFIG_CMD_PSTORE=y
+CONFIG_CMD_PSTORE_MEM_ADDR=0x42ff0000
+CONFIG_CMD_UUID=y
+CONFIG_CMD_HASH=y
 CONFIG_CMD_SMC=y
-CONFIG_CMD_FAT=y
-CONFIG_CMD_FS_GENERIC=y
+CONFIG_CMD_FS_UUID=y
 CONFIG_PARTITION_TYPE_GUID=y
 CONFIG_ENV_OVERWRITE=y
 CONFIG_ENV_IS_IN_MMC=y
@@ -46,7 +65,13 @@ CONFIG_ENV_VARS_UBOOT_RUNTIME_CONFIG=y
 CONFIG_NET_RANDOM_ETHADDR=y
 CONFIG_REGMAP=y
 CONFIG_SYSCON=y
+CONFIG_BUTTON=y
+CONFIG_BUTTON_GPIO=y
 CONFIG_CLK=y
+CONFIG_GPIO_HOG=y
+CONFIG_LED=y
+CONFIG_LED_BLINK=y
+CONFIG_LED_GPIO=y
 CONFIG_MMC_HS200_SUPPORT=y
 CONFIG_MMC_MTK=y
 CONFIG_PHY_FIXED=y
@@ -59,6 +84,7 @@ CONFIG_MTK_POWER_DOMAIN=y
 CONFIG_DM_REGULATOR=y
 CONFIG_DM_REGULATOR_FIXED=y
 CONFIG_DM_SERIAL=y
+CONFIG_SERIAL_RX_BUFFER=y
 CONFIG_MTK_SERIAL=y
 CONFIG_FAT_WRITE=y
 CONFIG_HEXDUMP=y
--- a/configs/mt7981_rfb_defconfig
+++ b/configs/mt7981_rfb_defconfig
@@ -6,38 +6,78 @@ CONFIG_TEXT_BASE=0x41e00000
 CONFIG_SYS_MALLOC_F_LEN=0x4000
 CONFIG_NR_DRAM_BANKS=1
 CONFIG_DEFAULT_DEVICE_TREE="mt7981-rfb"
+CONFIG_OF_LIBFDT_OVERLAY=y
 CONFIG_TARGET_MT7981=y
 CONFIG_SYS_LOAD_ADDR=0x46000000
 CONFIG_DEBUG_UART_BASE=0x11002000
 CONFIG_DEBUG_UART_CLOCK=********
 CONFIG_DEBUG_UART=y
-# CONFIG_AUTOBOOT is not set
+CONFIG_FIT=y
+CONFIG_SPI_BOOT=y
+CONFIG_OF_SYSTEM_SETUP=y
 CONFIG_DEFAULT_FDT_FILE="mt7981-rfb"
 CONFIG_SYS_CBSIZE=512
 CONFIG_SYS_PBSIZE=1049
 CONFIG_LOGLEVEL=7
 CONFIG_LOG=y
+CONFIG_BOARD_LATE_INIT=y
+CONFIG_HUSH_PARSER=y
 CONFIG_SYS_PROMPT="MT7981> "
+CONFIG_CMD_CPU=y
+CONFIG_CMD_LICENSE=y
 # CONFIG_BOOTM_NETBSD is not set
 # CONFIG_BOOTM_PLAN9 is not set
 # CONFIG_BOOTM_RTEMS is not set
 # CONFIG_BOOTM_VXWORKS is not set
-# CONFIG_CMD_ELF is not set
+CONFIG_CMD_BOOTMENU=y
+CONFIG_CMD_ASKENV=y
+CONFIG_CMD_ERASEENV=y
+CONFIG_CMD_ENV_FLAGS=y
+CONFIG_CMD_STRINGS=y
 # CONFIG_CMD_UNLZ4 is not set
 # CONFIG_CMD_UNZIP is not set
+CONFIG_CMD_DM=y
 CONFIG_CMD_GPIO=y
 CONFIG_CMD_MTD=y
+CONFIG_CMD_PCI=y
 CONFIG_CMD_SF_TEST=y
+CONFIG_CMD_TFTPSRV=y
+CONFIG_CMD_RARP=y
+CONFIG_CMD_CDP=y
+CONFIG_CMD_SNTP=y
+CONFIG_CMD_LINK_LOCAL=y
+CONFIG_CMD_DHCP=y
+CONFIG_CMD_DNS=y
 CONFIG_CMD_PING=y
+CONFIG_CMD_PXE=y
+CONFIG_CMD_CACHE=y
+CONFIG_CMD_PSTORE=y
+CONFIG_CMD_PSTORE_MEM_ADDR=0x42ff0000
+CONFIG_CMD_UUID=y
+CONFIG_CMD_HASH=y
 CONFIG_CMD_SMC=y
+CONFIG_CMD_FS_GENERIC=y
+CONFIG_CMD_FS_UUID=y
 CONFIG_CMD_UBI=y
 CONFIG_CMD_UBI_RENAME=y
 CONFIG_ENV_OVERWRITE=y
+CONFIG_ENV_IS_IN_UBI=y
+CONFIG_SYS_REDUNDAND_ENVIRONMENT=y
+CONFIG_ENV_UBI_PART="ubi"
+CONFIG_ENV_UBI_VOLUME="ubootenv"
+CONFIG_ENV_UBI_VOLUME_REDUND="ubootenv2"
+CONFIG_SYS_RELOC_GD_ENV_ADDR=y
 CONFIG_ENV_VARS_UBOOT_RUNTIME_CONFIG=y
 CONFIG_NET_RANDOM_ETHADDR=y
 CONFIG_REGMAP=y
 CONFIG_SYSCON=y
+CONFIG_BUTTON=y
+CONFIG_BUTTON_GPIO=y
 CONFIG_CLK=y
+CONFIG_GPIO_HOG=y
+CONFIG_LED=y
+CONFIG_LED_BLINK=y
+CONFIG_LED_GPIO=y
 # CONFIG_MMC is not set
 CONFIG_MTD=y
 CONFIG_DM_MTD=y
@@ -62,6 +102,7 @@ CONFIG_PINCTRL_MT7981=y
 CONFIG_POWER_DOMAIN=y
 CONFIG_MTK_POWER_DOMAIN=y
 CONFIG_DM_SERIAL=y
+CONFIG_SERIAL_RX_BUFFER=y
 CONFIG_MTK_SERIAL=y
 CONFIG_SPI=y
 CONFIG_DM_SPI=y
--- a/configs/mt7981_sd_rfb_defconfig
+++ b/configs/mt7981_sd_rfb_defconfig
@@ -8,37 +8,56 @@ CONFIG_NR_DRAM_BANKS=1
 CONFIG_ENV_SIZE=0x80000
 CONFIG_ENV_OFFSET=0x300000
 CONFIG_DEFAULT_DEVICE_TREE="mt7981-sd-rfb"
+CONFIG_OF_LIBFDT_OVERLAY=y
 CONFIG_TARGET_MT7981=y
 CONFIG_SYS_LOAD_ADDR=0x46000000
 CONFIG_DEBUG_UART_BASE=0x11002000
 CONFIG_DEBUG_UART_CLOCK=********
 CONFIG_DEBUG_UART=y
 # CONFIG_EFI_LOADER is not set
-# CONFIG_AUTOBOOT is not set
+CONFIG_FIT=y
+CONFIG_BOOTSTD_FULL=y
+CONFIG_OF_SYSTEM_SETUP=y
 CONFIG_DEFAULT_FDT_FILE="mt7981-sd-rfb"
 CONFIG_SYS_CBSIZE=512
 CONFIG_SYS_PBSIZE=1049
 CONFIG_LOGLEVEL=7
 CONFIG_LOG=y
+CONFIG_BOARD_LATE_INIT=y
 CONFIG_SYS_PROMPT="MT7981> "
+CONFIG_CMD_CPU=y
+CONFIG_CMD_LICENSE=y
 # CONFIG_BOOTM_NETBSD is not set
 # CONFIG_BOOTM_PLAN9 is not set
 # CONFIG_BOOTM_RTEMS is not set
 # CONFIG_BOOTM_VXWORKS is not set
-# CONFIG_CMD_ELF is not set
+CONFIG_CMD_BOOTMENU=y
+CONFIG_CMD_ASKENV=y
+CONFIG_CMD_ERASEENV=y
+CONFIG_CMD_ENV_FLAGS=y
+CONFIG_CMD_STRINGS=y
 # CONFIG_CMD_UNLZ4 is not set
 # CONFIG_CMD_UNZIP is not set
+CONFIG_CMD_DM=y
 CONFIG_CMD_GPIO=y
 CONFIG_CMD_GPT=y
 CONFIG_CMD_GPT_RENAME=y
 CONFIG_CMD_LSBLK=y
 CONFIG_CMD_MMC=y
-CONFIG_CMD_PART=y
 CONFIG_CMD_READ=y
-CONFIG_CMD_PING=y
+CONFIG_CMD_TFTPSRV=y
+CONFIG_CMD_RARP=y
+CONFIG_CMD_CDP=y
+CONFIG_CMD_SNTP=y
+CONFIG_CMD_LINK_LOCAL=y
+CONFIG_CMD_DNS=y
+CONFIG_CMD_CACHE=y
+CONFIG_CMD_PSTORE=y
+CONFIG_CMD_PSTORE_MEM_ADDR=0x42ff0000
+CONFIG_CMD_UUID=y
+CONFIG_CMD_HASH=y
 CONFIG_CMD_SMC=y
-CONFIG_CMD_FAT=y
-CONFIG_CMD_FS_GENERIC=y
+CONFIG_CMD_FS_UUID=y
 CONFIG_PARTITION_TYPE_GUID=y
 CONFIG_ENV_OVERWRITE=y
 CONFIG_ENV_IS_IN_MMC=y
@@ -46,7 +65,13 @@ CONFIG_ENV_VARS_UBOOT_RUNTIME_CONFIG=y
 CONFIG_NET_RANDOM_ETHADDR=y
 CONFIG_REGMAP=y
 CONFIG_SYSCON=y
+CONFIG_BUTTON=y
+CONFIG_BUTTON_GPIO=y
 CONFIG_CLK=y
+CONFIG_GPIO_HOG=y
+CONFIG_LED=y
+CONFIG_LED_BLINK=y
+CONFIG_LED_GPIO=y
 CONFIG_MMC_HS200_SUPPORT=y
 CONFIG_MMC_MTK=y
 CONFIG_PHY_FIXED=y
@@ -59,6 +84,7 @@ CONFIG_MTK_POWER_DOMAIN=y
 CONFIG_DM_REGULATOR=y
 CONFIG_DM_REGULATOR_FIXED=y
 CONFIG_DM_SERIAL=y
+CONFIG_SERIAL_RX_BUFFER=y
 CONFIG_MTK_SERIAL=y
 CONFIG_FAT_WRITE=y
 CONFIG_HEXDUMP=y
--- a/configs/mt7981_snfi_nand_rfb_defconfig
+++ b/configs/mt7981_snfi_nand_rfb_defconfig
@@ -6,37 +6,73 @@ CONFIG_TEXT_BASE=0x41e00000
 CONFIG_SYS_MALLOC_F_LEN=0x4000
 CONFIG_NR_DRAM_BANKS=1
 CONFIG_DEFAULT_DEVICE_TREE="mt7981-snfi-nand-rfb"
-CONFIG_SYS_PROMPT="MT7981> "
+CONFIG_OF_LIBFDT_OVERLAY=y
 CONFIG_TARGET_MT7981=y
+CONFIG_SYS_LOAD_ADDR=0x46000000
 CONFIG_DEBUG_UART_BASE=0x11002000
 CONFIG_DEBUG_UART_CLOCK=********
-CONFIG_SYS_LOAD_ADDR=0x46000000
 CONFIG_DEBUG_UART=y
-# CONFIG_AUTOBOOT is not set
+CONFIG_FIT=y
+CONFIG_BOOTSTD_FULL=y
+CONFIG_SPI_BOOT=y
+CONFIG_OF_SYSTEM_SETUP=y
 CONFIG_DEFAULT_FDT_FILE="mt7981-snfi-nand-rfb"
-CONFIG_LOGLEVEL=7
-CONFIG_LOG=y
 CONFIG_SYS_CBSIZE=512
 CONFIG_SYS_PBSIZE=1049
+CONFIG_LOGLEVEL=7
+CONFIG_LOG=y
+CONFIG_BOARD_LATE_INIT=y
+CONFIG_SYS_PROMPT="MT7981> "
+CONFIG_CMD_CPU=y
+CONFIG_CMD_LICENSE=y
 # CONFIG_BOOTM_NETBSD is not set
 # CONFIG_BOOTM_PLAN9 is not set
 # CONFIG_BOOTM_RTEMS is not set
 # CONFIG_BOOTM_VXWORKS is not set
-# CONFIG_CMD_ELF is not set
+CONFIG_CMD_BOOTMENU=y
+CONFIG_CMD_ASKENV=y
+CONFIG_CMD_ERASEENV=y
+CONFIG_CMD_ENV_FLAGS=y
+CONFIG_CMD_STRINGS=y
 # CONFIG_CMD_UNLZ4 is not set
 # CONFIG_CMD_UNZIP is not set
+CONFIG_CMD_DM=y
 CONFIG_CMD_GPIO=y
 CONFIG_CMD_MTD=y
-CONFIG_CMD_PING=y
+CONFIG_CMD_PCI=y
+CONFIG_CMD_TFTPSRV=y
+CONFIG_CMD_RARP=y
+CONFIG_CMD_CDP=y
+CONFIG_CMD_SNTP=y
+CONFIG_CMD_LINK_LOCAL=y
+CONFIG_CMD_DNS=y
+CONFIG_CMD_CACHE=y
+CONFIG_CMD_PSTORE=y
+CONFIG_CMD_PSTORE_MEM_ADDR=0x42ff0000
+CONFIG_CMD_UUID=y
+CONFIG_CMD_HASH=y
 CONFIG_CMD_SMC=y
+CONFIG_CMD_FS_UUID=y
 CONFIG_CMD_UBI=y
 CONFIG_CMD_UBI_RENAME=y
 CONFIG_ENV_OVERWRITE=y
+CONFIG_ENV_IS_IN_UBI=y
+CONFIG_SYS_REDUNDAND_ENVIRONMENT=y
+CONFIG_ENV_UBI_PART="ubi"
+CONFIG_ENV_UBI_VOLUME="ubootenv"
+CONFIG_ENV_UBI_VOLUME_REDUND="ubootenv2"
+CONFIG_SYS_RELOC_GD_ENV_ADDR=y
 CONFIG_ENV_VARS_UBOOT_RUNTIME_CONFIG=y
 CONFIG_NET_RANDOM_ETHADDR=y
 CONFIG_REGMAP=y
 CONFIG_SYSCON=y
+CONFIG_BUTTON=y
+CONFIG_BUTTON_GPIO=y
 CONFIG_CLK=y
+CONFIG_GPIO_HOG=y
+CONFIG_LED=y
+CONFIG_LED_BLINK=y
+CONFIG_LED_GPIO=y
 # CONFIG_MMC is not set
 CONFIG_MTD=y
 CONFIG_DM_MTD=y
@@ -50,6 +86,6 @@ CONFIG_PINCTRL_MT7981=y
 CONFIG_POWER_DOMAIN=y
 CONFIG_MTK_POWER_DOMAIN=y
 CONFIG_DM_SERIAL=y
+CONFIG_SERIAL_RX_BUFFER=y
 CONFIG_MTK_SERIAL=y
 CONFIG_HEXDUMP=y
-CONFIG_LMB_MAX_REGIONS=64
--- a/configs/mt7981_nor_rfb_defconfig
+++ b/configs/mt7981_nor_rfb_defconfig
@@ -5,37 +5,74 @@ CONFIG_ARCH_MEDIATEK=y
 CONFIG_TEXT_BASE=0x41e00000
 CONFIG_SYS_MALLOC_F_LEN=0x4000
 CONFIG_NR_DRAM_BANKS=1
+CONFIG_ENV_SIZE=0x4000
+CONFIG_ENV_OFFSET=0x0
 CONFIG_DEFAULT_DEVICE_TREE="mt7981-rfb"
-CONFIG_SYS_PROMPT="MT7981> "
+CONFIG_OF_LIBFDT_OVERLAY=y
 CONFIG_TARGET_MT7981=y
+CONFIG_SYS_LOAD_ADDR=0x46000000
 CONFIG_DEBUG_UART_BASE=0x11002000
 CONFIG_DEBUG_UART_CLOCK=********
-CONFIG_SYS_LOAD_ADDR=0x46000000
 CONFIG_DEBUG_UART=y
-# CONFIG_AUTOBOOT is not set
+CONFIG_FIT=y
+CONFIG_BOOTSTD_FULL=y
+CONFIG_SPI_BOOT=y
+CONFIG_OF_SYSTEM_SETUP=y
 CONFIG_DEFAULT_FDT_FILE="mt7981-rfb"
-CONFIG_LOGLEVEL=7
-CONFIG_LOG=y
 CONFIG_SYS_CBSIZE=512
 CONFIG_SYS_PBSIZE=1049
+CONFIG_LOGLEVEL=7
+CONFIG_LOG=y
+CONFIG_BOARD_LATE_INIT=y
+CONFIG_SYS_PROMPT="MT7981> "
+CONFIG_CMD_CPU=y
+CONFIG_CMD_LICENSE=y
 # CONFIG_BOOTM_NETBSD is not set
 # CONFIG_BOOTM_PLAN9 is not set
 # CONFIG_BOOTM_RTEMS is not set
 # CONFIG_BOOTM_VXWORKS is not set
-# CONFIG_CMD_ELF is not set
+CONFIG_CMD_BOOTMENU=y
+CONFIG_CMD_ASKENV=y
+CONFIG_CMD_ERASEENV=y
+CONFIG_CMD_ENV_FLAGS=y
+CONFIG_CMD_STRINGS=y
 # CONFIG_CMD_UNLZ4 is not set
 # CONFIG_CMD_UNZIP is not set
+CONFIG_CMD_DM=y
 CONFIG_CMD_GPIO=y
 CONFIG_CMD_MTD=y
+CONFIG_CMD_PCI=y
 CONFIG_CMD_SF_TEST=y
-CONFIG_CMD_PING=y
+CONFIG_CMD_TFTPSRV=y
+CONFIG_CMD_RARP=y
+CONFIG_CMD_CDP=y
+CONFIG_CMD_SNTP=y
+CONFIG_CMD_LINK_LOCAL=y
+CONFIG_CMD_DNS=y
+CONFIG_CMD_CACHE=y
+CONFIG_CMD_PSTORE=y
+CONFIG_CMD_PSTORE_MEM_ADDR=0x42ff0000
+CONFIG_CMD_UUID=y
+CONFIG_CMD_HASH=y
 CONFIG_CMD_SMC=y
+CONFIG_CMD_FS_UUID=y
 CONFIG_ENV_OVERWRITE=y
+CONFIG_ENV_IS_IN_MTD=y
+CONFIG_SYS_REDUNDAND_ENVIRONMENT=y
+CONFIG_ENV_MTD_NAME="u-boot-env"
+CONFIG_ENV_SIZE_REDUND=0x4000
+CONFIG_SYS_RELOC_GD_ENV_ADDR=y
 CONFIG_ENV_VARS_UBOOT_RUNTIME_CONFIG=y
 CONFIG_NET_RANDOM_ETHADDR=y
 CONFIG_REGMAP=y
 CONFIG_SYSCON=y
+CONFIG_BUTTON=y
+CONFIG_BUTTON_GPIO=y
 CONFIG_CLK=y
+CONFIG_GPIO_HOG=y
+CONFIG_LED=y
+CONFIG_LED_BLINK=y
+CONFIG_LED_GPIO=y
 # CONFIG_MMC is not set
 CONFIG_MTD=y
 CONFIG_DM_MTD=y
@@ -60,9 +97,9 @@ CONFIG_PINCTRL_MT7981=y
 CONFIG_POWER_DOMAIN=y
 CONFIG_MTK_POWER_DOMAIN=y
 CONFIG_DM_SERIAL=y
+CONFIG_SERIAL_RX_BUFFER=y
 CONFIG_MTK_SERIAL=y
 CONFIG_SPI=y
 CONFIG_DM_SPI=y
 CONFIG_MTK_SPIM=y
 CONFIG_HEXDUMP=y
-CONFIG_LMB_MAX_REGIONS=64
