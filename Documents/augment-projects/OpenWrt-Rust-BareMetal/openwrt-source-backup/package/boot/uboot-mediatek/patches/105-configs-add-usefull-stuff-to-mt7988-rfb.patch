--- a/configs/mt7988_sd_rfb_defconfig
+++ b/configs/mt7988_sd_rfb_defconfig
@@ -5,37 +5,76 @@ CONFIG_ARCH_MEDIATEK=y
 CONFIG_TEXT_BASE=0x41e00000
 CONFIG_SYS_MALLOC_F_LEN=0x4000
 CONFIG_NR_DRAM_BANKS=1
+CONFIG_ENV_SIZE=0x40000
+CONFIG_ENV_OFFSET=0x400000
 CONFIG_DEFAULT_DEVICE_TREE="mt7988-sd-rfb"
+CONFIG_OF_LIBFDT_OVERLAY=y
 CONFIG_TARGET_MT7988=y
 CONFIG_SYS_LOAD_ADDR=0x46000000
+CONFIG_PRE_CON_BUF_ADDR=0x4007EF00
 CONFIG_DEBUG_UART_BASE=0x11000000
 CONFIG_DEBUG_UART_CLOCK=********
+CONFIG_ENV_OFFSET_REDUND=0x440000
+CONFIG_PCI=y
 CONFIG_DEBUG_UART=y
 # CONFIG_EFI_LOADER is not set
+CONFIG_FIT=y
+CONFIG_BOOTSTD_FULL=y
+CONFIG_SD_BOOT=y
+CONFIG_SPI_BOOT=y
 # CONFIG_AUTOBOOT is not set
 CONFIG_DEFAULT_FDT_FILE="mt7988-sd-rfb"
 CONFIG_SYS_CBSIZE=512
 CONFIG_SYS_PBSIZE=1049
 CONFIG_LOGLEVEL=7
+CONFIG_PRE_CONSOLE_BUFFER=y
 CONFIG_LOG=y
+CONFIG_BOARD_LATE_INIT=y
 CONFIG_SYS_PROMPT="MT7988> "
+CONFIG_CMD_CPU=y
+CONFIG_CMD_LICENSE=y
 # CONFIG_BOOTM_NETBSD is not set
 # CONFIG_BOOTM_PLAN9 is not set
 # CONFIG_BOOTM_RTEMS is not set
 # CONFIG_BOOTM_VXWORKS is not set
-# CONFIG_CMD_ELF is not set
+CONFIG_CMD_BOOTMENU=y
+CONFIG_CMD_ASKENV=y
+CONFIG_CMD_ERASEENV=y
+CONFIG_CMD_ENV_FLAGS=y
+CONFIG_CMD_STRINGS=y
 CONFIG_CMD_CLK=y
 CONFIG_CMD_DM=y
 CONFIG_CMD_GPIO=y
 CONFIG_CMD_PWM=y
+CONFIG_CMD_GPT=y
 CONFIG_CMD_MMC=y
 CONFIG_CMD_MTD=y
-CONFIG_CMD_PING=y
+CONFIG_CMD_PCI=y
+CONFIG_CMD_USB=y
+CONFIG_CMD_TFTPSRV=y
+CONFIG_CMD_RARP=y
+CONFIG_CMD_CDP=y
+CONFIG_CMD_SNTP=y
+CONFIG_CMD_LINK_LOCAL=y
+CONFIG_CMD_DNS=y
+CONFIG_CMD_CACHE=y
+CONFIG_CMD_PSTORE=y
+CONFIG_CMD_PSTORE_MEM_ADDR=0x42ff0000
+CONFIG_CMD_UUID=y
+CONFIG_CMD_HASH=y
 CONFIG_CMD_SMC=y
-CONFIG_DOS_PARTITION=y
-CONFIG_EFI_PARTITION=y
+CONFIG_CMD_FS_UUID=y
+CONFIG_CMD_UBI=y
+CONFIG_CMD_UBI_RENAME=y
 CONFIG_PARTITION_TYPE_GUID=y
+CONFIG_OF_EMBED=y
+CONFIG_ENV_OVERWRITE=y
+CONFIG_ENV_IS_IN_MMC=y
+CONFIG_SYS_REDUNDAND_ENVIRONMENT=y
+CONFIG_SYS_RELOC_GD_ENV_ADDR=y
 CONFIG_ENV_VARS_UBOOT_RUNTIME_CONFIG=y
+CONFIG_VERSION_VARIABLE=y
+CONFIG_NETCONSOLE=y
 CONFIG_USE_IPADDR=y
 CONFIG_IPADDR="***********"
 CONFIG_USE_NETMASK=y
@@ -44,28 +83,43 @@ CONFIG_USE_SERVERIP=y
 CONFIG_SERVERIP="***********"
 CONFIG_PROT_TCP=y
 CONFIG_NET_RANDOM_ETHADDR=y
-CONFIG_REGMAP=y
-CONFIG_SYSCON=y
+CONFIG_BUTTON=y
+CONFIG_BUTTON_GPIO=y
 CONFIG_CLK=y
+CONFIG_GPIO_HOG=y
+CONFIG_LED=y
+CONFIG_LED_BLINK=y
+CONFIG_LED_GPIO=y
 CONFIG_MMC_HS200_SUPPORT=y
 CONFIG_MMC_MTK=y
 CONFIG_MTD=y
 CONFIG_DM_MTD=y
 CONFIG_MTD_SPI_NAND=y
+CONFIG_MTD_UBI_FASTMAP=y
 CONFIG_PHY_FIXED=y
 CONFIG_MEDIATEK_ETH=y
+CONFIG_PCIE_MEDIATEK=y
+CONFIG_PHY=y
+CONFIG_PHY_MTK_TPHY=y
 CONFIG_PINCTRL=y
 CONFIG_PINCONF=y
 CONFIG_PINCTRL_MT7988=y
 CONFIG_POWER_DOMAIN=y
 CONFIG_MTK_POWER_DOMAIN=y
+CONFIG_DM_REGULATOR=y
+CONFIG_DM_REGULATOR_FIXED=y
+CONFIG_DM_REGULATOR_GPIO=y
 CONFIG_DM_PWM=y
 CONFIG_PWM_MTK=y
 CONFIG_RAM=y
 CONFIG_DM_SERIAL=y
+CONFIG_SERIAL_RX_BUFFER=y
 CONFIG_MTK_SERIAL=y
 CONFIG_SPI=y
 CONFIG_DM_SPI=y
 CONFIG_MTK_SPIM=y
-CONFIG_LZO=y
+CONFIG_USB=y
+CONFIG_USB_XHCI_HCD=y
+CONFIG_USB_XHCI_MTK=y
+CONFIG_ZSTD=y
 CONFIG_HEXDUMP=y
--- a/configs/mt7988_rfb_defconfig
+++ b/configs/mt7988_rfb_defconfig
@@ -6,36 +6,76 @@ CONFIG_TEXT_BASE=0x41e00000
 CONFIG_SYS_MALLOC_F_LEN=0x4000
 CONFIG_NR_DRAM_BANKS=1
 CONFIG_DEFAULT_DEVICE_TREE="mt7988-rfb"
+CONFIG_OF_LIBFDT_OVERLAY=y
 CONFIG_TARGET_MT7988=y
 CONFIG_SYS_LOAD_ADDR=0x44000000
+CONFIG_PRE_CON_BUF_ADDR=0x4007EF00
 CONFIG_DEBUG_UART_BASE=0x11000000
 CONFIG_DEBUG_UART_CLOCK=********
+CONFIG_PCI=y
 CONFIG_DEBUG_UART=y
 # CONFIG_EFI_LOADER is not set
-# CONFIG_AUTOBOOT is not set
+CONFIG_FIT=y
+CONFIG_BOOTSTD_FULL=y
+CONFIG_SD_BOOT=y
+CONFIG_SPI_BOOT=y
+CONFIG_OF_SYSTEM_SETUP=y
 CONFIG_DEFAULT_FDT_FILE="mt7988-rfb"
 CONFIG_SYS_CBSIZE=512
 CONFIG_SYS_PBSIZE=1049
 CONFIG_LOGLEVEL=7
+CONFIG_PRE_CONSOLE_BUFFER=y
 CONFIG_LOG=y
+CONFIG_BOARD_LATE_INIT=y
 CONFIG_SYS_PROMPT="MT7988> "
+CONFIG_CMD_CPU=y
+CONFIG_CMD_LICENSE=y
 # CONFIG_BOOTM_NETBSD is not set
 # CONFIG_BOOTM_PLAN9 is not set
 # CONFIG_BOOTM_RTEMS is not set
 # CONFIG_BOOTM_VXWORKS is not set
-# CONFIG_CMD_ELF is not set
+CONFIG_CMD_BOOTMENU=y
+CONFIG_CMD_ASKENV=y
+CONFIG_CMD_ERASEENV=y
+CONFIG_CMD_ENV_FLAGS=y
+CONFIG_CMD_STRINGS=y
 CONFIG_CMD_CLK=y
 CONFIG_CMD_DM=y
 CONFIG_CMD_GPIO=y
 CONFIG_CMD_PWM=y
+CONFIG_CMD_GPT=y
 CONFIG_CMD_MMC=y
 CONFIG_CMD_MTD=y
-CONFIG_CMD_PING=y
+CONFIG_CMD_PCI=y
+CONFIG_CMD_SF_TEST=y
+CONFIG_CMD_USB=y
+CONFIG_CMD_TFTPSRV=y
+CONFIG_CMD_RARP=y
+CONFIG_CMD_CDP=y
+CONFIG_CMD_SNTP=y
+CONFIG_CMD_LINK_LOCAL=y
+CONFIG_CMD_DNS=y
+CONFIG_CMD_CACHE=y
+CONFIG_CMD_PSTORE=y
+CONFIG_CMD_PSTORE_MEM_ADDR=0x42ff0000
+CONFIG_CMD_UUID=y
+CONFIG_CMD_HASH=y
 CONFIG_CMD_SMC=y
-CONFIG_DOS_PARTITION=y
-CONFIG_EFI_PARTITION=y
+CONFIG_CMD_FS_UUID=y
+CONFIG_CMD_UBI=y
+CONFIG_CMD_UBI_RENAME=y
 CONFIG_PARTITION_TYPE_GUID=y
+CONFIG_OF_EMBED=y
+CONFIG_ENV_OVERWRITE=y
+CONFIG_ENV_IS_IN_UBI=y
+CONFIG_SYS_REDUNDAND_ENVIRONMENT=y
+CONFIG_ENV_UBI_PART="ubi"
+CONFIG_ENV_UBI_VOLUME="ubootenv"
+CONFIG_ENV_UBI_VOLUME_REDUND="ubootenv2"
+CONFIG_SYS_RELOC_GD_ENV_ADDR=y
 CONFIG_ENV_VARS_UBOOT_RUNTIME_CONFIG=y
+CONFIG_VERSION_VARIABLE=y
+CONFIG_NETCONSOLE=y
 CONFIG_USE_IPADDR=y
 CONFIG_IPADDR="***********"
 CONFIG_USE_NETMASK=y
@@ -44,9 +84,13 @@ CONFIG_USE_SERVERIP=y
 CONFIG_SERVERIP="***********"
 CONFIG_PROT_TCP=y
 CONFIG_NET_RANDOM_ETHADDR=y
-CONFIG_REGMAP=y
-CONFIG_SYSCON=y
+CONFIG_BUTTON=y
+CONFIG_BUTTON_GPIO=y
 CONFIG_CLK=y
+CONFIG_GPIO_HOG=y
+CONFIG_LED=y
+CONFIG_LED_BLINK=y
+CONFIG_LED_GPIO=y
 CONFIG_MMC_HS200_SUPPORT=y
 CONFIG_MMC_MTK=y
 CONFIG_MTD=y
@@ -64,20 +108,31 @@ CONFIG_SPI_FLASH_WINBOND=y
 CONFIG_SPI_FLASH_XMC=y
 CONFIG_SPI_FLASH_XTX=y
 CONFIG_SPI_FLASH_MTD=y
+CONFIG_MTD_UBI_FASTMAP=y
 CONFIG_PHY_FIXED=y
 CONFIG_MEDIATEK_ETH=y
+CONFIG_PCIE_MEDIATEK=y
+CONFIG_PHY=y
+CONFIG_PHY_MTK_TPHY=y
 CONFIG_PINCTRL=y
 CONFIG_PINCONF=y
 CONFIG_PINCTRL_MT7988=y
 CONFIG_POWER_DOMAIN=y
 CONFIG_MTK_POWER_DOMAIN=y
+CONFIG_DM_REGULATOR=y
+CONFIG_DM_REGULATOR_FIXED=y
+CONFIG_DM_REGULATOR_GPIO=y
 CONFIG_DM_PWM=y
 CONFIG_PWM_MTK=y
 CONFIG_RAM=y
 CONFIG_DM_SERIAL=y
+CONFIG_SERIAL_RX_BUFFER=y
 CONFIG_MTK_SERIAL=y
 CONFIG_SPI=y
 CONFIG_DM_SPI=y
 CONFIG_MTK_SPIM=y
-CONFIG_LZO=y
+CONFIG_USB=y
+CONFIG_USB_XHCI_HCD=y
+CONFIG_USB_XHCI_MTK=y
+CONFIG_ZSTD=y
 CONFIG_HEXDUMP=y
--- a/arch/arm/dts/mt7988-rfb.dts
+++ b/arch/arm/dts/mt7988-rfb.dts
@@ -195,6 +195,23 @@
 		spi-max-frequency = <52000000>;
 		spi-rx-bus-width = <4>;
 		spi-tx-bus-width = <4>;
+
+		partitions {
+			compatible = "fixed-partitions";
+			#address-cells = <1>;
+			#size-cells = <1>;
+
+			partition@0 {
+				label = "bl2";
+				reg = <0x0 0x200000>;
+			};
+
+			partition@200000 {
+				label = "ubi";
+				reg = <0x200000 0x7e00000>;
+				compatible = "linux,ubi";
+			};
+		};
 	};
 };
 
