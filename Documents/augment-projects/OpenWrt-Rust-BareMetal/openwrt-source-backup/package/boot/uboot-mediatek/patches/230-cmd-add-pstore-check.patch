--- a/cmd/pstore.c
+++ b/cmd/pstore.c
@@ -208,6 +208,58 @@ static int pstore_set(struct cmd_tbl *cm
 }
 
 /**
+ * pstore_check() - Check for pstore records
+ * @cmdtp: Command data struct pointer
+ * @flag: Command flag
+ * @argc: Command-line argument count
+ * @argv: Array of command-line arguments
+ *
+ * Return: 0 if there are records in pstore, 1 otherwise
+ */
+static int pstore_check(struct cmd_tbl *cmdtp, int flag,  int argc,
+			char * const argv[])
+{
+	phys_addr_t ptr;
+	char *buffer;
+	u32 size;
+	int header_len = 0;
+	bool compressed;
+
+	if (pstore_length == 0) {
+		printf("Please set PStore configuration\n");
+		return CMD_RET_USAGE;
+	}
+
+	if (buffer_size == 0)
+		pstore_init_buffer_size();
+
+	buffer = malloc_cache_aligned(buffer_size);
+
+	ptr = pstore_addr;
+	phys_addr_t ptr_end = ptr + pstore_length - pstore_pmsg_size
+			- pstore_ftrace_size - pstore_console_size;
+
+	while (ptr < ptr_end) {
+		size = pstore_get_buffer(PERSISTENT_RAM_SIG, ptr,
+					 pstore_record_size, buffer);
+		ptr += pstore_record_size;
+
+		if (size == 0)
+			continue;
+
+		header_len = pstore_read_kmsg_hdr(buffer, &compressed);
+		if (header_len == 0)
+			continue;
+
+		free(buffer);
+		return 0;
+	}
+
+	free(buffer);
+	return 1;
+}
+
+/**
  * pstore_print_buffer() - Print buffer
  * @type: buffer type
  * @buffer: buffer to print
@@ -459,6 +511,7 @@ static int pstore_save(struct cmd_tbl *c
 
 static struct cmd_tbl cmd_pstore_sub[] = {
 	U_BOOT_CMD_MKENT(set, 8, 0, pstore_set, "", ""),
+	U_BOOT_CMD_MKENT(check, 1, 0, pstore_check, "", ""),
 	U_BOOT_CMD_MKENT(display, 3, 0, pstore_display, "", ""),
 	U_BOOT_CMD_MKENT(save, 4, 0, pstore_save, "", ""),
 };
@@ -566,6 +619,8 @@ U_BOOT_CMD(pstore, 10, 0, do_pstore,
 	   "  'pmsg-size' is the size of the user space logs record.\n"
 	   "  'ecc-size' enables/disables ECC support and specifies ECC buffer size in\n"
 	   "  bytes (0 disables it, 1 is a special value, means 16 bytes ECC).\n"
+	   "pstore check\n"
+	   "- Returns true if there are records in pstore.\n"
 	   "pstore display [record-type] [nb]\n"
 	   "- Display existing records in pstore reserved memory. A 'record-type' can\n"
 	   "  be given to only display records of this kind. 'record-type' can be one\n"
