--- /dev/null
+++ b/arch/arm/dts/openwrt-one.dts
@@ -0,0 +1,203 @@
+// SPDX-License-Identifier: GPL-2.0
+/*
+ * Copyright (c) 2024 <PERSON> <<EMAIL>>
+ */
+
+/dts-v1/;
+#include "mt7981.dtsi"
+#include <dt-bindings/gpio/gpio.h>
+#include <dt-bindings/input/input.h>
+
+/ {
+	#address-cells = <1>;
+	#size-cells = <1>;
+	model = "OpenWrt One";
+	compatible = "openwrt,one", "mediatek,mt7981";
+	chosen {
+		stdout-path = &uart0;
+		tick-timer = &timer0;
+	};
+
+	memory@******** {
+		device_type = "memory";
+		reg = <0x******** 0x10000000>;
+	};
+
+	keys {
+		compatible = "gpio-keys";
+
+		user {
+			label = "front";
+			gpios = <&pio 0 GPIO_ACTIVE_LOW>;
+			linux,code = <BTN_0>;
+		};
+
+		reset {
+			label = "back";
+			gpios = <&pio 1 GPIO_ACTIVE_LOW>;
+			linux,code = <BTN_1>;
+		};
+	};
+
+	leds {
+		compatible = "gpio-leds";
+
+		red {
+			label = "red";
+			gpios = <&pio 9 GPIO_ACTIVE_HIGH>;
+		};
+
+		white {
+			label = "white";
+			gpios = <&pio 13 GPIO_ACTIVE_HIGH>;
+		};
+
+		green {
+			label = "green";
+			gpios = <&pio 15 GPIO_ACTIVE_HIGH>;
+		};
+	};
+};
+
+&uart0 {
+	status = "okay";
+};
+
+&eth {
+	status = "okay";
+	mediatek,gmac-id = <1>;
+	phy-mode = "gmii";
+	phy-handle = <&phy0>;
+
+	phy0: eth-phy@0 {
+		compatible = "ethernet-phy-ieee802.3-c22";
+		reg = <0>;
+	};
+};
+
+&pio {
+	spi_flash_pins: spi0-pins-func-1 {
+		mux {
+			function = "flash";
+			groups = "spi0", "spi0_wp_hold";
+		};
+
+		conf-pu {
+			pins = "SPI0_CS", "SPI0_HOLD", "SPI0_WP";
+			drive-strength = <MTK_DRIVE_8mA>;
+			bias-pull-up = <MTK_PUPD_SET_R1R0_11>;
+		};
+
+		conf-pd {
+			pins = "SPI0_CLK", "SPI0_MOSI", "SPI0_MISO";
+			drive-strength = <MTK_DRIVE_8mA>;
+			bias-pull-down = <MTK_PUPD_SET_R1R0_11>;
+		};
+	};
+
+	spi2_flash_pins: spi2-spi2-pins {
+		mux {
+			function = "spi";
+			groups = "spi2", "spi2_wp_hold";
+		};
+
+		conf-pu {
+			pins = "SPI2_CS", "SPI2_HOLD", "SPI2_WP";
+			drive-strength = <MTK_DRIVE_8mA>;
+			bias-pull-down = <MTK_PUPD_SET_R1R0_00>;
+		};
+
+		conf-pd {
+			pins = "SPI2_CLK", "SPI2_MOSI", "SPI2_MISO";
+			drive-strength = <MTK_DRIVE_8mA>;
+			bias-pull-down = <MTK_PUPD_SET_R1R0_00>;
+		};
+	};
+};
+
+&spi0 {
+	#address-cells = <1>;
+	#size-cells = <0>;
+	pinctrl-names = "default";
+	pinctrl-0 = <&spi_flash_pins>;
+	status = "okay";
+	must_tx;
+	enhance_timing;
+	dma_ext;
+	ipm_design;
+	support_quad;
+	tick_dly = <2>;
+	sample_sel = <0>;
+
+	spi_nand@0 {
+		compatible = "spi-nand";
+		reg = <0>;
+		spi-max-frequency = <52000000>;
+
+		partitions {
+			compatible = "fixed-partitions";
+			#address-cells = <1>;
+			#size-cells = <1>;
+
+			partition@0 {
+				label = "bl2";
+				reg = <0x0 0x100000>;
+			};
+
+			partition@200000 {
+				label = "ubi";
+				reg = <0x100000 0xff00000>;
+			};
+		};
+	};
+};
+
+&spi2 {
+	#address-cells = <1>;
+	#size-cells = <0>;
+	pinctrl-names = "default";
+	pinctrl-0 = <&spi2_flash_pins>;
+	status = "okay";
+	must_tx;
+	enhance_timing;
+	dma_ext;
+	ipm_design;
+	tick_dly = <2>;
+	sample_sel = <0>;
+
+	spi_nor@0 {
+		compatible = "jedec,spi-nor";
+		reg = <0>;
+		spi-max-frequency = <5000000>;
+
+		partitions {
+			compatible = "fixed-partitions";
+			#address-cells = <1>;
+			#size-cells = <1>;
+
+			partition@00000 {
+				label = "bl2-nor";
+				reg = <0x00000 0x0040000>;
+			};
+
+			partition@40000 {
+				label = "factory";
+				reg = <0x40000 0x00C0000>;
+			};
+
+			partition@100000 {
+				label = "fip-nor";
+				reg = <0x100000 0x0080000>;
+			};
+
+			partition@180000 {
+				label = "recovery";
+				reg = <0x180000 0xc80000>;
+			};
+		};
+	};
+};
+
+&watchdog {
+	status = "disabled";
+};
--- /dev/null
+++ b/configs/mt7981_openwrt-one-nor_defconfig
@@ -0,0 +1,125 @@
+CONFIG_ARM=y
+CONFIG_SYS_HAS_NONCACHED_MEMORY=y
+CONFIG_POSITION_INDEPENDENT=y
+CONFIG_ARCH_MEDIATEK=y
+CONFIG_TEXT_BASE=0x41e00000
+CONFIG_SYS_MALLOC_F_LEN=0x4000
+CONFIG_NR_DRAM_BANKS=1
+CONFIG_ENV_SIZE=0x8000
+CONFIG_DEFAULT_DEVICE_TREE="openwrt-one"
+CONFIG_OF_LIBFDT_OVERLAY=y
+CONFIG_TARGET_MT7981=y
+CONFIG_RESET_BUTTON_LABEL="back"
+CONFIG_SYS_LOAD_ADDR=0x46000000
+CONFIG_DEBUG_UART_BASE=0x11002000
+CONFIG_DEBUG_UART_CLOCK=********
+CONFIG_DEBUG_UART=y
+CONFIG_FIT=y
+CONFIG_SPI_BOOT=y
+CONFIG_AUTOBOOT_MENU_SHOW=y
+CONFIG_USE_PREBOOT=y
+CONFIG_DEFAULT_FDT_FILE="openwrt-one"
+CONFIG_SYS_CBSIZE=512
+CONFIG_SYS_PBSIZE=1049
+CONFIG_LOGLEVEL=7
+CONFIG_LOG=y
+CONFIG_BOARD_LATE_INIT=y
+CONFIG_HUSH_PARSER=y
+CONFIG_SYS_PROMPT="OpenWrt One> "
+CONFIG_SYS_MAXARGS=16
+CONFIG_CMD_CPU=y
+CONFIG_CMD_LICENSE=y
+# CONFIG_BOOTM_NETBSD is not set
+# CONFIG_BOOTM_PLAN9 is not set
+# CONFIG_BOOTM_RTEMS is not set
+# CONFIG_BOOTM_VXWORKS is not set
+# CONFIG_CMD_BOOTEFI_BOOTMGR is not set
+CONFIG_CMD_BOOTMENU=y
+CONFIG_CMD_ASKENV=y
+CONFIG_CMD_ERASEENV=y
+CONFIG_CMD_ENV_FLAGS=y
+CONFIG_CMD_STRINGS=y
+# CONFIG_CMD_UNLZ4 is not set
+# CONFIG_CMD_UNZIP is not set
+CONFIG_CMD_DM=y
+CONFIG_CMD_GPIO=y
+CONFIG_CMD_PWM=y
+CONFIG_CMD_MTD=y
+CONFIG_CMD_PCI=y
+CONFIG_CMD_SF_TEST=y
+CONFIG_CMD_USB=y
+CONFIG_CMD_TFTPSRV=y
+CONFIG_CMD_RARP=y
+CONFIG_CMD_CDP=y
+CONFIG_CMD_SNTP=y
+CONFIG_CMD_LINK_LOCAL=y
+CONFIG_CMD_DHCP=y
+CONFIG_CMD_DNS=y
+CONFIG_CMD_PING=y
+CONFIG_CMD_PXE=y
+CONFIG_CMD_CACHE=y
+CONFIG_CMD_PSTORE=y
+CONFIG_CMD_PSTORE_MEM_ADDR=0x42ff0000
+CONFIG_CMD_UUID=y
+CONFIG_CMD_HASH=y
+CONFIG_CMD_SMC=y
+CONFIG_CMD_FAT=y
+CONFIG_CMD_FS_GENERIC=y
+CONFIG_CMD_FS_UUID=y
+CONFIG_CMD_UBI=y
+CONFIG_CMD_UBI_RENAME=y
+CONFIG_ENV_OVERWRITE=y
+CONFIG_SYS_REDUNDAND_ENVIRONMENT=y
+CONFIG_SYS_RELOC_GD_ENV_ADDR=y
+CONFIG_USE_DEFAULT_ENV_FILE=y
+CONFIG_DEFAULT_ENV_FILE="defenvs/openwrt-one-nor_env"
+CONFIG_ENV_VARS_UBOOT_RUNTIME_CONFIG=y
+CONFIG_VERSION_VARIABLE=y
+CONFIG_NET_RANDOM_ETHADDR=y
+CONFIG_BUTTON=y
+CONFIG_BUTTON_GPIO=y
+CONFIG_CLK=y
+CONFIG_GPIO_HOG=y
+CONFIG_LED=y
+CONFIG_LED_BLINK=y
+CONFIG_LED_GPIO=y
+# CONFIG_MMC is not set
+CONFIG_MTD=y
+CONFIG_DM_MTD=y
+CONFIG_MTD_SPI_NAND=y
+CONFIG_DM_SPI_FLASH=y
+CONFIG_SPI_FLASH_SFDP_SUPPORT=y
+CONFIG_SPI_FLASH_EON=y
+CONFIG_SPI_FLASH_GIGADEVICE=y
+CONFIG_SPI_FLASH_ISSI=y
+CONFIG_SPI_FLASH_MACRONIX=y
+CONFIG_SPI_FLASH_SPANSION=y
+CONFIG_SPI_FLASH_STMICRO=y
+CONFIG_SPI_FLASH_WINBOND=y
+CONFIG_SPI_FLASH_XMC=y
+CONFIG_SPI_FLASH_XTX=y
+CONFIG_SPI_FLASH_MTD=y
+CONFIG_UBI_SILENCE_MSG=y
+CONFIG_PHY_FIXED=y
+CONFIG_MEDIATEK_ETH=y
+CONFIG_PHY=y
+CONFIG_PHY_MTK_TPHY=y
+CONFIG_PINCTRL=y
+CONFIG_PINCONF=y
+CONFIG_PINCTRL_MT7981=y
+CONFIG_POWER_DOMAIN=y
+CONFIG_MTK_POWER_DOMAIN=y
+CONFIG_DM_PWM=y
+CONFIG_PWM_MTK=y
+CONFIG_DM_SERIAL=y
+CONFIG_SERIAL_RX_BUFFER=y
+CONFIG_MTK_SERIAL=y
+CONFIG_SPI=y
+CONFIG_DM_SPI=y
+CONFIG_MTK_SPIM=y
+CONFIG_USB=y
+CONFIG_USB_XHCI_HCD=y
+CONFIG_USB_XHCI_MTK=y
+CONFIG_USB_STORAGE=y
+CONFIG_UBIFS_SILENCE_MSG=y
+CONFIG_HEXDUMP=y
--- /dev/null
+++ b/configs/mt7981_openwrt-one-spi-nand_defconfig
@@ -0,0 +1,126 @@
+CONFIG_ARM=y
+CONFIG_SYS_HAS_NONCACHED_MEMORY=y
+CONFIG_POSITION_INDEPENDENT=y
+CONFIG_ARCH_MEDIATEK=y
+CONFIG_TEXT_BASE=0x41e00000
+CONFIG_SYS_MALLOC_F_LEN=0x4000
+CONFIG_NR_DRAM_BANKS=1
+CONFIG_DEFAULT_DEVICE_TREE="openwrt-one"
+CONFIG_OF_LIBFDT_OVERLAY=y
+CONFIG_TARGET_MT7981=y
+CONFIG_RESET_BUTTON_LABEL="back"
+CONFIG_SYS_LOAD_ADDR=0x46000000
+CONFIG_DEBUG_UART_BASE=0x11002000
+CONFIG_DEBUG_UART_CLOCK=********
+CONFIG_DEBUG_UART=y
+CONFIG_FIT=y
+CONFIG_SPI_BOOT=y
+CONFIG_AUTOBOOT_MENU_SHOW=y
+CONFIG_USE_PREBOOT=y
+CONFIG_DEFAULT_FDT_FILE="openwrt-one"
+CONFIG_SYS_CBSIZE=512
+CONFIG_SYS_PBSIZE=1049
+CONFIG_LOGLEVEL=7
+CONFIG_LOG=y
+CONFIG_BOARD_LATE_INIT=y
+CONFIG_HUSH_PARSER=y
+CONFIG_SYS_PROMPT="OpenWrt One> "
+CONFIG_SYS_MAXARGS=16
+CONFIG_CMD_CPU=y
+CONFIG_CMD_LICENSE=y
+# CONFIG_BOOTM_NETBSD is not set
+# CONFIG_BOOTM_PLAN9 is not set
+# CONFIG_BOOTM_RTEMS is not set
+# CONFIG_BOOTM_VXWORKS is not set
+# CONFIG_CMD_BOOTEFI_BOOTMGR is not set
+CONFIG_CMD_BOOTMENU=y
+CONFIG_CMD_ASKENV=y
+CONFIG_CMD_ERASEENV=y
+CONFIG_CMD_ENV_FLAGS=y
+CONFIG_CMD_STRINGS=y
+# CONFIG_CMD_UNLZ4 is not set
+# CONFIG_CMD_UNZIP is not set
+CONFIG_CMD_DM=y
+CONFIG_CMD_GPIO=y
+CONFIG_CMD_PWM=y
+CONFIG_CMD_MTD=y
+CONFIG_CMD_PCI=y
+CONFIG_CMD_SF_TEST=y
+CONFIG_CMD_USB=y
+CONFIG_CMD_TFTPSRV=y
+CONFIG_CMD_RARP=y
+CONFIG_CMD_CDP=y
+CONFIG_CMD_SNTP=y
+CONFIG_CMD_LINK_LOCAL=y
+CONFIG_CMD_DHCP=y
+CONFIG_CMD_DNS=y
+CONFIG_CMD_PING=y
+CONFIG_CMD_PXE=y
+CONFIG_CMD_CACHE=y
+CONFIG_CMD_PSTORE=y
+CONFIG_CMD_PSTORE_MEM_ADDR=0x42ff0000
+CONFIG_CMD_UUID=y
+CONFIG_CMD_HASH=y
+CONFIG_CMD_SMC=y
+CONFIG_CMD_FAT=y
+CONFIG_CMD_FS_GENERIC=y
+CONFIG_CMD_FS_UUID=y
+CONFIG_CMD_UBI=y
+CONFIG_CMD_UBI_RENAME=y
+CONFIG_ENV_OVERWRITE=y
+CONFIG_ENV_IS_IN_UBI=y
+CONFIG_SYS_REDUNDAND_ENVIRONMENT=y
+CONFIG_ENV_UBI_PART="ubi"
+CONFIG_ENV_UBI_VOLUME="ubootenv"
+CONFIG_ENV_UBI_VOLUME_REDUND="ubootenv2"
+CONFIG_SYS_RELOC_GD_ENV_ADDR=y
+CONFIG_USE_DEFAULT_ENV_FILE=y
+CONFIG_DEFAULT_ENV_FILE="defenvs/openwrt-one-spi-nand_env"
+CONFIG_ENV_VARS_UBOOT_RUNTIME_CONFIG=y
+CONFIG_VERSION_VARIABLE=y
+CONFIG_NET_RANDOM_ETHADDR=y
+CONFIG_BUTTON=y
+CONFIG_BUTTON_GPIO=y
+CONFIG_CLK=y
+CONFIG_GPIO_HOG=y
+CONFIG_LED=y
+CONFIG_LED_BLINK=y
+CONFIG_LED_GPIO=y
+# CONFIG_MMC is not set
+CONFIG_MTD=y
+CONFIG_DM_MTD=y
+CONFIG_MTD_SPI_NAND=y
+CONFIG_DM_SPI_FLASH=y
+CONFIG_SPI_FLASH_SFDP_SUPPORT=y
+CONFIG_SPI_FLASH_EON=y
+CONFIG_SPI_FLASH_GIGADEVICE=y
+CONFIG_SPI_FLASH_ISSI=y
+CONFIG_SPI_FLASH_MACRONIX=y
+CONFIG_SPI_FLASH_SPANSION=y
+CONFIG_SPI_FLASH_STMICRO=y
+CONFIG_SPI_FLASH_WINBOND=y
+CONFIG_SPI_FLASH_XMC=y
+CONFIG_SPI_FLASH_XTX=y
+CONFIG_SPI_FLASH_MTD=y
+CONFIG_PHY_FIXED=y
+CONFIG_MEDIATEK_ETH=y
+CONFIG_PHY=y
+CONFIG_PHY_MTK_TPHY=y
+CONFIG_PINCTRL=y
+CONFIG_PINCONF=y
+CONFIG_PINCTRL_MT7981=y
+CONFIG_POWER_DOMAIN=y
+CONFIG_MTK_POWER_DOMAIN=y
+CONFIG_DM_PWM=y
+CONFIG_PWM_MTK=y
+CONFIG_DM_SERIAL=y
+CONFIG_SERIAL_RX_BUFFER=y
+CONFIG_MTK_SERIAL=y
+CONFIG_SPI=y
+CONFIG_DM_SPI=y
+CONFIG_MTK_SPIM=y
+CONFIG_USB=y
+CONFIG_USB_XHCI_HCD=y
+CONFIG_USB_XHCI_MTK=y
+CONFIG_USB_STORAGE=y
+CONFIG_HEXDUMP=y
--- /dev/null
+++ b/defenvs/openwrt-one-nor_env
@@ -0,0 +1,48 @@
+ethaddr_factory=mtd read factory 0x46000000 0x0 0x20000 && env readmem -b ethaddr 0x4600002a 0x6 ; setenv ethaddr_factory
+bl2_mtd_write=mtd erase bl2-nor &&  mtd write bl2-nor $loadaddr 0x0 0x40000
+bl2_tftp_write=tftpboot $loadaddr $bootfile_bl2_nor && run bl2_mtd_write
+bootcmd=run check_button ; run led_start ; mtd read recovery ${loadaddr} ; bootm ; run led_loop_error
+bootconf=config-1
+bootdelay=0
+bootfile=openwrt-mediatek-filogic-openwrt_one-initramfs.itb
+bootfile_bl2_nor=openwrt-mediatek-filogic-openwrt_one-nor-preloader.bin
+bootfile_fip_nor=openwrt-mediatek-filogic-openwrt_one-nor-bl31-uboot.fip
+bootmenu_0=Initialize environment.=run _firstboot
+bootmenu_0d=Run default boot command.=run bootcmd
+bootmenu_1=Boot system via TFTP.=run tftp_boot ; run bootmenu_confirm_return
+bootmenu_2=[31mUnlock NOR. (Make sure the NOR/WP jumper is populated)[0m=sf probe 1:0 && sf protect unlock 0x0 0x1000000 ; run bootmenu_confirm_return
+bootmenu_3=[31mLoad BL31+U-Boot FIP via TFTP then write to NOR.[0m=run fip_tftp_write ; run bootmenu_confirm_return
+bootmenu_4=[31mLoad BL2 preloader via TFTP then write to NOR.[0m=run bl2_tftp_write ; run bootmenu_confirm_return
+bootmenu_5=[31mLoad recovery system via TFTP then write to NOR.[0m=run tftp_write ; run bootmenu_confirm_return
+bootmenu_6=[31mLock NOR. (Remove jumper afterwards)[0m=sf probe 1:0 && sf protect lock 0x0 0x1000000 ; run bootmenu_confirm_return
+bootmenu_7=Reboot.=reset
+bootmenu_confirm_return=askenv - Press ENTER to return to menu ; bootmenu 60
+bootmenu_default=0
+bootmenu_delay=0
+bootmenu_title=      [0;34m( ( ( [1;39mOpenWrt[0;34m ) ) )  [0;36m[SPI-NOR][0m
+check_button=if button front ; then run usb_recovery ; run led_loop_error ; fi
+fip_mtd_write=mtd erase fip-nor && mtd write fip-nor $loadaddr
+fip_tftp_write=tftpboot $loadaddr $bootfile_fip_nor && run fip_mtd_write
+ipaddr=*************
+led_done=led green off ; led white on
+led_loop_done=led white off ; led green on ; echo done ; while true ; do  sleep 1 ; done
+led_loop_error=led white off ; led green off ; while true ; do led red on ; sleep 1 ; led red off ; sleep 1 ; done
+led_boot=led green on ; led white on ; led red on
+led_start=led green off ; led red off; led white on
+loadaddr=0x46000000
+preboot=run led_boot
+recoverfile_bl2=openwrt-mediatek-filogic-openwrt_one-snand-preloader.bin
+recoverfile_ubi=openwrt-mediatek-filogic-openwrt_one-factory.ubi
+recovery_write_bl2=mtd erase bl2 && for offset in 0x0 0x40000 0x80000; do mtd write bl2 $loadaddr $offset 0x40000 ; done
+recovery_write_ubi=mtd erase ubi && mtd write ubi $loadaddr 0 ${filesize}
+serverip=*************
+tftp_boot=run led_start ; tftpboot $loadaddr $bootfile && bootm $loadaddr#$bootconf
+tftp_write=run led_start ; tftpboot $loadaddr $bootfile && mtd erase recovery 0x0 ${filesize} && mtd write recovery $loadaddr 0x0 ${filesize}
+usb_pgood_delay=4000
+usb_recovery=run led_start ; usb start && run usb_recovery_bl2 && run usb_recovery_ubi && run led_loop_done
+usb_recovery_bl2=fatload usb 0:1 ${loadaddr} ${recoverfile_bl2} && run recovery_write_bl2
+usb_recovery_ubi=fatload usb 0:1 ${loadaddr} ${recoverfile_ubi} && run recovery_write_ubi
+_firstboot=setenv _firstboot ; run ethaddr_factory ; run _switch_to_menu ; run _init_env ; bootmenu
+_init_env=setenv _init_env ; echo Initialize Env ; run ubi_create_env ; saveenv
+_switch_to_menu=setenv _switch_to_menu ; setenv bootdelay 3 ; setenv bootmenu_delay 3 ; setenv bootmenu_0 $bootmenu_0d ; setenv bootmenu_0d ; run _bootmenu_update_title
+_bootmenu_update_title=setenv _bootmenu_update_title ; setenv bootmenu_title "$bootmenu_title       [33m$ver[0m"
--- /dev/null
+++ b/defenvs/openwrt-one-spi-nand_env
@@ -0,0 +1,62 @@
+ethaddr_factory=mtd read factory 0x46000000 0x0 0x20000 && env readmem -b ethaddr 0x4600002a 0x6 ; setenv ethaddr_factory
+ipaddr=*************
+serverip=*************
+loadaddr=0x46000000
+console=earlycon=uart8250,mmio32,0x11002000 console=ttyS0
+bootcmd=run check_buttons ; run led_start ; run boot_calibration ; run boot_production ; run boot_recovery
+bootconf=config-1
+bootdelay=0
+bootfile=openwrt-mediatek-filogic-openwrt_one-initramfs.itb
+bootfile_bl2=openwrt-mediatek-filogic-openwrt_one-snand-preloader.bin
+bootfile_fip=openwrt-mediatek-filogic-openwrt_one-snand-bl31-uboot.fip
+bootfile_upg=openwrt-mediatek-filogic-openwrt_one-squashfs-sysupgrade.itb
+bootmenu_confirm_return=askenv - Press ENTER to return to menu ; run led_boot ; bootmenu 60
+bootmenu_default=0
+bootmenu_delay=0
+bootmenu_title=      [0;34m( ( ( [1;39mOpenWrt[0;34m ) ) )  [0;36m[SPI-NAND][0m
+bootmenu_0=Initialize environment.=run _firstboot
+bootmenu_0d=Run default boot command.=run boot_default
+bootmenu_1=Boot system via TFTP.=run boot_tftp ; run bootmenu_confirm_return
+bootmenu_2=Boot production system from NAND.=run boot_production ; run bootmenu_confirm_return
+bootmenu_3=Boot recovery system from NAND.=run boot_recovery ; run bootmenu_confirm_return
+bootmenu_4=Load production system via TFTP then write to NAND.=noboot=1 ; replacevol=1 ; run boot_tftp_production ; noboot= ; replacevol= ; run bootmenu_confirm_return
+bootmenu_5=Load recovery system via TFTP then write to NAND.=noboot=1 ; replacevol=1 ; run boot_tftp_recovery ; noboot= ; replacevol= ; run bootmenu_confirm_return
+bootmenu_6=[31mLoad BL31+U-Boot FIP via TFTP then write to NAND.[0m=run boot_tftp_write_fip ; run bootmenu_confirm_return
+bootmenu_7=[31mLoad BL2 preloader via TFTP then write to NAND.[0m=run boot_tftp_write_bl2 ; run bootmenu_confirm_return
+bootmenu_8=Reboot.=reset
+bootmenu_9=Reset all settings to factory defaults.=run reset_factory ; reset
+boot_default=run bootcmd ; run boot_recovery ; replacevol=1 ; run boot_tftp_forever
+boot_calibration=ubi read $loadaddr calibration && bootm $loadaddr#$bootconf
+boot_production=led white on ; run ubi_read_production && bootm $loadaddr#$bootconf ; led white off
+boot_recovery=led green on ; run ubi_read_recovery && bootm $loadaddr#$bootconf ; led green off
+boot_tftp=run led_start ; tftpboot $loadaddr $bootfile && bootm $loadaddr#$bootconf
+boot_tftp_forever=led green off ; led white off ; led red on ; while true ; do run boot_tftp_recovery ; led red off ; sleep 1 ; done
+boot_tftp_production=tftpboot $loadaddr $bootfile_upg && test $replacevol = 1 && iminfo $loadaddr && run ubi_write_production ; if test $noboot = 1 ; then else bootm $loadaddr#$bootconf ; fi
+boot_tftp_recovery=tftpboot $loadaddr $bootfile && test $replacevol = 1 && iminfo $loadaddr && run ubi_write_recovery ; if test $noboot = 1 ; then else bootm $loadaddr#$bootconf ; fi
+boot_tftp=tftpboot $loadaddr $bootfile && bootm $loadaddr#$bootconf
+boot_tftp_write_fip=tftpboot $loadaddr $bootfile_fip && run ubi_write_fip && run reset_factory
+boot_tftp_write_bl2=tftpboot $loadaddr $bootfile_bl2 && run snand_write_bl2
+check_buttons=if button front ; then run boot_recovery ; run boot_tftp ; run led_loop_error ; else if button back ; then ; run usb_recover ; run led_loop_error ; fi ; fi
+led_boot=led green on ; led white on ; led red on
+led_done=led green on ; led white off ; led red off
+led_loop_done=led white off ; led green on ; echo done ; while true ; do  sleep 1 ; done
+led_loop_error=led white off ; led green off ; while true ; do led red on ; sleep 1 ; led red off ; sleep 1 ; done
+led_start=led white on ; led green off ; led red off
+preboot=run led_boot
+reset_factory=mw $loadaddr 0xff 0x1f000 ; ubi write $loadaddr ubootenv 0x1f000 ; ubi write $loadaddr ubootenv2 0x1f000 ; ubi remove rootfs_data
+snand_write_bl2=mtd erase bl2 && for offset in 0x0 0x40000 0x80000 0xc0000 ; do mtd write bl2 $loadaddr $offset 0x40000 ; done
+ubi_create_env=ubi check ubootenv || ubi create ubootenv 0x1f000 dynamic ; ubi check ubootenv2 || ubi create ubootenv2 0x1f000 dynamic
+ubi_prepare_rootfs=if ubi check rootfs_data ; then else if env exists rootfs_data_max ; then ubi create rootfs_data $rootfs_data_max dynamic || ubi create rootfs_data - dynamic ; else ubi create rootfs_data - dynamic ; fi ; fi
+ubi_read_production=ubi read $loadaddr fit && iminfo $loadaddr && run ubi_prepare_rootfs
+ubi_read_recovery=ubi check recovery && ubi read $loadaddr recovery
+ubi_remove_rootfs=ubi check rootfs_data && ubi remove rootfs_data
+usb_pgood_delay=4000
+usb_recover=run led_start ; usb start && run usb_recover_production && run led_loop_done
+usb_recover_production=fatload usb 0:1 ${loadaddr} ${bootfile_upg} && iminfo $loadaddr && run ubi_write_production
+ubi_write_fip=run ubi_remove_rootfs ; ubi check fip && ubi remove fip ; ubi create fip $filesize static && ubi write $loadaddr fip $filesize
+ubi_write_production=ubi check fit && ubi remove fit ; run ubi_remove_rootfs ; ubi create fit $filesize dynamic && ubi write $loadaddr fit $filesize
+ubi_write_recovery=ubi check recovery && ubi remove recovery ; run ubi_remove_rootfs ; ubi create recovery $filesize dynamic && ubi write $loadaddr recovery $filesize
+_init_env=setenv _init_env ; run ubi_create_env ; saveenv ; saveenv
+_firstboot=setenv _firstboot ; run ethaddr_factory ; run _switch_to_menu ; run _init_env ; bootmenu
+_switch_to_menu=setenv _switch_to_menu ; setenv bootdelay 3 ; setenv bootmenu_delay 3 ; setenv bootmenu_0 $bootmenu_0d ; setenv bootmenu_0d ; run _bootmenu_update_title
+_bootmenu_update_title=setenv _bootmenu_update_title ; setenv bootmenu_title "$bootmenu_title       [33m$ver[0m"
