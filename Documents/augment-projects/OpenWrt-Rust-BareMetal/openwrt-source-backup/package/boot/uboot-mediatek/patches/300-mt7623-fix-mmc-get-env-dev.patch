--- a/board/mediatek/mt7623/mt7623_rfb.c
+++ b/board/mediatek/mt7623/mt7623_rfb.c
@@ -5,6 +5,7 @@
 
 #include <config.h>
 #include <mmc.h>
+#include <part.h>
 #include <asm/global_data.h>
 
 DECLARE_GLOBAL_DATA_PTR;
@@ -22,8 +23,9 @@ int mmc_get_boot_dev(void)
 {
 	int g_mmc_devid = -1;
 	char *uflag = (char *)0x81DFFFF0;
+	struct blk_desc *desc;
 
-	if (!find_mmc_device(1))
+	if (blk_get_device_by_str("mmc", "1", &desc) < 0)
 		return 0;
 
 	if (strncmp(uflag,"eMMC",4)==0) {
@@ -38,6 +40,23 @@ int mmc_get_boot_dev(void)
 
 int mmc_get_env_dev(void)
 {
-	return mmc_get_boot_dev();
+	struct udevice *dev;
+	const char *mmcdev;
+
+	switch (mmc_get_boot_dev()) {
+	case 0:
+		mmcdev = "mmc@11230000";
+		break;
+	case 1:
+		mmcdev = "mmc@11240000";
+		break;
+	default:
+		return -1;
+	}
+
+	if (uclass_get_device_by_name(UCLASS_MMC, mmcdev, &dev))
+		return -1;
+
+	return dev_seq(dev);
 }
 #endif
