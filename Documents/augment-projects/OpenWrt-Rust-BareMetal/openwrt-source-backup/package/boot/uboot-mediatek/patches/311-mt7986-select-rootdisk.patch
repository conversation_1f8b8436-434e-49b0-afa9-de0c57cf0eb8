--- a/board/mediatek/mt7986/mt7986_rfb.c
+++ b/board/mediatek/mt7986/mt7986_rfb.c
@@ -10,7 +10,9 @@
 #include <env.h>
 #include <init.h>
 #include <asm/global_data.h>
+#include <asm/io.h>
 #include <linux/delay.h>
+#include <linux/libfdt.h>
 
 #ifndef CONFIG_RESET_BUTTON_LABEL
 #define CONFIG_RESET_BUTTON_LABEL "reset"
@@ -82,3 +84,54 @@ int board_nmbm_init(void)
 
 	return 0;
 }
+
+#define	MT7986_BOOT_NOR		0
+#define	MT7986_BOOT_SPIM_NAND	1
+#define	MT7986_BOOT_EMMC	2
+#define	MT7986_BOOT_SNFI_NAND	3
+
+int ft_system_setup(void *blob, struct bd_info *bd)
+{
+	const u32 *media_handle_p;
+	int chosen, len, ret;
+	const char *media;
+	u32 media_handle;
+
+	switch ((readl(0x1001f6f0) & 0x300) >> 8) {
+	case MT7986_BOOT_NOR:
+		media = "rootdisk-nor";
+		break
+		;;
+	case MT7986_BOOT_SPIM_NAND:
+		media = "rootdisk-spim-nand";
+		break
+		;;
+	case MT7986_BOOT_EMMC:
+		media = "rootdisk-emmc";
+		break
+		;;
+	case MT7986_BOOT_SNFI_NAND:
+		media = "rootdisk-sd";
+		break
+		;;
+	}
+
+	chosen = fdt_path_offset(blob, "/chosen");
+	if (chosen <= 0)
+		return 0;
+
+	media_handle_p = fdt_getprop(blob, chosen, media, &len);
+	if (media_handle_p <= 0 || len != 4)
+		return 0;
+
+	media_handle = *media_handle_p;
+	ret = fdt_setprop(blob, chosen, "rootdisk", &media_handle, sizeof(media_handle));
+	if (ret) {
+		printf("cannot set media phandle %s as rootdisk /chosen node\n", media);
+		return ret;
+	}
+
+	printf("set /chosen/rootdisk to bootrom media: %s (phandle 0x%08x)\n", media, fdt32_to_cpu(media_handle));
+
+	return 0;
+}
