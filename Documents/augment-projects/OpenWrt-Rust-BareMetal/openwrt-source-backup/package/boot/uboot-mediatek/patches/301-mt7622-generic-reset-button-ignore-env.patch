--- a/board/mediatek/mt7622/mt7622_rfb.c
+++ b/board/mediatek/mt7622/mt7622_rfb.c
@@ -5,9 +5,16 @@
  */
 
 #include <config.h>
+#include <dm.h>
+#include <button.h>
 #include <env.h>
 #include <init.h>
 #include <asm/global_data.h>
+#include <linux/delay.h>
+
+#ifndef CONFIG_RESET_BUTTON_LABEL
+#define CONFIG_RESET_BUTTON_LABEL "reset"
+#endif
 
 #include <mtd.h>
 #include <linux/mtd/mtd.h>
@@ -21,6 +28,28 @@ int board_init(void)
 	return 0;
 }
 
+int board_late_init(void)
+{
+	struct udevice *dev;
+
+	if (!button_get_by_label(CONFIG_RESET_BUTTON_LABEL, &dev)) {
+		puts("reset button found\n");
+#ifdef CONFIG_RESET_BUTTON_SETTLE_DELAY
+		if (CONFIG_RESET_BUTTON_SETTLE_DELAY > 0) {
+			button_get_state(dev);
+			mdelay(CONFIG_RESET_BUTTON_SETTLE_DELAY);
+		}
+#endif
+		if (button_get_state(dev) == BUTTON_ON) {
+			puts("button pushed, resetting environment\n");
+			gd->env_valid = ENV_INVALID;
+		}
+	}
+
+ 	env_relocate();
+ 	return 0;
+ }
+
 int board_nmbm_init(void)
 {
 #ifdef CONFIG_ENABLE_NAND_NMBM
--- a/arch/arm/mach-mediatek/Kconfig
+++ b/arch/arm/mach-mediatek/Kconfig
@@ -170,4 +170,11 @@ config MTK_TZ_MOVABLE
 	select OF_SYSTEM_SETUP
 	bool
 
+config RESET_BUTTON_LABEL
+	string "Button to trigger factory reset"
+	default "reset"
+
+config RESET_BUTTON_SETTLE_DELAY
+	int "Delay to wait for button to settle"
+	default 0
 endif
