--- a/arch/arm/dts/mt7622.dtsi
+++ b/arch/arm/dts/mt7622.dtsi
@@ -37,6 +37,30 @@
 		};
 	};
 
+	psci {
+		compatible  = "arm,psci-1.0";
+		method      = "smc";
+	};
+
+	reserved-memory {
+		#address-cells = <1>;
+		#size-cells = <1>;
+		ranges;
+
+		/* 64 KiB reserved for ramoops/pstore */
+		ramoops@42ff0000 {
+			compatible = "ramoops";
+			reg = <0x42ff0000 0x10000>;
+			record-size = <0x1000>;
+		};
+
+		/* 192 KiB reserved for ARM Trusted Firmware (BL31) */
+		secmon_reserved: secmon@43000000 {
+			reg = <0x43000000 0x30000>;
+			no-map;
+		};
+	};
+
 	snfi: snfi@1100d000 {
 		compatible = "mediatek,mtk-snfi-spi";
 		reg = <0x1100d000 0x2000>;
