--- a/arch/arm/mach-kirkwood/Kconfig
+++ b/arch/arm/mach-kirkwood/Kconfig
@@ -68,6 +68,9 @@ config TARGET_SBx81LIFXCAT
 config TARGET_DB_88F6281_BP
 	bool "Marvell DB-88F6281-BP"
 
+config TARGET_NSA325
+	bool "Zyxel NSA325 board"
+
 endchoice
 
 config SYS_SOC
@@ -91,6 +94,7 @@ source "board/Seagate/goflexhome/Kconfig
 source "board/Seagate/nas220/Kconfig"
 source "board/zyxel/nsa310/Kconfig"
 source "board/zyxel/nsa310s/Kconfig"
+source "board/zyxel/nsa325/Kconfig"
 source "board/alliedtelesis/SBx81LIFKW/Kconfig"
 source "board/alliedtelesis/SBx81LIFXCAT/Kconfig"
 source "board/Marvell/db-88f6281-bp/Kconfig"
--- /dev/null
+++ b/board/zyxel/nsa325/Kconfig
@@ -0,0 +1,12 @@
+if TARGET_NSA325
+
+config SYS_BOARD
+	default "nsa325"
+
+config SYS_VENDOR
+	default "zyxel"
+
+config SYS_CONFIG_NAME
+	default "nsa325"
+
+endif
--- /dev/null
+++ b/board/zyxel/nsa325/MAINTAINERS
@@ -0,0 +1,6 @@
+NSA325 BOARD
+M:	Alberto Bursi <<EMAIL>>
+S:	Maintained
+F:	board/zyxel/nsa325/
+F:	include/configs/nsa325.h
+F:	configs/nsa325_defconfig
--- /dev/null
+++ b/board/zyxel/nsa325/Makefile
@@ -0,0 +1,13 @@
+#
+# <AUTHOR> <EMAIL>
+#
+# Based on
+# (C) Copyright 2009
+# Marvell Semiconductor <www.marvell.com>
+# <AUTHOR> <EMAIL>
+#
+# SPDX-License-Identifier:	GPL-2.0+
+#
+
+obj-y	:= nsa325.o
+
--- /dev/null
+++ b/board/zyxel/nsa325/kwbimage.cfg
@@ -0,0 +1,78 @@
+# <AUTHOR> <EMAIL>
+#
+# Extracted from Zyxel GPL source for u-boot-1.1.4_NSA325v2
+#
+# See file CREDITS for list of people who contributed to this
+# project.
+#
+# This program is free software; you can redistribute it and/or
+# modify it under the terms of the GNU General Public License as
+# published by the Free Software Foundation; either version 2 of
+# the License, or (at your option) any later version.
+#
+# This program is distributed in the hope that it will be useful,
+# but WITHOUT ANY WARRANTY; without even the implied warranty of
+# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
+# GNU General Public License for more details.
+#
+# You should have received a copy of the GNU General Public License
+# along with this program; if not, write to the Free Software
+# Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston,
+# MA 02110-1301 USA
+#
+# Refer docs/README.kwimage for more details about how-to configure
+# and create kirkwood boot image
+#
+
+# Boot Media configurations
+#BOOT_FROM	uart
+BOOT_FROM       nand
+NAND_ECC_MODE   default
+NAND_PAGE_SIZE  0x0800
+
+# SOC registers configuration using bootrom header extension
+# Maximum KWBIMAGE_MAX_CONFIG configurations allowed
+
+# Configure RGMII-0 interface pad voltage to 1.8V
+DATA 0xFFD100e0 0x1b1b1b9b
+
+#Dram initalization
+DATA 0xFFD01400 0x4301503E      # DDR Configuration register
+DATA 0xFFD01404 0xB9843000      # DDR Controller Control Low
+DATA 0xFFD01408 0x33137777      # DDR Timing (Low)
+DATA 0xFFD0140C 0x16000C55      # DDR Timing (High)
+DATA 0xFFD01410 0x04000000      # DDR Address Control
+DATA 0xFFD01414 0x00000000	#  DDR Open Pages Control
+DATA 0xFFD01418 0x00000000	#  DDR Operation
+DATA 0xFFD0141C 0x00000672	#  DDR Mode
+DATA 0xFFD01420 0x00000004	#  DDR Extended Mode
+DATA 0xFFD01424 0x0000F14F	#  DDR Controller Control High
+DATA 0xFFD01428 0x000D6720	# DDR3 ODT Read Timing
+DATA 0xFFD0147C 0x0000B571	# DDR2 ODT Write Timing
+DATA 0xFFD01504 0x1FFFFFF1      # CS[0]n Size
+DATA 0xFFD01508 0x20000000      # CS[1]n Base address to 512Mb
+DATA 0xFFD0150C 0x1FFFFFF4      # CS[1]n Size 512Mb Window enabled for CS1
+DATA 0xFFD01514 0x00000000      # CS[2]n Size, window disabled
+DATA 0xFFD0151C 0x00000000      # CS[3]n Size, window disabled
+DATA 0xFFD01494 0x00120000      #  DDR ODT Control (Low)
+DATA 0xFFD01498 0x00000000      #  DDR ODT Control (High)
+DATA 0xFFD0149C 0x0000E803      # CPU ODT Control
+
+DATA 0xFFD015D0 0x00000630
+DATA 0xFFD015D4 0x00000046
+DATA 0xFFD015D8 0x00000008
+DATA 0xFFD015DC 0x00000000
+DATA 0xFFD015E0 0x00000023
+DATA 0xFFD015E4 0x00203C18
+DATA 0xFFD01620 0x00384800
+DATA 0xFFD01480 0x00000001
+DATA 0xFFD20134 0x66666666
+DATA 0xFFD20138 0x00066666
+
+#Disable nsa325 hardware watchdog to allow successful kwbooting
+DATA 0xFFD10100 0x00004000 # set GPIO 14 to high to disable the watchdog
+DATA 0xFFD10104 0xFFFFBFFF # set GPIO 14 to output (to block any other input to it)
+
+# End of Header extension
+DATA 0x0 0x0
+
--- /dev/null
+++ b/board/zyxel/nsa325/nsa325.c
@@ -0,0 +1,265 @@
+/*
+ * Copyright (C) 2015 bodhi <<EMAIL>>
+ *
+ * Based on
+ * Copyright (C) 2014  Jason Plum <<EMAIL>>
+ *
+ * Based on nsa320.c originall written by
+ * Copyright (C) 2012  Peter Schildmann <<EMAIL>>
+ *
+ * Based on guruplug.c originally written by
+ * Siddarth Gore <<EMAIL>>
+ * (C) Copyright 2009
+ * Marvell Semiconductor <www.marvell.com>
+ *
+ * See file CREDITS for list of people who contributed to this
+ * project.
+ *
+ * This program is free software; you can redistribute it and/or
+ * modify it under the terms of the GNU General Public License as
+ * published by the Free Software Foundation; either version 2 of
+ * the License, or (at your option) any later version.
+ *
+ * This program is distributed in the hope that it will be useful,
+ * but WITHOUT ANY WARRANTY; without even the implied warranty of
+ * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
+ * GNU General Public License for more details.
+ *
+ * You should have received a copy of the GNU General Public License
+ * along with this program; if not, write to the Free Software
+ * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston,
+ * MA 02110-1301 USA
+ */
+
+#include <common.h>
+#include <miiphy.h>
+#include <asm/arch/soc.h>
+#include <asm/arch/mpp.h>
+#include <asm/arch/cpu.h>
+#include <asm/gpio.h>
+#include <asm/io.h>
+#include "nsa325.h"
+#include <asm/arch/gpio.h>
+
+DECLARE_GLOBAL_DATA_PTR;
+
+int board_early_init_f(void)
+{
+	/*
+	 * default gpio configuration
+	 * There are maximum 64 gpios controlled through 2 sets of registers
+	 * the below configuration configures mainly initial LED status
+	 */
+	mvebu_config_gpio(NSA325_VAL_LOW, NSA325_VAL_HIGH,
+		       NSA325_OE_LOW, NSA325_OE_HIGH);
+
+	/* Multi-Purpose Pins Functionality configuration */
+	/* (all LEDs & power off active high) */
+	u32 kwmpp_config[] = {
+		MPP0_NF_IO2,
+		MPP1_NF_IO3,
+		MPP2_NF_IO4,
+		MPP3_NF_IO5,
+		MPP4_NF_IO6,
+		MPP5_NF_IO7,
+		MPP6_SYSRST_OUTn,
+		MPP7_GPO,
+		MPP8_TW_SDA,		/* PCF8563 RTC chip   */
+		MPP9_TW_SCK,		/* connected to TWSI  */
+		MPP10_UART0_TXD,
+		MPP11_UART0_RXD,
+		MPP12_GPO,		/* HDD2 LED (green)   */
+		MPP13_GPIO,		/* HDD2 LED (red)     */
+		MPP14_GPIO,		/* MCU DATA pin (in)  */
+		MPP15_GPIO,		/* USB LED (green)    */
+		MPP16_GPIO,		/* MCU CLK pin (out)  */
+		MPP17_GPIO,		/* MCU ACT pin (out)  */
+		MPP18_NF_IO0,
+		MPP19_NF_IO1,
+		MPP20_GPIO,
+		MPP21_GPIO,		/* USB power          */
+		MPP22_GPIO,
+		MPP23_GPIO,
+		MPP24_GPIO,
+		MPP25_GPIO,
+		MPP26_GPIO,
+		MPP27_GPIO,
+		MPP28_GPIO,		/* SYS LED (green)    */
+		MPP29_GPIO,		/* SYS LED (orange)   */
+		MPP30_GPIO,
+		MPP31_GPIO,
+		MPP32_GPIO,
+		MPP33_GPIO,
+		MPP34_GPIO,
+		MPP35_GPIO,
+		MPP36_GPIO,		/* reset button       */
+		MPP37_GPIO,		/* copy button        */
+		MPP38_GPIO,		/* VID B0             */
+		MPP39_GPIO,		/* COPY LED (green)   */
+		MPP40_GPIO,		/* COPY LED (red)     */
+		MPP41_GPIO,		/* HDD1 LED (green)   */
+		MPP42_GPIO,		/* HDD1 LED (red)     */
+		MPP43_GPIO,		/* HTP pin            */
+		MPP44_GPIO,		/* buzzer             */
+		MPP45_GPIO,		/* VID B1             */
+		MPP46_GPIO,		/* power button       */
+		MPP47_GPIO,		/* HDD2 power         */
+		MPP48_GPIO,		/* power off          */
+		0
+	};
+	kirkwood_mpp_conf(kwmpp_config, NULL);
+	return 0;
+}
+
+int board_init(void)
+{
+
+	/* address of boot parameters */
+	gd->bd->bi_boot_params = mvebu_sdram_bar(0) + 0x100;
+
+	/* This disables the hardware watchdog in the mcu on this board. */
+	kw_gpio_set_valid(14, 1);
+	kw_gpio_direction_output(14, 0);
+	kw_gpio_set_value(14, 1);
+
+	return 0;
+}
+
+#ifdef CONFIG_RESET_PHY_R
+/* Configure and enable MV88E1318 PHY */
+void reset_phy(void)
+{
+	u16 reg;
+	u16 devadr;
+	char *name = "egiga0";
+
+	if (miiphy_set_current_dev(name))
+		return;
+
+	/* command to read PHY dev address */
+	if (miiphy_read(name, 0xEE, 0xEE, (u16 *) &devadr)) {
+		printf("Err..%s could not read PHY dev address\n",
+			__FUNCTION__);
+		return;
+	}
+
+	/* Set RGMII delay */
+	miiphy_write(name, devadr, MV88E1318_PGADR_REG, MV88E1318_MAC_CTRL_PG);
+	miiphy_read(name, devadr, MV88E1318_MAC_CTRL_REG, &reg);
+	reg |= (MV88E1318_RGMII_RXTM_CTRL | MV88E1318_RGMII_TXTM_CTRL);
+	miiphy_write(name, devadr, MV88E1318_MAC_CTRL_REG, reg);
+	miiphy_write(name, devadr, MV88E1318_PGADR_REG, 0);
+
+	/* reset the phy */
+	miiphy_reset(name, devadr);
+
+	/* The Zyxel NSA325 uses the 88E1310S Alaska (interface identical to 88E1318) */
+	/* and has an MCU attached to the LED[2] via tristate interrupt */
+	reg = 0;
+
+	/* switch to LED register page */
+	miiphy_write(name, devadr, MV88E1318_PGADR_REG, MV88E1318_LED_PG);
+	/* read out LED polarity register */
+	miiphy_read(name, devadr, MV88E1318_LED_POL_REG, &reg);
+	/* clear 4, set 5 - LED2 low, tri-state */
+	reg &= ~(MV88E1318_LED2_4);
+	reg |= (MV88E1318_LED2_5);
+	/* write back LED polarity register */
+	miiphy_write(name, devadr, MV88E1318_LED_POL_REG, reg);
+	/* jump back to page 0, per the PHY chip documenation. */
+	miiphy_write(name, devadr, MV88E1318_PGADR_REG, 0);
+
+	/* Set the phy back to auto-negotiation mode. Onboard mcu sets it as 10Mbits/s on poweroff for WoL function */
+	miiphy_write(name, devadr, 0x4, 0x1e1);
+	miiphy_write(name, devadr, 0x9, 0x300);
+	/* Downshift */
+	miiphy_write(name, devadr, 0x10, 0x3860);
+	miiphy_write(name, devadr, 0x0, 0x9140);
+
+	printf("MV88E1318 PHY initialized on %s\n", name);
+
+}
+#endif /* CONFIG_RESET_PHY_R */
+
+#ifdef CONFIG_SHOW_BOOT_PROGRESS
+void show_boot_progress(int val)
+{
+	struct kwgpio_registers *gpio0 = (struct kwgpio_registers *)MVEBU_GPIO0_BASE;
+	u32 dout0 = readl(&gpio0->dout);
+	u32 blen0 = readl(&gpio0->blink_en);
+
+	struct kwgpio_registers *gpio1 = (struct kwgpio_registers *)MVEBU_GPIO1_BASE;
+	u32 dout1 = readl(&gpio1->dout);
+	u32 blen1 = readl(&gpio1->blink_en);
+
+	switch (val) {
+	case BOOTSTAGE_ID_DECOMP_IMAGE:
+		writel(blen0 & ~(SYS_GREEN_LED | SYS_ORANGE_LED), &gpio0->blink_en);
+		writel((dout0 & ~SYS_GREEN_LED) | SYS_ORANGE_LED, &gpio0->dout);
+		break;
+	case BOOTSTAGE_ID_RUN_OS:
+		writel(dout0 & ~SYS_ORANGE_LED, &gpio0->dout);
+		writel(blen0 | SYS_GREEN_LED, &gpio0->blink_en);
+		break;
+	case BOOTSTAGE_ID_NET_START:
+		writel(dout1 & ~COPY_RED_LED, &gpio1->dout);
+		writel((blen1 & ~COPY_RED_LED) | COPY_GREEN_LED, &gpio1->blink_en);
+		break;
+	case BOOTSTAGE_ID_NET_LOADED:
+		writel(blen1 & ~(COPY_RED_LED | COPY_GREEN_LED), &gpio1->blink_en);
+		writel((dout1 & ~COPY_RED_LED) | COPY_GREEN_LED, &gpio1->dout);
+		break;
+	case -BOOTSTAGE_ID_NET_NETLOOP_OK:
+	case -BOOTSTAGE_ID_NET_LOADED:
+		writel(dout1 & ~COPY_GREEN_LED, &gpio1->dout);
+		writel((blen1 & ~COPY_GREEN_LED) | COPY_RED_LED, &gpio1->blink_en);
+		break;
+	default:
+		if (val < 0) {
+			/* error */
+			printf("Error occured, error code = %d\n", -val);
+			writel(dout0 & ~SYS_GREEN_LED, &gpio0->dout);
+			writel(blen0 | SYS_ORANGE_LED, &gpio0->blink_en);
+		}
+		break;
+	}
+}
+#endif
+
+#if defined(CONFIG_KIRKWOOD_GPIO)
+/* Return GPIO button status */
+/*
+un-pressed:
+ gpio-36 (Reset Button ) in hi (act lo) - IRQ edge (clear )
+ gpio-37 (Copy Button  ) in hi (act lo) - IRQ edge (clear )
+ gpio-46 (Power Button ) in lo (act hi) - IRQ edge (clear )
+pressed
+ gpio-36 (Reset Button ) in lo (act hi) - IRQ edge (clear )
+ gpio-37 (Copy Button  ) in lo (act hi) - IRQ edge (clear )
+ gpio-46 (Power Button ) in hi (act lo) - IRQ edge (clear )
+*/
+
+static int
+do_read_button(cmd_tbl_t *cmdtp, int flag, int argc, char * const argv[])
+{
+	if (strcmp(argv[1], "power") == 0) {
+			kw_gpio_set_valid(BTN_POWER, GPIO_INPUT_OK);
+			kw_gpio_direction_input(BTN_POWER);
+			return !kw_gpio_get_value(BTN_POWER);
+	}
+	else if (strcmp(argv[1], "reset") == 0)
+		return kw_gpio_get_value(BTN_RESET);
+	else if (strcmp(argv[1], "copy") == 0)
+		return kw_gpio_get_value(BTN_COPY);
+	else
+		return -1;
+}
+
+
+U_BOOT_CMD(button, 2, 0, do_read_button,
+	   "Return GPIO button status 0=off 1=on",
+	   "- button power|reset|copy: test buttons states\n"
+);
+
+#endif
+
--- /dev/null
+++ b/board/zyxel/nsa325/nsa325.h
@@ -0,0 +1,77 @@
+/*
+ * Copyright (C) 2014  Jason Plum <<EMAIL>>
+ *
+ * Based on nsa320.h originall written by
+ * Copyright (C) 2012  Peter Schildmann <<EMAIL>>
+ *
+ * Based on guruplug.h originally written by
+ * Siddarth Gore <<EMAIL>>
+ * (C) Copyright 2009
+ * Marvell Semiconductor <www.marvell.com>
+ *
+ * See file CREDITS for list of people who contributed to this
+ * project.
+ *
+ * This program is free software; you can redistribute it and/or
+ * modify it under the terms of the GNU General Public License as
+ * published by the Free Software Foundation; either version 2 of
+ * the License, or (at your option) any later version.
+ *
+ * This program is distributed in the hope that it will be useful,
+ * but WITHOUT ANY WARRANTY; without even the implied warranty of
+ * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
+ * GNU General Public License for more details.
+ *
+ * You should have received a copy of the GNU General Public License
+ * along with this program; if not, write to the Free Software
+ * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston,
+ * MA 02110-1301 USA
+ */
+
+#ifndef __NSA325_H
+#define __NSA325_H
+
+/* low GPIO's */
+#define HDD2_GREEN_LED		(1 << 12)
+#define HDD2_RED_LED		(1 << 13)
+#define USB_GREEN_LED		(1 << 15)
+#define USB_POWER		(1 << 21)
+#define SYS_GREEN_LED		(1 << 28)
+#define SYS_ORANGE_LED		(1 << 29)
+
+#define PIN_USB_GREEN_LED	15
+#define PIN_USB_POWER		21
+
+#define NSA325_OE_LOW		(~(HDD2_GREEN_LED | HDD2_RED_LED | \
+				   USB_GREEN_LED | USB_POWER | \
+				   SYS_GREEN_LED | SYS_ORANGE_LED))
+#define NSA325_VAL_LOW		(SYS_GREEN_LED | USB_POWER)
+
+/* high GPIO's */
+#define COPY_GREEN_LED		(1 << 7)
+#define COPY_RED_LED		(1 << 8)
+#define HDD1_GREEN_LED		(1 << 9)
+#define HDD1_RED_LED		(1 << 10)
+#define HDD2_POWER          (1 << 15)
+#define WATCHDOG_SIGNAL     (1 << 14)
+
+#define NSA325_OE_HIGH		(~(COPY_GREEN_LED | COPY_RED_LED | \
+				   HDD1_GREEN_LED | HDD1_RED_LED | HDD2_POWER | WATCHDOG_SIGNAL ))
+#define NSA325_VAL_HIGH		(WATCHDOG_SIGNAL | HDD2_POWER)
+
+/* PHY related */
+#define MV88E1318_PGADR_REG		22
+#define MV88E1318_MAC_CTRL_PG	2
+#define MV88E1318_MAC_CTRL_REG	21
+#define MV88E1318_RGMII_TXTM_CTRL	(1 << 4)
+#define MV88E1318_RGMII_RXTM_CTRL	(1 << 5)
+#define MV88E1318_LED_PG        3
+#define MV88E1318_LED_POL_REG	17
+#define MV88E1318_LED2_4	    (1 << 4)
+#define MV88E1318_LED2_5	    (1 << 5)
+
+#define BTN_POWER				46
+#define BTN_RESET				36
+#define BTN_COPY				37
+
+#endif /* __NSA325_H */
--- /dev/null
+++ b/configs/nsa325_defconfig
@@ -0,0 +1,48 @@
+CONFIG_ARM=y
+CONFIG_SYS_DCACHE_OFF=y
+CONFIG_ARCH_CPU_INIT=y
+CONFIG_KIRKWOOD=y
+CONFIG_SYS_TEXT_BASE=0x600000
+CONFIG_TARGET_NSA325=y
+CONFIG_IDENT_STRING="\nZyxel NSA325 2-Bay Power Media Server"
+CONFIG_NR_DRAM_BANKS=2
+CONFIG_BOOTDELAY=3
+CONFIG_SYS_PROMPT="NSA325> "
+# CONFIG_CMD_IMLS is not set
+# CONFIG_CMD_FLASH is not set
+CONFIG_MVGBE=y
+CONFIG_MII=y
+CONFIG_SYS_NS16550=y
+CONFIG_CMD_FDT=y
+CONFIG_OF_LIBFDT=y
+CONFIG_CMD_SETEXPR=y
+CONFIG_CMD_DHCP=y
+CONFIG_CMD_MII=y
+CONFIG_CMD_PING=y
+CONFIG_CMD_DNS=y
+CONFIG_CMD_SNTP=y
+CONFIG_CMD_USB=y
+CONFIG_USB=y
+CONFIG_CMD_DATE=y
+CONFIG_CMD_EXT2=y
+CONFIG_CMD_EXT4=y
+CONFIG_CMD_FAT=y
+CONFIG_CMD_JFFS2=y
+CONFIG_MTD=y
+CONFIG_MTD_RAW_NAND=y
+CONFIG_MTDPARTS_DEFAULT="mtdparts=orion_nand:0x0c0000(uboot),0x80000(uboot_env),0x7ec0000(ubi)"
+CONFIG_CMD_MTDPARTS=y
+CONFIG_CMD_ENV=y
+CONFIG_CMD_NAND=y
+CONFIG_EFI_PARTITION=y
+CONFIG_ENV_IS_IN_NAND=y
+CONFIG_ENV_SIZE=0x20000
+CONFIG_ENV_OFFSET=0xC0000
+CONFIG_ENV_SECT_SIZE=0x20000
+CONFIG_ENV_ADDR=0xC0000
+CONFIG_CMD_UBI=y
+CONFIG_USB_EHCI_HCD=y
+CONFIG_USB_STORAGE=y
+CONFIG_LZMA=y
+CONFIG_LZO=y
+CONFIG_SYS_LONGHELP=y
--- /dev/null
+++ b/include/configs/nsa325.h
@@ -0,0 +1,106 @@
+/*
+ * (C) Copyright 2016 bodhi <<EMAIL>>
+ *
+ * Based on
+ * Copyright (C) 2014 Jason Plum <<EMAIL>>
+ * Based on
+ * Copyright (C) 2012  Peter Schildmann <<EMAIL>>
+ *
+ * Based on guruplug.h originally written by
+ * Siddarth Gore <<EMAIL>>
+ * (C) Copyright 2009
+ * Marvell Semiconductor <www.marvell.com>
+ *
+ * See file CREDITS for list of people who contributed to this
+ * project.
+ *
+ * This program is free software; you can redistribute it and/or
+ * modify it under the terms of the GNU General Public License as
+ * published by the Free Software Foundation; either version 2 of
+ * the License, or (at your option) any later version.
+ *
+ * This program is distributed in the hope that it will be useful,
+ * but WITHOUT ANY WARRANTY; without even the implied warranty of
+ * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
+ * GNU General Public License for more details.
+ *
+ * You should have received a copy of the GNU General Public License
+ * along with this program; if not, write to the Free Software
+ * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston,
+ * MA 02110-1301 USA
+ */
+
+#ifndef _CONFIG_NSA325_H
+#define _CONFIG_NSA325_H
+
+/*
+ * High Level Configuration Options (easy to change)
+ */
+#define CONFIG_FEROCEON_88FR131	1	/* CPU Core subversion */
+#define CONFIG_KW88F6281	1	/* SOC Name */
+
+#define CONFIG_SKIP_LOWLEVEL_INIT	/* disable board lowlevel_init */
+
+/*
+ * Misc Configuration Options
+ */
+#define CONFIG_SHOW_BOOT_PROGRESS 1	/* boot progess display (LED's) */
+
+/*
+ * Commands configuration
+ */
+#define CONFIG_PREBOOT
+
+/*
+ * mv-common.h should be defined after CMD configs since it used them
+ * to enable certain macros
+ */
+#include "mv-common.h"
+
+/*
+ * Default environment variables
+ */
+#define CONFIG_BOOTCOMMAND \
+	"ubi part ubi; " \
+	"ubi read 0x800000 kernel; " \
+	"bootm 0x800000"
+
+#define CONFIG_EXTRA_ENV_SETTINGS \
+	"console=console=ttyS0,115200\0"	\
+	"mtdids=nand0=orion_nand\0"		\
+	"mtdparts="CONFIG_MTDPARTS_DEFAULT "\0"	\
+	"bootargs_root=\0"
+
+/*
+ * Ethernet Driver configuration
+ */
+#ifdef CONFIG_CMD_NET
+#define CONFIG_MVGBE_PORTS		{1, 0}	/* enable port 0 only */
+#define CONFIG_PHY_BASE_ADR		0x1
+#define CONFIG_NETCONSOLE
+#endif /* CONFIG_CMD_NET */
+
+/*
+ * SATA Driver configuration
+ */
+#ifdef CONFIG_MVSATA_IDE
+#define CONFIG_SYS_ATA_IDE0_OFFSET      MV_SATA_PORT0_OFFSET
+#define CONFIG_SYS_ATA_IDE1_OFFSET      MV_SATA_PORT1_OFFSET
+#endif /* CONFIG_MVSATA_IDE */
+
+/*
+ * File system
+ */
+#define CONFIG_JFFS2_NAND
+#define CONFIG_JFFS2_LZO
+
+/*
+ *  Date Time
+ */
+#ifdef CONFIG_CMD_DATE
+#define CONFIG_RTC_MV
+#endif /* CONFIG_CMD_DATE */
+
+#define CONFIG_KIRKWOOD_GPIO /* Enable GPIO Support */
+
+#endif /* _CONFIG_NSA325_H */
