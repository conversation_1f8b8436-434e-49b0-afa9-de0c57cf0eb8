--- a/arch/arm/mach-kirkwood/Kconfig
+++ b/arch/arm/mach-kirkwood/Kconfig
@@ -25,6 +25,9 @@ config TARGET_LSXL
 config TARGET_POGO_E02
 	bool "pogo_e02 Board"
 
+config TARGET_POGOPLUGV4
+    bool "Pogoplug V4 Board"
+
 config TARGET_DNS325
 	bool "dns325 Board"
 
@@ -83,6 +86,7 @@ source "board/Marvell/guruplug/Kconfig"
 source "board/Marvell/sheevaplug/Kconfig"
 source "board/buffalo/lsxl/Kconfig"
 source "board/cloudengines/pogo_e02/Kconfig"
+source "board/cloudengines/pogoplugv4/Kconfig"
 source "board/d-link/dns325/Kconfig"
 source "board/iomega/iconnect/Kconfig"
 source "board/keymile/Kconfig"
--- a/arch/arm/mach-kirkwood/include/mach/kw88f6192.h
+++ b/arch/arm/mach-kirkwood/include/mach/kw88f6192.h
@@ -15,6 +15,6 @@
 #define KW_REGS_PHY_BASE		KW88F6192_REGS_PHYS_BASE
 
 /* TCLK Core Clock defination */
-#define CONFIG_SYS_TCLK	  166000000 /* 166MHz */
+#define CONFIG_SYS_TCLK	  166666667 /* 166MHz */
 
 #endif /* _CONFIG_KW88F6192_H */
--- a/arch/arm/mach-kirkwood/include/mach/mpp.h
+++ b/arch/arm/mach-kirkwood/include/mach/mpp.h
@@ -216,10 +216,12 @@
 #define MPP33_GPIO		MPP( 33, 0x0, 1, 1, 0,   1,   1,   1    )
 #define MPP33_TDM_DTX		MPP( 33, 0x2, 0, 1, 0,   0,   1,   1    )
 #define MPP33_GE1_13		MPP( 33, 0x3, 0, 0, 0,   1,   1,   1    )
+#define MPP33_SATA1_ACTn        MPP( 33, 0x5, 0, 1, 0,   1,   1,   1    )
 
 #define MPP34_GPIO		MPP( 34, 0x0, 1, 1, 0,   1,   1,   1    )
 #define MPP34_TDM_SPI_CS1	MPP( 34, 0x2, 0, 1, 0,   0,   1,   1    )
 #define MPP34_GE1_14		MPP( 34, 0x3, 0, 0, 0,   1,   1,   1    )
+#define MPP34_SATA1_ACTn       MPP( 34, 0x5, 0, 1, 0,   1,   1,   1    )
 
 #define MPP35_GPIO		MPP( 35, 0x0, 1, 1, 1,   1,   1,   1    )
 #define MPP35_TDM_CH0_TX_QL	MPP( 35, 0x2, 0, 1, 0,   0,   1,   1    )
--- /dev/null
+++ b/board/cloudengines/pogoplugv4/Kconfig
@@ -0,0 +1,12 @@
+if TARGET_POGOPLUGV4
+
+config SYS_BOARD
+	default "pogoplugv4"
+
+config SYS_VENDOR
+	default "cloudengines"
+
+config SYS_CONFIG_NAME
+	default "pogoplugv4"
+
+endif
--- /dev/null
+++ b/board/cloudengines/pogoplugv4/MAINTAINERS
@@ -0,0 +1,6 @@
+POGOPLUGV4 BOARD
+M:	Alberto Bursi <<EMAIL>>
+S:	Maintained
+F:	board/cloudengines/pogoplugv4/
+F:	include/configs/pogoplugv4.h
+F:	configs/pogoplugv4_defconfig
--- /dev/null
+++ b/board/cloudengines/pogoplugv4/Makefile
@@ -0,0 +1,11 @@
+#
+# <AUTHOR> <EMAIL>
+#
+# Based on
+# Marvell Semiconductor <www.marvell.com>
+# <AUTHOR> <EMAIL>
+#
+# SPDX-License-Identifier:	GPL-2.0+
+#
+
+obj-y	:= pogoplugv4.o
--- /dev/null
+++ b/board/cloudengines/pogoplugv4/kwbimage.cfg
@@ -0,0 +1,167 @@
+#
+# Copyright (C) 2012
+# <AUTHOR> <EMAIL>
+#
+# Based on Kirkwood support:
+# (C) Copyright 2009
+# Marvell Semiconductor <www.marvell.com>
+# Written-by: Prafulla Wadaskar <prafulla <at> marvell.com>
+#
+# See file CREDITS for list of people who contributed to this
+# project.
+#
+# This program is free software; you can redistribute it and/or
+# modify it under the terms of the GNU General Public License as
+# published by the Free Software Foundation; either version 2 of
+# the License, or (at your option) any later version.
+#
+# This program is distributed in the hope that it will be useful,
+# but WITHOUT ANY WARRANTY; without even the implied warranty of
+# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
+# GNU General Public License for more details.
+#
+# You should have received a copy of the GNU General Public License
+# along with this program; If not, see <http://www.gnu.org/licenses/>.
+#
+# Refer docs/README.kwimage for more details about how-to configure
+# and create kirkwood boot image
+#
+
+# Boot Media configurations   (DONE)
+BOOT_FROM	nand
+NAND_ECC_MODE	default
+NAND_PAGE_SIZE	0x0800
+
+# SOC registers configuration using bootrom header extension
+# Maximum KWBIMAGE_MAX_CONFIG configurations allowed
+
+# Configure RGMII-0 interface pad voltage to 1.8V (SHOULD BE SAME)
+DATA 0xffd100e0 0x1b1b1b9b
+
+#Dram initalization for SINGLE x16 CL=3 @ 200MHz   (need CL=3 @ 200MHz?)
+DATA 0xffd01400 0x43000618	# DDR Configuration register
+# bit13-0:  0x200 (200 DDR2 clks refresh rate)
+# bit23-14: zero
+# bit24: 1= enable exit self refresh mode on DDR access
+# bit25: 1 required
+# bit29-26: zero
+# bit31-30: 01
+
+DATA 0xffd01404 0x34143000	# DDR Controller Control Low
+# bit 4:    0=addr/cmd in smame cycle
+# bit 5:    0=clk is driven during self refresh, we don't care for APX
+# bit 6:    0=use recommended falling edge of clk for addr/cmd
+# bit14:    0=input buffer always powered up
+# bit18:    1=cpu lock transaction enabled
+# bit23-20: 3=recommended value for CL=3 and STARTBURST_DEL disabled bit31=0
+# bit27-24: 6= CL+3, STARTBURST sample stages, for freqs 400MHz, unbuffered DIMM
+# bit30-28: 3 required
+# bit31:    0=no additional STARTBURST delay
+
+DATA 0xffd01408 0x11012227	# DDR Timing (Low) (active cycles value +1)
+# bit3-0:   TRAS lsbs
+# bit7-4:   TRCD
+# bit11- 8: TRP
+# bit15-12: TWR
+# bit19-16: TWTR
+# bit20:    TRAS msb
+# bit23-21: 0x0
+# bit27-24: TRRD
+# bit31-28: TRTP
+
+DATA 0xffd0140c 0x00000819	#  DDR Timing (High)
+# bit6-0:   TRFC
+# bit8-7:   TR2R
+# bit10-9:  TR2W
+# bit12-11: TW2W
+# bit31-13: zero required
+
+DATA 0xffd01410 0x00000001	#  DDR Address Control  (changed to Dockstar vals)
+# bit1-0:   00, Cs0width=x16
+# bit3-2:   10, Cs0size=512Mb
+# bit5-4:   00, Cs2width=nonexistent
+# bit7-6:   00, Cs1size =nonexistent
+# bit9-8:   00, Cs2width=nonexistent
+# bit11-10: 00, Cs2size =nonexistent
+# bit13-12: 00, Cs3width=nonexistent
+# bit15-14: 00, Cs3size =nonexistent
+# bit16:    0,  Cs0AddrSel
+# bit17:    0,  Cs1AddrSel
+# bit18:    0,  Cs2AddrSel
+# bit19:    0,  Cs3AddrSel
+# bit31-20: 0 required
+
+DATA 0xffd01414 0x00000000	#  DDR Open Pages Control
+# bit0:    0,  OpenPage enabled
+# bit31-1: 0 required
+
+DATA 0xffd01418 0x00000000	#  DDR Operation
+# bit3-0:   0x0, DDR cmd
+# bit31-4:  0 required
+
+DATA 0xffd0141c 0x00000632	#  DDR Mode
+# bit2-0:   2, BurstLen=2 required
+# bit3:     0, BurstType=0 required
+# bit6-4:   4, CL=5				(<===== change to CL=3 ?)
+# bit7:     0, TestMode=0 normal
+# bit8:     0, DLL reset=0 normal
+# bit11-9:  6, auto-precharge write recovery ????????????
+# bit12:    0, PD must be zero
+# bit31-13: 0 required
+
+DATA 0xffd01420 0x00000040	#  DDR Extended Mode
+# bit0:    0,  DDR DLL enabled
+# bit1:    0,  DDR drive strenght normal
+# bit2:    0,  DDR ODT control lsd (disabled)
+# bit5-3:  000, required
+# bit6:    1,  DDR ODT control msb, (disabled)
+# bit9-7:  000, required
+# bit10:   0,  differential DQS enabled
+# bit11:   0, required
+# bit12:   0, DDR output buffer enabled
+# bit31-13: 0 required
+
+DATA 0xffd01424 0x0000F07F	#  DDR Controller Control High
+# bit2-0:  111, required
+# bit3  :  1  , MBUS Burst Chop disabled
+# bit6-4:  111, required
+# bit7  :  0
+# bit8  :  0  , no sample stage
+# bit9  :  0  , no half clock cycle addition to dataout
+# bit10 :  0  , 1/4 clock cycle skew enabled for addr/ctl signals
+# bit11 :  0  , 1/4 clock cycle skew disabled for write mesh
+# bit15-12: 1111 required
+# bit31-16: 0    required
+
+DATA 0xffd01428 0x00085520	# DDR2 ODT Read Timing (default values)
+DATA 0xffd0147c 0x00008552	# DDR2 ODT Write Timing (default values)
+
+DATA 0xFFD01500 0x00000000	# CS[0]n Base address to 0x0
+DATA 0xFFD01504 0x07FFFFF1	# CS[0]n Size
+# bit0:    1,  Window enabled
+# bit1:    0,  Write Protect disabled
+# bit3-2:  00, CS0 hit selected
+# bit23-4: ones, required
+# bit31-24: 0x07, Size (i.e. 128MB)
+
+DATA 0xFFD0150C 0x00000000	# CS[1]n Size, window disabled
+DATA 0xFFD01514 0x00000000	# CS[2]n Size, window disabled
+DATA 0xFFD0151C 0x00000000	# CS[3]n Size, window disabled
+
+DATA 0xffd01494 0x00030000	#  DDR ODT Control (Low)		 (DONE)
+# bit3-0:  2, ODT0Rd, MODT[0] asserted during read from DRAM CS1
+# bit7-4:  1, ODT0Rd, MODT[0] asserted during read from DRAM CS0
+# bit19-16:2, ODT0Wr, MODT[0] asserted during write to DRAM CS1
+# bit23-20:1, ODT0Wr, MODT[0] asserted during write to DRAM CS0
+
+DATA 0xffd01498 0x00000000	#  DDR ODT Control (High)  (DONE)
+# bit1-0:  00, ODT0 controlled by ODT Control (low) register above
+# bit3-2:  01, ODT1 active NEVER!
+# bit31-4: zero, required
+
+DATA 0xffd0149c 0x0000e803	# CPU ODT Control	 (DONE)
+DATA 0xffd01480 0x00000001	# DDR Initialization Control	 (DONE)
+#bit0=1, enable DDR init upon this register write
+
+# End of Header extension
+DATA 0x0 0x0
--- /dev/null
+++ b/board/cloudengines/pogoplugv4/pogoplugv4.c
@@ -0,0 +1,223 @@
+/*
+ * Copyright (C) 2016 bodhi <<EMAIL>>
+ * Copyright (C) 2014 bodhi <<EMAIL>>
+ *
+ * Based on
+ *
+ * Copyright (C) 2014 <<EMAIL>>
+ *
+ * Copyright (C) 2012
+ * David Purdy <<EMAIL>>
+ *
+ * Based on Kirkwood support:
+ * (C) Copyright 2009
+ * Marvell Semiconductor <www.marvell.com>
+ * Written-by: Prafulla Wadaskar <<EMAIL>>
+ *
+ * See file CREDITS for list of people who contributed to this
+ * project.
+ *
+ * This program is free software; you can redistribute it and/or
+ * modify it under the terms of the GNU General Public License as
+ * published by the Free Software Foundation; either version 2 of
+ * the License, or (at your option) any later version.
+ *
+ * This program is distributed in the hope that it will be useful,
+ * but WITHOUT ANY WARRANTY; without even the implied warranty of
+ * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
+ * GNU General Public License for more details.
+ *
+ * You should have received a copy of the GNU General Public License
+ * along with this program; If not, see <http://www.gnu.org/licenses/>.
+ */
+
+#include <common.h>
+#include <miiphy.h>
+#include <asm/arch/cpu.h>
+#include <asm/arch/soc.h>
+#include <asm/arch/mpp.h>
+#include <asm/io.h>
+#include "pogoplugv4.h"
+#include <asm/arch/gpio.h>
+
+#ifdef CONFIG_KIRKWOOD_MMC
+#include <kirkwood_mmc.h>
+#endif /* CONFIG_KIRKWOOD_MMC */
+
+
+DECLARE_GLOBAL_DATA_PTR;
+
+int board_early_init_f(void)
+{
+	/*
+	 * default gpio configuration
+	 * There are maximum 64 gpios controlled through 2 sets of registers
+	 * the  below configuration configures mainly initial LED status
+	 */
+	mvebu_config_gpio(POGOPLUGV4_OE_VAL_LOW,
+			POGOPLUGV4_OE_VAL_HIGH,
+			POGOPLUGV4_OE_LOW, POGOPLUGV4_OE_HIGH);
+
+	/* Multi-Purpose Pins Functionality configuration */
+	u32 kwmpp_config[] = {
+		MPP0_NF_IO2,
+		MPP1_NF_IO3,
+		MPP2_NF_IO4,
+		MPP3_NF_IO5,
+		MPP4_NF_IO6,
+		MPP5_NF_IO7,
+		MPP6_SYSRST_OUTn,
+		MPP7_GPO,
+		MPP8_TW_SDA,
+		MPP9_TW_SCK,
+		MPP10_UART0_TXD,
+		MPP11_UART0_RXD,
+		MPP12_SD_CLK,
+		MPP13_SD_CMD,
+		MPP14_SD_D0,
+		MPP15_SD_D1,
+		MPP16_SD_D2,
+		MPP17_SD_D3,
+		MPP18_NF_IO0,
+		MPP19_NF_IO1,
+		MPP20_SATA1_ACTn,
+		MPP21_SATA0_ACTn,
+		MPP22_GPIO,	/* Green LED */
+		MPP23_GPIO,
+		MPP24_GPIO,	/* Red LED */
+		MPP25_GPIO,
+		MPP26_GPIO,
+		MPP27_GPIO,
+		MPP28_GPIO,
+		MPP29_GPIO,	/* Eject button */
+		MPP30_GPIO,
+		MPP31_GPIO,
+		MPP32_GPIO,
+		MPP33_GPIO,
+		MPP34_GPIO,
+		MPP35_GPIO,	/* FR6192 has only 36 GPIOs */
+		0
+	};
+	kirkwood_mpp_conf(kwmpp_config, NULL);
+
+	return 0;
+}
+
+int board_init(void)
+{
+	/* Boot parameters address */
+	gd->bd->bi_boot_params = mvebu_sdram_bar(0) + 0x100;
+
+        kw_gpio_set_valid(20, 1);
+        kw_gpio_set_valid(21, 1);
+        kw_gpio_set_valid(22, 1);
+        kw_gpio_set_valid(24, 1);
+
+	return 0;
+}
+
+#ifdef CONFIG_RESET_PHY_R
+/* Configure and initialize PHY */
+void reset_phy(void)
+{
+	u16 reg;
+	u16 devadr;
+	char *name = "egiga0";
+
+	if (miiphy_set_current_dev(name))
+		return;
+
+	/* command to read PHY dev address */
+	if (miiphy_read(name, 0xEE, 0xEE, (u16 *) &devadr)) {
+		printf("Err..(%s) could not read PHY dev address\n", __func__);
+		return;
+	}
+
+	/*
+	 * Enable RGMII delay on Tx and Rx for CPU port
+	 * Ref: sec 4.7.2 of chip datasheet
+	 */
+	miiphy_write(name, devadr, MV88E1116_PGADR_REG, 2);
+	miiphy_read(name, devadr, MV88E1116_MAC_CTRL_REG, &reg);
+	reg |= (MV88E1116_RGMII_RXTM_CTRL | MV88E1116_RGMII_TXTM_CTRL);
+	miiphy_write(name, devadr, MV88E1116_MAC_CTRL_REG, reg);
+	miiphy_write(name, devadr, MV88E1116_PGADR_REG, 0);
+
+	/* reset the phy */
+	miiphy_reset(name, devadr);
+
+	debug("88E1116 Initialized on %s\n", name);
+}
+#endif /* CONFIG_RESET_PHY_R */
+
+#ifdef CONFIG_KIRKWOOD_MMC
+int board_mmc_init(bd_t *bis)
+{
+       kw_mmc_initialize(bis);
+       return 0;
+}
+#endif /* CONFIG_KIRKWOOD_MMC */
+
+
+#define GREEN_LED	(1 << 22)
+#define RED_LED		(1 << 24)
+#define BOTH_LEDS	(GREEN_LED | RED_LED)
+#define NEITHER_LED	0
+
+static void set_leds(u32 leds, u32 blinking)
+{
+	struct kwgpio_registers *r;
+	u32 oe;
+	u32 bl;
+
+	r = (struct kwgpio_registers *)MVEBU_GPIO0_BASE;
+	oe = readl(&r->oe) | BOTH_LEDS;
+	writel(oe & ~leds, &r->oe);	/* active low */
+	bl = readl(&r->blink_en) & ~BOTH_LEDS;
+	writel(bl | blinking, &r->blink_en);
+}
+
+void show_boot_progress(int val)
+{
+	switch (val) {
+	case BOOTSTAGE_ID_RUN_OS:		/* booting Linux */
+		set_leds(BOTH_LEDS, NEITHER_LED);
+		break;
+	case BOOTSTAGE_ID_NET_ETH_START:	/* Ethernet initialization */
+		set_leds(GREEN_LED, GREEN_LED);
+		break;
+	default:
+		if (val < 0)	/* error */
+			set_leds(RED_LED, RED_LED);
+		break;
+	}
+}
+
+#if defined(CONFIG_KIRKWOOD_GPIO)
+/* Return GPIO button status */
+/*
+un-pressed:
+ gpio-29 (Eject Button ) in hi (act lo) - IRQ edge (clear )
+pressed
+ gpio-29 (Eject Button ) in lo (act hi) - IRQ edge (clear )
+*/
+
+static int
+do_read_button(cmd_tbl_t *cmdtp, int flag, int argc, char * const argv[])
+{
+	if (strcmp(argv[1], "eject") == 0) {
+			kw_gpio_set_valid(BTN_EJECT, GPIO_INPUT_OK);
+			kw_gpio_direction_input(BTN_EJECT);
+			return kw_gpio_get_value(BTN_EJECT);
+	}
+	else
+		return -1;
+}
+
+
+U_BOOT_CMD(button, 2, 0, do_read_button,
+	   "Return GPIO button status 0=off 1=on",
+	   "- button eject: test buttons states\n"
+);
+
+#endif
--- /dev/null
+++ b/board/cloudengines/pogoplugv4/pogoplugv4.h
@@ -0,0 +1,50 @@
+/*
+ * Copyright (C) 2016
+ * bodhi <<EMAIL>>
+ *
+ * Copyright (C) 2012
+ * David Purdy <<EMAIL>>
+ *
+ * Based on Kirkwood support:
+ * (C) Copyright 2009
+ * Marvell Semiconductor <www.marvell.com>
+ * Written-by: Prafulla Wadaskar <<EMAIL>>
+ *
+ * See file CREDITS for list of people who contributed to this
+ * project.
+ *
+ * This program is free software; you can redistribute it and/or
+ * modify it under the terms of the GNU General Public License as
+ * published by the Free Software Foundation; either version 2 of
+ * the License, or (at your option) any later version.
+ *
+ * This program is distributed in the hope that it will be useful,
+ * but WITHOUT ANY WARRANTY; without even the implied warranty of
+ * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
+ * GNU General Public License for more details.
+ *
+ * You should have received a copy of the GNU General Public License
+ * along with this program; If not, see <http://www.gnu.org/licenses/>.
+ */
+
+#ifndef __POGOPLUGV4_H
+#define __POGOPLUGV4_H
+
+/* GPIO configuration */
+#define POGOPLUGV4_OE_LOW				(~(0))
+#define POGOPLUGV4_OE_HIGH				(~(0))
+#define POGOPLUGV4_OE_VAL_LOW			(1 << 29)
+#define POGOPLUGV4_OE_VAL_HIGH			0
+
+/* PHY related */
+#define MV88E1116_LED_FCTRL_REG			10
+#define MV88E1116_CPRSP_CR3_REG			21
+#define MV88E1116_MAC_CTRL_REG			21
+#define MV88E1116_PGADR_REG			22
+#define MV88E1116_RGMII_TXTM_CTRL		(1 << 4)
+#define MV88E1116_RGMII_RXTM_CTRL		(1 << 5)
+
+/* button */
+#define BTN_EJECT				29
+
+#endif /* __POGOPLUGV4_H */
--- /dev/null
+++ b/configs/pogoplugv4_defconfig
@@ -0,0 +1,51 @@
+CONFIG_ARM=y
+CONFIG_SYS_DCACHE_OFF=y
+CONFIG_ARCH_CPU_INIT=y
+CONFIG_KIRKWOOD=y
+CONFIG_SYS_TEXT_BASE=0x600000
+CONFIG_TARGET_POGOPLUGV4=y
+CONFIG_SYS_PROMPT="pogoplugv4> "
+CONFIG_IDENT_STRING="\nPogoplug V4"
+CONFIG_NR_DRAM_BANKS=2
+CONFIG_BOOTDELAY=3
+# CONFIG_CMD_IMLS is not set
+# CONFIG_CMD_FLASH is not set
+CONFIG_MVGBE=y
+CONFIG_MII=y
+CONFIG_SYS_NS16550=y
+CONFIG_CMD_FDT=y
+CONFIG_OF_LIBFDT=y
+CONFIG_OF_BOOTZ=y
+CONFIG_CMD_SETEXPR=y
+CONFIG_CMD_DHCP=y
+CONFIG_CMD_MII=y
+CONFIG_CMD_PING=y
+CONFIG_CMD_DNS=y
+CONFIG_CMD_SNTP=y
+CONFIG_CMD_USB=y
+CONFIG_CMD_DATE=y
+CONFIG_CMD_EXT2=y
+CONFIG_CMD_EXT4=y
+CONFIG_CMD_FAT=y
+CONFIG_CMD_JFFS2=y
+CONFIG_MTD=y
+CONFIG_MTD_RAW_NAND=y
+CONFIG_MTDPARTS_DEFAULT="mtdparts=orion_nand:0x1c0000(uboot),0x40000(uboot_env),0x7e00000(ubi)"
+CONFIG_CMD_MTDPARTS=y
+CONFIG_CMD_ENV=y
+CONFIG_CMD_NAND=y
+CONFIG_CMD_MMC=y
+CONFIG_CMD_GPIO=y
+CONFIG_EFI_PARTITION=y
+CONFIG_ENV_IS_IN_NAND=y
+CONFIG_ENV_SIZE=0x20000
+CONFIG_ENV_OFFSET=0x1C0000
+CONFIG_ENV_SECT_SIZE=0x20000
+CONFIG_ENV_ADDR=0x1C0000
+CONFIG_CMD_UBI=y
+CONFIG_USB=y
+CONFIG_USB_EHCI_HCD=y
+CONFIG_USB_STORAGE=y
+CONFIG_LZMA=y
+CONFIG_LZO=y
+CONFIG_SYS_LONGHELP=y
--- a/drivers/gpio/kw_gpio.c
+++ b/drivers/gpio/kw_gpio.c
@@ -147,3 +147,36 @@ void kw_gpio_set_blink(unsigned pin, int
 	/* Set blinking. */
 	__set_blinking(pin, blink);
 }
+
+
+/*
+ *  Hooks to  GENERIC_GPIO primitives
+ */
+
+int gpio_direction_input(unsigned pin)
+{
+       return kw_gpio_direction_input(pin);
+}
+
+int gpio_direction_output(unsigned pin, int value)
+{
+        return kw_gpio_direction_output(pin, value);
+}
+
+void gpio_set_value(unsigned pin, int value) {
+       kw_gpio_set_value(pin, value);
+}
+
+int gpio_get_value(unsigned pin) {
+       return kw_gpio_get_value(pin);
+}
+
+int gpio_request(unsigned gpio, const char *label)
+{
+       return 0;
+}
+
+int gpio_free(unsigned gpio)
+{
+       return 0;
+}
--- a/drivers/mmc/Makefile
+++ b/drivers/mmc/Makefile
@@ -66,6 +66,7 @@ obj-$(CONFIG_MMC_SDHCI_TANGIER)		+= tang
 obj-$(CONFIG_MMC_SDHCI_TEGRA)		+= tegra_mmc.o
 obj-$(CONFIG_MMC_SDHCI_XENON)		+= xenon_sdhci.o
 obj-$(CONFIG_MMC_SDHCI_ZYNQ)		+= zynq_sdhci.o
+obj-$(CONFIG_KIRKWOOD_MMC)		+= kirkwood_mmc.o
 
 obj-$(CONFIG_MMC_SUNXI)			+= sunxi_mmc.o
 obj-$(CONFIG_MMC_UNIPHIER)		+= tmio-common.o uniphier-sd.o
--- /dev/null
+++ b/drivers/mmc/kirkwood_mmc.c
@@ -0,0 +1,482 @@
+/*
+ * (C) Copyright 2014 bodhi <<EMAIL>>
+ *
+ * Based on
+ *
+ * (C) Copyright 2014 <<EMAIL>>
+ *
+ * Based on
+ *
+ * Driver for Marvell SDIO/MMC controller
+ *
+ * (C) Copyright 2012
+ * Marvell Semiconductor <www.marvell.com>
+ * Written-by: Gérald Kerma <uboot at doukki.net>
+ * See file CREDITS for list of people who contributed to this
+ * project.
+ *
+ * This program is free software; you can redistribute it and/or
+ * modify it under the terms of the GNU General Public License as
+ * published by the Free Software Foundation; either version 2 of
+ * the License, or (at your option) any later version.
+ *
+ * This program is distributed in the hope that it will be useful,
+ * but WITHOUT ANY WARRANTY; without even the implied warranty of
+ * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
+ * GNU General Public License for more details.
+ *
+ * You should have received a copy of the GNU General Public License
+ * along with this program; if not, write to the Free Software
+ * Foundation, Inc., 59 Temple Place, Suite 330, Boston,
+ * MA 02111-1307 USA
+ */
+
+#include <common.h>
+#include <malloc.h>
+#include <part.h>
+#include <mmc.h>
+#include <asm/io.h>
+#include <asm/arch/cpu.h>
+#include <asm/arch/soc.h>
+
+#include <kirkwood_mmc.h>
+
+#define DRIVER_NAME	"kwsdio"
+
+static int kw_mmc_setup_data(struct mmc_data *data)
+{
+	u32 ctrl_reg;
+
+#ifdef DEBUG
+	printf("%s, data %s : blocks=%d blksz=%d\n", DRIVER_NAME,
+		(data->flags & MMC_DATA_READ) ? "read" : "write",
+		data->blocks, data->blocksize);
+#endif
+
+	/* default to maximum timeout */
+	ctrl_reg = kwsd_read(SDIO_HOST_CTRL);
+	ctrl_reg |= SDIO_HOST_CTRL_TMOUT(SDIO_HOST_CTRL_TMOUT_MAX);
+	kwsd_write(SDIO_HOST_CTRL, ctrl_reg);
+
+	if (data->flags & MMC_DATA_READ) {
+		kwsd_write(SDIO_SYS_ADDR_LOW,(u32)data->dest & 0xffff);
+		kwsd_write(SDIO_SYS_ADDR_HI,(u32)data->dest >> 16);
+	} else {
+		kwsd_write(SDIO_SYS_ADDR_LOW,(u32)data->src & 0xffff);
+		kwsd_write(SDIO_SYS_ADDR_HI,(u32)data->src >> 16);
+	}
+
+	kwsd_write(SDIO_BLK_COUNT, data->blocks);
+	kwsd_write(SDIO_BLK_SIZE, data->blocksize);
+
+	return 0;
+}
+
+static int kw_mmc_send_cmd(struct mmc *mmc, struct mmc_cmd *cmd, struct mmc_data *data)
+{
+	int	timeout = 10;
+	int err;
+	ushort waittype = 0;
+	ushort resptype = 0;
+	ushort xfertype = 0;
+	ushort resp_indx = 0;
+
+#ifdef CONFIG_MMC_DEBUG
+	printf("cmdidx [0x%x] resp_type[0x%x] cmdarg[0x%x]\n", cmd->cmdidx, cmd->resp_type, cmd->cmdarg);
+#endif
+
+	udelay(10000);
+
+#ifdef CONFIG_MMC_DEBUG
+	printf("%s: cmd %d (hw state 0x%04x)\n", DRIVER_NAME, cmd->cmdidx, kwsd_read(SDIO_HW_STATE));
+#endif
+
+	/* Checking if card is busy */
+	while ((kwsd_read(SDIO_HW_STATE) & CARD_BUSY)) {
+		if (timeout == 0) {
+			printf("%s: card busy!\n", DRIVER_NAME);
+			return -1;
+		}
+		timeout--;
+		udelay(1000);
+	}
+
+	/* Set up for a data transfer if we have one */
+	if (data) {
+		if ((err = kw_mmc_setup_data(data)))
+			return err;
+	}
+
+	resptype = SDIO_CMD_INDEX(cmd->cmdidx);
+
+	/* Analyzing resptype/xfertype/waittype for the command */
+	if (cmd->resp_type & MMC_RSP_BUSY)
+		resptype |= SDIO_CMD_RSP_48BUSY;
+	else if (cmd->resp_type & MMC_RSP_136)
+		resptype |= SDIO_CMD_RSP_136;
+	else if (cmd->resp_type & MMC_RSP_PRESENT)
+		resptype |= SDIO_CMD_RSP_48;
+	else
+		resptype |= SDIO_CMD_RSP_NONE;
+
+	if (cmd->resp_type & MMC_RSP_CRC)
+		resptype |= SDIO_CMD_CHECK_CMDCRC;
+
+	if (cmd->resp_type & MMC_RSP_OPCODE)
+		resptype |= SDIO_CMD_INDX_CHECK;
+
+	if (cmd->resp_type & MMC_RSP_PRESENT) {
+		resptype |= SDIO_UNEXPECTED_RESP;
+		waittype |= SDIO_NOR_UNEXP_RSP;
+	}
+
+	if (data) {
+		resptype |= SDIO_CMD_DATA_PRESENT | SDIO_CMD_CHECK_DATACRC16;
+		xfertype |= SDIO_XFER_MODE_HW_WR_DATA_EN;
+		if (data->flags & MMC_DATA_READ) {
+			xfertype |= SDIO_XFER_MODE_TO_HOST;
+			waittype = SDIO_NOR_DMA_INI;
+		} else
+			waittype |= SDIO_NOR_XFER_DONE;
+	} else
+		waittype |= SDIO_NOR_CMD_DONE;
+
+	/* Setting cmd arguments */
+	kwsd_write(SDIO_ARG_LOW, cmd->cmdarg & 0xffff);
+	kwsd_write(SDIO_ARG_HI, cmd->cmdarg >> 16);
+
+	/* Setting Xfer mode */
+	kwsd_write(SDIO_XFER_MODE, xfertype);
+
+	kwsd_write(SDIO_NOR_INTR_STATUS, ~SDIO_NOR_CARD_INT);
+	kwsd_write(SDIO_ERR_INTR_STATUS, SDIO_POLL_MASK);
+
+	/* Sending command */
+	kwsd_write(SDIO_CMD, resptype);
+/*
+	kwsd_write(SDIO_CMD, KW_MMC_MAKE_CMD(cmd->cmdidx, resptype));
+*/
+
+	kwsd_write(SDIO_NOR_INTR_EN, SDIO_POLL_MASK);
+	kwsd_write(SDIO_ERR_INTR_EN, SDIO_POLL_MASK);
+
+	/* Waiting for completion */
+	timeout = 1000000;
+
+	while (!((kwsd_read(SDIO_NOR_INTR_STATUS)) & waittype)) {
+		if (kwsd_read(SDIO_NOR_INTR_STATUS) & SDIO_NOR_ERROR) {
+#ifdef DEBUG
+			printf("%s: kw_mmc_send_cmd: error! cmdidx : %d, err reg: %04x\n", DRIVER_NAME, cmd->cmdidx,
+wsd_read(SDIO_ERR_INTR_STATUS));
+#endif
+			if (kwsd_read(SDIO_ERR_INTR_STATUS) & (SDIO_ERR_CMD_TIMEOUT | SDIO_ERR_DATA_TIMEOUT)) {
+				return -ETIMEDOUT;
+			}
+			return -ECOMM;
+		}
+
+		timeout--;
+		udelay(1);
+		if (timeout <= 0) {
+			printf("%s: command timed out\n", DRIVER_NAME);
+			return -ETIMEDOUT;
+		}
+	}
+
+	/* Handling response */
+	if (cmd->resp_type & MMC_RSP_136) {
+		uint response[8];
+		for (resp_indx = 0; resp_indx < 8; resp_indx++)
+			response[resp_indx] = kwsd_read(SDIO_RSP(resp_indx));
+
+		cmd->response[0] =		((response[0] & 0x03ff) << 22) |
+								((response[1] & 0xffff) << 6) |
+								((response[2] & 0xfc00) >> 10);
+		cmd->response[1] =		((response[2] & 0x03ff) << 22) |
+								((response[3] & 0xffff) << 6) |
+								((response[4] & 0xfc00) >> 10);
+		cmd->response[2] =		((response[4] & 0x03ff) << 22) |
+								((response[5] & 0xffff) << 6) |
+								((response[6] & 0xfc00) >> 10);
+		cmd->response[3] =		((response[6] & 0x03ff) << 22) |
+								((response[7] & 0x3fff) << 8);
+	} else if (cmd->resp_type & MMC_RSP_PRESENT) {
+		uint response[3];
+		for (resp_indx = 0; resp_indx < 3; resp_indx++)
+			response[resp_indx] = kwsd_read(SDIO_RSP(resp_indx));
+
+		cmd->response[0] = 		((response[2] & 0x003f) << (8 - 8))		|
+								((response[1] & 0xffff) << (14 - 8))	|
+								((response[0] & 0x03ff) << (30 - 8));
+		cmd->response[1] = 		((response[0] & 0xfc00) >> 10);
+		cmd->response[2] =		0;
+		cmd->response[3] =		0;
+	}
+
+#ifdef CONFIG_MMC_DEBUG
+	printf("%s: resp[0x%x] ", DRIVER_NAME, cmd->resp_type);
+	printf("[0x%x] ", cmd->response[0]);
+	printf("[0x%x] ", cmd->response[1]);
+	printf("[0x%x] ", cmd->response[2]);
+	printf("[0x%x] ", cmd->response[3]);
+	printf("\n");
+#endif
+
+	return 0;
+}
+
+#if 0
+/* Disable these three functions as they are not used anyway */
+
+static void kwsd_power_up(void)
+{
+#ifdef DEBUG
+	printf("%s: power up\n", DRIVER_NAME);
+#endif
+	/* disable interrupts */
+	kwsd_write(SDIO_NOR_INTR_EN, 0);
+	kwsd_write(SDIO_ERR_INTR_EN, 0);
+
+	/* SW reset */
+	kwsd_write(SDIO_SW_RESET, SDIO_SW_RESET_NOW);
+
+	kwsd_write(SDIO_XFER_MODE, 0);
+
+	/* enable status */
+	kwsd_write(SDIO_NOR_STATUS_EN, SDIO_POLL_MASK);
+	kwsd_write(SDIO_ERR_STATUS_EN, SDIO_POLL_MASK);
+
+	/* enable interrupts status */
+	kwsd_write(SDIO_NOR_INTR_STATUS, SDIO_POLL_MASK);
+	kwsd_write(SDIO_ERR_INTR_STATUS, SDIO_POLL_MASK);
+}
+
+static void kwsd_power_down(void)
+{
+#ifdef DEBUG
+	printf("%s: power down\n", DRIVER_NAME);
+#endif
+	/* disable interrupts */
+	kwsd_write(SDIO_NOR_INTR_EN, 0);
+	kwsd_write(SDIO_ERR_INTR_EN, 0);
+
+	/* SW reset */
+	kwsd_write(SDIO_SW_RESET, SDIO_SW_RESET_NOW);
+
+	kwsd_write(SDIO_XFER_MODE, SDIO_XFER_MODE_STOP_CLK);
+
+	/* disable status */
+	kwsd_write(SDIO_NOR_STATUS_EN, 0);
+	kwsd_write(SDIO_ERR_STATUS_EN, 0);
+
+	/* enable interrupts status */
+	kwsd_write(SDIO_NOR_INTR_STATUS, SDIO_POLL_MASK);
+	kwsd_write(SDIO_ERR_INTR_STATUS, SDIO_POLL_MASK);
+}
+
+static u32 kw_mmc_get_base_clock(void)
+{
+	/* Original version did a check for board device id and revision id
+	 * and assigned one of these clocks:
+	 *   KW_MMC_BASE_FAST_CLK_100 (revid == 0 && devid != MV88F6282_DEV_ID)
+	 *   KW_MMC_BASE_FAST_CLK_200 (revid != 0 || devid == MV88F6282_DEV_ID)
+	 * However, this check was disabled and
+	 *   KW_MMC_BASE_FAST_CLOCK
+	 * was returned in every case.
+	 * Therefore, all of the dead logic was removed. */
+	return KW_MMC_BASE_FAST_CLOCK;
+}
+#endif /* #if 0 */
+
+static inline u32 kw_mmc_get_base_clock(void)
+{
+	/* get MMC base clock. If any logic other than just returning
+	 * a fixed value is ever used, remove inline modifier. */
+
+	/* Possible values:
+	 *  - KW_MMC_BASE_FAST_CLOCK   (166 MHz)
+	 *  - KW_MMC_BASE_FAST_CLK_100 (100 MHz)
+	 *  - KW_MMC_BASE_FAST_CLK_200 (200 MHz)
+	 *
+	 * Tests have shown that 200 MHz is more reliable than
+	 * 166 MHz, so this value is used. */
+	return KW_MMC_BASE_FAST_CLK_200;
+}
+
+static void kw_mmc_set_clk(unsigned int clock)
+{
+	unsigned int m;
+
+	if (clock == 0) {
+#ifdef DEBUG
+		printf("%s: clock off\n", DRIVER_NAME);
+#endif
+		kwsd_write(SDIO_XFER_MODE, SDIO_XFER_MODE_STOP_CLK);
+		kwsd_write(SDIO_CLK_DIV, KW_MMC_BASE_DIV_MAX);
+	} else {
+		m = kw_mmc_get_base_clock() / (2 * clock) - 1;
+		if (m > KW_MMC_BASE_DIV_MAX)
+			m = KW_MMC_BASE_DIV_MAX;
+#ifdef DEBUG
+		printf("%s: kw_mmc_set_clk: base = %d dividor = 0x%x clock=%d\n", DRIVER_NAME,
+w_mmc_get_base_clock(), m, clock);
+#endif
+		kwsd_write(SDIO_CLK_DIV, m & KW_MMC_BASE_DIV_MAX);
+	}
+	udelay(10000);
+}
+
+static void kw_mmc_set_bus(unsigned int bus)
+{
+	u32 ctrl_reg = 0;
+
+	ctrl_reg = kwsd_read(SDIO_HOST_CTRL);
+	ctrl_reg &= ~SDIO_HOST_CTRL_DATA_WIDTH_4_BITS;
+
+	switch (bus) {
+		case 4:
+			ctrl_reg |= SDIO_HOST_CTRL_DATA_WIDTH_4_BITS;
+			break;
+		case 1:
+		default:
+			ctrl_reg |= SDIO_HOST_CTRL_DATA_WIDTH_1_BIT;
+	}
+	/* default transfer mode */
+	ctrl_reg |= SDIO_HOST_CTRL_BIG_ENDIAN;
+	ctrl_reg &= ~SDIO_HOST_CTRL_LSB_FIRST;
+
+	/* default to maximum timeout */
+	ctrl_reg |= SDIO_HOST_CTRL_TMOUT(SDIO_HOST_CTRL_TMOUT_MAX);
+
+	ctrl_reg |= SDIO_HOST_CTRL_PUSH_PULL_EN;
+
+	ctrl_reg |= SDIO_HOST_CTRL_CARD_TYPE_MEM_ONLY;
+
+	/*
+	 * The HI_SPEED_EN bit is causing trouble with many (but not all)
+	 * high speed SD, SDHC and SDIO cards.  Not enabling that bit
+	 * makes all cards work.  So let's just ignore that bit for now
+	 * and revisit this issue if problems for not enabling this bit
+	 * are ever reported.
+	 */
+#if 0
+	if (ios->timing == MMC_TIMING_MMC_HS ||
+	    ios->timing == MMC_TIMING_SD_HS)
+		ctrl_reg |= SDIO_HOST_CTRL_HI_SPEED_EN;
+#endif
+
+#ifdef DEBUG
+	printf("%s: ctrl 0x%04x: %s %s %s\n", DRIVER_NAME, ctrl_reg,
+		(ctrl_reg & SDIO_HOST_CTRL_PUSH_PULL_EN) ?
+			"push-pull" : "open-drain",
+		(ctrl_reg & SDIO_HOST_CTRL_DATA_WIDTH_4_BITS) ?
+			"4bit-width" : "1bit-width",
+		(ctrl_reg & SDIO_HOST_CTRL_HI_SPEED_EN) ?
+			"high-speed" : "");
+#endif
+
+	kwsd_write(SDIO_HOST_CTRL, ctrl_reg);
+	udelay(10000);
+}
+
+static void kw_mmc_set_ios(struct mmc *mmc)
+{
+#ifdef DEBUG
+	printf("%s: bus[%d] clock[%d]\n", DRIVER_NAME, mmc->bus_width, mmc->clock);
+#endif
+	kw_mmc_set_bus(mmc->bus_width);
+	kw_mmc_set_clk(mmc->clock);
+}
+
+static int kw_mmc_init(struct mmc *mmc)
+{
+#ifdef DEBUG
+	printf("%s: kw_mmc_init\n", DRIVER_NAME);
+#endif
+
+	/*
+	* Setting host parameters
+	* Initial Host Ctrl : Timeout : max , Normal Speed mode, 4-bit data mode
+	* Big Endian, SD memory Card, Push_pull CMD Line
+	*/
+	kwsd_write(SDIO_HOST_CTRL,
+		SDIO_HOST_CTRL_TMOUT(SDIO_HOST_CTRL_TMOUT_MAX) |
+		SDIO_HOST_CTRL_DATA_WIDTH_4_BITS |
+		SDIO_HOST_CTRL_BIG_ENDIAN |
+		SDIO_HOST_CTRL_PUSH_PULL_EN |
+		SDIO_HOST_CTRL_CARD_TYPE_MEM_ONLY);
+
+	kwsd_write(SDIO_CLK_CTRL, 0);
+
+	/* enable status */
+	kwsd_write(SDIO_NOR_STATUS_EN, SDIO_POLL_MASK);
+	kwsd_write(SDIO_ERR_STATUS_EN, SDIO_POLL_MASK);
+
+	/* disable interrupts */
+	kwsd_write(SDIO_NOR_INTR_EN, 0);
+	kwsd_write(SDIO_ERR_INTR_EN, 0);
+
+	/* SW reset */
+	kwsd_write(SDIO_SW_RESET, SDIO_SW_RESET_NOW);
+
+	udelay(10000);
+	return 0;
+}
+
+int kw_mmc_initialize(bd_t *bis)
+{
+	struct mmc *mmc = NULL;
+        struct mmc_config *cfg = NULL;
+        struct mmc_ops *ops = NULL;
+	char *name = NULL;
+
+#ifdef DEBUG
+	printf("%s: %s base_clock = %d\n", DRIVER_NAME, kirkwood_id(), kw_mmc_get_base_clock());
+#endif
+	mmc = malloc(sizeof(struct mmc));
+	if (!mmc)
+		return -1;
+        memset(mmc, 0, sizeof(*mmc));
+
+        cfg = malloc(sizeof(*cfg));
+        if (cfg == NULL)
+                return -1;
+        memset(cfg, 0, sizeof(*cfg));
+        mmc->cfg = cfg;   /* provided configuration */
+
+        ops = malloc(sizeof(*ops));
+        if (ops == NULL)
+                return -1;
+        memset(ops, 0, sizeof(*ops));
+        cfg->ops = ops;
+
+        name = malloc(sizeof(DRIVER_NAME)+1);
+        if (name == NULL)
+                return -1;
+        cfg->name = name;
+
+	sprintf(cfg->name, DRIVER_NAME);
+
+	ops->send_cmd	= kw_mmc_send_cmd;
+	ops->set_ios	= kw_mmc_set_ios;
+	ops->init	= kw_mmc_init;
+
+	cfg->voltages = MMC_VDD_32_33 | MMC_VDD_33_34;
+	cfg->host_caps = MMC_MODE_4BIT | MMC_MODE_HS;
+
+	cfg->f_min = kw_mmc_get_base_clock()/KW_MMC_BASE_DIV_MAX;
+	cfg->f_max = KW_MMC_CLOCKRATE_MAX;
+	cfg->b_max = CONFIG_SYS_MMC_MAX_BLK_COUNT;
+
+	mmc = mmc_create (cfg, NULL);
+
+        if (mmc == NULL) {
+		free(name);
+		free(ops);
+                free(cfg);
+	        printf("\nFailed to Initialize MMC\n");
+                return -1;
+        }
+
+	return 0;
+}
--- a/include/configs/mv-common.h
+++ b/include/configs/mv-common.h
@@ -75,4 +75,10 @@
 #define CONFIG_SYS_MAX_NAND_DEVICE     1
 #endif
 
+/*
+ * Kirkwood MMC
+ */
+#if defined(CONFIG_KIRKWOOD) && defined(CONFIG_CMD_MMC)
+#define CONFIG_SYS_MMC_BASE KW_SDIO_BASE
+#endif /* defined(CONFIG_KIRKWOOD) && defined(CONFIG_CMD_MMC) */
 #endif /* _MV_COMMON_H */
--- /dev/null
+++ b/include/configs/pogoplugv4.h
@@ -0,0 +1,113 @@
+/*
+ * Copyright (C) 2014-2016 bodhi <<EMAIL>>
+ * Based on
+ *
+ * Copyright (C) 2012
+ * David Purdy <<EMAIL>>
+ *
+ * Based on Kirkwood support:
+ * (C) Copyright 2009
+ * Marvell Semiconductor <www.marvell.com>
+ * Written-by: Prafulla Wadaskar <<EMAIL>>
+ *
+ * See file CREDITS for list of people who contributed to this
+ * project.
+ *
+ * This program is free software; you can redistribute it and/or
+ * modify it under the terms of the GNU General Public License as
+ * published by the Free Software Foundation; either version 2 of
+ * the License, or (at your option) any later version.
+ *
+ * This program is distributed in the hope that it will be useful,
+ * but WITHOUT ANY WARRANTY; without even the implied warranty of
+ * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
+ * GNU General Public License for more details.
+ *
+ * You should have received a copy of the GNU General Public License
+ * along with this program; If not, see <http://www.gnu.org/licenses/>.
+ */
+
+#ifndef _CONFIG_POGOPLUGV4_H
+#define _CONFIG_POGOPLUGV4_H
+
+/*
+ * Machine type definition and ID
+ */
+#define MACH_TYPE_POGOPLUGV4		3960
+#define CONFIG_MACH_TYPE		MACH_TYPE_POGOPLUGV4
+
+/*
+ * High Level Configuration Options (easy to change)
+ */
+#define CONFIG_FEROCEON_88FR131		/* #define CPU Core subversion */
+#define CONFIG_KW88F6192		/* SOC Name */
+#define CONFIG_SKIP_LOWLEVEL_INIT	/* disable board lowlevel_init */
+
+#define CONFIG_FEATURE_COMMAND_EDITING
+#define CONFIG_SYS_64BIT_LBA
+
+/*
+ * Commands configuration
+ */
+
+#define CONFIG_KIRKWOOD_GPIO
+#define CONFIG_PREBOOT
+
+/*
+ * mv-common.h should be defined after CMD configs since it used them
+ * to enable certain macros
+ */
+#include "mv-common.h"
+
+/*
+ * Default environment variables
+ */
+#define CONFIG_BOOTCOMMAND \
+	"usb reset ; " \
+	"fatload usb 0:1 0x2000000 initramfs.bin ; "\
+	"bootm 0x2000000 ; " \
+	"ubi part ubi; " \
+	"ubi read 0x800000 kernel; " \
+	"bootm 0x800000"
+
+#define CONFIG_EXTRA_ENV_SETTINGS \
+	"console=console=ttyS0,115200\0"	\
+	"mtdids=nand0=orion_nand\0"		\
+	"mtdparts="CONFIG_MTDPARTS_DEFAULT "\0"	\
+	"bootargs_root=\0"
+
+/*
+ * Ethernet Driver configuration
+ */
+#ifdef CONFIG_CMD_NET
+#define CONFIG_NETCONSOLE
+#define CONFIG_MVGBE_PORTS	{1, 0}	/* enable port 0 only */
+#define CONFIG_PHY_BASE_ADR	0
+#endif /* CONFIG_CMD_NET */
+
+/*
+ * File system
+ */
+#define CONFIG_JFFS2_NAND
+#define CONFIG_JFFS2_LZO
+
+/*
+ * SATA
+ */
+#ifdef CONFIG_MVSATA_IDE
+#define CONFIG_SYS_ATA_IDE0_OFFSET	MV_SATA_PORT0_OFFSET
+#endif
+
+/*
+ *  Date Time
+ */
+#ifdef CONFIG_CMD_DATE
+#define CONFIG_RTC_MV
+#endif /* CONFIG_CMD_DATE */
+
+/*
+ * Kirkwood GPIO
+ */
+#define CONFIG_KIRKWOOD_GPIO
+
+#endif /* _CONFIG_POGOPLUGV4_H */
--- /dev/null
+++ b/include/kirkwood_mmc.h
@@ -0,0 +1,268 @@
+/*
+ * (C) Copyright 2014 <<EMAIL>>
+ *
+ * Based on
+ *
+ * (C) Copyright 2012
+ * Marvell Semiconductor <www.marvell.com>
+ * Written-by: Gérald Kerma <uboot at doukki.net>
+ * See file CREDITS for list of people who contributed to this
+ * project.
+ *
+ * This program is free software; you can redistribute it and/or
+ * modify it under the terms of the GNU General Public License as
+ * published by the Free Software Foundation; either version 2 of
+ * the License, or (at your option) any later version.
+ *
+ * This program is distributed in the hope that it will be useful,
+ * but WITHOUT ANY WARRANTY; without even the implied warranty of
+ * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
+ * GNU General Public License for more details.
+ *
+ * You should have received a copy of the GNU General Public License
+ * along with this program; if not, write to the Free Software
+ * Foundation, Inc., 59 Temple Place, Suite 330, Boston,
+ * MA 02111-1307 USA
+ */
+
+#ifndef __KIRKWOOD_MMC_H__
+#define __KIRKWOOD_MMC_H__
+
+/*
+ * Clock rates
+ */
+
+#define KW_MMC_CLOCKRATE_MAX			50000000
+#define KW_MMC_BASE_DIV_MAX			0x7ff
+#define KW_MMC_BASE_FAST_CLOCK		CONFIG_SYS_TCLK
+#define KW_MMC_BASE_FAST_CLK_100		100000000
+#define KW_MMC_BASE_FAST_CLK_200		200000000
+
+/*
+ * Macros
+ */
+#define kwsd_write(offs, val)	writel(val, CONFIG_SYS_MMC_BASE + (offs))
+#define kwsd_read(offs)			readl(CONFIG_SYS_MMC_BASE + (offs))
+
+#define KW_MMC_MAKE_CMD(c, f) (((c & 0xff) << 8) | (f & 0xff))
+
+/* SDIO register */
+#define SDIO_SYS_ADDR_LOW			0x000
+#define SDIO_SYS_ADDR_HI			0x004
+#define SDIO_BLK_SIZE				0x008
+#define SDIO_BLK_COUNT				0x00c
+#define SDIO_ARG_LOW				0x010
+#define SDIO_ARG_HI					0x014
+#define SDIO_XFER_MODE				0x018
+#define SDIO_CMD					0x01c
+#define SDIO_RSP(i)					(0x020 + ((i)<<2))
+#define SDIO_RSP0					0x020
+#define SDIO_RSP1					0x024
+#define SDIO_RSP2					0x028
+#define SDIO_RSP3					0x02c
+#define SDIO_RSP4					0x030
+#define SDIO_RSP5					0x034
+#define SDIO_RSP6					0x038
+#define SDIO_RSP7					0x03c
+#define SDIO_BUF_DATA_PORT			0x040
+#define SDIO_RSVED					0x044
+#define SDIO_HW_STATE				0x048
+#define SDIO_PRESENT_STATE0			0x048
+#define SDIO_PRESENT_STATE1			0x04c
+#define SDIO_HOST_CTRL				0x050
+#define SDIO_BLK_GAP_CTRL			0x054
+#define SDIO_CLK_CTRL				0x058
+#define SDIO_SW_RESET				0x05c
+#define SDIO_NOR_INTR_STATUS		0x060
+#define SDIO_ERR_INTR_STATUS		0x064
+#define SDIO_NOR_STATUS_EN			0x068
+#define SDIO_ERR_STATUS_EN			0x06c
+#define SDIO_NOR_INTR_EN			0x070
+#define SDIO_ERR_INTR_EN			0x074
+#define SDIO_AUTOCMD12_ERR_STATUS	0x078
+#define SDIO_CURR_BYTE_LEFT			0x07c
+#define SDIO_CURR_BLK_LEFT			0x080
+#define SDIO_AUTOCMD12_ARG_LOW		0x084
+#define SDIO_AUTOCMD12_ARG_HI		0x088
+#define SDIO_AUTOCMD12_INDEX		0x08c
+#define SDIO_AUTO_RSP(i)			(0x090 + ((i)<<2))
+#define SDIO_AUTO_RSP0				0x090
+#define SDIO_AUTO_RSP1				0x094
+#define SDIO_AUTO_RSP2				0x098
+#define SDIO_CLK_DIV				0x128
+
+#define WINDOW_CTRL(i)				(0x108 + ((i) << 3))
+#define WINDOW_BASE(i)				(0x10c + ((i) << 3))
+
+/* SDIO_PRESENT_STATE */
+#define CARD_BUSY				(1 << 1)
+#define CMD_INHIBIT				(1 << 0)
+#define CMD_TXACTIVE			(1 << 8)
+#define CMD_RXACTIVE			(1 << 9)
+#define CMD_AUTOCMD12ACTIVE		(1 << 14)
+#define CMD_BUS_BUSY			(CMD_AUTOCMD12ACTIVE |	\
+								CMD_RXACTIVE |	\
+								CMD_TXACTIVE |	\
+								CMD_INHIBIT |	\
+								CARD_BUSY)
+
+/*
+ * SDIO_CMD
+ */
+
+#define SDIO_CMD_RSP_NONE				(0 << 0)
+#define SDIO_CMD_RSP_136				(1 << 0)
+#define SDIO_CMD_RSP_48					(2 << 0)
+#define SDIO_CMD_RSP_48BUSY				(3 << 0)
+
+#define SDIO_CMD_CHECK_DATACRC16		(1 << 2)
+#define SDIO_CMD_CHECK_CMDCRC			(1 << 3)
+#define SDIO_CMD_INDX_CHECK				(1 << 4)
+#define SDIO_CMD_DATA_PRESENT			(1 << 5)
+#define SDIO_UNEXPECTED_RESP			(1 << 7)
+
+#define SDIO_CMD_INDEX(x)				((x) << 8)
+
+/*
+ * SDIO_XFER_MODE
+ */
+
+#define SDIO_XFER_MODE_STOP_CLK			(1 << 5)
+#define SDIO_XFER_MODE_HW_WR_DATA_EN	(1 << 1)
+#define SDIO_XFER_MODE_AUTO_CMD12		(1 << 2)
+#define SDIO_XFER_MODE_INT_CHK_EN		(1 << 3)
+#define SDIO_XFER_MODE_TO_HOST			(1 << 4)
+#define SDIO_XFER_MODE_DMA				(0 << 6)
+
+/*
+ * SDIO_HOST_CTRL
+ */
+
+#define SDIO_HOST_CTRL_PUSH_PULL_EN 		(1 << 0)
+
+#define SDIO_HOST_CTRL_CARD_TYPE_MEM_ONLY 	(0 << 1)
+#define SDIO_HOST_CTRL_CARD_TYPE_IO_ONLY 	(1 << 1)
+#define SDIO_HOST_CTRL_CARD_TYPE_IO_MEM_COMBO 	(2 << 1)
+#define SDIO_HOST_CTRL_CARD_TYPE_IO_MMC 	(3 << 1)
+#define SDIO_HOST_CTRL_CARD_TYPE_MASK	 	(3 << 1)
+
+#define SDIO_HOST_CTRL_BIG_ENDIAN 		(1 << 3)
+#define SDIO_HOST_CTRL_LSB_FIRST 		(1 << 4)
+#define SDIO_HOST_CTRL_DATA_WIDTH_1_BIT 	(0 << 9)
+#define SDIO_HOST_CTRL_DATA_WIDTH_4_BITS 	(1 << 9)
+#define SDIO_HOST_CTRL_HI_SPEED_EN 		(1 << 10)
+
+#define SDIO_HOST_CTRL_TMOUT_MAX 		0xf
+#define SDIO_HOST_CTRL_TMOUT_MASK 		(0xf << 11)
+#define SDIO_HOST_CTRL_TMOUT(x) 		((x) << 11)
+#define SDIO_HOST_CTRL_TMOUT_EN 		(1 << 15)
+
+/*
+ * SDIO_SW_RESET
+ */
+
+#define SDIO_SW_RESET_NOW			(1 << 8)
+
+/*
+ * Normal interrupt status bits
+ */
+
+#define SDIO_NOR_ERROR				(1 << 15)
+#define SDIO_NOR_UNEXP_RSP			(1 << 14)
+#define SDIO_NOR_AUTOCMD12_DONE		(1 << 13)
+#define SDIO_NOR_SUSPEND_ON			(1 << 12)
+#define SDIO_NOR_LMB_FF_8W_AVAIL	(1 << 11)
+#define SDIO_NOR_LMB_FF_8W_FILLED	(1 << 10)
+#define SDIO_NOR_READ_WAIT_ON		(1 << 9)
+#define SDIO_NOR_CARD_INT			(1 << 8)
+#define SDIO_NOR_READ_READY			(1 << 5)
+#define SDIO_NOR_WRITE_READY		(1 << 4)
+#define SDIO_NOR_DMA_INI			(1 << 3)
+#define SDIO_NOR_BLK_GAP_EVT		(1 << 2)
+#define SDIO_NOR_XFER_DONE			(1 << 1)
+#define SDIO_NOR_CMD_DONE			(1 << 0)
+
+/*
+ * Error status bits
+ */
+
+#define SDIO_ERR_CRC_STATUS			(1 << 14)
+#define SDIO_ERR_CRC_STARTBIT		(1 << 13)
+#define SDIO_ERR_CRC_ENDBIT			(1 << 12)
+#define SDIO_ERR_RESP_TBIT			(1 << 11)
+#define SDIO_ERR_XFER_SIZE			(1 << 10)
+#define SDIO_ERR_CMD_STARTBIT		(1 << 9)
+#define SDIO_ERR_AUTOCMD12			(1 << 8)
+#define SDIO_ERR_DATA_ENDBIT		(1 << 6)
+#define SDIO_ERR_DATA_CRC			(1 << 5)
+#define SDIO_ERR_DATA_TIMEOUT		(1 << 4)
+#define SDIO_ERR_CMD_INDEX			(1 << 3)
+#define SDIO_ERR_CMD_ENDBIT			(1 << 2)
+#define SDIO_ERR_CMD_CRC			(1 << 1)
+#define SDIO_ERR_CMD_TIMEOUT		(1 << 0)
+#define SDIO_POLL_MASK 				0xffff /* enable all for polling */
+
+#define MMC_BLOCK_SIZE                  512
+
+/*
+ * CMD12 error status bits
+ */
+
+#define SDIO_AUTOCMD12_ERR_NOTEXE		(1 << 0)
+#define SDIO_AUTOCMD12_ERR_TIMEOUT		(1 << 1)
+#define SDIO_AUTOCMD12_ERR_CRC			(1 << 2)
+#define SDIO_AUTOCMD12_ERR_ENDBIT		(1 << 3)
+#define SDIO_AUTOCMD12_ERR_INDEX		(1 << 4)
+#define SDIO_AUTOCMD12_ERR_RESP_T_BIT		(1 << 5)
+#define SDIO_AUTOCMD12_ERR_RESP_STARTBIT	(1 << 6)
+
+#define MMC_RSP_PRESENT	(1 << 0)
+#define MMC_RSP_136	(1 << 1)		/* 136 bit response */
+#define MMC_RSP_CRC	(1 << 2)		/* expect valid crc */
+#define MMC_RSP_BUSY	(1 << 3)		/* card may send busy */
+#define MMC_RSP_OPCODE	(1 << 4)		/* response contains opcode */
+
+#define MMC_BUSMODE_OPENDRAIN	1
+#define MMC_BUSMODE_PUSHPULL	2
+
+#define MMC_BUS_WIDTH_1		0
+#define MMC_BUS_WIDTH_4		2
+#define MMC_BUS_WIDTH_8		3
+
+#define MMC_CAP_4_BIT_DATA	(1 << 0)	/* Can the host do 4 bit transfers */
+#define MMC_CAP_MMC_HIGHSPEED	(1 << 1)	/* Can do MMC high-speed timing */
+#define MMC_CAP_SD_HIGHSPEED	(1 << 2)	/* Can do SD high-speed timing */
+#define MMC_CAP_SDIO_IRQ	(1 << 3)	/* Can signal pending SDIO IRQs */
+#define MMC_CAP_SPI		(1 << 4)	/* Talks only SPI protocols */
+#define MMC_CAP_NEEDS_POLL	(1 << 5)	/* Needs polling for card-detection */
+#define MMC_CAP_8_BIT_DATA	(1 << 6)	/* Can the host do 8 bit transfers */
+
+#define MMC_CAP_NONREMOVABLE	(1 << 8)	/* Nonremovable e.g. eMMC */
+#define MMC_CAP_WAIT_WHILE_BUSY	(1 << 9)	/* Waits while card is busy */
+#define MMC_CAP_ERASE		(1 << 10)	/* Allow erase/trim commands */
+#define MMC_CAP_1_8V_DDR	(1 << 11)	/* can support */
+						/* DDR mode at 1.8V */
+#define MMC_CAP_1_2V_DDR	(1 << 12)	/* can support */
+						/* DDR mode at 1.2V */
+#define MMC_CAP_POWER_OFF_CARD	(1 << 13)	/* Can power off after boot */
+#define MMC_CAP_BUS_WIDTH_TEST	(1 << 14)	/* CMD14/CMD19 bus width ok */
+#define MMC_CAP_UHS_SDR12	(1 << 15)	/* Host supports UHS SDR12 mode */
+#define MMC_CAP_UHS_SDR25	(1 << 16)	/* Host supports UHS SDR25 mode */
+#define MMC_CAP_UHS_SDR50	(1 << 17)	/* Host supports UHS SDR50 mode */
+#define MMC_CAP_UHS_SDR104	(1 << 18)	/* Host supports UHS SDR104 mode */
+#define MMC_CAP_UHS_DDR50	(1 << 19)	/* Host supports UHS DDR50 mode */
+#define MMC_CAP_DRIVER_TYPE_A	(1 << 23)	/* Host supports Driver Type A */
+#define MMC_CAP_DRIVER_TYPE_C	(1 << 24)	/* Host supports Driver Type C */
+#define MMC_CAP_DRIVER_TYPE_D	(1 << 25)	/* Host supports Driver Type D */
+#define MMC_CAP_CMD23		(1 << 30)	/* CMD23 supported. */
+#define MMC_CAP_HW_RESET	(1 << 31)	/* Hardware reset */
+
+/*
+ * Functions prototypes
+ *
+ * Original patch had static function declarations in this header file.
+ * Those should rather not be declared in the header as they only cause compiler warnings.
+ */
+int kw_mmc_initialize(bd_t *bis);
+
+#endif /* __KIRKWOOD_MMC_H__ */
