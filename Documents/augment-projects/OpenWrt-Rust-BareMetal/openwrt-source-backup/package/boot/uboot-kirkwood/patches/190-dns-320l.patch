--- a/arch/arm/mach-kirkwood/Kconfig
+++ b/arch/arm/mach-kirkwood/Kconfig
@@ -28,6 +28,9 @@ config TARGET_POGO_E02
 config TARGET_POGOPLUGV4
     bool "Pogoplug V4 Board"
 
+config TARGET_DNS320L
+	bool "dns320l Board"
+
 config TARGET_DNS325
 	bool "dns325 Board"
 
@@ -93,6 +96,7 @@ source "board/Marvell/sheevaplug/Kconfig
 source "board/buffalo/lsxl/Kconfig"
 source "board/cloudengines/pogo_e02/Kconfig"
 source "board/cloudengines/pogoplugv4/Kconfig"
+source "board/d-link/dns320l/Kconfig"
 source "board/d-link/dns325/Kconfig"
 source "board/iomega/iconnect/Kconfig"
 source "board/keymile/Kconfig"
--- /dev/null
+++ b/board/d-link/dns320l/dns320l.c
@@ -0,0 +1,113 @@
+// SPDX-License-Identifier: GPL-2.0+
+/*
+ * Copyright (C) 2015
+ * <PERSON> <<EMAIL>>
+ * <PERSON> Dinh <<EMAIL>>
+ */
+
+#include <common.h>
+#include <miiphy.h>
+#include <net.h>
+#include <asm/arch/cpu.h>
+#include <asm/arch/soc.h>
+#include <asm/arch/mpp.h>
+#include <asm/arch/gpio.h>
+#include <asm/io.h>
+#include "dns320l.h"
+
+DECLARE_GLOBAL_DATA_PTR;
+
+int board_early_init_f(void)
+{
+	/*
+	 * default gpio configuration
+	 * There are maximum 64 gpios controlled through 2 sets of registers
+	 * the below configuration configures mainly initial LED status
+	 */
+	mvebu_config_gpio(DNS320L_OE_VAL_LOW, DNS320L_OE_VAL_HIGH,
+			  DNS320L_OE_LOW, DNS320L_OE_HIGH);
+
+	/* (all LEDs & power off active high) */
+	/* Multi-Purpose Pins Functionality configuration */
+	static const u32 kwmpp_config[] = {
+		MPP0_NF_IO2,
+		MPP1_NF_IO3,
+		MPP2_NF_IO4,
+		MPP3_NF_IO5,
+		MPP4_NF_IO6,
+		MPP5_NF_IO7,
+		MPP6_SYSRST_OUTn,
+		MPP7_GPO,
+		MPP8_TW_SDA,
+		MPP9_TW_SCK,
+		MPP10_UART0_TXD,
+		MPP11_UART0_RXD,
+		MPP12_GPO,
+		MPP13_GPIO,
+		MPP14_GPIO,
+		MPP15_GPIO,
+		MPP16_GPIO,
+		MPP17_GPIO,
+		MPP18_NF_IO0,
+		MPP19_NF_IO1,
+		MPP20_SATA1_ACTn,	/* sata1(left) status led */
+		MPP21_SATA0_ACTn,	/* sata0(right) status led */
+		MPP22_GPIO,
+		MPP23_GPIO,
+		MPP24_GPIO,
+		MPP25_GPIO,
+		MPP26_GPIO,
+		MPP27_GPIO,
+		MPP28_GPIO,
+		MPP29_GPIO,
+		MPP30_GPIO,
+		MPP31_GPIO,
+		MPP32_GPIO,
+		0
+	};
+	kirkwood_mpp_conf(kwmpp_config, NULL);
+
+	kw_gpio_set_value(DNS320L_GPIO_SATA_EN , 1);
+
+	return 0;
+}
+
+int board_init(void)
+{
+	/* address of boot parameters */
+	gd->bd->bi_boot_params = mvebu_sdram_bar(0) + 0x100;
+
+	return 0;
+}
+
+#ifdef CONFIG_RESET_PHY_R
+/* Configure and initialize PHY */
+void reset_phy(void)
+{
+	u16 reg;
+	u16 phyaddr;
+	char *name = "egiga0";
+
+	if (miiphy_set_current_dev(name))
+		return;
+
+	/* read PHY dev address */
+	if (miiphy_read(name, 0xee, 0xee, (u16 *) &phyaddr)) {
+		printf("could not read PHY dev address\n");
+		return;
+	}
+
+	/* set RGMII delay */
+	miiphy_write(name, phyaddr, MV88E1318_PGADR_REG, MV88E1318_MAC_CTRL_PG);
+	miiphy_read(name, phyaddr, MV88E1318_MAC_CTRL_REG, &reg);
+	reg |= (MV88E1318_RGMII_RX_CTRL | MV88E1318_RGMII_TX_CTRL);
+	miiphy_write(name, phyaddr, MV88E1318_MAC_CTRL_REG, reg);
+	miiphy_write(name, phyaddr, MV88E1318_PGADR_REG, 0);
+
+	/* reset PHY */
+	if (miiphy_reset(name, phyaddr))
+		return;
+
+	printf("MV88E1318 PHY initialized on %s\n", name);
+}
+#endif /* CONFIG_RESET_PHY_R */
--- /dev/null
+++ b/board/d-link/dns320l/dns320l.h
@@ -0,0 +1,39 @@
+/* SPDX-License-Identifier: GPL-2.0+ */
+/*
+ * Copyright (C) 2011
+ * Stefan Herbrechtsmeier <<EMAIL>>
+ *
+ * Based on Kirkwood support:
+ * (C) Copyright 2009
+ * Marvell Semiconductor <www.marvell.com>
+ * Written-by: Prafulla Wadaskar <<EMAIL>>
+ */
+
+#ifndef __DNS320L_H
+#define __DNS320L_H
+
+/* GPIO configuration */
+#define DNS320L_GPIO_SATA_EN	24
+#define HDD_L_GREEN_LED		(1 << 22)
+#define HDD_R_GREEN_LED		(1 << 23)
+#define USB_BLUE_LED		(1 << 25)
+#define USB_ORANGE_LED		(1 << 26)
+
+#define DNS320L_OE_LOW		(~(HDD_L_GREEN_LED | HDD_R_GREEN_LED | \
+                                   USB_BLUE_LED | USB_ORANGE_LED))
+#define DNS320L_OE_VAL_LOW	(0)
+
+/* high GPIO's */
+#define WATCHDOG_SIGNAL		(1 << 14)
+
+#define DNS320L_OE_HIGH		(~(WATCHDOG_SIGNAL))
+#define DNS320L_OE_VAL_HIGH	((WATCHDOG_SIGNAL))
+
+/* PHY related */
+#define MV88E1318_PGADR_REG	22
+#define MV88E1318_MAC_CTRL_PG	2
+#define MV88E1318_MAC_CTRL_REG	21
+#define MV88E1318_RGMII_TX_CTRL	(1 << 4)
+#define MV88E1318_RGMII_RX_CTRL	(1 << 5)
+
+#endif /* __DNS320L_H */
--- /dev/null
+++ b/board/d-link/dns320l/Kconfig
@@ -0,0 +1,18 @@
+# SPDX-License-Identifier: GPL-2.0+
+#
+# Copyright (C) 2015
+# <AUTHOR> <EMAIL>
+# <AUTHOR> <EMAIL>
+
+if TARGET_DNS320L
+
+config SYS_BOARD
+	default "dns320l"
+
+config SYS_VENDOR
+	default "d-link"
+	
+config SYS_CONFIG_NAME
+	default "dns320l"
+
+endif
--- /dev/null
+++ b/board/d-link/dns320l/kwbimage.cfg
@@ -0,0 +1,41 @@
+# SPDX-License-Identifier: GPL-2.0+
+#
+# Copyright (C) 2015
+# <AUTHOR> <EMAIL>
+# <AUTHOR> <EMAIL>
+# Refer to doc/README.kwbimage for more details about how-to
+# configure and create kirkwood boot images.
+#
+
+# Boot Media configurations
+BOOT_FROM       nand
+NAND_ECC_MODE   default
+NAND_PAGE_SIZE  0x0800
+
+# Configure RGMII-0 interface pad voltage to 1.8V
+DATA 0xFFD100e0 0x1b1b1b9b
+
+DATA 0xFFD01400 0x43010c30
+DATA 0xFFD01404 0x39543000
+DATA 0xFFD01408 0x22125451
+DATA 0xFFD0140C 0x00000833
+DATA 0xFFD01410 0x0000000C
+DATA 0xFFD01414 0x00000000
+DATA 0xFFD01418 0x00000000
+DATA 0xFFD0141C 0x00000652
+DATA 0xFFD01420 0x00000004
+DATA 0xFFD01424 0x0000F17F
+DATA 0xFFD01428 0x00085520
+DATA 0xFFD0147c 0x00008552
+DATA 0xFFD01504 0x0FFFFFF1
+DATA 0xFFD01508 0x10000000
+DATA 0xFFD0150C 0x00000000
+DATA 0xFFD01514 0x00000000
+DATA 0xFFD0151C 0x00000000
+DATA 0xFFD01494 0x00010000
+DATA 0xFFD01498 0x00000000
+DATA 0xFFD0149C 0x0000E403
+DATA 0xFFD01480 0x00000001
+DATA 0xFFD20134 0x66666666
+DATA 0xFFD20138 0x66666666
+DATA 0x0 0x0
--- /dev/null
+++ b/board/d-link/dns320l/MAINTAINERS
@@ -0,0 +1,8 @@
+NSA310S BOARD
+M:	Gerald Kerma <<EMAIL>>
+M:	Tony Dinh <<EMAIL>>
+M:	Luka Perkov <<EMAIL>>
+S:	Maintained
+F:	board/d-link/dns320l/
+F:	include/configs/dns320l.h
+F:	configs/dns320l_defconfig
--- /dev/null
+++ b/board/d-link/dns320l/Makefile
@@ -0,0 +1,7 @@
+# SPDX-License-Identifier: GPL-2.0+
+#
+# Copyright (C) 2015
+# <AUTHOR> <EMAIL>
+# <AUTHOR> <EMAIL>
+
+obj-y	:= dns320l.o
--- /dev/null
+++ b/configs/dns320l_defconfig
@@ -0,0 +1,48 @@
+CONFIG_ARM=y
+CONFIG_SYS_DCACHE_OFF=y
+CONFIG_ARCH_CPU_INIT=y
+CONFIG_KIRKWOOD=y
+CONFIG_SYS_TEXT_BASE=0x600000
+CONFIG_TARGET_DNS320L=y
+CONFIG_ENV_SIZE=0x20000
+CONFIG_ENV_OFFSET=0x100000
+CONFIG_NR_DRAM_BANKS=2
+CONFIG_BOOTDELAY=3
+CONFIG_USE_PREBOOT=y
+# CONFIG_DISPLAY_BOARDINFO is not set
+CONFIG_HUSH_PARSER=y
+CONFIG_SYS_PROMPT="dns320l => "
+CONFIG_CMD_BOOTZ=y
+# CONFIG_CMD_FLASH is not set
+CONFIG_CMD_IDE=y
+CONFIG_CMD_NAND=y
+CONFIG_CMD_USB=y
+# CONFIG_CMD_SETEXPR is not set
+CONFIG_CMD_DHCP=y
+CONFIG_CMD_MII=y
+CONFIG_CMD_PING=y
+CONFIG_CMD_EXT2=y
+CONFIG_CMD_FAT=y
+CONFIG_CMD_JFFS2=y
+CONFIG_CMD_MTDPARTS=y
+CONFIG_MTDPARTS_DEFAULT="mtdparts=orion_nand:0x100000@0x0(uboot),0x20000@0x100000(ubootenv),0x6de0000@0x120000(ubi),0xa00000@0x6f00000(mini),0x500000@0x7900000(config),0x200000@0x7e00000(my-dlink)"
+CONFIG_CMD_UBI=y
+CONFIG_ISO_PARTITION=y
+CONFIG_ENV_IS_IN_NAND=y
+CONFIG_SYS_RELOC_GD_ENV_ADDR=y
+CONFIG_MVSATA_IDE=y
+# CONFIG_MMC is not set
+CONFIG_MTD=y
+CONFIG_MTD_RAW_NAND=y
+CONFIG_MVGBE=y
+CONFIG_MII=y
+CONFIG_SYS_NS16550=y
+CONFIG_USB=y
+CONFIG_USB_EHCI_HCD=y
+CONFIG_USB_STORAGE=y
+CONFIG_FIT=y
+CONFIG_FIT_VERBOSE=y
+CONFIG_LZMA=y
+CONFIG_LZO=y
+CONFIG_OF_LIBFDT=y
+CONFIG_NET_RANDOM_ETHADDR=y
--- /dev/null
+++ b/include/configs/dns320l.h
@@ -0,0 +1,69 @@
+/* SPDX-License-Identifier: GPL-2.0+ */
+/*
+ * Copyright (C) 2015
+ * Gerald Kerma <<EMAIL>>
+ * Tony Dinh <<EMAIL>>
+ * Luka Perkov <<EMAIL>>
+ */
+
+#ifndef _CONFIG_DNS320L_H
+#define _CONFIG_DNS320L_H
+
+/* high level configuration options */
+#define CONFIG_FEROCEON_88FR131	1	/* CPU Core subversion */
+#define CONFIG_KW88F6192		1	/* SOC Name */
+#define CONFIG_KW88F6702		1	/* SOC Name */
+#define CONFIG_SKIP_LOWLEVEL_INIT	/* disable board lowlevel_init */
+
+/* compression configuration */
+#define CONFIG_BZIP2
+
+/* commands configuration */
+
+/*
+ * mv-common.h should be defined after CMD configs since it used them
+ * to enable certain macros
+ */
+#include "mv-common.h"
+
+/* environment variables configuration */
+
+/* default environment variables */
+#define CONFIG_BOOTCOMMAND \
+	"ubi part ubi; " \
+	"ubi read 0x800000 kernel; " \
+	"bootm 0x800000"
+
+#define CONFIG_EXTRA_ENV_SETTINGS \
+	"console=console=ttyS0,115200\0" \
+	"mtdids=nand0=orion_nand\0" \
+	"mtdparts="CONFIG_MTDPARTS_DEFAULT "\0" \
+	"bootargs_root=ubi.mtd=2 root=ubi0:rootfs rootfstype=ubifs rw\0"
+
+/* Ethernet driver configuration */
+#ifdef CONFIG_CMD_NET
+#define CONFIG_NETCONSOLE
+#define CONFIG_MVGBE_PORTS	{1, 0}	/* enable port 0 only */
+#define CONFIG_PHY_BASE_ADR	1
+#define CONFIG_RESET_PHY_R
+#endif /* CONFIG_CMD_NET */
+
+/* SATA driver configuration */
+#ifdef CONFIG_IDE
+#define __io
+#define CONFIG_IDE_PREINIT
+#define CONFIG_MVSATA_IDE_USE_PORT0
+#define CONFIG_SYS_ATA_IDE0_OFFSET	MV_SATA_PORT0_OFFSET
+#endif /* CONFIG_IDE */
+
+/*
+ * Enable GPI0 support
+*/
+#define CONFIG_KIRKWOOD_GPIO
+
+/* RTC driver configuration */
+#ifdef CONFIG_CMD_DATE
+#define CONFIG_RTC_MV
+#endif /* CONFIG_CMD_DATE */
+
+#endif /* _CONFIG_DNS320L_H */
