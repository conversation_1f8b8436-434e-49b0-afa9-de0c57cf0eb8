--- a/include/configs/pogo_e02.h
+++ b/include/configs/pogo_e02.h
@@ -42,17 +42,17 @@
  * Default environment variables
  */
 #define CONFIG_BOOTCOMMAND \
-	"setenv bootargs $(bootargs_console); " \
-	"run bootcmd_usb; " \
-	"bootm 0x00800000 0x01100000"
+	"setenv bootargs ${console} ${mtdparts} ${bootargs_root}; "	\
+	"ubi part ubi; "						\
+	"ubifsmount ubi:rootfs; "					\
+	"ubi read 0x800000 kernel; "					\
+	"bootm 0x800000"
 
 #define CONFIG_EXTRA_ENV_SETTINGS \
-	"mtdparts=mtdparts=orion_nand:1M(u-boot),4M(uImage)," \
-	"32M(rootfs),-(data)\0"\
-	"mtdids=nand0=orion_nand\0"\
-	"bootargs_console=console=ttyS0,115200\0" \
-	"bootcmd_usb=usb start; ext2load usb 0:1 0x00800000 /uImage; " \
-	"ext2load usb 0:1 0x01100000 /uInitrd\0"
+	"console=console=ttyS0,115200\0"	\
+	"mtdids=nand0=orion_nand\0"		\
+	"mtdparts="CONFIG_MTDPARTS_DEFAULT "\0"	\
+	"bootargs_root=\0"
 
 /*
  * Ethernet Driver configuration
--- a/configs/pogo_e02_defconfig
+++ b/configs/pogo_e02_defconfig
@@ -5,7 +5,7 @@ CONFIG_KIRKWOOD=y
 CONFIG_SYS_TEXT_BASE=0x600000
 CONFIG_TARGET_POGO_E02=y
 CONFIG_ENV_SIZE=0x20000
-CONFIG_ENV_OFFSET=0x60000
+CONFIG_ENV_OFFSET=0xE0000
 CONFIG_NR_DRAM_BANKS=2
 CONFIG_IDENT_STRING="\nPogo E02"
 CONFIG_BOOTDELAY=3
@@ -23,6 +23,7 @@ CONFIG_CMD_EXT2=y
 CONFIG_CMD_FAT=y
 CONFIG_CMD_JFFS2=y
 CONFIG_CMD_MTDPARTS=y
+CONFIG_MTDPARTS_DEFAULT="mtdparts=orion_nand:0xe0000@0x0(uboot),0x20000@0xe0000(uboot_env),0x100000@0x100000(second_stage_uboot),-@0x200000(ubi)"
 CONFIG_CMD_UBI=y
 CONFIG_ISO_PARTITION=y
 CONFIG_OF_CONTROL=y
