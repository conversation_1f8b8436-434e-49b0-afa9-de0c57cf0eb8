--- a/configs/nsa310s_defconfig
+++ b/configs/nsa310s_defconfig
@@ -5,7 +5,7 @@ CONFIG_KIRKWOOD=y
 CONFIG_SYS_TEXT_BASE=0x600000
 CONFIG_TARGET_NSA310S=y
 CONFIG_ENV_SIZE=0x20000
-CONFIG_ENV_OFFSET=0xE0000
+CONFIG_ENV_OFFSET=0xC0000
 CONFIG_NR_DRAM_BANKS=2
 CONFIG_BOOTDELAY=3
 CONFIG_USE_PREBOOT=y
@@ -25,7 +25,7 @@ CONFIG_CMD_EXT2=y
 CONFIG_CMD_FAT=y
 CONFIG_CMD_JFFS2=y
 CONFIG_CMD_MTDPARTS=y
-CONFIG_MTDPARTS_DEFAULT="mtdparts=orion_nand:0xe0000@0x0(uboot),0x20000@0xe0000(uboot_env),0x100000@0x100000(second_stage_uboot),-@0x200000(root)"
+CONFIG_MTDPARTS_DEFAULT="mtdparts=orion_nand:0xc0000@0x0(uboot),0x80000@0xc0000(uboot_env),-@0x140000(ubi)"
 CONFIG_CMD_UBI=y
 CONFIG_ISO_PARTITION=y
 CONFIG_ENV_IS_IN_NAND=y
--- a/include/configs/nsa310s.h
+++ b/include/configs/nsa310s.h
@@ -30,22 +30,17 @@
 
 /* default environment variables */
 #define CONFIG_BOOTCOMMAND \
-	"setenv bootargs ${console} ${mtdparts} ${bootargs_root}; " \
-	"ubi part root; " \
-	"ubifsmount ubi:rootfs; " \
-	"ubifsload 0x800000 ${kernel}; " \
-	"ubifsload 0x700000 ${fdt}; " \
-	"ubifsumount; " \
-	"fdt addr 0x700000; fdt resize; fdt chosen; " \
-	"bootz 0x800000 - 0x700000"
+	"setenv bootargs ${console} ${mtdparts} ${bootargs_root}; "	\
+	"ubi part ubi; "						\
+	"ubifsmount ubi:rootfs; "					\
+	"ubi read 0x800000 kernel; "					\
+	"bootm 0x800000"
 
 #define CONFIG_EXTRA_ENV_SETTINGS \
-	"console=console=ttyS0,115200\0" \
-	"mtdids=nand0=orion_nand\0" \
-	"mtdparts="CONFIG_MTDPARTS_DEFAULT \
-	"kernel=/boot/zImage\0" \
-	"fdt=/boot/nsa310s.dtb\0" \
-	"bootargs_root=ubi.mtd=3 root=ubi0:rootfs rootfstype=ubifs rw\0"
+	"console=console=ttyS0,115200\0"	\
+	"mtdids=nand0=orion_nand\0"		\
+	"mtdparts="CONFIG_MTDPARTS_DEFAULT "\0"	\
+	"bootargs_root=\0"
 
 /* Ethernet driver configuration */
 #ifdef CONFIG_CMD_NET
