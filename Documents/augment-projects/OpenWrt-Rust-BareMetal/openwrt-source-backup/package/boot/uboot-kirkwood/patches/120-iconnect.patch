--- a/include/configs/iconnect.h
+++ b/include/configs/iconnect.h
@@ -44,17 +44,15 @@
  */
 #define CONFIG_BOOTCOMMAND \
 	"setenv bootargs ${console} ${mtdparts} ${bootargs_root}; "	\
-	"ubi part rootfs; "						\
-	"ubifsmount ubi:rootfs; "					\
-	"ubifsload 0x800000 ${kernel}; "				\
+	"ubi part ubi; " \
+	"ubi read 0x800000 kernel; " \
 	"bootm 0x800000"
 
 #define CONFIG_EXTRA_ENV_SETTINGS \
 	"console=console=ttyS0,115200\0"	\
 	"mtdids=nand0=orion_nand\0"		\
-	"mtdparts="CONFIG_MTDPARTS_DEFAULT	\
-	"kernel=/boot/uImage\0"			\
-	"bootargs_root=noinitrd ubi.mtd=2 root=ubi0:rootfs rootfstype=ubifs\0"
+	"mtdparts="CONFIG_MTDPARTS_DEFAULT "\0"	\
+	"bootargs_root=\0"
 
 /*
  * Ethernet driver configuration
--- a/configs/iconnect_defconfig
+++ b/configs/iconnect_defconfig
@@ -5,7 +5,7 @@ CONFIG_KIRKWOOD=y
 CONFIG_SYS_TEXT_BASE=0x600000
 CONFIG_TARGET_ICONNECT=y
 CONFIG_ENV_SIZE=0x20000
-CONFIG_ENV_OFFSET=0x80000
+CONFIG_ENV_OFFSET=0xE0000
 CONFIG_NR_DRAM_BANKS=2
 CONFIG_IDENT_STRING=" Iomega iConnect"
 CONFIG_BOOTDELAY=3
@@ -16,13 +16,14 @@ CONFIG_SYS_PROMPT="iconnect => "
 CONFIG_CMD_NAND=y
 CONFIG_CMD_USB=y
 # CONFIG_CMD_SETEXPR is not set
+CONFIG_CMD_DHCP=y
 CONFIG_CMD_MII=y
 CONFIG_CMD_PING=y
 CONFIG_CMD_EXT2=y
 CONFIG_CMD_FAT=y
 CONFIG_CMD_JFFS2=y
 CONFIG_CMD_MTDPARTS=y
-CONFIG_MTDPARTS_DEFAULT="mtdparts=orion_nand:0x80000@0x0(uboot),0x20000@0x80000(uboot_env),-@0xa0000(rootfs)"
+CONFIG_MTDPARTS_DEFAULT="mtdparts=orion_nand:0xe0000@0x0(uboot),0x20000@0xe0000(uboot_env),0x100000@0x100000(second_stage_uboot),-@0x200000(ubi)"
 CONFIG_CMD_UBI=y
 CONFIG_ISO_PARTITION=y
 CONFIG_OF_CONTROL=y
