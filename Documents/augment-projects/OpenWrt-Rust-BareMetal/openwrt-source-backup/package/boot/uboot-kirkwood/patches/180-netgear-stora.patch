--- a/arch/arm/mach-kirkwood/Kconfig
+++ b/arch/arm/mach-kirkwood/Kconfig
@@ -44,6 +44,9 @@ config TARGET_NET2BIG_V2
 config TARGET_NETSPACE_V2
 	bool "LaCie netspace_v2 Board"
 
+config TARGET_NETGEAR_MS2110
+	bool "Netgear MS2110 Board"
+
 config TARGET_IB62X0
 	bool "ib62x0 Board"
 
@@ -95,6 +98,7 @@ source "board/iomega/iconnect/Kconfig"
 source "board/keymile/Kconfig"
 source "board/LaCie/net2big_v2/Kconfig"
 source "board/LaCie/netspace_v2/Kconfig"
+source "board/Marvell/netgear_ms2110/Kconfig"
 source "board/raidsonic/ib62x0/Kconfig"
 source "board/Seagate/dockstar/Kconfig"
 source "board/Seagate/goflexhome/Kconfig"
--- /dev/null
+++ b/board/Marvell/netgear_ms2110/Kconfig
@@ -0,0 +1,12 @@
+if TARGET_NETGEAR_MS2110
+
+config SYS_BOARD
+	default "netgear_ms2110"
+
+config SYS_VENDOR
+	default "Marvell"
+
+config SYS_CONFIG_NAME
+	default "netgear_ms2110"
+
+endif
--- /dev/null
+++ b/board/Marvell/netgear_ms2110/kwbimage.cfg
@@ -0,0 +1,167 @@
+#
+# (C) Copyright 2009
+# Marvell Semiconductor <www.marvell.com>
+# <AUTHOR> <EMAIL>
+#
+# See file CREDITS for list of people who contributed to this
+# project.
+#
+# This program is free software; you can redistribute it and/or
+# modify it under the terms of the GNU General Public License as
+# published by the Free Software Foundation; either version 2 of
+# the License, or (at your option) any later version.
+#
+# This program is distributed in the hope that it will be useful,
+# but WITHOUT ANY WARRANTY; without even the implied warranty of
+# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
+# GNU General Public License for more details.
+#
+# You should have received a copy of the GNU General Public License
+# along with this program; if not, write to the Free Software
+# Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston,
+# MA 02110-1301 USA
+#
+# Refer docs/README.kwimage for more details about how-to configure
+# and create kirkwood boot image
+#
+
+# Boot Media configurations
+BOOT_FROM	nand
+NAND_ECC_MODE	default
+NAND_PAGE_SIZE	0x0800
+
+# SOC registers configuration using bootrom header extension
+# Maximum KWBIMAGE_MAX_CONFIG configurations allowed
+
+# Configure RGMII-0 interface pad voltage to 1.8V
+DATA 0xFFD100e0 0x1b1b1b9b
+
+#Dram initalization for SINGLE x16 CL=5 @ 400MHz
+DATA 0xFFD01400 0x43000c30	# DDR Configuration register
+# bit13-0:  0xc30 (3120 DDR2 clks refresh rate)
+# bit23-14: zero
+# bit24: 1= enable exit self refresh mode on DDR access
+# bit25: 1 required
+# bit29-26: zero
+# bit31-30: 01
+
+DATA 0xFFD01404 0x37543000	# DDR Controller Control Low
+# bit 4:    0=addr/cmd in smame cycle
+# bit 5:    0=clk is driven during self refresh, we don't care for APX
+# bit 6:    0=use recommended falling edge of clk for addr/cmd
+# bit14:    0=input buffer always powered up
+# bit18:    1=cpu lock transaction enabled
+# bit23-20: 5=recommended value for CL=5 and STARTBURST_DEL disabled bit31=0
+# bit27-24: 7= CL+2, STARTBURST sample stages, for freqs 400MHz, unbuffered DIMM
+# bit30-28: 3 required
+# bit31:    0=no additional STARTBURST delay
+
+DATA 0xFFD01408 0x22125451	# DDR Timing (Low) (active cycles value +1)
+# bit3-0:   TRAS lsbs
+# bit7-4:   TRCD
+# bit11- 8: TRP
+# bit15-12: TWR
+# bit19-16: TWTR
+# bit20:    TRAS msb
+# bit23-21: 0x0
+# bit27-24: TRRD
+# bit31-28: TRTP
+
+DATA 0xFFD0140C 0x00000833	#  DDR Timing (High)
+# bit6-0:   TRFC
+# bit8-7:   TR2R
+# bit10-9:  TR2W
+# bit12-11: TW2W
+# bit31-13: zero required
+
+DATA 0xFFD01410 0x00000099	#  DDR Address Control
+# bit1-0:   00, Cs0width=x8
+# bit3-2:   11, Cs0size=1Gb
+# bit5-4:   00, Cs1width=x8
+# bit7-6:   11, Cs1size=1Gb
+# bit9-8:   00, Cs2width=nonexistent
+# bit11-10: 00, Cs2size =nonexistent
+# bit13-12: 00, Cs3width=nonexistent
+# bit15-14: 00, Cs3size =nonexistent
+# bit16:    0,  Cs0AddrSel
+# bit17:    0,  Cs1AddrSel
+# bit18:    0,  Cs2AddrSel
+# bit19:    0,  Cs3AddrSel
+# bit31-20: 0 required
+
+DATA 0xFFD01414 0x00000000	#  DDR Open Pages Control
+# bit0:    0,  OpenPage enabled
+# bit31-1: 0 required
+
+DATA 0xFFD01418 0x00000000	#  DDR Operation
+# bit3-0:   0x0, DDR cmd
+# bit31-4:  0 required
+
+DATA 0xFFD0141C 0x00000C52	#  DDR Mode
+# bit2-0:   2, BurstLen=2 required
+# bit3:     0, BurstType=0 required
+# bit6-4:   4, CL=5
+# bit7:     0, TestMode=0 normal
+# bit8:     0, DLL reset=0 normal
+# bit11-9:  6, auto-precharge write recovery ????????????
+# bit12:    0, PD must be zero
+# bit31-13: 0 required
+
+DATA 0xFFD01420 0x00000004	#  DDR Extended Mode
+# bit0:    0,  DDR DLL enabled
+# bit1:    0,  DDR drive strenght normal
+# bit2:    1,  DDR ODT control lsd (disabled)
+# bit5-3:  000, required
+# bit6:    0,  DDR ODT control msb, (disabled)
+# bit9-7:  000, required
+# bit10:   0,  differential DQS enabled
+# bit11:   0, required
+# bit12:   0, DDR output buffer enabled
+# bit31-13: 0 required
+
+DATA 0xFFD01424 0x0000F17F	#  DDR Controller Control High
+# bit2-0:  111, required
+# bit3  :  1  , MBUS Burst Chop disabled
+# bit6-4:  111, required
+# bit7  :  0
+# bit8  :  1  , add writepath sample stage, must be 1 for DDR freq >= 300MHz
+# bit9  :  0  , no half clock cycle addition to dataout
+# bit10 :  0  , 1/4 clock cycle skew enabled for addr/ctl signals
+# bit11 :  0  , 1/4 clock cycle skew disabled for write mesh
+# bit15-12: 1111 required
+# bit31-16: 0    required
+
+DATA 0xFFD01428 0x00085520	# DDR2 ODT Read Timing (default values)
+DATA 0xFFD0147C 0x00008552	# DDR2 ODT Write Timing (default values)
+
+DATA 0xFFD01500 0x00000000	# CS[0]n Base address to 0x0
+DATA 0xFFD01504 0x03FFFFF1	# CS[0]n Size
+# bit0:    1,  Window enabled
+# bit1:    0,  Write Protect disabled
+# bit3-2:  00, CS0 hit selected
+# bit23-4: ones, required
+# bit31-24: 0x0F, Size (i.e. 256MB)
+
+DATA 0xFFD01508 0x04000000	# CS[1]n Base address to 256Mb
+DATA 0xFFD0150C 0x03FFFFF5	# CS[1]n Size 256Mb Window enabled for CS1
+
+DATA 0xFFD01514 0x00000000	# CS[2]n Size, window disabled
+DATA 0xFFD0151C 0x00000000	# CS[3]n Size, window disabled
+
+DATA 0xFFD01494 0x00120012	#  DDR ODT Control (Low)
+# bit3-0:  2, ODT0Rd, MODT[0] asserted during read from DRAM CS1
+# bit7-4:  1, ODT0Rd, MODT[0] asserted during read from DRAM CS0
+# bit19-16:2, ODT0Wr, MODT[0] asserted during write to DRAM CS1
+# bit23-20:1, ODT0Wr, MODT[0] asserted during write to DRAM CS0
+
+DATA 0xFFD01498 0x00000000	#  DDR ODT Control (High)
+# bit1-0:  00, ODT0 controlled by ODT Control (low) register above
+# bit3-2:  01, ODT1 active NEVER!
+# bit31-4: zero, required
+
+DATA 0xFFD0149C 0x0000E40F	# CPU ODT Control
+DATA 0xFFD01480 0x00000001	# DDR Initialization Control
+#bit0=1, enable DDR init upon this register write
+
+# End of Header extension
+DATA 0x0 0x0
--- /dev/null
+++ b/board/Marvell/netgear_ms2110/MAINTAINERS
@@ -0,0 +1,6 @@
+NETGEAR_MS2110 BOARD
+M:	bodhi <<EMAIL>>
+S:	Maintained
+F:	board/Marvell/netgear_ms2110
+F:	include/configs/netgear_ms2110.h
+F:	configs/netgear_ms2110_defconfig
--- /dev/null
+++ b/board/Marvell/netgear_ms2110/Makefile
@@ -0,0 +1,13 @@
+#
+# <AUTHOR> <EMAIL>
+#
+# Based on
+# (C) Copyright 2009
+# Marvell Semiconductor <www.marvell.com>
+# <AUTHOR> <EMAIL>
+#
+# SPDX-License-Identifier:	GPL-2.0+
+#
+
+obj-y	:= netgear_ms2110.o
+
--- /dev/null
+++ b/board/Marvell/netgear_ms2110/netgear_ms2110.c
@@ -0,0 +1,152 @@
+/*
+ * Copyright (C) 2014-2017 bodhi <<EMAIL>>
+ *
+ * Based on Kirkwood support:
+ * (C) Copyright 2009
+ * Marvell Semiconductor <www.marvell.com>
+ * Written-by: Prafulla Wadaskar <<EMAIL>>
+ *
+ * See file CREDITS for list of people who contributed to this
+ * project.
+ *
+ * This program is free software; you can redistribute it and/or
+ * modify it under the terms of the GNU General Public License as
+ * published by the Free Software Foundation; either version 2 of
+ * the License, or (at your option) any later version.
+ *
+ * This program is distributed in the hope that it will be useful,
+ * but WITHOUT ANY WARRANTY; without even the implied warranty of
+ * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
+ * GNU General Public License for more details.
+ *
+ * You should have received a copy of the GNU General Public License
+ * along with this program; if not, write to the Free Software
+ * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston,
+ * MA 02110-1301 USA
+ */
+
+#include <common.h>
+#include <miiphy.h>
+#include <netdev.h>
+#include <asm/arch/soc.h>
+#include <asm/arch/mpp.h>
+#include "netgear_ms2110.h"
+#include <asm/arch/cpu.h>
+#include <asm/mach-types.h>
+
+DECLARE_GLOBAL_DATA_PTR;
+int board_early_init_f(void)
+{
+	/*
+	 * default gpio configuration
+	 * There are maximum 64 gpios controlled through 2 sets of registers
+	 * the  below configuration configures mainly initial LED status
+	 */
+	mvebu_config_gpio(NETGEAR_MS2110_OE_VAL_LOW,
+			NETGEAR_MS2110_OE_VAL_HIGH,
+			NETGEAR_MS2110_OE_LOW, NETGEAR_MS2110_OE_HIGH);
+
+	/* Multi-Purpose Pins Functionality configuration */
+	u32 kwmpp_config[] = {
+		MPP0_NF_IO2,
+		MPP1_NF_IO3,
+		MPP2_NF_IO4,
+		MPP3_NF_IO5,
+		MPP4_NF_IO6,
+		MPP5_NF_IO7,
+		MPP6_SYSRST_OUTn,
+		MPP7_SPI_SCn,
+		MPP8_TW_SDA,
+		MPP9_TW_SCK,
+		MPP10_UART0_TXD,
+		MPP11_UART0_RXD,
+		MPP12_SD_CLK,
+		MPP13_SD_CMD,
+		MPP14_SD_D0,
+		MPP15_SD_D1,
+		MPP16_SD_D2,
+		MPP17_SD_D3,
+		MPP18_NF_IO0,
+		MPP19_NF_IO1,
+		MPP20_SATA1_ACTn,
+		MPP21_SATA0_ACTn,
+		MPP22_GPIO,
+		MPP23_GPIO,
+		MPP24_GE1_4,
+		MPP25_GE1_5,
+		MPP26_GE1_6,
+		MPP27_GE1_7,
+		MPP28_GPIO,
+		MPP29_GPIO,
+		MPP30_GPIO,
+		MPP31_GPIO,
+		MPP32_GPIO,
+		MPP33_GE1_13,
+		MPP34_SATA1_ACTn,
+		MPP35_GPIO,
+		MPP36_GPIO,
+		MPP37_GPIO,
+		MPP38_GPIO,
+		MPP39_GPIO,
+		MPP40_GPIO,
+		MPP41_GPIO,
+		MPP42_GPIO,
+		MPP43_GPIO,
+		MPP44_GPIO,
+		MPP45_TDM_PCLK,
+		MPP46_TDM_FS,
+		MPP47_TDM_DRX,
+		MPP48_TDM_DTX,
+		MPP49_GPIO,
+		0
+	};
+	kirkwood_mpp_conf(kwmpp_config, NULL);
+	return 0;
+}
+
+int board_init(void)
+{	/*
+	 * arch number of board
+	 */
+	gd->bd->bi_arch_number = MACH_TYPE_NETGEAR_MS2110;
+
+	/* adress of boot parameters */
+	gd->bd->bi_boot_params = mvebu_sdram_bar(0) + 0x100;
+
+	return 0;
+}
+#ifdef CONFIG_RESET_PHY_R
+/* Configure and enable MV88E1116 PHY */
+void reset_phy(void)
+{
+	u16 reg;
+	u16 devadr;
+	char *name = "egiga0";
+
+	if (miiphy_set_current_dev(name))
+		return;
+
+	/* command to read PHY dev address */
+	if (miiphy_read(name, 0xEE, 0xEE, (u16 *) &devadr)) {
+		printf("Err..%s could not read PHY dev address\n",
+			__FUNCTION__);
+		return;
+	}
+
+	/*
+	 * Enable RGMII delay on Tx and Rx for CPU port
+	 * Ref: sec 4.7.2 of chip datasheet
+	 */
+	miiphy_write(name, devadr, MV88E1116_PGADR_REG, 2);
+	miiphy_read(name, devadr, MV88E1116_MAC_CTRL_REG, &reg);
+	reg |= (MV88E1116_RGMII_RXTM_CTRL | MV88E1116_RGMII_TXTM_CTRL);
+	miiphy_write(name, devadr, MV88E1116_MAC_CTRL_REG, reg);
+	miiphy_write(name, devadr, MV88E1116_PGADR_REG, 0);
+
+	/* reset the phy */
+	miiphy_reset(name, devadr);
+
+	printf("88E1116 Initialized on %s\n", name);
+}
+#endif /* CONFIG_RESET_PHY_R */
+
--- /dev/null
+++ b/board/Marvell/netgear_ms2110/netgear_ms2110.h
@@ -0,0 +1,41 @@
+/*
+ * (C) Copyright 2009
+ * Marvell Semiconductor <www.marvell.com>
+ * Written-by: Prafulla Wadaskar <<EMAIL>>
+ *
+ * See file CREDITS for list of people who contributed to this
+ * project.
+ *
+ * This program is free software; you can redistribute it and/or
+ * modify it under the terms of the GNU General Public License as
+ * published by the Free Software Foundation; either version 2 of
+ * the License, or (at your option) any later version.
+ *
+ * This program is distributed in the hope that it will be useful,
+ * but WITHOUT ANY WARRANTY; without even the implied warranty of
+ * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
+ * GNU General Public License for more details.
+ *
+ * You should have received a copy of the GNU General Public License
+ * along with this program; if not, write to the Free Software
+ * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston,
+ * MA 02110-1301 USA
+ */
+
+#ifndef __NETGEAR_MS2110_H
+#define __NETGEAR_MS2110_H
+
+#define NETGEAR_MS2110_OE_LOW			(~(1 << 7))
+#define NETGEAR_MS2110_OE_HIGH			(~(1 << 2 | 1 << 12))
+#define NETGEAR_MS2110_OE_VAL_LOW		(0)
+#define NETGEAR_MS2110_OE_VAL_HIGH		(1 << 12)
+
+/* PHY related */
+#define MV88E1116_LED_FCTRL_REG		10
+#define MV88E1116_CPRSP_CR3_REG		21
+#define MV88E1116_MAC_CTRL_REG		21
+#define MV88E1116_PGADR_REG		22
+#define MV88E1116_RGMII_TXTM_CTRL	(1 << 4)
+#define MV88E1116_RGMII_RXTM_CTRL	(1 << 5)
+
+#endif /* __NETGEAR_MS2110_H */
--- /dev/null
+++ b/configs/netgear_ms2110_defconfig
@@ -0,0 +1,50 @@
+CONFIG_ARM=y
+CONFIG_KIRKWOOD=y
+CONFIG_SYS_DCACHE_OFF=y
+CONFIG_ARCH_CPU_INIT=y
+CONFIG_TARGET_NETGEAR_MS2110=y
+CONFIG_IDENT_STRING="\nNetgear Stora MS2110"
+CONFIG_SYS_PROMPT="Stora> "
+CONFIG_HUSH_PARSER=y
+CONFIG_SYS_TEXT_BASE=0x600000
+# CONFIG_DISPLAY_BOARDINFO is not set
+# CONFIG_CMD_IMLS is not set
+# CONFIG_CMD_FLASH is not set
+CONFIG_CMD_IDE=y
+CONFIG_MVGBE=y
+CONFIG_MII=y
+CONFIG_SYS_NS16550=y
+CONFIG_OF_LIBFDT=y
+CONFIG_CMD_FDT=y
+CONFIG_CMD_BOOTZ=y
+CONFIG_CMD_SETEXPR=y
+CONFIG_CMD_DATE=y
+CONFIG_CMD_DHCP=y
+CONFIG_CMD_MII=y
+CONFIG_CMD_PING=y
+CONFIG_CMD_EXT2=y
+CONFIG_CMD_EXT4=y
+CONFIG_CMD_FAT=y
+CONFIG_CMD_JFFS2=y
+CONFIG_CMD_USB=y
+CONFIG_ISO_PARTITION=y
+CONFIG_EFI_PARTITION=y
+# CONFIG_MMC is not set
+CONFIG_MTD=y
+CONFIG_MTD_RAW_NAND=y
+CONFIG_MTDPARTS_DEFAULT="mtdparts=orion_nand:0xe0000@0x0(uboot),0x20000@0xe0000(uboot_env),-@0x100000(ubi)"
+CONFIG_CMD_MTDPARTS=y
+CONFIG_CMD_NAND=y
+CONFIG_ENV_IS_IN_NAND=y
+CONFIG_ENV_SIZE=0x20000
+CONFIG_ENV_ADDR=0xe0000
+CONFIG_ENV_OFFSET=0xe0000
+CONFIG_ENV_SECT_SIZE=0x20000
+CONFIG_CMD_UBI=y
+CONFIG_USB=y
+CONFIG_USB_EHCI_HCD=y
+CONFIG_USB_STORAGE=y
+CONFIG_BLK=y
+CONFIG_MVSATA_IDE=y
+CONFIG_DM_RTC=y
+CONFIG_RTC_MV=y
--- /dev/null
+++ b/include/configs/netgear_ms2110.h
@@ -0,0 +1,155 @@
+/*
+ * (C) Copyright 2014-2017 bodhi <<EMAIL>>
+ * (C) Copyright 2020 Zoltan HERPAI <<EMAIL>>
+ *
+ * Based on Kirkwood support: 
+ * (C) Copyright 2009
+ * Marvell Semiconductor <www.marvell.com>
+ * Written-by: Prafulla Wadaskar <<EMAIL>>
+ *
+ * See file CREDITS for list of people who contributed to this
+ * project.
+ *
+ * This program is free software; you can redistribute it and/or
+ * modify it under the terms of the GNU General Public License as
+ * published by the Free Software Foundation; either version 2 of
+ * the License, or (at your option) any later version.
+ *
+ * This program is distributed in the hope that it will be useful,
+ * but WITHOUT ANY WARRANTY; without even the implied warranty of
+ * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
+ * GNU General Public License for more details.
+ *
+ * You should have received a copy of the GNU General Public License
+ * along with this program; if not, write to the Free Software
+ * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston,
+ * MA 02110-1301 USA
+ */
+
+#ifndef _CONFIG_NGMS2110_H
+#define _CONFIG_NGMS2110_H
+
+/*
+ * High Level Configuration Options (easy to change)
+ */
+#define CONFIG_MARVELL		1
+#define CONFIG_ARM926EJS	1	/* Basic Architecture */
+#define CONFIG_FEROCEON_88FR131	1	/* CPU Core subversion */
+#define CONFIG_KIRKWOOD		1	/* SOC Family Name */
+#define CONFIG_KW88F6281	1	/* SOC Name */
+#define CONFIG_MACH_NETGEAR_MS2110	/* Machine type */
+#define CONFIG_SKIP_LOWLEVEL_INIT	/* disable board lowlevel_init */
+
+/*
+ * Commands configuration
+ */
+#define CONFIG_SYS_NO_FLASH             /* Declare no flash (NOR/SPI) */
+#define CONFIG_SYS_MVFS                 /* Picks up Filesystem from mv-common.h */
+#define CONFIG_CMD_ENV
+#define CONFIG_PREBOOT
+#define CONFIG_SYS_HUSH_PARSER
+#define CONFIG_SYS_PROMPT_HUSH_PS2 "> "
+
+/* #define CONFIG_CMD_AUTOSCRIPT */
+
+/*
+ * mv-common.h should be defined after CMD configs since it used them
+ * to enable certain macros
+ */
+#include "mv-common.h"
+
+/* Remove or override few declarations from mv-common.h */
+//#undef CONFIG_SYS_PROMPT        /* previously defined in mv-common.h */
+//#define CONFIG_SYS_PROMPT       "Netgear Stora> "
+
+/*
+ * NAND configuration
+ */
+#ifdef CONFIG_CMD_NAND
+#define CONFIG_NAND_KIRKWOOD
+#define CONFIG_SYS_MAX_NAND_DEVICE	1
+#define NAND_MAX_CHIPS			1
+#define CONFIG_SYS_NAND_BASE		0xD8000000	/* KW_DEFADR_NANDF */
+#define NAND_ALLOW_ERASE_ALL		1
+#endif
+
+/*
+ * Default environment variables
+*/
+#define CONFIG_BOOTCOMMAND \
+        "setenv bootargs ${console} ${mtdparts} ${bootargs_root}; "     \
+        "ubi part ubi; "                                                \
+        "ubifsmount ubi:rootfs; "                                       \
+        "ubi read 0x800000 kernel; "                                    \
+        "bootm 0x800000"
+
+#define CONFIG_EXTRA_ENV_SETTINGS \
+	"arcNumber=2743\0" \
+	"console=console=ttyS0,115200\0"        \
+	"ethact=egiga0\0" \
+	"ethaddr=52:3b:20:9c:11:51\0" \
+	"ipaddr=*************\0" \
+	"mtdids=nand0=orion_nand\0"             \
+	"mtdparts="CONFIG_MTDPARTS_DEFAULT "\0" \
+	"serverip=*************\0" \
+	"bootargs_root=\0"
+
+/* size in bytes reserved for initial data */
+#define CONFIG_SYS_GBL_DATA_SIZE	128
+
+/*
+ * Other required minimal configurations
+ */
+#define CONFIG_STACKSIZE	0x00100000	/* regular stack- 1M */
+
+/*
+ * Ethernet Driver configuration
+ */
+#ifdef CONFIG_CMD_NET
+#define CONFIG_NETCONSOLE      /* include NetConsole support */
+#define CONFIG_NET_MULTI       /* specify more that one ports available */
+#define CONFIG_KIRKWOOD_EGIGA_PORTS    {1,0}   /* enable first port */
+#define CONFIG_MV88E61XX_MULTICHIP_ADRMODE
+#define CONFIG_DIS_AUTO_NEG_SPEED_GMII /*Disable Auto speed negociation */
+#define CONFIG_PHY_SPEED       _1000BASET      /*Force PHYspeed to 1GBPs */
+#define CONFIG_MVGBE_PORTS	{1, 0}	/* enable port 0 only */
+#define CONFIG_PHY_BASE_ADR	0x0A
+#define CONFIG_RESET_PHY_R	/* use reset_phy() to init PHY */
+#endif /* CONFIG_CMD_NET */
+
+/*
+ * USB/EHCI
+ */
+#ifdef CONFIG_CMD_USB
+#define CONFIG_USB_EHCI			/* Enable EHCI USB support */
+#define CONFIG_USB_EHCI_KIRKWOOD	/* on Kirkwood platform	*/
+#define CONFIG_EHCI_IS_TDI
+#define CONFIG_SUPPORT_VFAT
+#endif /* CONFIG_CMD_USB */
+
+/*
+ * File system
+ */
+#define CONFIG_JFFS2_NAND
+#define CONFIG_JFFS2_LZO
+#define CONFIG_MTD_DEVICE               /* needed for mtdparts commands */
+
+/*
+ * SATA 
+ */
+
+#ifdef CONFIG_MVSATA_IDE
+#define CONFIG_SYS_ATA_IDE0_OFFSET      MV_SATA_PORT0_OFFSET
+#define CONFIG_SYS_ATA_IDE1_OFFSET      MV_SATA_PORT1_OFFSET
+#endif
+
+/*
+ *  Date Time
+ *   */
+#ifdef CONFIG_CMD_DATE
+#define CONFIG_RTC_MV
+#define CONFIG_CMD_SNTP
+#define CONFIG_CMD_DNS
+#endif /* CONFIG_CMD_DATE */
+
+#endif /* _CONFIG_NGMS2110_H */
