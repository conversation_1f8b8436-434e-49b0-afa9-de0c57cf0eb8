From 7ffab66a99831ce5e3037b608d73565c9d1abd20 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <paweld<PERSON><PERSON><PERSON>@gmail.com>
Date: Thu, 23 Jan 2020 22:09:51 +0100
Subject: [PATCH 2/2] phy: mv88e61xx: add support for MV88E6171

This patch add MV88E6171 id to driver data.

Tested on Checkpoint L-50 board.

Cc: <PERSON> <<EMAIL>>
Cc: <PERSON> <<EMAIL>>
Cc: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
Cc: <PERSON> <thar<PERSON>@gateworks.com>
Signed-off-by: <PERSON><PERSON><PERSON> <paweldem<PERSON><EMAIL>>
---
 drivers/net/phy/mv88e61xx.c | 14 ++++++++++++++
 1 file changed, 14 insertions(+)

--- a/drivers/net/phy/mv88e61xx.c
+++ b/drivers/net/phy/mv88e61xx.c
@@ -180,6 +180,7 @@
 #define PORT_SWITCH_ID_6071		0x0710
 #define PORT_SWITCH_ID_6096		0x0980
 #define PORT_SWITCH_ID_6097		0x0990
+#define PORT_SWITCH_ID_6171		0x1710
 #define PORT_SWITCH_ID_6172		0x1720
 #define PORT_SWITCH_ID_6176		0x1760
 #define PORT_SWITCH_ID_6220		0x2200
@@ -997,6 +998,7 @@ static int mv88e61xx_probe(struct phy_de
 	switch (priv->id) {
 	case PORT_SWITCH_ID_6096:
 	case PORT_SWITCH_ID_6097:
+	case PORT_SWITCH_ID_6171:
 	case PORT_SWITCH_ID_6172:
 	case PORT_SWITCH_ID_6176:
 	case PORT_SWITCH_ID_6240:
@@ -1152,6 +1154,17 @@ static struct phy_driver mv88e61xx_drive
 	.shutdown = &genphy_shutdown,
 };
 
+static struct phy_driver mv88e617x_driver = {
+	.name = "Marvell MV88E617x",
+	.uid = 0x01410e70,
+	.mask = 0xfffffff0,
+	.features = PHY_GBIT_FEATURES,
+	.probe = mv88e61xx_probe,
+	.config = mv88e61xx_phy_config,
+	.startup = mv88e61xx_phy_startup,
+	.shutdown = &genphy_shutdown,
+};
+
 static struct phy_driver mv88e609x_driver = {
 	.name = "Marvell MV88E609x",
 	.uid = 0x1410c89,
@@ -1177,6 +1190,7 @@ static struct phy_driver mv88e6071_drive
 int phy_mv88e61xx_init(void)
 {
 	phy_register(&mv88e61xx_driver);
+	phy_register(&mv88e617x_driver);
 	phy_register(&mv88e609x_driver);
 	phy_register(&mv88e6071_driver);
 
