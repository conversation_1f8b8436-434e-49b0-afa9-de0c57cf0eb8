# Copyright (C) 2006 OpenWrt.org
# Copyright (C) 2010 Vertical Communications

# commands for emitting messages to network in failsafe mode

indicate_failsafe_led () {
	set_state failsafe
}

indicate_failsafe() {
	[ "$pi_preinit_no_failsafe" = "y" ] && return
	local consoles="$(cat /sys/class/tty/console/active)"
	[ -n "$consoles" ] || consoles=console
	for console in $consoles; do
		[ -c "/dev/$console" ] && echo "- failsafe -" >"/dev/$console"
	done
	preinit_net_echo "Entering Failsafe!\n"
	indicate_failsafe_led
	echo OpenWrt-failsafe > /proc/sys/kernel/hostname
}

boot_hook_add failsafe indicate_failsafe
