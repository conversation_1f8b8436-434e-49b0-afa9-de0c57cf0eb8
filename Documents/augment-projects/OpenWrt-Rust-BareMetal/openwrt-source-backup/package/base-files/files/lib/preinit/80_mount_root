# Copyright (C) 2006 OpenWrt.org
# Copyright (C) 2010 Vertical Communications

missing_lines() {
	local file1 file2 line
	file1="$1"
	file2="$2"
	oIFS="$IFS"
	IFS=":"
	while read line; do
		set -- $line
		grep -q "^$1:" "$file2" || echo "$line"
	done < "$file1"
	IFS="$oIFS"
}

# Rootfs mount options can be passed by declaring in the kernel
# cmdline as much options as needed prefixed with "rootfs_mount_options."
#
# Example:
# rootfs_mount_options.compress_algorithm=zstd rootfs_mount_options.noinline_data
#
compose_rootfs_mount_options() {
	local mount_options
	local cmdlinevar

	for cmdlinevar in $(cat /proc/cmdline); do
		if [ "$cmdlinevar" != "${cmdlinevar#rootfs_mount_options\.}" ]; then
			append mount_options "${cmdlinevar#rootfs_mount_options\.}"
		fi
	done

	echo $mount_options
}

do_mount_root() {
	mount_root start "$(compose_rootfs_mount_options)"
	boot_run_hook preinit_mount_root
	[ -f /sysupgrade.tgz -o -f /tmp/sysupgrade.tar ] && {
		echo "- config restore -"
		cp /etc/passwd /etc/group /etc/shadow /tmp
		cd /
		[ -f /sysupgrade.tgz ] && tar xzf /sysupgrade.tgz
		[ -f /tmp/sysupgrade.tar ] && tar xf /tmp/sysupgrade.tar
		missing_lines /tmp/passwd /etc/passwd >> /etc/passwd
		missing_lines /tmp/group /etc/group >> /etc/group
		missing_lines /tmp/shadow /etc/shadow >> /etc/shadow
		rm /tmp/passwd /tmp/group /tmp/shadow
		# Prevent configuration corruption on a power loss
		sync
	}
}

[ "$INITRAMFS" = "1" ] || boot_hook_add preinit_main do_mount_root
