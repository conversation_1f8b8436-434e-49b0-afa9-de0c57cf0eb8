. /lib/functions.sh

migrate_ports() {
	local config="$1"
	local type ports ifname

	config_get type "$config" type
	[ "$type" != "bridge" ] && return

	config_get ports "$config" ports
	[ -n "$ports" ] && return

	config_get ifname "$config" ifname
	[ -z "$ifname" ] && return

	for port in $ifname; do
		uci add_list network.$config.ports="$port"
	done
	uci delete network.$config.ifname
}

migrate_bridge() {
	local config="$1"
	local type ifname

	config_get type "$config" type
	[ "$type" != "bridge" ] && return

	config_get ifname "$config" ifname

	uci -q batch <<-EOF
		add network device
		set network.@device[-1].name='br-$config'
		set network.@device[-1].type='bridge'
	EOF
	for port in $ifname; do
		uci add_list network.@device[-1].ports="$port"
	done

	uci -q batch <<-EOF
		delete network.$config.type
		delete network.$config.ifname
		set network.$config.device='br-$config'
	EOF
}

config_load network
config_foreach migrate_ports device
config_foreach migrate_bridge interface
