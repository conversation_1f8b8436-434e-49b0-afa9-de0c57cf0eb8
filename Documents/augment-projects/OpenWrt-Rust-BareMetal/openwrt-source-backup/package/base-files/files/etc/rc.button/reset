#!/bin/sh

. /lib/functions.sh

OVERLAY="$( grep ' /overlay ' /proc/mounts )"

case "$ACTION" in
pressed)
	[ -z "$OVERLAY" ] && return 0

	return 5
;;
timeout)
	. /etc/diag.sh
	set_state failsafe
;;
released)
	if [ "$SEEN" -lt 1 ]
	then
		echo "REBOOT" > /dev/console
		sync
		reboot
	elif [ "$SEEN" -ge 5 -a -n "$OVERLAY" ]
	then
		echo "FACTORY RESET" > /dev/console
		factoryreset -y && reboot &
	fi
;;
esac

return 0
