. /usr/share/libubox/jshn.sh

json_init
json_load "$(cat /etc/board.json)"

if json_is_a credentials object; then
	json_select credentials
		json_get_vars root_password_hash root_password_hash
		if [ -n "$root_password_hash" ]; then
			sed -i "s|^root:[^:]*|root:$root_password_hash|g" /etc/shadow
		fi

		json_get_vars root_password_plain root_password_plain
		if [ -n "$root_password_plain" ]; then
			(echo "$root_password_plain"; sleep 1; echo "$root_password_plain") | passwd root
		fi
	json_select ..
fi
