# Copyright (C) 2006-2012 OpenWrt.org
# Copyright (C) 2010 Vertical Communications
#
# This is free software, licensed under the GNU General Public License v2.
# See /LICENSE for more information.
#

config TARGET_DEFAULT_LAN_IP_FROM_PREINIT
	bool "Use preinit IP configuration as default LAN IP" if IMAGEOPT
	default n
	help
		Enabling this will set the default LAN IP address and netmask
		to the preinit values set in the image config.

menuconfig PREINITOPT
	bool "Preinit configuration options" if IMAGEOPT
	default n
	help
		These options are used to control the environment used to initialize
		the system before running init (which typically mean /sbin/init which
		switches to multiuser mode).

config TARGET_PREINIT_SUPPRESS_STDERR
	bool "Suppress stderr messages during preinit" if PREINITOPT
	default y
	help
		Sends stderr to null during preinit.  This is the default behaviour
		in previous versions of OpenWrt.  This also prevents init process
		itself from displaying stderr, however processes launched by init
		in multiuser through inittab will use the current terminal (e.g.
		the ash shell launched by inittab will display stderr).  That's
		the same behaviour as seen in previous version of OpenWrt.

config TARGET_PREINIT_DISABLE_FAILSAFE
	bool
	prompt "Disable failsafe" if PREINITOPT
	default n
	help
		Disable failsafe mode.  While it is very handy while
		experimenting or developing it really ought to be
		disabled in production environments as it is a major
		security loophole.

config TARGET_PREINIT_TIMEOUT
	int
	prompt "Failsafe/Debug wait timeout" if PREINITOPT
	default 4
	help
		How long to wait for failsafe mode to be entered or for
		a debug option to be pressed before continuing with a
		regular boot.

config TARGET_PREINIT_SHOW_NETMSG
	bool
	prompt "Show all preinit network messages" if PREINITOPT
	default n
	help
		Show preinit all network messages (via netmsg broadcast), not only
		the message indicating to press reset to enter failsafe.  Note that
		if the architecture doesn't define an interface, and there is no
		'Preinit network interface' defined, then no messages will be
		emitted, even if this is set.

config TARGET_PREINIT_SUPPRESS_FAILSAFE_NETMSG
	bool
	prompt "Suppress network message indicating failsafe" if ( PREINITOPT && !TARGET_PREINIT_SHOW_NETMSG && !TARGET_PREINIT_DISABLE_FAILSAFE )
	default n
	help
		If "Show all preinit network messages" above is not set, then
		setting this option suppresses the only message that would be
		emitted otherwise, name the network message to enter failsafe
		(via netmsg).

config TARGET_PREINIT_IFNAME
	string
	prompt "Preinit network interface" if PREINITOPT
	default ""
	help
		Interface for sending preinit messages to network, and any other
		default networking in failsafe or preinit.  If empty
		uses $ifname (if defined in /etc/preinit.arch).

config TARGET_PREINIT_IP
	string
	prompt "IP address for preinit network messages" if PREINITOPT
	default "***********"
	help
		IP address used to configure interface for preinit network
		messages, including failsafe messages

config TARGET_PREINIT_NETMASK
	string
	prompt "Netmask for preinit network messages" if PREINITOPT
	default "*************"
	help
		Netmask used to configure interface for preinit network
		messages, including failsafes messages

config TARGET_PREINIT_BROADCAST
	string
	prompt "Broadcast address for preinit network messages" if PREINITOPT
	default "*************"
	help
		Broadcast address to which to send preinit network messages, as
		as failsafe messages


menuconfig INITOPT
	bool "Init configuration options" if IMAGEOPT
	default n
	help
		These option choose the command that will run as the 'init' command
		(that is which is responsible for controlling the system once preinit
		transfers control to it) as well as some options controlling its
		behaviour.  Normally init is /sbin/init.

	config TARGET_INIT_PATH
		string
		prompt "PATH for regular boot" if INITOPT
		default "/usr/sbin:/usr/bin:/sbin:/bin"
		help
			Default PATH used during normal operation

	config TARGET_INIT_ENV
		string
		prompt "Environment variables to set when starting init (start with none)" if INITOPT
		default ""
		help
			Should be a space separated list of variable assignments.  These
			variables will be present in the environment.  Spaces may not be
			present (including through expansion) even in a quoted string
			(env doesn't understanding quoting).

	config TARGET_INIT_CMD
		string
		prompt "Init command" if INITOPT
		default "/sbin/init"
		help
			The executable to run as the init process.  Is 'exec'd by
			preinit (which is the init that the kernel launches on boot).

	config TARGET_INIT_SUPPRESS_STDERR
		bool
		prompt "Suppress stderr messages of init" if INITOPT
		default y
		help
			Prevents showing stderr messages for init command if not already
			suppressed during preinit.  This is the default behaviour in
			previous versions of OpenWrt.  Removing this does nothing if
			stderr is suppressed during preinit (which is the default).


menuconfig VERSIONOPT
	bool "Version configuration options" if IMAGEOPT
	default n
	help
		These options allow to override the version information embedded in
		the /etc/openwrt_version, /etc/openwrt_release, /etc/banner,
		/etc/opkg.conf, and /etc/os-release files. Usually there is no need
		to set these, but they're useful for release builds or custom OpenWrt
		redistributions that should carry custom version tags.

if VERSIONOPT

	config VERSION_DIST
		string
		prompt "Release distribution"
		default "OpenWrt"
		help
			This is the name of the release distribution.
			If unspecified, it defaults to OpenWrt.

	config VERSION_NUMBER
		string
		prompt "Release version number"
		help
			This is the release version number embedded in the image.
			If unspecified, it defaults to SNAPSHOT for the master branch
			or to ##.##-SNAPSHOT on release branches.

	config VERSION_CODE
		string
		prompt "Release version code"
		help
			This is the release version code embedded in the image.
			If unspecified, it defaults to a revision number describing the
			repository version of the source, e.g. the number of commits
			since a branch point or a short Git commit ID.

	config VERSION_REPO
		string
		prompt "Release repository"
		default "https://downloads.openwrt.org/snapshots"
		help
			This is the repository address embedded in the image, it defaults
			to the trunk snapshot repo; the url may contain the following placeholders:
			 %R .. Repository revision ID
			 %V .. Configured release version number or "SNAPSHOT", uppercase
			 %v .. Configured release version number or "snapshot", lowercase
			 %C .. Configured release revision code or value of %R, uppercase
			 %c .. Configured release revision code or value of %R, lowercase
			 %D .. Distribution name or "OpenWrt", uppercase
			 %d .. Distribution name or "openwrt", lowercase
			 %T .. Target name
			 %S .. Target/Subtarget name
			 %A .. Package architecture
			 %t .. Build taint flags, e.g. "no-all busybox"
			 %M .. Manufacturer name or "OpenWrt"
			 %P .. Product name or "Generic"
			 %h .. Hardware revision or "v0"

	config VERSION_HOME_URL
		string
		prompt "Release Homepage"
		help
			This is the release version homepage

	config VERSION_MANUFACTURER
		string
		prompt "Manufacturer name"
		help
			This is the manufacturer name embedded in /etc/device_info
			Useful for OEMs building OpenWrt based firmware

	config VERSION_MANUFACTURER_URL
		string
		prompt "Manufacturer URL"
		help
			This is an URL to the manufacturer's website embedded in /etc/device_info
			Useful for OEMs building OpenWrt based firmware

	config VERSION_BUG_URL
		string
		prompt "Bug reporting URL"
		help
			This is an URL to provide users for providing bug reports

	config VERSION_SUPPORT_URL
		string
		prompt "Support URL"
		help
			This an URL to provide users seeking support

	config VERSION_FIRMWARE_URL
		string
		prompt "Firmware URL"
		help
			This is an URL to provide users for downloading firmware

	config VERSION_PRODUCT
		string
		prompt "Product name"
		help
			This is the product name embedded in /etc/device_info
			Useful for OEMs building OpenWrt based firmware

	config VERSION_HWREV
		string
		prompt "Hardware revision"
		help
			This is the hardware revision string embedded in /etc/device_info
			Useful for OEMs building OpenWrt based firmware

	config VERSION_FILENAMES
		bool
		prompt "Version number in filenames"
		default y
		help
			Enable this to include the version number in firmware image, SDK-
			and Image Builder archive file names

	config VERSION_CODE_FILENAMES
		bool
		prompt "Revision code in filenames"
		default y
		help
			Enable this to include the revision identifier or the configured
			version code into the firmware image, SDK- and Image Builder archive
			file names
endif


menuconfig PER_FEED_REPO
	bool "Separate feed repositories" if IMAGEOPT
	default y
	help
		If set, a separate repository is generated within bin/*/packages/
		for the core packages and each enabled feed.

source "tmp/.config-feeds.in"
