name: Build libubox

on:
  push:
  pull_request:

jobs:
  build-backports:
    name: Build libubox
    runs-on: ubuntu-latest

    steps:
      - name: Install Dependencies
        run: |
          sudo apt update && sudo apt install cmake make gcc pkg-config python3 libjson-c-dev lua5.1 liblua5.1-0-dev

      - name: Checkout
        uses: actions/checkout@v4

      - name: Install libubox
        run: |
          cmake -DBUILD_EXAMPLES=OFF -DUNIT_TESTING=ON
          make
          sudo make install
