name: "CodeQL"

on:
  push:
    branches: [ "master" ]
  pull_request:
    branches: [ "master" ]
  schedule:
    - cron: '31 12 * * 1'

jobs:
  analyze:
    name: <PERSON><PERSON><PERSON> (c-cpp)
    runs-on: ubuntu-latest
    timeout-minutes: 360
    permissions:
      security-events: write

    steps:
      - name: Install Dependencies
        run: |
          sudo apt update && sudo apt install cmake make gcc pkg-config python3 libjson-c-dev lua5.1 liblua5.1-0-dev

      - name: Checkout repository
        uses: actions/checkout@v4

      # Initializes the CodeQL tools for scanning.
      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: c-cpp
          queries: security-and-quality

      - name: Install libubox
        run: |
          cmake -DBUILD_EXAMPLES=OFF
          make
          sudo make install

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
        with:
          category: "/language:c-cpp"
