FIND_PACKAGE(PythonInterp 3 REQUIRED)
FILE(GLOB test_cases "test_*.t")

SET(PYTHON_VENV_DIR "${CMAKE_CURRENT_BINARY_DIR}/.venv")
SET(PYTHON_VENV_PIP "${PYTHON_VENV_DIR}/bin/pip")
SET(PYTHON_VENV_CRAM "${PYTHON_VENV_DIR}/bin/cram")

ADD_CUSTOM_COMMAND(
	OUTPUT ${PYTHON_VENV_CRAM}
	COMMAND ${PYTHON_EXECUTABLE} -m venv ${PYTHON_VENV_DIR}
	COMMAND ${PYTHON_VENV_PIP} install cram
)
ADD_CUSTOM_TARGET(prepare-cram-venv ALL DEPENDS ${PYTHON_VENV_CRAM})

ADD_TEST(
	NAME cram
	COMMAND ${PYTHON_VENV_CRAM} ${test_cases}
	WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
)

SET_PROPERTY(TEST cram APPEND PROPERTY ENVIRONMENT "JSHN=$<TARGET_FILE:jshn>")
SET_PROPERTY(TEST cram APPEND PROPERTY ENVIRONMENT "TEST_BIN_DIR=$<TARGET_FILE_DIR:test-avl>")
