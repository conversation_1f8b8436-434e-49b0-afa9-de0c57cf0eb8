# target/*
"target/airoha":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/airoha/**"
"target/apm821xx":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/apm821xx/**"
"target/armsr":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/armsr/**"
"target/at91":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/at91/**"
    - "package/boot/at91bootstrap/**"
    - "package/boot/uboot-at91/**"
"target/ath79":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/ath79/**"
"target/bcm27xx":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/bcm27xx/**"
    - "package/kernel/bcm27xx-gpu-fw/**"
    - "package/utils/bcm27xx-utils/**"
"target/bcm47xx":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/bcm47xx/**"
"target/bcm4908":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/bcm4908/**"
    - "package/boot/uboot-bcm4908/**"
    - "package/boot/arm-trusted-firmware-bcm63xx/**"
"target/bcm53xx":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/bcm53xx/**"
    - "package/boot/uboot-bcm53xx/**"
"target/bmips":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/bmips/**"
    - "package/boot/uboot-bmips/**"
    - "package/kernel/bcm63xx-cfe/**"
"target/d1":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/d1/**"
    - "package/boot/uboot-d1/**"
    - "package/boot/opensbi/**"
"target/gemini":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/gemini/**"
"target/imx":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/imx/**"
    - "package/boot/imx-bootlets/**"
    - "package/boot/uboot-imx/**"
"target/ipq40xx":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/ipq40xx/**"
"target/ipq806x":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/ipq806x/**"
"target/qualcommax":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/qualcommax/**"
    - "package/kernel/qca-ssdk/**"
    - "package/kernel/qca-nss-dp/**"
"target/qualcommbe":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/qualcommbe/**"
"target/ixp4xx":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/ixp4xx/**"
    - "package/boot/apex/Makefile/**"
    - "package/firmware/ixp4xx-microcode/**"
"target/kirkwood":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/kirkwood/**"
    - "package/boot/uboot-kirkwood/**"
"target/lantiq":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/lantiq/**"
    - "package/kernel/lantiq/**"
    - "package/firmware/lantiq/**"
    - "package/boot/uboot-lantiq/**"
"target/layerscape":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/layerscape/**"
    - "package/firmware/layerscape/**"
    - "package/boot/tfa-layerscape/**"
    - "package/boot/uboot-layerscape/**"
    - "package/network/utils/layerscape/**"
"target/loongarch64":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/loongarch64/**"
"target/malta":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/malta/**"
"target/mediatek":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/mediatek/**"
    - "package/boot/arm-trusted-firmware-mediatek/**"
    - "package/boot/uboot-mediatek/**"
"target/mpc85xx":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/mpc85xx/**"
"target/mvebu":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/mvebu/**"
    - "package/boot/arm-trusted-firmware-mvebu/**"
    - "package/boot/uboot-mvebu/**"
"target/mxs":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/mxs/**"
    - "package/boot/uboot-mxs/**"
"target/octeon":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/octeon/**"
"target/omap":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/omap/**"
    - "package/boot/uboot-omap/**"
"target/pistachio":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/pistachio/**"
"target/qoriq":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/qoriq/**"
"target/ramips":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/ramips/**"
"target/realtek":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/realtek/**"
"target/rockchip":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/rockchip/**"
    - "package/boot/arm-trusted-firmware-rockchip/**"
    - "package/boot/uboot-rockchip/**"
"target/sifiveu":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/sifiveu/**"
    - "package/boot/uboot-sifiveu/**"
    - "package/boot/opensbi/**"
"target/siflower":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/siflower/**"
"target/starfive":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/starfive/**"
"target/stm32":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/stm32/**"
    - "package/boot/arm-trusted-firmware-stm32/**"
    - "package/boot/optee-os-stm32/**"
    - "package/boot/uboot-stm32/**"
"target/sunxi":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/sunxi/**"
    - "package/boot/arm-trusted-firmware-sunxi/**"
    - "package/boot/uboot-sunxi/**"
"target/tegra":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/tegra/**"
    - "package/boot/uboot-tegra/**"
"target/uml":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/uml/**"
"target/x86":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/x86/**"
"target/zynq":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/zynq/**"
    - "package/boot/uboot-zynq/**"
# target/imagebuilder
"target/imagebuilder":
- changed-files:
  - any-glob-to-any-file:
    - "target/imagebuilder/**"
# kernel
"kernel":
- changed-files:
  - any-glob-to-any-file:
    - "target/linux/generic/**"
    - "target/linux/**/config-*"
    - "target/linux/**/patches-*"
    - "target/linux/**/files/**"
    - "package/kernel/linux/**"
# core packages
"core packages":
- changed-files:
  - any-glob-to-any-file:
    - "package/**"
# build/scripts/tools
"build/scripts/tools":
- changed-files:
  - any-glob-to-any-file:
    - "include/**"
    - "scripts/**"
    - "tools/**"
# toolchain
"toolchain":
- changed-files:
  - any-glob-to-any-file:
    - "toolchain/**"
# GitHub/CI
"GitHub/CI":
- changed-files:
  - any-glob-to-any-file:
    - ".github/**"
