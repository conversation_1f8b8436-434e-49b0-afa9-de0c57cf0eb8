name: Build and Push prebuilt tools container

on:
  push:
    paths:
      - 'include/version.mk'
      - 'include/cmake.mk'
      - 'tools/**'
      - '.github/workflows/push-containers.yml'
      - 'toolchain/**'
    branches-ignore:
      - master

permissions:
  contents: read

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build-push-containers:
    name: Build and Push all prebuilt containers
    permissions:
      contents: read
      packages: write
      actions: write
    uses: openwrt/actions-shared-workflows/.github/workflows/push-containers.yml@main
