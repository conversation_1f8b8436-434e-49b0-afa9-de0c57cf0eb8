name: 'Pull Request Labeler'
on:
  - pull_request_target

permissions:
  contents: read

jobs:
  labeler:
    permissions:
      contents: read # to determine modified files (actions/labeler)
      pull-requests: write # to add labels to PRs (actions/labeler)

    name: Pull Request Labeler
    runs-on: ubuntu-latest
    steps:
      - uses: actions/labeler@v5
        with:
          repo-token: '${{ secrets.GITHUB_TOKEN }}'

      - name: Check Branch
        id: check-branch
        run: |
          if echo "${{ github.base_ref }}" | grep -q -E 'openwrt-[0-9][0-9]\.[0-9][0-9]'; then
              echo "release-tag=$(echo ${{ github.base_ref }} | sed 's/openwrt-/release\//')" >> $GITHUB_OUTPUT
          fi

      - uses: buildsville/add-remove-label@v2.0.1
        if: ${{ steps.check-branch.outputs.release-tag }}
        with:
          token: ${{secrets.GITHUB_TOKEN}}
          labels: ${{ steps.check-branch.outputs.release-tag }}
          type: add
