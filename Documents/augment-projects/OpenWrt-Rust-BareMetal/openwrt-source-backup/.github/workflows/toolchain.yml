name: Build Toolchains

on:
  pull_request:
    paths:
      - '.github/workflows/toolchain.yml'
      - 'toolchain/**'
  push:
    paths:
      - '.github/workflows/toolchain.yml'
      - 'toolchain/**'
    branches-ignore:
      - master

permissions:
  contents: read

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.event_name == 'pull_request' }}

jobs:
  build-toolchains:
    name: Build Toolchains for each target
    permissions:
      contents: read
      packages: read
      actions: write
    uses: openwrt/actions-shared-workflows/.github/workflows/toolchain.yml@main
