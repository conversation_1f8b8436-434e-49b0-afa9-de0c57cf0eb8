name: Build all core packages

on:
  pull_request:
    paths:
      - '.github/workflows/packages.yml'
      - 'config/**'
      - 'include/**'
      - 'package/**'
      - 'target/linux/generic/**'
      - 'toolchain/**'
  push:
    paths:
      - '.github/workflows/packages.yml'
      - 'config/**'
      - 'include/**'
      - 'package/**'
      - 'target/linux/generic/**'
      - 'toolchain/**'
    branches-ignore:
      - master

permissions:
  contents: read

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.event_name == 'pull_request' }}

jobs:
  build-packages:
    name: Build all core packages for selected target
    permissions:
      contents: read
      packages: read
      actions: write
    secrets:
      ccache_s3_endpoint: ${{ secrets.CCACHE_S3_ENDPOINT }}
      ccache_s3_bucket: ${{ secrets.CCACHE_S3_BUCKET }}
      ccache_s3_access_key: ${{ secrets.CCACHE_S3_ACCESS_KEY }}
      ccache_s3_secret_key: ${{ secrets.CCACHE_S3_SECRET_KEY }}
    uses: openwrt/actions-shared-workflows/.github/workflows/packages.yml@main
