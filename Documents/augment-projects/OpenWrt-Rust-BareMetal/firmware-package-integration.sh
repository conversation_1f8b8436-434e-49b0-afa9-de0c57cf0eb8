#!/bin/bash

# OpenWrt Rust Firmware - Essential Package Integration Script
# Installs and integrates 16 essential packages with the firmware test environment

echo "=========================================="
echo "OpenWrt Rust Firmware Package Integration"
echo "=========================================="
echo ""

# Check if we're in the right directory
if [ ! -f "Cargo.toml" ]; then
    echo "❌ Error: Please run this script from the OpenWrt-Rust-BareMetal directory"
    exit 1
fi

echo "✅ OpenWrt Rust Firmware project detected"
echo "   Project: $(pwd)"
echo ""

# Function to install packages via opkg
install_packages() {
    echo "📦 INSTALLING ESSENTIAL PACKAGES VIA OPKG"
    echo "=========================================="
    echo ""
    
    # Update package lists first
    echo "🔄 Updating package lists..."
    opkg update
    echo ""
    
    # Define package categories
    declare -A CATEGORIES
    CATEGORIES[networking]="ip-full firewall dnsmasq-full iperf3 tcpdump"
    CATEGORIES[security]="openssh-sftp-server ca-bundle stunnel"
    CATEGORIES[monitoring]="vnstat htop logread sysstat"
    CATEGORIES[admin]="screen nano curl jq"
    
    # Install packages by category
    for category in networking security monitoring admin; do
        packages=${CATEGORIES[$category]}
        
        case $category in
            networking) echo "🌐 CORE NETWORKING & UTILITIES" ;;
            security) echo "🔒 SECURITY & ENCRYPTION" ;;
            monitoring) echo "📊 MONITORING & DEBUGGING" ;;
            admin) echo "🛠️  ADMIN ESSENTIALS" ;;
        esac
        echo "================================"
        
        for package in $packages; do
            echo "Installing $package..."
            opkg install $package
            
            if [ $? -eq 0 ]; then
                echo "   ✅ $package installed successfully"
            else
                echo "   ⚠️  $package installation failed (may not be available)"
            fi
            echo ""
        done
        echo ""
    done
}

# Function to integrate packages with Rust firmware
integrate_with_firmware() {
    echo "🔧 INTEGRATING PACKAGES WITH RUST FIRMWARE"
    echo "==========================================="
    echo ""
    
    # Create integration configuration
    cat > package-integration.json << 'EOF'
{
  "firmware_integration": {
    "project": "OpenWrt Rust Bare-Metal",
    "packages": {
      "networking": {
        "ip-full": {
          "purpose": "Advanced IP routing for VLANs/VPNs",
          "integration": "Network stack configuration",
          "test_command": "ip addr show"
        },
        "firewall": {
          "purpose": "Packet filtering and security",
          "integration": "Security layer integration",
          "test_command": "fw3 --help"
        },
        "dnsmasq-full": {
          "purpose": "DHCP/DNS server with DoT/DoH",
          "integration": "Network services layer",
          "test_command": "dnsmasq --version"
        },
        "iperf3": {
          "purpose": "Network bandwidth testing",
          "integration": "Performance testing framework",
          "test_command": "iperf3 --version"
        },
        "tcpdump": {
          "purpose": "Network packet analysis",
          "integration": "Network debugging tools",
          "test_command": "tcpdump --version"
        }
      },
      "security": {
        "openssh-sftp-server": {
          "purpose": "Secure file transfers",
          "integration": "Security services layer",
          "test_command": "sftp-server --help"
        },
        "ca-bundle": {
          "purpose": "SSL certificate validation",
          "integration": "TLS/SSL infrastructure",
          "test_command": "ls /etc/ssl/certs/"
        },
        "stunnel": {
          "purpose": "TLS/SSL tunneling",
          "integration": "Secure communication layer",
          "test_command": "stunnel -version"
        }
      },
      "monitoring": {
        "vnstat": {
          "purpose": "Network traffic monitoring",
          "integration": "System monitoring framework",
          "test_command": "vnstat --version"
        },
        "htop": {
          "purpose": "Process monitoring",
          "integration": "System resource monitoring",
          "test_command": "htop --version"
        },
        "logread": {
          "purpose": "System log viewing",
          "integration": "Logging and debugging",
          "test_command": "logread -h"
        },
        "sysstat": {
          "purpose": "System performance monitoring",
          "integration": "Performance analysis tools",
          "test_command": "iostat -V"
        }
      },
      "admin": {
        "screen": {
          "purpose": "Terminal multiplexer",
          "integration": "Administrative tools",
          "test_command": "screen -version"
        },
        "nano": {
          "purpose": "Text editor",
          "integration": "Configuration editing",
          "test_command": "nano --version"
        },
        "curl": {
          "purpose": "HTTP/HTTPS testing",
          "integration": "Network testing tools",
          "test_command": "curl --version"
        },
        "jq": {
          "purpose": "JSON processing",
          "integration": "Configuration and API tools",
          "test_command": "jq --version"
        }
      }
    }
  }
}
EOF
    
    echo "✅ Package integration configuration created: package-integration.json"
    echo ""
}

# Function to test package integration
test_integration() {
    echo "🧪 TESTING PACKAGE INTEGRATION"
    echo "==============================="
    echo ""
    
    # Test each package
    packages="ip-full firewall dnsmasq-full iperf3 tcpdump openssh-sftp-server ca-bundle stunnel vnstat htop logread sysstat screen nano curl jq"
    
    installed_count=0
    total_count=0
    
    for package in $packages; do
        total_count=$((total_count + 1))
        echo "Testing $package..."
        
        if opkg list-installed | grep -q "^$package "; then
            version=$(opkg list-installed | grep "^$package " | awk '{print $3}')
            echo "   ✅ $package ($version) - INSTALLED"
            installed_count=$((installed_count + 1))
        else
            echo "   ❌ $package - NOT INSTALLED"
        fi
    done
    
    echo ""
    echo "📊 INTEGRATION SUMMARY"
    echo "======================"
    echo "Total packages: $total_count"
    echo "Successfully installed: $installed_count"
    echo "Success rate: $((installed_count * 100 / total_count))%"
    echo ""
}

# Function to create firmware test with packages
create_firmware_test() {
    echo "🚀 CREATING FIRMWARE TEST WITH PACKAGES"
    echo "========================================"
    echo ""
    
    # Create enhanced test script
    cat > test-firmware-with-packages.sh << 'EOF'
#!/bin/bash

echo "=========================================="
echo "OpenWrt Rust Firmware + Packages Test"
echo "=========================================="
echo ""

# Test Rust firmware compilation
echo "🔧 Testing Rust firmware compilation..."
cargo build --bin simple-qemu-test --target x86_64-unknown-none --release

if [ $? -eq 0 ]; then
    echo "✅ Rust firmware compiled successfully"
else
    echo "❌ Rust firmware compilation failed"
    exit 1
fi

# Test essential packages
echo ""
echo "📦 Testing essential packages..."

# Test jq (JSON processing)
if command -v jq >/dev/null 2>&1; then
    echo '{"firmware": "OpenWrt Rust", "status": "testing"}' | jq '.firmware'
    echo "✅ jq JSON processing works"
else
    echo "⚠️  jq not available"
fi

# Test curl (HTTP client)
if command -v curl >/dev/null 2>&1; then
    echo "✅ curl HTTP client available"
else
    echo "⚠️  curl not available"
fi

# Test network tools
if command -v ip >/dev/null 2>&1; then
    echo "✅ ip networking tools available"
else
    echo "⚠️  ip tools not available"
fi

echo ""
echo "🎉 Firmware + Packages integration test completed!"
EOF
    
    chmod +x test-firmware-with-packages.sh
    echo "✅ Enhanced firmware test script created: test-firmware-with-packages.sh"
    echo ""
}

# Main execution
echo "🚀 Starting package installation and integration..."
echo ""

# Check if opkg is available (simulate for development environment)
if command -v opkg >/dev/null 2>&1; then
    echo "✅ opkg package manager found"
    install_packages
else
    echo "⚠️  opkg not found - simulating package installation for development"
    echo "   In a real OpenWrt environment, packages would be installed via:"
    echo ""
    echo "   # Core Networking & Utilities"
    echo "   opkg install ip-full firewall dnsmasq-full iperf3 tcpdump"
    echo ""
    echo "   # Security & Encryption"
    echo "   opkg install openssh-sftp-server ca-bundle stunnel"
    echo ""
    echo "   # Monitoring & Debugging"
    echo "   opkg install vnstat htop logread sysstat"
    echo ""
    echo "   # Admin Essentials"
    echo "   opkg install screen nano curl jq"
    echo ""
fi

# Always create integration files
integrate_with_firmware
create_firmware_test

if command -v opkg >/dev/null 2>&1; then
    test_integration
else
    echo "📋 DEVELOPMENT ENVIRONMENT SUMMARY"
    echo "=================================="
    echo "✅ Integration configuration created"
    echo "✅ Enhanced firmware test script created"
    echo "✅ Package integration framework ready"
    echo ""
    echo "🔧 To complete integration in OpenWrt environment:"
    echo "1. Transfer this project to OpenWrt device"
    echo "2. Run: ./firmware-package-integration.sh"
    echo "3. Execute: ./test-firmware-with-packages.sh"
fi

echo ""
echo "🎉 Package integration setup completed!"
echo "=========================================="
